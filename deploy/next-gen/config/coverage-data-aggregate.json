{"ACRBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,8,9,10,11,13,15,16,19,20,23,25,26,27,28,29,30,31,33,37,38,39,40,41,42", "uncoveredLines": ""}, "ATRAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "ATRBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "AbsenceAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6", "uncoveredLines": ""}, "AbsenceAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "AbsenceBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AbsenceManagementAuraSelector": {"coverageRate": "0.79", "coveredLines": "13,14,15,16,22,24,25,26,27,28,29,34,36,39,40,41,54,55,62,63,66,67,68,69,70,72,75,76,78,80,81,82,84,85,87,89,92,93,94,95,96,99,100,101,102,103,104,108", "uncoveredLines": "2,3,4,5,8,30,44,45,46,49,51,58,59"}, "AbsenceManagementService": {"coverageRate": "0.67", "coveredLines": "3,4,6,7,10,11,12,15,16,17,25,26,27,28,31,39,40,41,42,45,47,48,49,50,53,56,57,60,63,64", "uncoveredLines": "20,21,22,34,35,36,58,67,68,69,70,71,72,75,76"}, "AbsencePendingApprovalEmailController": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,10,11,12,13,14,15,18,19,21,23,24,25,26,27,28,30,33,34", "uncoveredLines": ""}, "AbsenceRequestAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10", "uncoveredLines": ""}, "AbsenceRequestBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8", "uncoveredLines": ""}, "AbsenceRequestBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,8,9,12,13,14,15,18,19,20", "uncoveredLines": ""}, "AbsenceRequestBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": ""}, "AbsenceRequestGroupSobjectHandler": {"coverageRate": "0.91", "coveredLines": "2,3,4,5,6,8,9,12,13,14,18,19,21,22,23,24,31,32,33,34,35,36,37,40,41,48,50,53,54,57,60,61,62,68,69,70,71,72,74", "uncoveredLines": "15,26,27,63"}, "AbsenceRequestSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,11,12,13", "uncoveredLines": ""}, "AbsenceRequestService": {"coverageRate": "1.00", "coveredLines": "5,6,7,10,11,12,15,16,17,18,20,21,22,23,28,29,30", "uncoveredLines": ""}, "AbsenceRequestTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AbsenceRestrictionResourceDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AbsenceSelector": {"coverageRate": "1.00", "coveredLines": "2,3,18,22,23,26", "uncoveredLines": ""}, "AbsenceService": {"coverageRate": "0.88", "coveredLines": "3,4,5,6,7,8,9,10,11,13,14,15,16,17,21,22,23,24,25,26,27,28,29,30,31,32,36,40,41,42,43,44,45,46,47,48,49,51,52,54,58,59,60,61,62,65,66,68,69,72,75,76,77,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,101,102,108,109,110,112,113,117,118,120,121,125,126,127,128,129,133,134,135,136,137,138,140,141,142,146,147,148,149,150,151,153,154,155,157,158,160,161,162,163,164,165,166,167,168,170,171,172,176,177,178,179,180,181,182,183,184,185,186,187,188,191,194,195,197,198,199,200,201,202,203,206,208,221,222,224,225,228,229,230,234,235,236,238,239,240,243,244,245,246,249,250,252,255,256,257,258,259,260,261,262,263,264,266,269,270,274,275,276,277,278,279,281,283,286,287,289,290,291,292,294,298,301,302,303,304,307,308,309,310,311,312,313,314,315,316,317,321,323,324,325,326,327,329,333,334,335,336,337,341,342,343,345,351,354,355,356,357,358,362,363,364,365,366,367,368,369,370,372,374,375,379,386,391,392,393,394,395,399,403,404,405,406,407,408,409,410,411,412,413,415,418,419,420,421,422,423,424,425,426,429,431,432,433,434,435,436,439,442,443,444,445,446,448,451,452,453,454,477,478,479,480,481,482,484,485,486,487,489,490,493,494,495,497,500,501,502,503,504,505,506,507,510,511,512,513,515,516,518,519,521,522,524,527,530,535,536,537,538,540,541,543,544,546,547,549,550,552,555,560,561,562,563,564,565,570,571,574,575,576,577,579,580,581,582,583,587,588,589,590,600,601,602,603,604,605,608", "uncoveredLines": "33,34,37,38,70,98,99,103,104,106,189,192,209,210,211,212,213,214,217,330,388,389,396,397,400,401,456,457,459,460,462,463,465,466,468,469,471,472,525,528,531,553,556,566,567,592,593,594,595,596"}, "AbsenceTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AbsenceTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,16,17,18,19,20,26,27,28,29,30", "uncoveredLines": "12,22,32"}, "Absence_RequestTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,7,8,9,10,11,17,18,19,20,21,27,28,29,30,31,37,38,39,40,41", "uncoveredLines": "13,23,33,43"}, "AccMAccessAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8", "uncoveredLines": ""}, "AccMAccessAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,11,12,13", "uncoveredLines": ""}, "AccMAccessAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,16,17,18", "uncoveredLines": ""}, "AccMAccessBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "AccMAccessBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "AccMAccessBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "AccountAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,9", "uncoveredLines": ""}, "AccountAfterInsertTriggerHandler": {"coverageRate": "0.95", "coveredLines": "5,6,7,10,11,12,13,14,15,16,17,18,19,20,23,24,25,26", "uncoveredLines": "27"}, "AccountAfterUpdateTriggerHandler": {"coverageRate": "0.96", "coveredLines": "7,8,9,10,11,15,16,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,42,43,44,45,52,53,54,55,56,57,58,60,61,62,65,66,67,68,72,73,74,78,79,80,84,85,86,90,91,94,98,99,100,101,102,105,106,107,109,110,114,115,118,119,123,124,127,128,134,135", "uncoveredLines": "46,116,125"}, "AccountBatchUpdate": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10", "uncoveredLines": ""}, "AccountBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10,11,12,13,14,15,16,19,20,21,22", "uncoveredLines": ""}, "AccountBeforeUpdateTriggerHandler": {"coverageRate": "0.95", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,30,31,32,33,34,35,36,37,42,43,44,45,46,51", "uncoveredLines": "47,52"}, "AccountBillingController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountBillingSetUpController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountClientCodeGenerateQueueableJob": {"coverageRate": "0.66", "coveredLines": "2,15,16,19,20,21,24,25,26,27,28,30,31,32,34,35,36,37,39,40,41,42,48,49,52,56,57,63,72", "uncoveredLines": "7,9,11,43,44,46,53,54,58,64,65,66,68,73,74"}, "AccountConflictController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountContactAddedDTO": {"coverageRate": "0.64", "coveredLines": "3,6,9,12,14,15,16,17,18", "uncoveredLines": "21,22,23,24,25"}, "AccountContactAddedDetailDTO": {"coverageRate": "0.43", "coveredLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "uncoveredLines": "62,63,64,65,66,67,68,69,70,71,72,73,74,75,77,78,79,81,82,101"}, "AccountContactRelationController": {"coverageRate": "1.00", "coveredLines": "3,4,5,6", "uncoveredLines": ""}, "AccountContactRelationSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6,9,10,13"}, "AccountContactRelationTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "AccountContactRelationTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "AccountController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountDeleteContext": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AccountDuplicateDetectController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountEngagementController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountEngagementService": {"coverageRate": "0.80", "coveredLines": "2,21,23,25,27,29,31,33,35,38,39,40,43,44,45,46,47,48,49,50,51,52,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71,72,75,76,79,85,86,87,88,89,90,91,92,93,126,127,130,131,132,133,135,138,139,140,141,142,156,157,160,161,162,167,168,169,172,175,176,177,179,180,181,182,183,185,187,188,189,190,202,205,207,208,209,210,211,212,213,216,218,221,222,225,226,227,228,229,230,231,232,234,235,238,239,240,241,243,244,245,246,247,249,250,251,254,255,256,257,258,259,260,264,265,268,269,270,271,272,273,275,276,278,279,284,285,287,288,289,290,292,293,295,297,300,301,302,303,304,307,310,311,312,313,315,316,317,318,321,322,323,324,325,328", "uncoveredLines": "41,95,96,97,98,100,101,102,103,104,105,106,108,109,110,112,113,114,115,117,118,121,123,144,145,146,147,148,151,153,158,163,164,170,191,192,193,195,196,197,223,266,281"}, "AccountHistoryController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountHistorySelector": {"coverageRate": "0.20", "coveredLines": "2", "uncoveredLines": "4,5,6,20"}, "AccountHistoryService": {"coverageRate": "0.98", "coveredLines": "2,3,4,5,6,7,8,9,10,11,14,19,20,23,24,27,28,29,30,31,33,34,37,38,39,40,41,42,43,47,48,49,50,51,52,53,54,55,56,57,58,59,64", "uncoveredLines": "16"}, "AccountInvoiceReviewController": {"coverageRate": "0.49", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13,14,15,17,19,20,21,22,25,27,28,29,30,31,32,33,34,35,37,41,42,43,44,47,48,49,50,51,61,62,64,65,66,68,69,70,71,72,76,77,79,80,81,83,85,86,87,90,137,138,148,149,150,151,152,161,162,163,164,165,166,169,170,171,172,173,201,202,203,215,216,217,251,253,256,257,258,259,262", "uncoveredLines": "23,54,55,56,57,58,91,92,94,95,97,98,99,100,101,102,103,104,105,106,107,108,110,111,112,113,114,116,117,118,121,122,123,126,127,128,129,130,131,132,135,141,142,143,144,145,155,156,157,158,176,177,178,179,180,183,184,185,186,187,190,191,192,194,196,197,198,205,208,209,210,212,220,221,222,223,224,225,226,227,229,230,232,233,235,236,238,239,241,242,244,245"}, "AccountLevelInvoiceCallBack": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountLevelTimecardManagerPredicate": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,14,15,16,17,21,22,23,24,26,29,30,31,33,34,35,36,37,39,40", "uncoveredLines": ""}, "AccountMarketMappingSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6", "uncoveredLines": ""}, "AccountMarketMappingService": {"coverageRate": "0.94", "coveredLines": "4,5,8,9,10,13,14,15,16,19,20,22,23,24,26", "uncoveredLines": "11"}, "AccountMemberAccessEventTrigger": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountMemberAccessSelector": {"coverageRate": "0.78", "coveredLines": "2,3,4,8,9,10,14,15,16,26,27,28,29,32,35,37,38,39,42,45,48,49,50,54,57,59,60,61,62,64,67,70,71,72,75,81,82,83,93,94,95,99,100,108,111,112,113,116,122,123,124,127,143,144,145,148,153,154,157", "uncoveredLines": "20,21,22,40,51,87,88,89,96,101,102,103,105,133,134,135,138"}, "AccountMemberAccessService": {"coverageRate": "0.96", "coveredLines": "2,3,22,23,24,25,26,27,28,29,30,31,35,42,43,44,45,46,49,50,51,52,54,55,56,57,58,63,64,65,66,67,68,70,71,72,74,75,76,78,79,80,81,83,86,87,88,89,92,93,94,95,102,103,105,106,107,108,111,112,114,115,117,118,119,122,124,125,126,127,128,130,133,138,139,140,141,142,144,145,147,148,152,153,154,155,156,157,158,159,160,165,166,170,171,175,176,177,178,179,182,184,186,189,190,193,194,195,196,197,198,199,200,203,206,207,209,210,211,212,213,216,220,221,224,225,228,229,230,231,232,236,237,238,239,240,241,242,243,247,248,249,250,251,252,253,256,259,260,261,262,263,264,265,266,267,268,269,270,272,276,279,280,281,282,283,284,285,286,287,289,292,293,294,295,296,297,298,300,301,303,304,305,306,307,308,309,311,314,317,318,319,320,321,322,323,325,326,327,328,329,330,331,332,333,335,338,341,342,343,344,345,346,347,350,353,354,355,356,357,358,362,365,366,367,368,369,370,373,376,377,378,379,380,381,383,386,387,389,390,391,392,393,396,400,403,404,405,409,410,411,412,416,417,418,419,420,423,424,426,427,428,433,434,436,437,447,448,449,459,460,462,463,464,465,467,469,470,471,472,473,474,475,478,481,482,483,484,485,487,488,491,494,495,498,499,500,501,504,505,506,507,509,513,514,515,516,517,518,520,521,524,525,526,527,528,529,530,533,536,539,542,543,544,545,546,547,548,549,551,554,557,561,562,563,564,565,569,570,573,574,575,576,577,578,581,584,586,587,588,590,593,594,595,596,597,600,601,602,603,604,605,609,610", "uncoveredLines": "131,134,161,162,180,214,394,429,430,438,439,440,441,452,453,454,531"}, "AccountMemberAccessTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AccountOrgChartController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountOrgChartService": {"coverageRate": "0.93", "coveredLines": "2,3,5,13,15,17,18,19,21,22,23,24,25,26,30,31,32,33,35,38,39,41,42,43,45,46", "uncoveredLines": "9,10"}, "AccountSalesMULocationPredicate": {"coverageRate": "1.00", "coveredLines": "2,5,6,7,11,12,15,16,17,20,21,22,24", "uncoveredLines": ""}, "AccountSelector": {"coverageRate": "0.75", "coveredLines": "2,5,6,7,65,87,88,89,93,94,95,114,119,120,129,133,134,135,143,148,149,150,182,183,186,187,189,190,219,229,230,233,234,241,242,243,261,266,267,268,274,275,278,279,293,297,298,301,302,305,306,309,310,313,314,321,325,326,327,342,351,352,353,354,359,363,364,365,414,419,420,423,424,427,428,431,437,438,439,441,442,445,446,447,450,455,456,477,481,482,483,486,491,492,495,521,522", "uncoveredLines": "70,71,72,82,154,155,170,173,174,175,179,188,225,226,237,238,270,347,348,499,500,501,504,505,506,507,508,509,510,511,513,516,518"}, "AccountService": {"coverageRate": "0.92", "coveredLines": "2,15,16,17,18,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,62,63,66,67,68,69,72,73,74,76,78,79,81,83,84,85,86,89,91,92,93,94,98,99,101,102,103,104,105,109,110,111,112,113,114,115,117,118,119,123,124,131,132,133,137,138,139,140,141,146,147,148,149,150,153,154,156,157,158,159,160,161,162,163,164,165,166,167,168,171,172,175,179,180,181,182,187,188,190,192,194,195,196,197,198,199,201,202,203,204,205,207,209,211,214,217,218,219,220,221,225,228,229,230,231,232,233,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,254,255,257,258,259,260,261,262,263,265,266,268,270,273,274,275,278,279,280,282,285,286,287,288,289,290,291,294,295,296,299,300,301,302,303,304,306,309,310,311,312,313,316,317,319,322,323,324,325,327,328,329,330,332,333,337,338,340,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,365,366,367,370,373,374,375,378,382,394,395,396,397,398,399,400,401,402,403,406,407,408,409,410,415,416,417,418,419,420,421,422,424,427,428,429,430,435,436,437,438,444,445,446,447,448,449,452,453,455,456,457,458,463,464,465,466,467,471,472,475,477,479,480,481,482,486,487,491,492,493,494,495,496,499,500,503,504,505,507,508,509,510,511,512,513,514,516,518,519,520,521,522,523,525,530,531,534,535,542,543,551,552,553,554,555,556,557,560,561,562,565,566,567,568,569,570,574,577,579,580,581,592,593,594,595,596,597,609,612,613,614,616,617,618,620,621,622,625,627,630,631,641,642,645,647,649,650,651,652,661,664,665,666,667,668,669,670,671,675,678,679,680,681,682,683,688,689,690,693,699,701,702,703,704,705,708,709,710,713,714,716,717,718,720,721,723,724,725,726,728,730,731,732,740,741,742,743,745,748,749,750,751,752,753,754,757,758,761,762,763,765,766,767,768,771,776,778,779,780,781,782,783,785,788,789,804,807,808,809,810,813,815,817,818,821,822,823,826,827,828,829,831,832,833,835,838,839,840,841,842,845,846,847,848,849,852,853,854,855,856,859,860,861,862,863,864,865,866,870,871,872,876,877,878,881,886,887,888,889,892,893,895,896,897,898,899,901,904,905,907,908,909,910,911,912,913,914,916,921,922,925,926,927,928,931,934,935,938,939,941,942,943,947,948,949,954,955,956,957,958,959,961,962,967,968,971,974,975,976,977,978,979,980,981,983,984,989,990,993,996,997,998,999,1003,1004,1005,1006,1007,1012,1013,1014,1015,1016,1018,1021,1022,1025,1026,1028,1029,1032,1034,1036,1038,1039,1043,1044,1045,1046,1047,1048,1053,1056,1060,1061,1062,1063,1064,1067,1070,1071,1072,1073,1074,1075,1079,1082,1083,1085,1086,1087,1090,1091,1094,1095,1096,1097,1098,1099,1101,1102,1103,1104,1105,1106,1107,1109,1110,1111,1113,1118,1119,1120,1123,1126,1127,1128,1129,1130,1133,1136,1137,1139,1140,1142,1145,1146,1148,1149,1152,1154,1158,1160,1161,1166,1167,1168,1169,1170,1171,1172,1174,1175,1176,1177,1178,1180,1181,1185,1188,1189,1190,1191,1192,1193,1195,1196,1197,1198,1199,1200,1201,1205,1206,1207,1210,1213,1214,1215,1216,1217,1218,1220,1221,1223,1224,1225,1226,1230,1231,1234,1235,1236,1237,1238,1243,1244,1245,1246,1247,1248,1249,1250,1252,1256,1257,1259,1260,1262,1263,1264,1266,1269,1270,1271,1272,1274,1279,1280,1281,1283,1284,1287,1291,1292,1293,1294,1295,1296,1302,1303,1304,1305,1306,1308,1312,1313,1315,1316,1370,1371,1372,1373,1374,1375,1376,1378,1381,1384,1385,1386,1387,1389,1390,1391,1394,1395,1396,1397,1398,1399,1403,1404,1407,1409,1410,1411,1412,1413,1422,1423,1424,1426,1427,1428,1429,1430,1433,1434,1435,1436,1437,1438,1439,1441,1442,1447,1448,1449,1450,1452,1455,1456,1458,1459,1465,1466,1467,1468,1473,1475,1478,1479,1480,1481,1482,1486,1487,1488,1489,1491,1492,1493,1498,1499,1500,1505,1506,1507,1508,1512,1515,1516,1517,1518,1519,1522,1523,1525,1526,1527,1528,1531,1532,1533,1537,1538,1539,1540,1541,1542,1543,1546,1547,1550,1551,1552,1553,1556,1557,1558,1559,1560,1561,1562,1563,1565,1566,1569,1570,1571,1574,1577,1578,1579,1580,1582,1583,1585,1586,1590,1591,1592,1593,1596,1599,1600,1601,1602,1604,1606,1609,1610,1611,1612,1614,1615,1617,1618,1620,1621,1622,1623,1624,1625,1628,1629,1631,1635,1636,1637,1638,1639,1642,1643,1644,1645,1649,1652,1653,1654,1655,1656,1659,1663,1664,1665,1666,1668,1672,1673,1674,1675,1677,1680,1681,1682,1684,1686,1689,1690,1691,1694,1695,1696,1698,1699,1700,1701,1702,1704,1705,1707,1708,1709,1714,1715,1716,1719,1720,1721,1722,1724,1727,1728,1729,1730,1732,1733,1734,1736,1737,1738,1744,1745,1746,1747,1748,1749,1752,1754,1755,1757,1763,1764,1765,1768,1769,1772,1773,1774,1775,1776,1777,1778,1779,1783,1784,1785,1787,1788,1789,1790,1791,1792,1794,1797,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1812,1813,1814,1815,1818,1819,1820,1823,1824,1825,1826,1827,1828,1829,1830", "uncoveredLines": "121,439,538,539,546,547,548,571,572,582,583,585,587,598,599,600,602,603,604,623,632,633,635,637,653,654,656,691,772,773,774,790,793,794,795,796,797,799,917,1049,1050,1051,1155,1314,1318,1322,1323,1324,1325,1327,1329,1330,1331,1332,1333,1334,1335,1336,1337,1339,1340,1341,1342,1349,1352,1353,1356,1357,1358,1359,1360,1361,1362,1364,1365,1367,1379,1418,1419,1460,1461,1462,1469,1470,1471,1494,1501,1529,1554,1640,1717,1750,1758,1780,1810,1821"}, "AccountServiceLWCAdapter": {"coverageRate": "0.50", "coveredLines": "3,4,5", "uncoveredLines": "9,10,11"}, "AccountShareBatchableJob": {"coverageRate": "0.91", "coveredLines": "2,10,12,13,14,15,16,17,18,21,22,24,25,27,31,32,33,34,35,36,37,38,39,44,45,49,52,53,54,55,56,58,59,60,61,63,64,69,70,73,76,79,80,83,84,87,88,89,90,92,93", "uncoveredLines": "28,29,47,74,85"}, "AccountShareRevokeBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,10,11,13,14,15,16,17,18,20,21,23,27,28,29,32,33,34,35,36,40,42,43,46,49,50,51,53,57,58,59,60,62,63,64,65,66,67,69,72,73,74,75,76,77,78,79,81,86", "uncoveredLines": ""}, "AccountShareSelector": {"coverageRate": "1.00", "coveredLines": "2,5,6,7,11,12,15,16,17,18,21,22,25,26,27,28,31,34,35,36,37,39", "uncoveredLines": ""}, "AccountSnapShotController": {"coverageRate": "1.00", "coveredLines": "2,5,6", "uncoveredLines": ""}, "AccountTMemberAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,11,12,13,14", "uncoveredLines": ""}, "AccountTMemberAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,10,12,13,14,15,16,17,18,19,20", "uncoveredLines": ""}, "AccountTMemberAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,10,11,13,14,15,16,17,18,19,20", "uncoveredLines": ""}, "AccountTargetController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountTargetSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AccountTeamMemberManageController": {"coverageRate": "0.59", "coveredLines": "4,5,6,7,8,9,28,29,30,31,32,34,51,52,56,57,61,62,63,92,93,97,99,100,102,103,108,111,112,113,114,118,119,120,123,124,127,128,129,130,131,133,134,135,137,138", "uncoveredLines": "12,13,17,18,22,23,24,38,39,43,44,45,47,66,68,69,70,71,72,74,78,79,80,82,83,84,85,88,94,104,105,115"}, "AccountTeamMemberSelector": {"coverageRate": "0.71", "coveredLines": "2,3,14,15,18,19,22,23,26,27,30,31,32,36,37,38,42,43,44,48,49,52,53,68,69,70,74,77,79,80,81,82,84,87,94,95,102,103,106,107,108,109,110,112,114", "uncoveredLines": "6,7,10,11,56,57,60,61,64,65,71,90,91,98,99,117,118,119"}, "AccountTeamMemberService": {"coverageRate": "0.96", "coveredLines": "2,17,18,19,21,22,23,24,25,26,27,28,29,30,31,32,35,48,49,50,51,52,53,54,55,56,57,58,61,62,63,64,65,66,67,68,69,70,72,76,79,80,81,82,83,85,86,87,88,89,91,94,95,96,97,98,99,101,104,105,106,107,109,112,113,114,115,118,119,120,121,122,123,124,125,126,127,132,135,136,137,138,140,141,142,143,144,145,146,147,148,149,150,151,152,154,155,161,162,163,166,167,168,169,170,173,176,177,180,181,182,183,184,185,187,190,192,193,196,197,198,199,200,202,203,204,207,208,209,210,212,214,215,216,217,218,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,248,249,250,251,252,253,256,257,259,262,263,264,265,266,267,268,269,272,275,276,277,278,279,280,283,284,288,289,290,291,292,293,296,297,301,302,303,307,308,309,310,311,312,313,314,315,317,318,321,322,323,325,326,327,328,329,330,331,334,337,338,339,343,345,346,347,348,349,353,354,355,356,357,358,360,362,366,367,368,369,370,372,374,378,379,380,381,382,383,386,390,391,392,393,394,395,396,397,398,400,402,406,407,408,409,410,411,412,413,417,418,419,422,423,424,425,426,427,429,430,431,434,435,438,439,441,442,443,449,453,454,455,456,457,458,461,462,463,464,465,466,467,475,478,479,491,492,493,496,497,498,499,500,501,502,503,506,507,515,516,518,519,520,521,522,523,528,530,531,532,533,535,538,539,540,541,542,543,544,545,546,548,549,554,558,559,560,561,563,567,568,569,570,571,572,574,576", "uncoveredLines": "157,205,319,332,436,450,451,468,469,480,481,482,483,484,487"}, "AccountTeamMemberTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AccountTeamMemberTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,16,17,18,19,20,26,27,28,29,30", "uncoveredLines": "12,22,32"}, "AccountTeamRequestTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AccountTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AccountTriggerDispatcher": {"coverageRate": "0.88", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,18,19,20,21,22,27,28,29,30,31,36,37,38,39,40,45,46,47,48,49,51,54,55,56,57,58", "uncoveredLines": "15,24,33,42,60"}, "Account_Member_AccessTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,18,19,20,21,22,27,28,29,30,31,36,37,38,39,40,45,46,47,48,49,54,55,56,57,58", "uncoveredLines": "15,24,33,42,51,60"}, "Account_Team_RequestTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,15,16,17,18,19", "uncoveredLines": "11,21"}, "Accountshare": {"coverageRate": "0.48", "coveredLines": "5,6,7,10,11,12,13,14,15,16,18,19,20,21,23,34,36,37,39,40,43,53,56,57,79,80,81,82", "uncoveredLines": "24,25,26,29,30,31,58,59,60,61,62,63,64,66,67,70,71,83,84,85,86,87,88,89,90,91,92,93,94,95"}, "ActivityContext": {"coverageRate": "0.83", "coveredLines": "2,7,8,11,14,15,16,17,18,21", "uncoveredLines": "4,9"}, "ActivityController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ActivityDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ActivitySelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ActivityService": {"coverageRate": "0.93", "coveredLines": "2,8,9,10,11,12,15,16,17,18,19,22,23,24,27,28,29,30,33,34,35,36,39,40,41,42,45,46,47,48,49,50,51,52,53,55,58,61,62,63,64,65,66,67,68,69,70,71,74,75,77,78,80,81,84,85,86,88,89,91,92,95,96,97,98,100,101,102,103,104,106,109,110,112,113,116,119,120,123,126,129,130,131,132,133,135,138,139,140,141,142,143,145,146,147,148,149,151,152,154,156,157,158,159,162,165,166,167,168,170,171,172,173,174,175,176,177,178,179,180,182,185,189,190,191,192,193,194,195,196,197,198,199,201,202,204,209,212,213,216,217,218,219,223,226,227,228,230,231,245,246,247,248,249,251,252,253,256,259,260,264,265,268,269,272,275,276,277", "uncoveredLines": "114,121,183,214,220,234,235,236,237,238,239,242,261"}, "ActivityUnifiedDTO": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,10,11,12,13,14,15,16,17", "uncoveredLines": ""}, "ActualCostCalculationBatchableJob": {"coverageRate": "0.34", "coveredLines": "2,4,5,6,7,8,9,10,12,21,30,31,32,35,36,39,40,86,101,102,103,104,105,106,107,108,109,110,111,112,113,207,208,211,214,215,216,217,219,222,223,224,225,226,227,230,231,232,233,236,237,238,239,240,242,245", "uncoveredLines": "44,55,56,57,58,59,60,61,62,63,66,76,77,78,79,80,82,116,117,118,119,120,123,124,125,126,127,129,130,131,133,134,135,136,137,138,142,143,144,146,147,148,149,152,153,154,155,157,159,160,161,162,163,164,165,166,168,169,170,171,172,173,174,175,178,179,180,181,183,185,187,188,189,190,191,192,194,196,199,200,201,202,203,209,212,248,249,251,252,253,254,255,256,257,258,259,260,261,262,264,265,269,270,271,273,274,276"}, "ActualCostSelector": {"coverageRate": "0.33", "coveredLines": "2", "uncoveredLines": "5,6"}, "AdoptionTrackingMonthlyScheduleJob": {"coverageRate": "0.79", "coveredLines": "6,7,13,14,16,17,19,20,24,25,31,32,33,34,35,38,41,42,44,45,47,51,52,58,62,63,70,71,73,76,80,81,83,92,101,110,111,112,113,114,115,118,119,120,121,122,123,126,127,128,129,130,131,134,135,136,137,138,139,143", "uncoveredLines": "9,10,74,77,84,85,87,88,93,94,96,97,102,103,105,106"}, "AdoptionTrackingWeeklyScheduleJob": {"coverageRate": "0.88", "coveredLines": "6,7,13,14,16,18,19,23,24,35,36,37,38,39,42", "uncoveredLines": "9,10"}, "AmexAutoCodingDataHolder": {"coverageRate": "0.47", "coveredLines": "9,10,11,12,27,28,35,36", "uncoveredLines": "6,15,16,19,20,23,24,31,32"}, "AmexExpenseFactory": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,11", "uncoveredLines": ""}, "AmexRawDataService": {"coverageRate": "0.92", "coveredLines": "2,3,4,6,8,9,10,11,12,13,14,15,18,20,21,22,23,27,28,29,30,33,34,35,36,39,40,42,43,46,50,53,57,58,60,63,64,66,71,72,73,74,75,78,79,81,82,91,92,93,96,97,101,102,103,104,105,106,107,108,109,112,113,115,116,117,118,119,120,121,122,123,124,125,127,128,129,130,133,136,137,138,140,141,142,144,145,146,147,149,150,151,152,154,155,156,157,158,160,163,164,165,169,170,171,172,174,177,178,179,182,183,184,185,186,187,188,192,196,197,200,201,202,203,204,205,206,207,211,212,213,215,216,217,218,219,221,224,225,229,230,231,232,234,239,240,241,244,245,246,248,249,250,251,252,253,254,255,256,262,263,264,265,266,267,268,269,270,271,272,273,275,280,281,282,283,284,285,286,287,289,290,291,293,294,296,297,298,301,304,307,311,315,316,317,318,319,320,321,322,323,324,326,327,328,329,330,335,341,344,348,349,351,352,353,355,359,360,362,364,365,366,369,372", "uncoveredLines": "37,47,51,54,76,84,86,87,198,222,235,236,237,299,331,332,333,338,367"}, "AmountDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ApexClassSelector": {"coverageRate": "0.08", "coveredLines": "2", "uncoveredLines": "5,6,7,15,16,19,20,23,24,27,28"}, "ApexLogEvent": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ApexLogsCleanScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AppLogsMonitor": {"coverageRate": "0.82", "coveredLines": "2,4,12,13,15,16,17,18,19,23,25,26,27,30,31,32,36,37", "uncoveredLines": "7,8,9,38"}, "Application": {"coverageRate": "1.00", "coveredLines": "7,8,13,14,15,21,22,27,28,29", "uncoveredLines": ""}, "ArchiveNonSickLeaveTimecardsProd": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AssignPermissionSetFailureSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "AssignPermissionSetRetryScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AssignPermissionSetRetryService": {"coverageRate": "0.73", "coveredLines": "2,4,5,9,13,14,17,18,19,21,23,25,26,27,28,32,33,37,38,39,41,42,43,44,46,49,50,53,56,57,58,59,62,63,64,65,66,69,70,71,72,73,74,76,77,81,82,85,88,89,90,91,94,98,99,100,101,146,147,148,149,152,153,154,156,158,159,160,161,165,168,169,170,171,172,173,176,177,178,181", "uncoveredLines": "102,106,111,112,113,115,116,117,118,120,121,122,123,124,125,126,128,129,130,132,133,134,135,137,138,140,141,143,174"}, "AssignProfileFailureRetryScheduleJob": {"coverageRate": "0.93", "coveredLines": "6,7,8,9,11,12,13,14,18,19,20,21,22,31,32,33,36,37,38,41,42,43,44,45,46,47,48,49,53,54,55,56,58,59,63,64,65,66,68,71,72,73,74,75,76,77,79,83,84,86,87,91,92,97,98,99,100,106,107,108,109,114,115,116,117,119,122,123,126,127,130,131,132,133,134,137,140,141,144,145,146,148,149,152", "uncoveredLines": "25,26,27,28,101,110"}, "AssignUserProfileQueueable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AssignmentAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "AssignmentAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8", "uncoveredLines": ""}, "AssignmentBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,9,10,11,12", "uncoveredLines": ""}, "AssignmentBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "AssignmentMissingTCJunctionAttacher": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,16,19,20,21,22,23,24,25,26,28,29,30,31,32,36", "uncoveredLines": ""}, "AssignmentProject_Milestone": {"coverageRate": "1.00", "coveredLines": "2,15,18,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,54", "uncoveredLines": ""}, "AssignmentSelector": {"coverageRate": "0.77", "coveredLines": "2,3,4,5,8,9,12,25,26,27,31,32,35,36,47,48,49,69,70,71,74,79,80,81,85,95,96,97,100,105,106,122,126,127,128,129,130,131,132,133,138,139,141,142,143,145,148,149,150,163,168,175,191,193,194,198,201,203,204,207,214,217,219,220,221,222,223,224,225,226,227,228,229,232,233,234,235,236,239,240,245,246,247,248,250,251,252,253,254,255,257,295,296,297,300,302,315,316,319,349,350,353,354,357,358,359", "uncoveredLines": "17,18,21,39,40,43,44,53,54,55,59,263,264,265,286,291,292,305,308,310,323,324,327,332,333,334,337,339,340,341,343,346"}, "AssignmentService": {"coverageRate": "0.98", "coveredLines": "3,15,16,17,18,19,20,21,22,23,24,25,29,40,41,42,43,44,45,46,47,48,51,52,53,56,57,58,59,62,63,64,65,68,71,72,73,74,75,79,80,81,83,84,85,88,91,94,95,96,97,98,100,101,104,105,106,107,108,110,111,112,113,117,118,121,122,123,124,125,126,129,132,133,136,137,138,141,144,145,147,148,149,150,154,157,158,159,160,161,162,163,166,167,168,169,170,171,172,175,179,180,181,182,183,184,185,186,187,188,189,190,191,192,194,199,203,204,206,207,208,210,211,212,214,215,216,217,218,220,223,224,225,226,228,231,232,233,234,235,236,238,240,241,243,244,246,247,249,250,253,254,257,261,262,263,264,265,269,270,271,272,273,274,275,276,277,283,286,287,288,289,291,295,296,298,299,300,301,305,306,307,308,309,314,315,316,317,320,321,322,326,329,330,331,332,337,338,339,340,341,342,343,346,349,350,351,352,353,354,357,360,361,364,365,368,369,370,371,372,373,376,377", "uncoveredLines": "76,86,318,355"}, "AssignmentShareBatchableJob": {"coverageRate": "0.86", "coveredLines": "2,10,12,18,19,20,21,22,23,26,27,29,30,32,37,38,39,42,45,46,47,48,52,53,57,60,61,64,65,68", "uncoveredLines": "33,34,40,55,66"}, "AssignmentShareRevokeBatchableJob": {"coverageRate": "0.81", "coveredLines": "2,3,12,20,21,22,23,24,25,26,29,30,31,32,33,34,36,37,39,42,43,46,47,53,57,58,59,60,63,64,65,66,67,68,69,70,71,72,74,77,80,81,82,83,96,97,100", "uncoveredLines": "48,49,50,85,86,88,89,90,91,93,98"}, "AssignmentShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,10,13", "uncoveredLines": ""}, "AssignmentTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AssignmentTriggerDispatcher": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,25,26,27,28,29,31,35,36,37,38,39", "uncoveredLines": "13,21,41"}, "AsyncApexJobFailedBackupBatchableJob": {"coverageRate": "0.59", "coveredLines": "2,3,4,8,9,10,13,14,15,31,52,53,54,55,56,57,58,59,60,61,62,63,66,67,68,69,71,72,73,74,75,76,77,78,80", "uncoveredLines": "18,19,20,21,22,23,25,27,28,34,35,36,37,38,39,40,41,42,43,44,45,46,48,49"}, "AsyncApexJobFailedBackupScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AsyncApexJobSelector": {"coverageRate": "0.92", "coveredLines": "2,10,11,12,13,14,15,16,18,19,20,23,26,29,30,32,33,36,37,38,39,41,42,43", "uncoveredLines": "24,27"}, "AsyncApexJobsSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,10,11,14,15,16,17", "uncoveredLines": ""}, "AsyncMarketoSalesInsightController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AttachmentAfterInsertTriggerHandler": {"coverageRate": "0.94", "coveredLines": "2,3,4,6,7,8,11,13,14,15,16,18,19,21,26,27", "uncoveredLines": "22"}, "AttachmentTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "AttachmentTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "AuraAssignmentsBaseSelector": {"coverageRate": "0.79", "coveredLines": "2,3,4,8,16,17,18,26,27,28,31,36,37,42,44,45,46,48,51,52,53,65,66,81,82,83,130", "uncoveredLines": "73,74,77,78,136,137,140"}, "AuraAssignmentsController": {"coverageRate": "0.67", "coveredLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,23,26,27,28,29,30,31,32,33,66,67,68,78,79,80,81,93,94,95,105,106,110,111,115,116,120,121,146,147,148,149,152,153,154,156,160,161,162,163,164,166,169,170,201,202,211,212,213,214,215,217,231,232,233,234,235,237,238,239,240,241,242,243,244,245,247,249,250,251,252,253,254,255,257,258,264,265,266,267,270,272,273,274,275,277,278,281,282,284,287,293,294,295,296,297,298,299,300,301,302,303,304,339,340,341,342,343,346,347,348,349,350,351,353,356,357,358,359,363,375,376,377,378,379,380,381,382,384,386,387,388,389,392,393,394,395,410,411,413,414,422,425,426,427,430,431,435,436,437,438,442,445,446,447,448,449,450,452,453,456,457,458,459,460,461,463,464,465,466,467,468,470,473,474,475,476,477,478,479,481,484,485,486,487,488,491,492,493,494,495,566,567,568,569,570,572,573,574,575,576,578,579,580,582,583,584,585,586,587,588,589,590,591,592,593,594,595,614,616,619,620,621,622,623,625,626,627,628,629,630,631,632,633,636,637,638,640,641,642,643,645,648,653,654,655,656,657,658,661,664,665,667,676,677,678,679,680,681,682,683,684,685,687,688,692,693,695,696,697,698,701,704,705,706,709,712,713,714,718,723,725,729", "uncoveredLines": "51,52,53,54,55,56,57,58,59,125,126,130,131,132,136,137,141,142,174,175,176,177,178,180,181,183,187,188,189,190,191,192,196,197,206,207,221,222,226,227,285,288,305,306,311,312,313,314,315,319,320,324,325,329,330,331,332,333,335,361,367,368,369,370,372,390,398,399,400,401,403,406,407,415,416,418,419,428,439,489,496,497,498,499,503,504,505,506,507,510,511,512,513,515,516,517,518,519,520,521,522,526,527,528,530,532,533,534,535,539,540,541,542,545,546,548,551,552,553,554,555,556,557,560,561,563,598,599,600,601,602,603,604,605,606,607,608,609,610,611,659,672,673,699,715,716"}, "AuraAssignmentsSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AuraAssignmentsService": {"coverageRate": "0.99", "coveredLines": "3,4,5,6,8,9,12,13,16,17,18,19,21,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,42,44,45,46,47,48,49,52,54,57,58,59,60,61,62,63,64,66,70,71,72,73,77,78,81,82,83,85,87,88,89,90,92,93,97,100,101,102,103,105,107,110,111,112", "uncoveredLines": "50"}, "AutoApproveTimecardBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AutoApproveTimecardScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AutoAssignSalesFunnelAccessScheduleJob": {"coverageRate": "0.72", "coveredLines": "2,7,8,9,14,15,16,21,22,23,28,29,30,35,36,39,40,43,45,46,50", "uncoveredLines": "10,17,24,31,47,51,52,53"}, "AutoCloseForTimeExpenseEntry": {"coverageRate": "0.56", "coveredLines": "3,4,5,6,7,8,12,16,17,18,26,27,28,29,30,33,34,35,36,37,38,39,40,41,62,63,64,65,69,70,71,72,73,74,75,76,103,104,110,111,113,114,115,116,117,118,121,122,123,124,131,132,133,134,135,136,137,151,152,158,159,161,162,163,165,166,167,168,169,170,184,185,186,187,195,197", "uncoveredLines": "21,22,23,42,43,44,45,46,47,48,50,51,55,56,57,58,59,77,78,79,80,81,83,84,87,88,89,90,91,93,94,95,98,99,101,105,106,125,126,128,139,140,141,143,144,146,147,153,154,172,173,174,176,177,180,181,189,190,198,199"}, "AutoDeactivateProjectSchedulable": {"coverageRate": "0.80", "coveredLines": "2,3,4,6,14,15,16,17,18,19,20,21,23,24,26,28,29,32,34,35", "uncoveredLines": "9,10,11,27,33"}, "AutoDeleteExpenseEventLogBatchableJob": {"coverageRate": "1.00", "coveredLines": "3,7,8,9,10,13,14,16,19,20,21,22,23,27,28,29,31", "uncoveredLines": ""}, "AutoDeleteMonthEndTimecardBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,5,6,9,10,11,12,15,16,17,21,22", "uncoveredLines": ""}, "AutoDeleteNoneExpExpenseReportBatchJob": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,8,9,11,12,13,17,18,22", "uncoveredLines": ""}, "AutoDeleteTimecardCustomSettingSchedule": {"coverageRate": "0.79", "coveredLines": "2,3,4,5,11,12,13,21,22,23,25,27,29,30,31,32,33,42,43,45,46,49", "uncoveredLines": "7,16,17,18,37,38"}, "AutoDeleteTimecardEventLogBatchableJob": {"coverageRate": "1.00", "coveredLines": "3,7,8,9,10,13,14,16,19,20,21,22,23,27,28,29", "uncoveredLines": ""}, "AutoInvoiceBEIBatchableJob": {"coverageRate": "1.00", "coveredLines": "4,5,8,9,12,13,16,17,21,22", "uncoveredLines": ""}, "AutoInvoiceBEIScheduleJob": {"coverageRate": "0.75", "coveredLines": "2,3,10,11,14,15", "uncoveredLines": "5,6"}, "AutoInvoiceBEIService": {"coverageRate": "0.73", "coveredLines": "2,3,9,10,11,12,13,16,17,36,37,38,39,40,41,42,43,44,47,52,53,54,57,58,76,77,78,79,80,81,83,87,93,94,95,96", "uncoveredLines": "45,46,48,49,59,60,61,62,68,69,70,71,73"}, "AutoRefreshAbsenceRequestApprover": {"coverageRate": "0.75", "coveredLines": "2,3,6,7,14,15", "uncoveredLines": "10,11"}, "AutoResumePausedSalesInvoiceSchedule": {"coverageRate": "0.71", "coveredLines": "2,3,4,5,6,10,24,25,26,27,28,29,31,34,35,36,37,39,40,41,42,43,44,59,60,61,64,65,66,68,71,72,73,74", "uncoveredLines": "13,14,15,16,17,18,20,46,47,48,49,50,54,55"}, "AutoSendFixedBidRevReportSchedule": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "AutoSendFixedBidRevReportService": {"coverageRate": "0.46", "coveredLines": "2,4,6,14,15,16,17,18,19,20,52,53,54,55,57,61,62,63,64,66,67,68,69,70,71,72,73,76,77,80,81,102,106,107,108,109,117,118,120,124,126,132,135,136", "uncoveredLines": "24,25,26,27,28,30,31,32,34,35,37,38,39,40,42,47,48,82,85,86,88,89,90,91,93,94,95,96,121,127,128,129,139,140,143,144,145,148,150,152,153,155,156,158,159,161,162,164,165,167,168,170"}, "BEBAccountLevelRevokeHandler": {"coverageRate": "0.77", "coveredLines": "2,6,7,9,10,11,14,15,16,18,20,23,24,25,27,29,32,33,34,41,44,45,46,54", "uncoveredLines": "35,36,37,47,48,49,50"}, "BEBAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "BEBBeforeInsertTriggerHandler": {"coverageRate": "0.60", "coveredLines": "9,10,13", "uncoveredLines": "5,6"}, "BEBProjectLevelRevokeHandler": {"coverageRate": "0.77", "coveredLines": "2,6,7,9,10,11,14,15,16,18,20,23,24,25,27,29,32,33,34,41,44,45,46,54", "uncoveredLines": "35,36,37,47,48,49,50"}, "BEIAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "BEIAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "BEIUpdateBatchableJob": {"coverageRate": "0.81", "coveredLines": "3,5,6,10,11,12,15,16,19,20,22,23,26,27,31,32,33", "uncoveredLines": "34,35,36,38"}, "BaseURLController": {"coverageRate": "0.36", "coveredLines": "2,9,12,13,17,18,22,23,55,56", "uncoveredLines": "26,27,28,29,30,31,32,35,38,39,40,42,43,45,46,48,50,51"}, "BatchAbortApexJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BatchApexErrorEventTrigger": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,9,10,11,12,13,14,15,16,18", "uncoveredLines": ""}, "BatchInsertAccountsAndOpportunities": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BatchSetChildAccountClientStatus": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BatchSetParentAccountClientStatus": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BatchSubProjectCreation": {"coverageRate": "0.73", "coveredLines": "2,3,4,5,7,11,12,13,14,24,25,26,27,28,29,36,37,40,41,44,45,48,49,52,53,57,58,59,60,61,62,74,75,77,78,84,85,86,87,88,92,93,94,95,100,103,104,105,106,107,109,110,111,112,115,116,117,118,121,122,128,129,130,131,132,133,135,136,137,138,139,142,143,144,148,149,188,189,190,191,192,193,196,210,211,212,213,214,215,216,223,224,226,230,231,232,233,236,237,238,239,240,241,244,247,248,249,251,252,253,254,255,256,257,258,260,261,262,263,265,269,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,290,291,292,293,294,296,297,300", "uncoveredLines": "32,33,63,64,65,66,67,68,69,70,79,80,81,97,98,123,124,145,150,151,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,174,175,176,177,181,182,183,184,199,200,201,202,203,204,207,218,219,221,227"}, "BillingEventAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "BillingEventAttacherHelper": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,10,14,15,18,19,20,21,23", "uncoveredLines": ""}, "BillingEventBatchSelector": {"coverageRate": "0.84", "coveredLines": "2,5,6,7,10,15,16,17,21,22,118,121,122,123,126,128", "uncoveredLines": "131,134,136"}, "BillingEventBatchService": {"coverageRate": "1.00", "coveredLines": "5,6,7,11,12,13,16,17,18,19,20,24,25,26,27,28,29,30,31,32,33,38", "uncoveredLines": ""}, "BillingEventBatchShareBatchableJob": {"coverageRate": "0.90", "coveredLines": "2,11,13,14,15,16,17,18,19,20,23,24,26,27,29,34,35,37,38,42,45,46,49,50,53,57,58,59,60,61,63,65,66,67,68,69,70,75", "uncoveredLines": "30,31,40,51"}, "BillingEventBatchShareRevokeBatchableJob": {"coverageRate": "0.71", "coveredLines": "2,3,14,25,37,38,39,40,41,42,43,44,45,46,47,50,51,52,53,55,56,58,59,61,63,64,65,67,68,70,71,73,76,82,83,86,87,88,89,90,91,92,93,96,97,98,99,100,101,102,103,104,106,107,108,109,112,113,117,118,122,123,124,140,143,144,160", "uncoveredLines": "79,114,115,125,126,127,128,129,130,131,132,133,134,135,136,145,146,147,148,149,150,151,152,153,154,155,156"}, "BillingEventBatchShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,12,16,17,20", "uncoveredLines": ""}, "BillingEventBatchTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "BillingEventBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,8,9,10,14,15,16,17,18,19,20", "uncoveredLines": ""}, "BillingEventCalculationSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BillingEventGenerateCallBack": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BillingEventItemRevokeHandler": {"coverageRate": "1.00", "coveredLines": "2,6,8,9,10,13,14,15,19,20,21,25,26,27,28,29,30,34,37,38,39,40,41,42,43,47", "uncoveredLines": ""}, "BillingEventItemSelector": {"coverageRate": "0.77", "coveredLines": "2,10,11,14,15,17,20,21,22,23,26,27,46,49,53,56,57,60,64,65,66,67,70,71,72,75,77", "uncoveredLines": "5,6,7,16,50,80,83,85"}, "BillingEventItemService": {"coverageRate": "1.00", "coveredLines": "7,8,9,10,11,15,20,21,22,23,26,27,29,30,32,33,34,38,39,40,41,42,44,45,47,53,54,55,56,57,61,62,63,64,65,66,67,68,69", "uncoveredLines": ""}, "BillingEventItemShareBatchableJob": {"coverageRate": "0.63", "coveredLines": "2,10,12,13,14,15,16,17,18,21,22,24,25,27,32,33,35,36,40,47,48,53,54,55,70", "uncoveredLines": "28,29,38,43,44,49,56,57,58,60,61,62,63,64,65"}, "BillingEventItemShareRevokeBatchableJob": {"coverageRate": "0.94", "coveredLines": "2,3,5,7,8,18,26,27,28,29,30,31,34,35,36,37,38,42,43,45,46,47,48,49,50,51,53,54,55,58,59,62,63,64,65,66,69,70,71,72,75,76,77,82,86,87,88,89,90,91,92,93,94,96,97,98,99,102,103,107,108", "uncoveredLines": "78,79,104,105"}, "BillingEventItemShareSelector": {"coverageRate": "0.63", "coveredLines": "2,10,11,12,15", "uncoveredLines": "4,5,6"}, "BillingEventItemTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "BillingEventReleaseCallBack": {"coverageRate": "0.63", "coveredLines": "3,4,5,31,32,34,35,36,38,40,41,42,43,44,45,47,48,52,53,54,55,59,63,64,65,67,68", "uncoveredLines": "7,9,11,13,15,17,18,21,22,23,24,25,26,49,50,60"}, "BillingEventRevokeHandler": {"coverageRate": "1.00", "coveredLines": "2,6,7,9,10,11,14,15,16,18,20,23,24,25,27,29,32,33,34,35,36,37,41,44,45,46,47,48,49,50,54", "uncoveredLines": ""}, "BillingEventSelector": {"coverageRate": "0.81", "coveredLines": "2,5,6,9,10,31,35,36,39,40,41,44,46", "uncoveredLines": "49,52,54"}, "BillingEventService": {"coverageRate": "0.96", "coveredLines": "8,9,10,11,12,13,17,23,24,25,26,27,30,31,32,33,37,38,39,40,41,42,43,44,46,47,49,50,51,52,53,54,56,57,59,60,62,63,65,66,69,70,72,73,76,77,81,82,83,84,85,89,90,91,92,93,94,95,96,97,98,103,113,114,115,116,118,121,122,123,124,126", "uncoveredLines": "107,108,109"}, "BillingEventShareBatchableJob": {"coverageRate": "0.63", "coveredLines": "2,10,12,13,14,15,16,17,18,21,22,24,25,27,32,33,35,36,40,47,48,51,55,56,57,72", "uncoveredLines": "28,29,38,43,44,49,58,59,60,62,63,64,65,66,67"}, "BillingEventShareRevokeBatchableJob": {"coverageRate": "0.89", "coveredLines": "2,3,10,17,25,26,27,28,29,30,31,34,35,36,38,39,41,42,44,46,52,53,56,57,58,59,60,61,62,63,64,66,67,68,69,72,73,77,78,82,83,88", "uncoveredLines": "49,74,75,84,85"}, "BillingEventShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,12", "uncoveredLines": ""}, "BillingEventTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "BillingUtils": {"coverageRate": "0.96", "coveredLines": "2,4,5,6,7,8,9,10,11,13,14,15,16,21,23,24,25,26,27,28,30,31,34", "uncoveredLines": "17"}, "Billing_EventTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,14,15,16,17,18,23,24,25,26,27,31", "uncoveredLines": "11,20,29"}, "Billing_Event_BatchTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,8,9,10,11,12,17,18,19,20,25,26,27,28,29", "uncoveredLines": "14,22,31"}, "Billing_Event_ItemTriggerDispatcher": {"coverageRate": "0.87", "coveredLines": "2,3,4,6,7,8,9,10,15,16,17,18,19", "uncoveredLines": "12,21"}, "BreakdownItemDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BusinessController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "BusinessService": {"coverageRate": "0.98", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,18,19,20,21,22,23,24,25,26,27,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,52,53,56,57,58,59,64,65,66,67,68,71,72,73,75,76,77,78,79,80,81,82,83,84,86,87,90,94,95,96,99,101,104,105,106,107,109,110,112,113", "uncoveredLines": "61,97"}, "CPNotificationScheduleJob": {"coverageRate": "0.85", "coveredLines": "9,10,11,12,13,16,17,18,32,33,34,35,36,55,56,57,61,62,63,64,67,70,71,72,73,76,78,79,80,81,82,83,88,92,95,96,97,98,101,103,104,105,113,117,120,121,123,124,125,126,127,136,142,143,144,145,146,147,148,149,150,155,161,164,165,166,167,168,169,171,172,176,177,180,181,184,185,188,189,191,192,194,197,200,201,202,203,204,206,209,210,211,212,213,215,216,218,219,220,221,222,223,225,232,233,234,235,236,237,239,242,243,244,245,247,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,271,272,273,274,275,276,277,278,279,280,281,284,285,286,298,299,300,301,303,304,305,306,308,309,310,311,312,313,314,315,316,317,318,319,321,322,323,324,328,330", "uncoveredLines": "25,27,28,39,40,41,42,45,46,47,48,51,52,58,85,106,107,108,110,128,129,130,131,133,152,173,226,227,229,230,231"}, "CRMAccountOperationWithSubscriberTrigger": {"coverageRate": "0.59", "coveredLines": "2,3,4,5,6,8,9,10,11,12", "uncoveredLines": "13,14,15,16,17,18,20"}, "CRMConstants": {"coverageRate": "1.00", "coveredLines": "29,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,54,103,105,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,186,187,188,189,190,193", "uncoveredLines": ""}, "CRMContactOperationWithSubscriberTrigger": {"coverageRate": "0.45", "coveredLines": "2,3,5,6,7", "uncoveredLines": "8,9,10,11,12,14"}, "CRMEmail": {"coverageRate": "0.50", "coveredLines": "2,24,25,26,27,28,30,32,34,35,37,38,39,40,42,43,44,45,46,48,49,51,52,54,55,57,58,61,62,64,65,66,69,255,256,257,269,278,280,281,282,283,285,286,288,291,292,294,297,307,308,311,361,371,372,375,385,386,387,388,389,391,392,393,395,396,397,398,399,400,403,404,405,406,407,408,411,412,414,415,417,418,420,421,422,423,425,426,428,429,431,432,437,438,440,441,443,444,446,447,450,451,452,453,454,455,456,457,458,459,460,461,466,467,468,469,470,471,472,475,476,484,485,486,487,488,489,490,492,493,494,496,497,500,501,637,638,639,640,641,642,643,644,645,647,649,650,654,655,656,657,658,659,662,663,666,667,677,678,679,680,681,682,683,685,686,687,688,698,699,700,701,702,703,704,705,707,708,713,714,715,716,717,718,722,723,724,725,727,728,729,730,731,732,733,734,735,736,738,739,740,741,743,745,746,754,755,756,757,758,759,760,761,763,764,765,767,768,771,772,774,775,778,779,780,781,783,784,786", "uncoveredLines": "72,73,75,76,77,78,79,80,81,83,85,88,89,91,92,93,94,95,96,97,99,101,104,106,107,111,112,114,115,116,117,118,119,120,121,125,126,127,128,129,130,131,132,135,136,137,138,141,142,143,144,147,148,149,150,154,157,160,169,170,171,172,174,175,178,179,181,182,184,185,190,191,192,193,194,195,196,197,200,203,212,213,214,215,217,218,221,222,224,225,227,228,233,234,235,236,237,238,239,240,243,246,247,248,249,250,251,252,260,261,262,264,265,289,309,314,315,318,319,322,323,324,325,326,328,329,330,333,334,337,338,342,343,345,346,348,349,351,352,353,355,356,358,359,504,505,506,508,509,510,511,512,513,514,515,516,529,530,531,532,535,536,537,538,539,540,543,544,547,548,549,550,551,552,555,556,558,559,561,562,564,566,567,568,569,570,571,572,575,584,585,586,587,588,589,590,591,592,598,599,603,604,607,608,613,614,619,620,628,691,692,693,694,720,737,742,789,790,793,794,795,796,797,798,799,800,801,802,803,805,806"}, "CRMEmailEventTrigger": {"coverageRate": "0.75", "coveredLines": "2,3,4,5,7,8,9,14,18", "uncoveredLines": "10,11,12"}, "CRMHelpPageController": {"coverageRate": "0.94", "coveredLines": "3,4,5,6,7,10,14,15,17,18,19,20,21,22,23,24,27", "uncoveredLines": "8"}, "CRMLeadOperationWithSubscriberTrigger": {"coverageRate": "0.50", "coveredLines": "2,3,4,6,7,8,9", "uncoveredLines": "10,11,12,13,14,15,17"}, "CRMLicenseAssignmentScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CRMLicenseAssignmentService": {"coverageRate": "0.77", "coveredLines": "6,7,9,11,13,14,15,16,17,19,22,29,30,31,32,33,36,37,38,39,40,41,42,46,47,49,52,53,54,55,56,59,60,61,62,64,67,68,69,71,72,73,74,77,79,80,81,82,83,84,86,88,89,90,91,92,96,97,98,102,103,105,108,109,111,112,116,117,118,119,122,123,127,128,163,164,165,166,169,172,173,174,175,177,180,181,182,183,184,187,190,191,192,193,194,195,196,201,232,233,234,237,238,240,244,245,246,247,248,249,250,254,255,258", "uncoveredLines": "131,132,133,134,135,136,140,141,142,144,145,146,147,151,152,153,154,155,159,204,205,207,208,211,213,214,215,217,222,223,224,225,226,229"}, "CRMNewUserAssignLicenseFuture": {"coverageRate": "0.65", "coveredLines": "2,4,5,8,9,10,11,12,13,14,17,21,25,27,28", "uncoveredLines": "31,32,36,40,41,44,46,47"}, "CRMOppContactRoleOperationWithSubscriberTrigger": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10,11,12,14", "uncoveredLines": ""}, "CRMOpportunityOperationWithSubscriberTrigger": {"coverageRate": "0.67", "coveredLines": "2,3,4,5,6,8,9,10,11,12,13,14", "uncoveredLines": "15,16,17,18,19,21"}, "CRMTriggerContext": {"coverageRate": "0.86", "coveredLines": "2,4,5,8,9,12", "uncoveredLines": "10"}, "CalculateAttributionValueBatchJob": {"coverageRate": "0.97", "coveredLines": "2,3,4,5,10,11,12,15,16,19,20,21,24,25,26,27,28,29,30,31,33,36,40,41,43,45,47,48,51,54,55,57,58,59,61,62,63,66,69,72,75,76,77,78,80,81,83,86,87,88,91,92,93,94,95,96,97,101,102,103,104,106,108,109,110,111,112,113,114,116,117,122", "uncoveredLines": "17,67"}, "CalculateGLMappingID": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7", "uncoveredLines": ""}, "CalculateHVOService": {"coverageRate": "0.66", "coveredLines": "2,3,4,5,6,8,10,17,18,19,20,21,24,25,28,31,32,33,38,39,40,41,43,45,46,47,51,52,56,58,60,61,62,64,65,66,69,70,71,72,73,74,83,86,94,95,96,97,98,100,101,102,104,109,110,111,120,121,123,149", "uncoveredLines": "26,29,35,48,77,78,79,114,115,117,124,125,126,129,130,131,134,135,136,138,139,140,142,143,145,152,153,154,155,156,159"}, "CalendarTool": {"coverageRate": "0.83", "coveredLines": "2,3,4,6,7,8,9,10,11,13,16,17,20,21,24,25,28,29,32,33,42,43,44,45,48,57,58,59,60,63,66,67,68,69,72,73,74,77,78,79,80,81,87,88,89,92,93,96,97,100,101,104,105,106", "uncoveredLines": "36,37,38,39,46,51,52,53,54,61,84"}, "CalloutClass": {"coverageRate": "0.83", "coveredLines": "2,9,10,14,15,16,18,19,30,31,34,35,36,39,40,41,42,43,44,46,47,51,52,53,56,59,60,61,62,63,64,65,69,70,71,72,73,74,75,80", "uncoveredLines": "20,25,26,48,49,54,77,78"}, "CamInfAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13,14,15,16", "uncoveredLines": ""}, "CamInfAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13,14,15", "uncoveredLines": ""}, "CamInfAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13,14,15", "uncoveredLines": ""}, "CamInfBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "CamInfBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,8", "uncoveredLines": ""}, "CampaignAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,8,9,10,11,12,13,14", "uncoveredLines": ""}, "CampaignBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13", "uncoveredLines": ""}, "CampaignBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "4,5,8,9,10,11,12", "uncoveredLines": ""}, "CampaignBeforeUpdateTriggerHandler": {"coverageRate": "0.85", "coveredLines": "2,4,5,6,8,9,10,11,12,16,19", "uncoveredLines": "13,17"}, "CampaignController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CampaignInfluenceController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CampaignInfluenceLogContext": {"coverageRate": "0.97", "coveredLines": "3,6,7,8,9,12,13,16,17,20,21,24,25,26,27,28,29,30,32,33,35,36,39,42,43,44,46,47,48,49,50,51,52,53,54,57,60,61,62,63,65,68,69,70,71,72,73,76,77,81,82,83,84,86,87,91,92,96,97,98,102", "uncoveredLines": "74,99"}, "CampaignInfluenceLogSelector": {"coverageRate": "0.90", "coveredLines": "2,3,6,7,10,11,16,17,18", "uncoveredLines": "12"}, "CampaignInfluenceLogService": {"coverageRate": "0.96", "coveredLines": "2,3,4,5,6,7,8,9,10,13,14,15,17,18,19,21,22,24,25,26,27,28,29,30,31,33,37,38,39,44,45,46,53,54,55,56,57,58,60,63,64,68,69,70,71,72,73,74,75,76,77,78,79,82,83,84,85,88,91,92,93,94,95,96,97,100,101,103,104,106,107,108,109,110,111", "uncoveredLines": "48,49,86"}, "CampaignInfluenceOperationEventTrigger": {"coverageRate": "0.54", "coveredLines": "2,3,5,6,7,8,9", "uncoveredLines": "10,11,12,13,14,16"}, "CampaignInfluenceOperationService": {"coverageRate": "0.99", "coveredLines": "2,3,4,5,6,7,8,9,12,13,14,15,16,17,18,19,20,22,23,25,28,31,32,33,34,35,38,40,41,43,45,46,47,49,50,51,52,53,55,60,61,62,64,65,69,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,90,91,94,95,96,97,98,99,104,105,106,110,111,112,113,114,117,118,119", "uncoveredLines": "36"}, "CampaignInfluenceSelector": {"coverageRate": "0.62", "coveredLines": "2,3,6,7,10,14,15,18,22,23,55,64,65,77,81,82,95,145,146,161,174,175,178,179,194,195,206,271,272,273,277,278,279,280,281,282,283,284,285,295,296,299,300,331,332,335,340,341,344,349,350,351,355,356,357,361,362,363,367,368,398", "uncoveredLines": "60,61,99,100,115,119,120,140,166,167,170,182,183,187,188,189,190,211,212,215,220,221,222,241,246,247,248,266,286,287,288,289,291,292,303,304,327"}, "CampaignInfluenceService": {"coverageRate": "0.96", "coveredLines": "2,3,4,5,6,7,8,11,12,13,14,15,16,17,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,36,37,39,42,50,51,52,53,54,55,58,59,60,61,62,63,64,65,67,68,69,70,71,72,73,75,78,79,80,81,84,85,87,88,89,90,95,96,97,102,105,106,109,114,115,116,117,118,119,120,122,123,124,125,126,128,130,132,135,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,183,184,185,188,190,191,192,194,195,196,197,198,200,201,202,203,204,205,207,210,211,212,214,217,221,222,223,224,226,228,230,231,232,234,235,236,237,238,243,244,247,248,249,250,251,254,257,258,259,260,261,262,264,267,268,269,270,271,272,276,277,278,279,280,281,284,285,290,291,292,295,296,297,298,299,301,302,303,305,309,310,312,313,314,315,317,318,319,320,324,325,326,327,328,329,330,331,332,333,334,336,337,338,339,340,341,346,349,354,355,356,357,358,359,362,363,364,365,366,367,368,370,371,373,375,378,381,382,383,384,386,387,388,390,393,394,397,398,401,402,403,408,409,410,411,412,415,416,418,421,422,423,425,426,427,428,430,431,432,434,435,438,439,440,445,446,447,448,450,451,452,453,457,458,459,460,461,462,463,464,465,470,474,475,476,478,479,480,482,484,485,486,487,488,489,490,493,496,497,498,500,501,502,505,509,510,511,515,516,517,518,519,520,521,522,523,524,527,528,530,532,534,537,538,539,540,541,543,544,546,549,552,557,558,559,560,561,562,564,565,566,567,568,573,577,578,579,580,581,583,586,588,589,590,591,592,595,596,597,598,599,602,606,607,609,610,611,612,613,616,620,622,624,625,626,628,630,631,633,635,638,641,642,643,644,645,647,648,650,653,656,657,660,661,662,663,664,665,667,670,671,672,674,675,676,680,683,684,685,686,687,689,690,694,702,703,704,705,706,709,710,711,712,713,714,715,717,721,722,723,724,725,726,728,729,732,733,734,735,739,740,741,742,744,747,748,749,750,751,755,756,757,758,760,763,768,769,770,773,778,779,780,781,783,784,786,788,789,790,793,794,795,797,799,802,803,806,811,812,813,816,817,819,820,822,823,825,826,827,829,830,833,836,839,840,841,844,849,851,853,854,855,858,859,861,862,868,869,870,871,872,873,874,875,877,878,880,881,882,884,885,890,893,894,895,896,897,898,899,900,903,906,909,910,911,914,915,916,917,920,923,924,926,928,930,931,932,935,936,938,939,941,942,944,945,947,949,952,960,962,963,966,967,970,971,974,979,980,981,982,984,986,994,995,996,997,998,999,1000,1005,1006,1007,1008,1009,1010,1011,1012,1016,1017,1020,1021,1022,1023,1026,1027,1030,1031,1032,1034,1035,1037,1038,1042,1045,1046,1049,1050,1051,1054,1057,1058,1059,1060,1061,1064,1067,1070,1071,1072,1073,1074,1075,1080,1081,1082,1083,1085,1087,1089,1090,1091,1092,1094,1095,1097,1098,1099,1101,1103,1105,1106,1107,1111,1112,1113,1114,1116,1117,1118,1121,1122,1123,1124,1125,1126,1127,1128,1129,1133,1136,1137,1138,1139,1140,1141,1142,1143,1145,1146,1147,1150,1153,1158,1159,1160,1161,1162,1163,1164,1166,1169,1172,1173,1174,1177,1178,1179,1182,1183,1184,1185,1186,1187,1190,1191,1192,1193,1194,1195,1197,1198,1203", "uncoveredLines": "91,92,93,127,227,306,369,404,593,617,688,695,696,697,845,846,856,879,975,985,987,988,989,990,991,992,1033,1047,1062,1108,1180,1188"}, "CampaignInfluenceTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "CampaignMemberAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,10,11,12,13,14,15,16,17,18,19,20", "uncoveredLines": ""}, "CampaignMemberAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21", "uncoveredLines": ""}, "CampaignMemberAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,9,10,11,12,13,14,15,16,17,18,19,20", "uncoveredLines": ""}, "CampaignMemberBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,9,10,11,13,15,16", "uncoveredLines": ""}, "CampaignMemberBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,10,12,13", "uncoveredLines": ""}, "CampaignMemberContext": {"coverageRate": "1.00", "coveredLines": "4,6,8,9,11,12,14,16,17,18,19,20,21,23,24,25,26,31,32,33,34,38,39,40,43,44,45,46,47,49,50,51,56,57,58,59,60,61,64", "uncoveredLines": ""}, "CampaignMemberController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CampaignMemberIsEngagementJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CampaignMemberSelector": {"coverageRate": "0.51", "coveredLines": "2,3,53,54,58,59,144,145,175,176,196,206,207,208,209,211,214,215,220,221,222,225,228,231,232,235,236,243,244,266,337,338,341,345,346,347,350,353,367,368,369,371,374,375,376,379,382,385,386,400,404,405,408,409,410,435,469,470,473,478,479,500,508,509,512", "uncoveredLines": "6,7,8,21,26,27,42,44,46,47,48,72,73,88,89,98,99,114,116,118,119,124,125,126,139,148,149,150,170,200,201,202,216,239,240,270,271,274,275,278,282,283,298,299,308,309,323,324,356,357,358,361,364,440,441,455,460,461,464,504,505,510"}, "CampaignMemberService": {"coverageRate": "0.95", "coveredLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,18,20,23,32,33,34,35,36,37,38,41,42,45,46,47,48,49,50,52,53,55,56,57,59,60,65,66,69,72,73,75,76,78,79,80,81,82,87,88,89,90,91,92,96,97,99,100,101,103,106,107,108,109,110,112,113,116,117,119,122,123,124,125,126,127,130,133,134,135,136,139,140,143,146,147,148,149,150,151,152,153,155,156,159,160,161,162,164,165,168,171,172,173,174,176,179,180,181,184,185,186,188,189,190,191,194,195,198,199,200,201,205,206,208,209,213,214,217,218,221,222,223,226,227,228,230,231,232,233,236,239,240,241,242,243,244,247,248,250,251,252,253,254,257,258,259,260,261,262,263,264,265,267,268,269,270,271,272,273,274,275,276,277,278,280,281,282,283,284,288,289,292,293,294,295,296,299,302,303,304,305,306,308,310,313,314,315,316,317,320,322,323,324,325,326,327,331,334,335,336,338,339,342,343,344,345,346,349,352,353,354,356,357,358,359,361,362,363,364,366,367,371,372,373,374,375,376,379,382,383,384,385,386,387,391,392,393,397,401,402,403,404,405,406,407,408,409,410,411,412,413,415,418,419,421,425,426,430,431,432,433,434,435,438,439,440,441,442,446,447,449,450,456,457,459,460,461,466,469,470,471,472,473,474,477,480,481,482,483,486,487,488,491,492,493,497,498,499,504,505,506,507,508,509,511,512,513,514,515,516,517,518,519,523,526,527,528,530,531,533,534,536,537,538,540,542,545,546,547,549,561,562,563,564,565,566,568,569,570,571,573,574,575,576,578,579,581,582,583,584,586,587,592,593,596,597,598,599,602,603,604,605,606,608,609,610,611,612,613,614,615,616,620,621,624,625,626,627,628,629,653,654,655,657,658,660,661,663,664,665,666,668,669,670,676,677,678,679,680,681,685,686,688,689,692,693,694,697,698,699,702,703,704,705,706,709,712,713,714,715", "uncoveredLines": "137,255,329,337,388,394,451,494,553,554,557,558,633,634,635,636,637,641,642,643,644,645,649,650"}, "CampaignMemberStatusHistorySelector": {"coverageRate": "0.71", "coveredLines": "2,3,4,8,9,10,14,15,18,19,20,24,25,42,43", "uncoveredLines": "28,29,33,34,35,37"}, "CampaignMemberStatusSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "CampaignMemberTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "CampaignMemberTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,8,9,10,11,12,17,18,19,20,21,26,27,28,29,30,35,36,37,38,39,44,45,46,47,48", "uncoveredLines": "14,23,32,41,50"}, "CampaignSelector": {"coverageRate": "0.58", "coveredLines": "2,3,10,11,14,15,18,19,22,23,24", "uncoveredLines": "6,7,28,29,30,34,35,36"}, "CampaignService": {"coverageRate": "0.92", "coveredLines": "2,3,4,5,6,8,12,13,16,17,18,22,23,24,25,26,27,28,30,31,33,34,36,42,43,44,45,47,48,54,55,56,57,59,62,63,64,65,67,68,69,72,73,74,75,76,77,78,79,80,81,85,86,99,100,102,103,104,105,106,109,110,111,112,113,116,117,119,120,121,124,125,126,127,128,130,134,135,136,137,138,139,140,143,144,145,148,151,152,153,154,155,156,157,158,159,162", "uncoveredLines": "29,90,91,92,93,94,95,97"}, "CampaignStatusUpdateScheduleJob": {"coverageRate": "0.71", "coveredLines": "2,3,4,10,11", "uncoveredLines": "6,7"}, "CampaignTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "CampaignTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,20,25,26,27,28,29,34,35,36,37,38", "uncoveredLines": "13,22,31,40"}, "Campaign_InfluenceTriggerDispatcher": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,6,8,9,10,11,12,18,19,20,21,22,24,27,28,29,30,31,37,38,39,40,41,47,48,49,50,51", "uncoveredLines": "14,33,43,53"}, "CapabilityDTO": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "3,6"}, "ChangeUserEmailEncodingBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ChatterHelper": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,10,11,12,14,15,16,17,20,21,22,23,26,27,28,29,32,33,34,35,38,39,40,41", "uncoveredLines": ""}, "CheckReCalculationScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CleanInvalidClusterAndServicesJob": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,11,12,14,15,16,18,19,20,21,22,24,25,26,28,29,31,36,37,39,40,41,42,43,45,47,48,49,50,51,56,57,58,61,62,63,64,67,70,71,72,75,76,77,78,81,84,87,88", "uncoveredLines": ""}, "CleanInvalidContactRelatedObjectJob": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,12,13,14,15,16,17,18,19,20,21", "uncoveredLines": ""}, "ClearAndFillContactStatusBatchJob": {"coverageRate": "0.37", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19,20,21,22,23,24,25,26,27,30,34,43,44,45,46,47,52,53,54,55,57,58,59,60,61,62,63,64,65,119,123,124,125,267,268,269,270,271,273,276,277,280,283,284,287,288,291,294,295,296,301,304,306,309,311,312,313,343,346,347,350,351,352,353,356,359", "uncoveredLines": "37,38,40,41,48,49,68,70,71,72,73,74,77,78,79,80,81,83,85,86,89,90,91,92,94,96,98,99,100,103,104,105,106,108,109,111,115,116,120,128,129,136,137,138,139,140,141,143,144,145,147,149,152,153,154,155,156,158,159,161,162,164,165,168,171,172,173,174,175,191,192,193,194,196,200,201,202,203,204,205,206,209,210,211,213,216,217,219,220,221,222,223,224,225,226,227,228,230,231,234,235,240,241,242,243,244,245,246,247,251,252,253,254,255,256,257,261,264,297,298,299,314,315,317,318,321,322,323,324,328,329,330,331,332,336,337,341,354,357"}, "ClearAndFillLeadContactStatusBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ClearAndFillLeadStatusBatchJob": {"coverageRate": "0.53", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,18,28,29,30,31,32,33,35,36,37,38,41,42,43,44,45,46,47,48,50,51,108,109,110,120,121,124,127,128,131,132,133,134,135,137,140,141,144,145,146,147,150,153", "uncoveredLines": "20,21,24,25,55,56,57,58,59,60,63,64,65,68,70,71,73,74,75,76,78,79,80,81,82,83,84,85,86,87,88,90,92,94,95,96,97,98,99,100,102,113,114,115,117,148,151"}, "ClearBEIsAndUpdateRelatedRecords": {"coverageRate": "0.73", "coveredLines": "2,4,6,7,8,9,10,11,13,14,15,17,18,19,20,21,22,34,35,36,37,39,40,41,44,49,50,51,54,55,56,59,60,61,64,70,74,75,78,79,82,83,86,91,92,93,94", "uncoveredLines": "16,23,24,26,29,38,42,45,46,65,66,67,71,87,95,96,97"}, "ClearBillingEventBatch": {"coverageRate": "1.00", "coveredLines": "2,4,5,8,9,10,12,13,15,16,17,18,19,21,22,23,25,26,28,29,30,31,34", "uncoveredLines": ""}, "ClearBillingEventBatchBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ClearCorpApartAutoCodingInfoScheduleJob": {"coverageRate": "0.33", "coveredLines": "2,3,5,24,25", "uncoveredLines": "4,8,10,11,12,13,17,19,20,21"}, "ClearS10ClearAbandonedBEI": {"coverageRate": "1.00", "coveredLines": "2,4,6,9,10,13,14,15,17,18,19,22,23,24", "uncoveredLines": ""}, "ClearS2UpdateTimecardSplitBatchable": {"coverageRate": "0.94", "coveredLines": "2,9,11,14,15,16,17,18,21,22,23,24,25,27,30,31,34,35,36,37,38,39,40,42,45,46,47,48,50", "uncoveredLines": "32,53"}, "ClearS4UpdateMilestoneBatchable": {"coverageRate": "0.92", "coveredLines": "2,8,9,10,11,12,15,16,17,18,21,22,25,26,27,28,29,30,31,33,36,37,38", "uncoveredLines": "23,40"}, "ClearS5UpdateMABatchable": {"coverageRate": "0.92", "coveredLines": "2,8,9,10,11,12,15,16,17,18,21,22,25,26,27,28,29,30,31,33,36,37,38", "uncoveredLines": "23,40"}, "ClearS7UpdateBEIBatchable": {"coverageRate": "0.74", "coveredLines": "2,8,9,10,11,12,15,16,17,18,21,22,25,26,27,28,30,33,34,35", "uncoveredLines": "23,37,38,39,40,41,44"}, "ClearS8DeleteBEIBatchable": {"coverageRate": "0.74", "coveredLines": "2,9,10,12,13,14,15,16,17,18,21,22,23,24,27,28,31,32,33,34,43,44,45", "uncoveredLines": "29,35,36,38,47,48,49,51"}, "ClearS9FinalClearBatch": {"coverageRate": "0.54", "coveredLines": "2,7,8,9,10,13,14,15,16,29,30,31,32,50,51,55,56,59,60,61", "uncoveredLines": "19,20,21,24,25,26,35,36,37,38,39,40,41,42,46,52,62"}, "ClearTimecardsBatchableJob": {"coverageRate": "0.95", "coveredLines": "2,5,6,9,10,11,12,15,16,19,20,21,22,23,24,25,26,27,29,31,34", "uncoveredLines": "17"}, "ClientPrincipleEmailService": {"coverageRate": "0.95", "coveredLines": "6,8,9,10,11,12,16,17,20,21,22,23,24,25,26,27,30,31,35,36,37,39,52,54,55,56,57,58,59,60,62,64,68,69,74,75,76,77,78,81,84,85,86,87,88,89,91,92,93,94,95,96,97,101,102,106,107,108,109,111,112,113,115,116,117,119,120,121,123,124,127,128,129,133,136,140,142,145,146,148,149,156,157,159,160,162,163,165,166,168,169,171,172,174,175,177,178,180,181,183,184,186,187,190,191,192,193,194,195,196,197,201,202,204,206,208,211,216,217,218,219,220,221,222,223,225,229,230,231,232,233,234,235,236,237,239", "uncoveredLines": "79,130,131,134,137,138,209"}, "CollaborationGroupMemberSelector": {"coverageRate": "0.50", "coveredLines": "2,3", "uncoveredLines": "6,7"}, "CollaborationGroupSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "CollectionUtils": {"coverageRate": "0.95", "coveredLines": "2,3,6,7,10,11,18,19,22,23,26,27,28,29,30,31,34,37,38,39,40,42,43,44,47,50,51,52,53,55,59,60,61,62,64,67,68,69,70,72,75,76,77,78,79,80,81,84,86,87,89,92,93,94,95,97,98,101,103,104,105,106,108,109,110,111,115,118,119,122,123,126,127,128,129,131,132,133,134,135,139,142,143,144,145,147,148,149,150,151,153,157,160,161,162,163,165,166,167,168,169,171,175,178,179,180,183,184,185,186,187,189,193,196,197,198,201,202,203,204,205,206,209,210,211,212,214,218,222,223,224,225,227,228,229,230,231,232,233,235,239,242,243,244,245,247,248,249,250,255,259,262,263,264,265,266,267,269,272,273,274,275,276,279,282,283,286,287,289,290,291,292,293,296,297,303,306,307,308,309,310,313,316,317,318,319,321,324,325,326,327,328,329,332,335,336,337,338,339,340,341,343,347,350,351,352,353,356,358,359,360,363,366,367,368,369,371,374,375,376,377,379,380,382,385,386,387,388,390,391,393,396,397,400,401,402,403,406,409,410,411,412,414,415,417,420,421,423,424,427,430,431,432,433,434,436,438,442,443,444,446,447,448,449,450,451,452,455,458,459,460,462,465,466,467,468,469,470,473,474,475,476,477,481,484,485,488,489,492,493,494,495,496,497,500,501,504,507,508,510,511,512,514,515,516,518,523,526,527,528,531,532,533,536", "uncoveredLines": "14,15,82,181,199,251,252,253,398,529,539,540,541,542,543,546"}, "CommonContactBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "CommonContactBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "CommonContactSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,21,25,26,27", "uncoveredLines": ""}, "CommonContactService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7", "uncoveredLines": ""}, "CommonUserAfterInsertHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "CommonUserAfterUpdateHandler": {"coverageRate": "1.00", "coveredLines": "3,5,7,9,10,11,12,13,15,16,17,18,19", "uncoveredLines": ""}, "CommonUserBeforeInsertHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10", "uncoveredLines": ""}, "CommonUserBeforeUpdateHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,11,12,13", "uncoveredLines": ""}, "CommonUserController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CommonUserService": {"coverageRate": "0.91", "coveredLines": "6,8,9,10,11,12,13,15,17,21,22,23,27,28,29,32,33,34,39,40,41,43,44,45,46,47,48,55,56,58,59,60,61,62,63,64,71,72,73,74,76,77,78,79,80,81,82,88,89,90,91,100,101,102,103,104,105,106,107,108,109,110,112,113,119,120,121,122,124,125,126,127,128,132,133,138,139,141,142,145,150,151,152,153,155,156,157,158,159,160,161,162,164,165,166,170,172,173,177,178,180,181,184,185,186,187,188,192,194,195,197,198,201,202,204,207,210,213,215,216,217,218,222,223,224,230,231,232,233,238,239,240,241,242,243,244,245,246,247,248,252,253,258,259,260,261,262,263,265,266,267,268,270,273,274,275,276,277,278,279,280,284,285,287,288,289,290,294,297,298,299,300,304,305,306,311,312,313,314,316,319,320,321,322,323,324,327,328,330,331,332,334,335,339,340,341,342,343,344,346,347,348,351,352,357,358,359,360,361,364,366,368,369,370,371,377,379,380,381,382,383,387,388,391,392,395,396,397,398,401,402,403,404,405,406,407,408,411,412,413,414,415,420,423,424,425,430,431,432,433,434,435,436,438,455,456,457,458,461,464,465,466,469,470,471,475,476,477,478,495,496", "uncoveredLines": "84,85,93,94,189,373,441,442,443,444,445,446,447,448,449,450,452,459,481,482,483,484,487,488,491,492"}, "CompetitorController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CompetitorDTO": {"coverageRate": "0.90", "coveredLines": "5,6,7,10,11,12,16,19,22", "uncoveredLines": "2"}, "CompetitorSelector": {"coverageRate": "0.85", "coveredLines": "2,15,16,17,20,25,26,27,30,35,36,37,38,39,41,44,45", "uncoveredLines": "5,6,7"}, "CompetitorService": {"coverageRate": "0.93", "coveredLines": "2,5,6,8,9,10,13,14,15,16,17,19,22,23,24,25,26,28,31,32,33,34,35,37,40,41,42,43,44,45,46,48,51,52,53,54,56,60,61,62,65,66,69,70,71,75,78,79,83,84,87,88,89,91,92,93,95,96,99,101,102,103,107,108,112,113,114,115,116,120,121,122,123,125,126,128,129,131,132,133,137,138,141,142,143,144,147,148,149,150,151,152,155,156,159", "uncoveredLines": "63,67,73,80,81,85,139"}, "ConflictInfo": {"coverageRate": "1.00", "coveredLines": "9,10,11,12,13,14,15,18,19,22,23", "uncoveredLines": ""}, "ConflictRoleDto": {"coverageRate": "0.50", "coveredLines": "3,5,7,9,11,13,24", "uncoveredLines": "15,16,17,18,19,20,21"}, "ConflictRoleService": {"coverageRate": "0.93", "coveredLines": "2,3,4,5,6,7,11,12,13,15,16,29,32,33,37,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,67,68,70,73,74,75,76,77,78,79,83,84,85,86,87,89,90,94,96,97,98,99,101,102,103,104,106,107,108,109,113,115,118,119,120,121,122,123,124,125,126,127,129,134,135,136,139,140,141,145,146,147,151,152,153,158,159,160,162,163,164,165,166,167,168,171,172,173,174,175,180,181,182,183,184,185,186,187,189,192,193,194,195,196,197,198,199,200,201,202,204,205,206,209,210,215,216,218,219,221,222,224,225,229,230,231,232,233,234,240,241,242,244,245,249,250,251,252,253,254,260,261,262,263,268,269,270,271,272,277,279,282,283,284,285,286,287,290,291,292,293,294,295,297,298,300,301,303,304,305,306,307,310,311,312,313,314,315,316,322,323,324,325,326,328,335,336,337,338,343,344,345,346,353,354,355,356,357,362,364,367,368,369,370,371,372,373,375,376,377,378,379,381,382,385,390,391,393,394,395,396,398,399,400,401,402,403,405,406,407,408,409,411,418,419,422,423,426,431,432,433,434,435,436,438,440,441,444,445,446,447,448,449,452,453,454,456,459,463,464,465,466,467,468,469,472,473,474,476,479,484,485,486,487,488,490,491,493,498,499,500,502,503,505,510,511,512,514,517,518,519,520,521,524,527,528,529,530,531,532,533,534,535,536,537,538,541,545,547,574,575,576,578,579,581,582,583,585,586,588,594,595,596,597,598,601,602,603,604,605,606,607,608,609,611,612,614,617,618,619,620,621,623,624,626,627,630,632,633,635,636,637,640,641,642,643,644,646,647,648,649,650,654,657,658,661,662,663,664,665,666,668,669,672,673,675,676,682,683,684,685,686,687,688,689,692,694,697,702,703,704,707,708,709,710,711,713,714,717,718,722,723", "uncoveredLines": "207,211,329,347,383,412,420,457,477,494,506,522,539,551,552,553,554,555,556,558,559,561,562,563,565,566,568,569,589,712"}, "CongaTemplateSelector": {"coverageRate": "1.00", "coveredLines": "2,5,6", "uncoveredLines": ""}, "ConnectApiHelper": {"coverageRate": "0.96", "coveredLines": "50,51,52,53,54,55,56,57,58,59,60,74,75,88,89,108,109,127,128,131,132,134,135,139,140,142,143,144,146,160,161,165,166,168,169,171,174,175,176,179,180,191,192,195,197,198,199,200,202,205,207,209,210,211,212,213,214,216,217,218,219,222,223,224,228,229,231,232,234,236,237,238,239,241,243,244,245,246,247,248,249,250,251,256,257,259,261,262,263,270,271,272,275,278,279,280,281,285,286,287,288,291,292,293,294,299,300,301,302,309,310,311,312,313,314,316,323,324,325,326,329,330,331,332,333,335,342,343,344,347,349,350,351,352,354,355,357,359,360,361,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,398,405,406,408,409,410,417,418,420,421,422", "uncoveredLines": "136,162,389,390,391,392,393"}, "Constants": {"coverageRate": "0.99", "coveredLines": "5,7,8,9,10,12,13,24,86,92,94,95,97,98,114,117,119,141,173,174,175,176,177,178,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,199,200,201,202,203,204,205,206,207,208,209,210,211,212,215,229,240,243,253,259,267,276,282,284,285,286,287,288,289,290,291,292,318,320,322,331,335,336", "uncoveredLines": "6"}, "ContactCreateControllerExtension": {"coverageRate": "0.90", "coveredLines": "10,11,12,15,16,19,20,22,23,24,25,27,28,34,35,38,39,40,43,44,45,46,47,48,52,53,54", "uncoveredLines": "29,30,31"}, "ContactDataProducer": {"coverageRate": "0.40", "coveredLines": "8,9,12,13,14,18,19,21", "uncoveredLines": "24,25,27,28,29,31,32,33,34,35,36,40"}, "ContactDto": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContactHVOOrAQLToMQLBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContactHistorySelector": {"coverageRate": "0.25", "coveredLines": "2,3", "uncoveredLines": "6,7,8,9,11,12"}, "ContactHistoryService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "ContactLeadSummaryDTO": {"coverageRate": "1.00", "coveredLines": "8,11,12,13", "uncoveredLines": ""}, "ContactMAExpiredDateScheduleJob": {"coverageRate": "0.95", "coveredLines": "2,5,6,12,13,14,16,17,20,21,22,23,24,25,29,31,32,33,34,35,36,37,38,39,42,43,46,47,48,49,51,56,57,58,59,61,62,63,64,65,70,73,74,75,76,79,80,81,84,87,88,89,90,91,92,95", "uncoveredLines": "8,9,77"}, "ContactMQLToSALBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContactMergeContext": {"coverageRate": "0.80", "coveredLines": "2,7,8,9,17,18,21,22,23,24,25,28", "uncoveredLines": "4,13,14"}, "ContactOperationService": {"coverageRate": "1.00", "coveredLines": "2,3,5,8,9,10,13,14,15,18,19,21,22,23,25,26,27,28,29,31,32,33,35,36,37,38,39,40,41,42,44,47,48,49,50,51,52,53,54,57,60,61,62,63,64,65,68,69", "uncoveredLines": ""}, "ContactOrLeadFieldToMergeDto": {"coverageRate": "1.00", "coveredLines": "10,11,12,13,14", "uncoveredLines": ""}, "ContactRoleController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContactSalesFollowupToHandoverNotesJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContactTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ContentDocBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10", "uncoveredLines": ""}, "ContentDocLinkAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10,11", "uncoveredLines": ""}, "ContentDocLinkBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "3,4,6,7,8,9", "uncoveredLines": ""}, "ContentDocLinkBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "3,4,5,6,8,9,10,11,12,13,14", "uncoveredLines": ""}, "ContentDocumentLinkSelector": {"coverageRate": "0.80", "coveredLines": "2,3,6,7,14,15,18,19", "uncoveredLines": "10,11"}, "ContentDocumentLinkTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ContentDocumentLinkTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,15,16,17,18,19,24,25,26,27,28", "uncoveredLines": "12,21,30"}, "ContentDocumentSelector": {"coverageRate": "0.29", "coveredLines": "3,4", "uncoveredLines": "7,8,9,13,14"}, "ContentDocumentTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ContentDocumentTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "ContentVersionBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10", "uncoveredLines": ""}, "ContentVersionSelector": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,8,9", "uncoveredLines": ""}, "ContentVersionTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ContentVersionTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "ContractAfterUpdateTriggerHandler": {"coverageRate": "0.94", "coveredLines": "2,3,4,6,7,8,9,10,11,12,16,17,18,19,22", "uncoveredLines": "20"}, "ContractBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "6,7,8,9,10", "uncoveredLines": ""}, "ContractBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,13,14,15,16,17", "uncoveredLines": ""}, "ContractBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,26,27,28,29", "uncoveredLines": ""}, "ContractController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContractDTO": {"coverageRate": "1.00", "coveredLines": "3", "uncoveredLines": ""}, "ContractFilesAccessToMAccessQueueable": {"coverageRate": "0.42", "coveredLines": "2,3,9,10,11,13,14,15,16,19,20,22,23,24,25,27,28", "uncoveredLines": "31,32,33,34,35,36,37,38,39,40,41,42,43,46,47,48,49,50,51,52,53,54,55"}, "ContractFilesAccessToTMemberQueueable": {"coverageRate": "0.45", "coveredLines": "2,3,9,10,11,13,14,15,16,19,20,22,23,24,25,27,28,30,31", "uncoveredLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58"}, "ContractLineItemBeforeInsertTrigHdl": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContractLineItemBeforeUpdateTrigHdl": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContractLineItemSelector": {"coverageRate": "0.57", "coveredLines": "8,9,10,13", "uncoveredLines": "2,3,4"}, "ContractLineItemTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ContractOppAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "ContractOppAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "ContractOppAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "ContractOppBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,9,10", "uncoveredLines": ""}, "ContractOppBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10", "uncoveredLines": ""}, "ContractOpportunityAllocationSelector": {"coverageRate": "0.31", "coveredLines": "23,24,25,43,82,83,84,95", "uncoveredLines": "2,3,4,17,49,50,51,54,60,61,62,65,70,71,72,76,77,78"}, "ContractOpportunityAllocationService": {"coverageRate": "0.98", "coveredLines": "7,9,11,13,15,16,17,23,24,25,28,31,32,33,34,39,40,41,42,43,44,45,46,47,48,49,50,52,56,59,60,61,62,63,65,67,70,71,72,73,75,76,77,78,80,83,86,91,92,93,96,98,99,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,123,124,125,126,130,133,134,135,138,139,140,141,147,148,149,151,152,153,154,156,157,160,163,164,167,168,170,171,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,197,198,199,205,208,209,210,211,212,214,217,218,219,220,223,224,225,227,231,232,233,234,235,236,239,240,244,245,246,250,251,254,255,256,257,258,261,262,264,265,266,267,270,271,272,273,274,278,279,280,281,282,285,286,288,289,290,291,292", "uncoveredLines": "102,144,201"}, "ContractOpportunityDTO": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "ContractOpportunityTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ContractRestApi": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContractSelector": {"coverageRate": "0.41", "coveredLines": "10,11,29,65,66,85,86,89,128,129,130,151,156,157,158,180,185,186,187,208,213,214,217", "uncoveredLines": "2,3,4,6,7,33,34,51,55,56,57,58,59,60,62,69,70,73,77,78,81,93,94,97,101,102,116,120,121,124,221,222,225"}, "ContractService": {"coverageRate": "0.85", "coveredLines": "2,4,5,6,8,10,12,13,14,16,18,24,27,29,31,33,35,37,39,41,43,45,53,56,57,66,67,68,71,72,73,76,77,78,83,84,85,86,89,93,109,120,121,122,123,128,131,132,133,134,135,136,138,139,143,144,145,146,147,148,150,154,157,158,161,162,163,164,165,166,167,169,172,175,176,177,178,179,180,182,183,184,186,188,189,193,194,195,197,198,201,204,205,206,209,210,211,212,213,215,217,218,221,222,223,224,225,226,227,228,229,232,234,235,236,239,240,242,243,244,245,248,249,251,252,257,258,259,260,262,263,264,267,268,269,270,273,274,275,276,277,278,279,284,285,286,287,288,289,290,291,293,297,298,299,312,313,318,319,320,321,322,323,324,325,326,327,328,330,333,334,336,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,360,361,364,365,366,367,368,369,372,373,374,375,376,377,379,397,398,399,400,401,402,403,404,405,406,407,411,412,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,439,440,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,465,466,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,492,493,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,513,560,561,562,565,568,572,573,574,575,576,580,581,588,589,590,593,594,595,596,597,601,602,607,608,609,614,615,616,617,618,619,620,624,625,626,627,628,629,637,638,639,660,661,664,665,666,669,670,671,672,673,676,677,679,680,683,684,685,686,687,688,689,713,714,715,716,717,718,719,720,721,724,725,726,729,730,734,735,736,737,738,739,740,742,743,744,745,746,750,751,754,755,756,757,759,762,763,767,768,770,771,772,774,775,776,777,779,783,784,785,788,789,792,793,796,797,798,800,803,806,807,809,810,811,813,814,815,816,818,819,820,821,822,824,825,826,827,833,840,841,842,843,844,846,899,900,901,903,904,905,906,907,908,910,911,916,919,920,921,922,924,925,926,927,928,929,930,932,947,948,949,950,951,952,953,954,956,959,960,961,962,963,965,966,969,972,973,974,977,978,979,980,981,984,987,988,989,991,992,993,995,997,998,1000,1001,1004,1005,1009,1012,1013,1014,1015,1020,1021,1022,1023,1024,1026,1027,1030,1033,1034,1036,1037,1041,1045,1046,1047,1048,1049,1050,1051,1052,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1075,1076,1077,1084,1085,1086,1088,1093,1094,1097,1098,1099,1100,1103,1104,1107,1108,1109,1110,1111,1115,1116,1117,1119,1120,1122,1123,1125,1126,1128,1131,1132,1133,1136,1137,1138,1140,1141,1144,1145,1146,1147,1149,1156,1157,1160,1161,1162,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1176,1178,1181,1182,1183,1185,1204,1205,1206,1207,1208,1212,1213,1216,1217,1219,1220,1221,1226,1227,1228,1231,1232,1235,1236,1237,1238,1239,1240,1244,1245,1246,1247,1248,1250,1251,1252,1256,1257,1258,1259,1260,1264,1265,1266,1267,1268,1269,1270,1274,1275,1276,1277,1278,1279,1280,1285,1286,1288,1290,1292,1293,1294,1295,1296,1297,1298,1300,1301,1302,1303,1304,1305,1307,1309,1311,1315,1316,1317,1319,1320,1321,1323,1324,1326,1327,1328,1329,1330,1331,1333,1334,1335,1336,1337,1338,1341,1343,1346,1347,1348,1349,1351,1352,1353,1356,1359,1360,1361,1362,1363,1364,1365,1367,1370,1371,1372,1374,1377,1378,1379,1381,1384,1385,1387,1388,1389,1392,1393,1394,1395,1397,1398,1403,1404,1405,1409,1410,1411,1412,1416,1417,1418,1419,1423,1424,1425,1426,1430,1431,1432,1433,1437,1438,1439,1440,1443,1444,1445,1446,1449,1450,1451,1452,1453,1455,1456,1459,1460,1461,1462,1463,1465,1466,1469,1470,1471,1472,1473,1474,1477,1480,1481,1482,1483,1484,1487,1490,1491,1492,1493,1495,1498,1499,1500,1501,1502,1507,1508,1509,1510,1511,1516,1517,1518,1519,1520,1523,1526,1527,1528,1529,1530,1531,1532,1533,1538,1539,1540,1541,1542,1543,1544,1545", "uncoveredLines": "58,59,60,112,113,114,116,117,124,125,246,300,301,302,303,304,305,306,307,309,315,383,384,385,386,389,390,391,392,393,394,516,517,518,519,520,522,523,524,527,528,529,530,531,533,537,538,539,540,542,545,546,547,548,549,550,552,556,557,566,569,582,583,603,610,611,632,642,644,645,646,649,650,651,652,653,654,694,695,696,697,698,699,700,701,702,704,706,707,708,710,836,837,849,850,853,854,857,858,859,862,863,864,866,867,868,869,870,872,873,875,877,878,882,883,884,887,889,890,891,892,894,895,912,937,938,939,940,941,944,1079,1153,1188,1189,1190,1191,1192,1193,1195,1197,1200,1201,1222,1223"}, "ContractStatusConstants": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ContractTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ContractTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,20,25,26,27,28,29,34,35,36,37,38", "uncoveredLines": "13,22,31,40"}, "Contract_Line_ItemTriggerDispatcher": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "Contract_OpportunityTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,8,9,10,11,12,17,18,19,20,21,26,27,28,29,30,36,37,38,39,40,46,47,48,49,50", "uncoveredLines": "14,23,32,42,52"}, "ContractingLocationMappingSelector": {"coverageRate": "0.60", "coveredLines": "2,3,6", "uncoveredLines": "10,11"}, "ConvertLeadAndMergeContactController": {"coverageRate": "0.92", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,35,36,37,38,39,40,41,42,43,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,69,71,72,73,74,75,76,79,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,109,111,112,113,114,115,116,119,120,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139,140,141,144,145,146,147,148,149,150,151,152,153,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,173,174,175,176,181,182,183,184,185,186,188,189,190,191,192,193,197,198,199,200,203,208,209,210,211,212,213,215,216,217,218,219,220,224,225,226,227,230,234,235,236,237,238,239,240,242,243,245,246,247,248,252,256,257,258,261,262,265,266,267,268,269,270,271,272,274,277,280,283,284,285,289,290,291,292,294,295,296,297,298,299,300,301,302,303,306,307,308,309,315,319,320,321,322,327,328,329,334,335,357,358,359,360,361,362,363,364,366,367,369,371,375,376,377,378,379,380,381,382,384,385,387,389,393,394,395,396,422,423,424,425,426,427,428,430,431,432,433,434,436,437,438,440,442,443,444,445,446,447,449,450,453,454,455,457,459,460,461,462,463,464,466,467,468,473,474,475,476,478,480,481,483,485,486,487,488,490,491,492,493,494,496,497,499,500,501,504,505,506,507,508,510,511,520,521,522,523,524,525,528,529,533,534,535,536,538,541,542,543,544,545,546,550,551,552,553,554,555,557,558,559,563,564,565,566,571,572,573,577,578,579,583,587,588,589,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,638,639,640,641,642,643,645,648,652,653,654,655,656,657,658,659,660,661,663,666,667,668,669,670,672,673,674,679,682,683,684,685,686,688,689,692,693,696,700,703,704,705,706,707,710,713,714,715,716,717,718,719,720,721,725,726,729,730,731,732,733,737,738,739,740,741,742,743,744,747,750,751,752,753,756,760,761,762,763,766,767,768,769,770,771,775,778,779,780,781,782,783,786,789,790,791,792,795,798,799,800,801,804,808,811,812,813,817,821,822,823,824,825,832,833,834,835,836,837,838,839,840,841,842,843,847,848,850,853,854,855,856,859,862,863,864,865,866,867,868,869,870,874,877,878,879,880,881,882,883,887,891,892,893,894,895,898,902,903,904,907,908,909,910,911,912,913,917,918,919,920,921,925,926,928,931,932,933,934,935,937,939,942,943,944,945,946,947,950,951,952,954,957,958,959,960,963,966,967,968,969,972,993,994,995,996,997,998,999,1002,1005,1006,1007,1010", "uncoveredLines": "339,340,341,343,344,348,349,350,352,353,400,401,402,403,404,407,408,409,410,411,412,414,417,512,513,514,516,568,631,632,633,634,635,636,664,697,802,805,814,828,905,975,976,977,978,979,980,981,982,986,987,990,1008"}, "CorporateApartmentAUSService": {"coverageRate": "1.00", "coveredLines": "10,11,12,13,15,16,17,20,21,24,25,26,27,29,30,31,32,34,36,37,40,43,44,45,46,47,48,49,50,53,54,55,56,57,60,61,62,65,66,69,72,73,74,75,76,78,81,82,83,84,85,86,87,88,90,93,94,95", "uncoveredLines": ""}, "CorporateApartmentAutoCodingBatchable": {"coverageRate": "0.85", "coveredLines": "2,6,17,23,24,25,26,29,30,31,32,33,36,37,38,39,42,45,48,49,52,53,56,62,63,64,65,66,68,69,72,73,76,78,79,80,83,87,88,89,90,91,93,94,97,98,100,102,103,104,107", "uncoveredLines": "11,12,13,40,43,54,57,70,95"}, "CorporateApartmentAutoCodingController": {"coverageRate": "0.94", "coveredLines": "16,17,18,19,20,22,23,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,43,45,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,92,100,101,102,103,104,105,106,107,108,109,110,111,114,115,116,117,119,120,121,122,133,134,137,138,139,142,143,144,145,146,148,150,151,154,157,158,159,161,162,164,165,166,169,170,171,172,174,175,178,179,180,181,183,184,185,187,188,189,190,191,192,194,195,198,199,200,202,204,205,210,211,213,215,218,219,220,221,222,223,226,229,230,234,235,236,239,240,242,243,248,249,250,251,253,257,258,260,261,262,264,265,267,268,269,270,272,273,275,276,277,278,280,285,286,287,288,289,290,293,294,296,297,298,299,300,301,302,303,311,313,314,319,322,323,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,342,343,344,345,346,347,348,349,350,351,352,353,354,355,359,362,363,364,365,366,367,368", "uncoveredLines": "124,125,126,128,224,227,237,305,306,307,308,369,370,373"}, "CorporateApartmentBaseService": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,6,8,9,10,13,14,15,18,19,20,22,23,25,27,28,30,32,33,35,38,39,41,43,50,53,54,69,70,73,74,77,78,81,82,85,86,89,90,91,93,94,98,101,103,105,106,107,108,109,110,111,112,113,116,119,121,122,125,126,127,128,129,130,133,136,137,139,141,143,147,148,149,150,151,152,153,154,155,156,157,158,159,161,163,166,167,168,169,170,171,172,173,176,177,178,179,181,182,184,185,187,190,191,192,193,196,199,200,202,204,205,206,207,209,210,211,213,214,215,222,226,229,230,231,232,233,234,235,236,247,248,249,250,251,252,253,254,255,258,262,263,264,265,266,268,271,272,273,274,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,300,301,302,303,304,305,307,309,313,314,317,318,319,321", "uncoveredLines": "57,58,61,62,65,66,95,99,131,134,216,217,218,219,220,237,238,239,240,241,242,243,244"}, "CorporateApartmentCNService": {"coverageRate": "0.90", "coveredLines": "9,10,16,17,18,19,21,22,23,27,28,29,30,32,33,34,37,38,39,40,42,44,47,48,49,50,51,52,54,58,59,62,63,64,65,66,67,68,69,72,73,76,79,80,100,101,102,104,105,107,109,110,112,115,117,118,120,121,123,125,126,127,128,129,130,132,133,134,135,136,138,141,144,145,146,147,148,149,150,152,156,161,162,165,166,169,170,171,173,174,176,177,179,180,184,185,186,188,189,191,193,196,197,198,199,200,201,202,203,204,205,206", "uncoveredLines": "45,83,84,85,86,87,88,89,90,92,95,96,97"}, "CorporateApartmentClearBatchJob": {"coverageRate": "1.00", "coveredLines": "2,4,7,8,11,13,14,15,16,19,22,23", "uncoveredLines": ""}, "CorporateApartmentDEService": {"coverageRate": "0.60", "coveredLines": "2,14,15,16,17,18,21,24,25,26,27,28,32,55,56,59,60,61,63,64,65,66,70,74,75,76,77,79,80,81,82,83,84,85,86,89,97,98,101,102,115,116,119,120", "uncoveredLines": "5,6,8,9,11,35,36,37,38,39,40,41,42,45,48,49,52,53,92,93,94,105,106,107,109,110,112,117,122"}, "CorporateApartmentNAService": {"coverageRate": "0.84", "coveredLines": "10,11,12,13,15,16,17,20,21,22,24,25,26,27,28,30,33,34,37,38,39,40,41,42,43,47,48,49,54,55,58,61,62,63,67,70,71,74,75,76,77,78,79,80,82,85,86,87,88,89,90,91,92,95,96,97,100,101,104,107,108,109,110,111,114", "uncoveredLines": "117,118,119,120,121,122,123,124,126,129,130,131"}, "CorporateApartmentSelector": {"coverageRate": "0.63", "coveredLines": "2,3,4,71,72,73,106,107,111,117,118,119,144,185,186,187,191,192,193", "uncoveredLines": "36,37,38,57,150,151,152,180,197,198,199"}, "CountNumOfEngagementThisYearBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CountNumOfEngagementThisYearScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CountryAndDivisionRegionSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "CountrySelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CreateOpportunityControllerExtension": {"coverageRate": "0.88", "coveredLines": "5,6,7,10,11,12,18,22,23,24,25,26,27,28,29,30,34,35,36,37,38,39", "uncoveredLines": "13,14,15"}, "CronTriggersSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,10,11,14,15,16", "uncoveredLines": ""}, "CsvUtils": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CurrencyController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CurrencySelector": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,8,15,16,17,18,24,25,26,27,28,31,32,35", "uncoveredLines": "11,12,21"}, "CurrencyService": {"coverageRate": "0.75", "coveredLines": "2,4,6,8,10,12,14,16,18,20,22,23,25,26,27,28,30,31,34,40,42,43,44,46,47,48,50,55,56,78,81,82,83,86,87,89,90,93,94,96,97,98,99,100,101,102,103,104,107,110,111,112,113,114,115,118", "uncoveredLines": "51,52,57,58,59,60,61,62,63,64,65,66,67,68,69,70,72,73,74"}, "CurrencyUpdateSchedulable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CurrentMonthForecastValue": {"coverageRate": "1.00", "coveredLines": "6,7,8,9", "uncoveredLines": ""}, "CustomAdminEditTimecardController": {"coverageRate": "0.30", "coveredLines": "2,3,6,7,8,9,10,14,15,16,18,20,22,94,101,102,103,104,105,106,107,108,111,112,113,115,116,118,119,121,141,142,143,151,152,153,159,162,163,164,165,166,168,175,176,177,178,179,181,184,185,186,187,188,190,198,199,201", "uncoveredLines": "26,28,32,34,38,40,44,46,50,52,56,58,62,64,69,70,71,73,74,78,79,80,81,82,83,84,85,86,88,89,90,126,127,131,132,135,137,146,147,148,154,155,156,157,169,170,171,172,173,174,193,194,195,206,208,212,213,214,215,216,218,219,220,223,224,225,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,252,253,255,256,257,258,259,260,261,264,267,268,269,271,274,275,276,278,281,282,283,284,287,288,289,290,291,292,293,294,295,296,297,298,299,300,302,303,304,305,306,308,309,310,311,313,314,315"}, "CustomDeleteController": {"coverageRate": "0.67", "coveredLines": "7,8,9,11,12,14,15,16,17,18,21,29", "uncoveredLines": "19,22,23,24,25,27"}, "CustomDeleteErrorController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CustomDeleteTimecardController": {"coverageRate": "0.48", "coveredLines": "2,3,4,5,6,8,22,23,25,26,27,28,29,50,51,53,54,55,58,60,65,67,68,69,81,82,85,86,87,88,90,91", "uncoveredLines": "10,12,16,18,33,34,35,36,39,40,41,42,43,44,45,46,47,61,62,71,72,76,77,93,94,95,96,98,99,100,101,102,103,104"}, "CustomDeleteTimecardSplitController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CustomEditTimecardSplitController": {"coverageRate": "0.81", "coveredLines": "2,4,5,14,15,16,18,20,24,25,26,27,28,29,30,31,32,33,34,35,37,38,39,40,44,45,48,49,50,51,52,54,63,64,65,66,67,70,71,72,74,76,79,80,81,83,84,85,98,99,100,107,108,109,112,113,114,115,116,117,118", "uncoveredLines": "57,59,86,87,88,89,90,91,92,93,94,101,102,103"}, "CustomExceptionData": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CustomMilestoneNotSetException": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "CustomSettingsSelector": {"coverageRate": "0.62", "coveredLines": "2,5,6,9,10,25,26,27,28,32,33,48,49,52,56,57,60,72,73,76,77,80,81,84,85,96,97,100,101", "uncoveredLines": "13,14,17,18,21,22,36,37,40,41,44,64,65,68,88,89,92,93"}, "DMLService": {"coverageRate": "0.41", "coveredLines": "7,8,9,10,12,13,16,17,40,41,44,45,52,53,68,69,72,73,76,77,80,81,88,89,128,129", "uncoveredLines": "20,21,24,25,28,29,32,33,36,37,48,49,56,57,60,61,64,65,84,85,92,93,96,97,100,101,104,105,108,109,112,113,116,117,120,121,124,125"}, "DMLServiceImpl": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "6,7,8,9,11,12,14,16,17,18,19,20,24,25,28,29,30,31,35,36,39,40,41,42,46,47,50,51,54,55,56,58,59,60,61,62,63,64,66,70,71,72,73,74,78,79,80,81,82,83,84,88,89,90,92,93,94,95,96,97,102,103,104,105,109,110,112,113,114,115,120,121,123,124,125,126,131,132,135,136,137,138,139,143,144,145,146,147,148,149,153,154,157,158,159,161,162,165,166,167,168,169,170,171,174,177,178,179,181,184,185,186,187,188,189,190,193,196,197,198,200,203,204,205,206,207,208,209,211,214,217,218,219,220,221,222,223,226,229,230,231,233,236,237,238,239,240,241,242,243,244,247,250,251,252,253,254,255,256,257,258,261,264,265,266,267,268,269,270,271,272,276,279,280,281,282,283,286,287,288,289,293,294,295,297,298,299,302,303,304,306"}, "DMLUtils": {"coverageRate": "0.78", "coveredLines": "2,3,4,5,7,8,10,12,13,14,20,21,24,25,26,27,31,32,35,36,37,42,43,46,47,50,51,52,54,55,56,57,58,59,60,66,67,68,74,75,76,77,78,79,80,105,106,108,109,110,111,116,117,119,120,121,122,131,132,133,149,150,153,154,155,157,158,161,162,163,164,165,170,173,174,177,180,181,182,183,184,185,186,189,192,193,194,196,199,200,201,202,210,213,214,215,216,217,222,225,226,229,232,233,234,235,236,237,238,239,240,243,246,247,248,249,250,251,252,253,254,257,260,261,262,263,264,265,266,267,268,272,275,276,277,278,279,282,283,284,289,290,291,293,294,295,298,299,302", "uncoveredLines": "15,16,38,62,69,70,84,85,86,88,89,90,91,92,93,98,99,100,101,127,128,134,135,139,140,141,142,143,144,145,166,167,175,203,204,205,207,218,219,227,285,300"}, "DateUtils": {"coverageRate": "0.91", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,17,19,20,23,24,25,26,29,30,31,32,33,34,36,38,42,43,44,45,47,54,55,56,58,59,60,62,63,66,67,70,71,76,77,78,80,84,85,86,87,88,91,92,93,96,97,98,99,100,102,105,106,109,110,111,114,115,117,118,119,120,121,122,123,126,129,130,131,132,134,135,137,146,147,150,151,152,153,159,160,163,164", "uncoveredLines": "49,50,73,140,141,142,154,155,156"}, "DayOfWeek": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealApprovalScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealCoachDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,15,18,21,23,24,25,26,27,28,29,30,33", "uncoveredLines": ""}, "DealCoachMappingSelector": {"coverageRate": "0.73", "coveredLines": "14,15,20,21,25,26,27,28", "uncoveredLines": "6,7,10"}, "DealCoachReassignAccessDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,14,15,16,17,18,21,22", "uncoveredLines": ""}, "DealOtherCoachSelector": {"coverageRate": "1.00", "coveredLines": "6,7,10,14,15", "uncoveredLines": ""}, "DealReviewAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10", "uncoveredLines": ""}, "DealReviewApprovalActionButtonDTO": {"coverageRate": "0.65", "coveredLines": "3,6,9,12,15,25,26,27,28,29,30", "uncoveredLines": "17,18,19,20,21,22"}, "DealReviewBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6", "uncoveredLines": ""}, "DealReviewBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11", "uncoveredLines": ""}, "DealReviewChatterMigrationBatch": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealReviewController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealReviewDTO": {"coverageRate": "0.88", "coveredLines": "7,9,11,13,15,17,19,21,23,25,29,41,42,43,44,45,46,47,48,49,50", "uncoveredLines": "3,5,27"}, "DealReviewFilesAccessToTMemberQueueable": {"coverageRate": "0.70", "coveredLines": "6,7,8,14,15,16,19,20,21,24,25,26,27,28,29,31,32,50,51,52,53,54,55,56,62,63,64,65,66,67,68", "uncoveredLines": "35,36,37,38,39,40,41,42,43,44,45,58,70"}, "DealReviewFinanceBatch": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealReviewFinanceNotifyJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealReviewSelector": {"coverageRate": "0.40", "coveredLines": "20,21,63,67,68,69,72,83,84,87", "uncoveredLines": "2,3,6,11,12,15,77,78,79,91,92,95,96,98,99"}, "DealReviewService": {"coverageRate": "0.77", "coveredLines": "2,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,52,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,86,87,88,91,93,94,95,97,99,100,157,158,159,160,162,163,165,167,173,174,175,176,177,180,181,182,183,184,186,187,188,189,191,192,195,197,199,200,201,204,205,206,207,210,211,212,213,216,217,218,219,222,223,224,225,226,227,236,237,238,239,240,243,244,246,247,248,251,252,253,256,258,259,260,265,266,267,270,271,275,276,279,280,281,282,283,284,287,290,291,292,293,296,297,298,300,301,303,304,305,306,307,311,312,314,315,316,317,320,321,325,326,329,330,335,336,338,339,340,343,345,346,349,351,352,354,375,376,377,378,379,380,382,383,384,385,386,388,389,390,391,392,394,395,396,397,398,402,403,406,409,410,412,413,417,418,421,424,425,427,430,435,436,437,438,439,440,443,445,446,447,448,450,452,454,455,456,459,460,461,462,463,464,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,579,580,581,582,583,585,587,588,589,590,591,594,597,598,599,600,601,603,604,605,606,607,609,610,615,616,617,618,619,620,622,625,626,627,628,629,630,631,632,634,635,636,640,641,644,645,646,648,651,652,653,654,655,657,658,659,662,663,664,665,666,667,670,673,674,676,677,678,679,682,683,684,686,688,691,692,694,695,696,699,700,701,702,703,706,709,710,711,712,714,717,718,719,720,721,722,723,726,729,730,731,732,734,735,738,745,746,747,749,750,751,753,754,755,756,757,758,759,761,764,765,767,768,770,771,772,773,774,778,779,780,781,782,783,785,791,792,793,794,795,796,797,800,803,808,809,810,811,812,815,818,819,821,822,823,825,826,827,828,829,831,832,834,835,836,837,838,839,840,841,842,843,844,845,846,848,851,852,853,854,855,857,858,860,861,862,863,864,866,867,868,870,871,873,876,877,880,881,882,883,884,888,889,890,891,892,893,896,902,903,904,905,907,908,909,910,911,912,913,915,916,918,919,920,921,922,923,924,925,928,929,930,931,932,933,934,935,938,939,944,945,946,947,950,951,952,954,955,956,959,960,963,964,965,966,967,970,971,973,974,975,977,980,981,982,983,984,985,989,990,991,993,994,998,999,1001,1002,1003,1012,1013,1014,1015,1016,1017,1018,1021,1022,1024,1025,1026,1033,1034,1035,1036,1037,1038,1041,1047,1048,1049,1050,1051,1053,1054,1056,1057,1059,1060,1063,1066,1067,1068,1069,1070,1071,1072,1075,1078,1079,1080,1081,1082,1083,1084,1086,1087,1088,1090,1092,1093,1094,1095,1099,1101,1102,1104,1105,1106,1107,1108,1109,1113,1114,1115,1117,1129,1130,1131,1132,1133,1134,1135,1136,1139,1156,1157,1158,1161,1162,1163,1164,1165,1166,1167,1169,1170,1171,1172,1173,1175,1176,1177,1179,1180,1182,1185,1186,1189,1190,1191,1192,1193,1197,1199,1201,1202,1203,1204,1206,1209,1210,1211,1212,1213,1214,1215,1218,1221,1222,1223,1224,1225,1226,1228,1229,1231,1232,1234,1235,1236,1238,1241,1242,1243,1244,1245,1246,1247,1248,1249,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1269,1270,1287,1298,1309,1310,1311,1312,1313,1314,1315,1316,1317,1319,1364,1365,1366,1367,1368,1369,1370,1373,1383,1386,1387,1388,1389,1390,1392,1393,1394,1395,1397,1402,1403,1406,1407,1408,1409,1410,1411,1412,1418,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1439,1440,1444,1445,1446,1447,1448,1449,1450,1453,1454,1458,1459,1460,1462,1463,1464,1466,1469,1470,1471,1472,1473,1474,1479,1480,1481,1482,1483,1487,1490,1491,1492,1494,1495,1496,1498,1500,1503,1504,1505,1507,1508,1509,1511,1513", "uncoveredLines": "105,106,107,108,109,110,111,112,113,115,119,120,121,122,123,124,125,126,127,129,131,132,133,135,139,140,141,142,143,144,145,146,147,148,149,150,152,153,168,232,233,327,347,355,360,361,362,364,365,367,368,370,371,404,407,419,422,428,492,493,494,495,497,500,501,502,503,506,507,508,510,511,512,513,514,516,518,519,520,521,525,526,527,528,529,530,533,534,535,536,539,540,541,542,544,546,549,552,553,554,555,556,559,560,561,562,564,565,567,568,570,571,573,574,576,724,741,742,786,798,799,801,802,804,805,874,899,900,948,995,1027,1028,1029,1039,1120,1121,1122,1123,1124,1126,1142,1143,1144,1145,1146,1147,1148,1149,1153,1183,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1322,1323,1324,1325,1326,1327,1328,1329,1331,1333,1334,1335,1337,1339,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1357,1359,1374,1375,1376,1377,1378,1379,1413,1414,1416,1436,1455"}, "DealReviewSyncApproveStatusBatch": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DealReviewTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "Deal_ReviewTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,16,17,18,19,20,26,27,28,29,30", "uncoveredLines": "12,22,32"}, "DefaultRateBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "DefaultRateBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "DeleteFirstRequestingInfoBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,14,15,16,17,19,20,21,24,25", "uncoveredLines": ""}, "DeleteResourceTypeMAPersonBatchJob": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,4,5,6,7,9,10,13,14,17,18"}, "DeleteTimecardController": {"coverageRate": "0.58", "coveredLines": "2,3,4,5,6,7,8,9,10,87,88,89,92,93,95,98,99,100,101,102,103,105,106,109,111,112,115,116,118,125,126,127,131,132,133,134,135,137,138,139,140,141,143,144,146,148,149,151,153,156,158,159,163,166,167,168,171,172,173,177,178,179,180,181,189,190,222,223,224,226,227,230,231,232,233,236,237,238,240,243,244,246,247,249,250,252,253,255,256,260,261,262,265,267,268,270,271,274,277,278,279,280,287,288", "uncoveredLines": "23,24,25,26,27,28,29,30,31,37,38,39,40,44,45,49,50,51,53,54,55,56,58,62,63,64,65,67,71,72,73,74,76,80,81,82,84,90,107,119,120,122,128,154,160,161,174,182,183,184,192,197,198,200,201,202,203,204,206,207,208,209,211,212,213,214,215,216,217,241,257,258,282,283,284"}, "DemandServiceCallout": {"coverageRate": "0.50", "coveredLines": "4,5,8,9,12,13,14,15,16,17,64,65,66,67,68,69,71,74,75,76,77,78,79,81,84,85,87,88,89,112,113,114,115,116,117,118,119,120,122,123,126,127,138,139,140,141,143,144,145,146,148,151,152,153,154,155,156,159,160,161,165,166,189,190,191,192,193,194", "uncoveredLines": "20,21,22,23,24,25,28,29,30,31,32,33,35,38,39,40,41,42,43,46,47,48,49,50,51,53,56,57,58,59,60,61,92,93,94,95,97,98,99,102,103,104,105,107,108,109,124,130,131,132,133,134,135,169,170,171,172,173,174,176,179,180,181,182,183,184,186"}, "DepartmentDTO": {"coverageRate": "0.20", "coveredLines": "86,87,88,89,90,91,92,93,94,95", "uncoveredLines": "11,12,13,14,15,16,17,18,19,22,23,26,27,30,31,34,35,38,39,42,43,46,47,50,51,54,55,58,59,62,63,66,67,70,71,74,75,78,79,82,83"}, "DepartmentScheduleJob": {"coverageRate": "0.75", "coveredLines": "2,3,10,11,14,15", "uncoveredLines": "5,6"}, "DepartmentSelector": {"coverageRate": "1.00", "coveredLines": "2,4,5,8,9,12,13,14,18,19,20", "uncoveredLines": ""}, "DepartmentService": {"coverageRate": "1.00", "coveredLines": "9,11,12,13,15,19,20,23,24,27,28,31,32,33,34,35,38,41,42,43,44,46,50,51,52,53,54,58,59,60,61,62,63,65,68,69,70,74", "uncoveredLines": ""}, "DistributedJournalBatchableJob": {"coverageRate": "0.82", "coveredLines": "2,4,5,6,7,8,9,11,48,57,58,59,60,61,62,63,64,65,66,68,87,100,101,102,103,104,105,106,107,108,109,110,111,114,116,117,122,123,126,127,128,130,131,134,135,138,139,140,141,142,143,146,147,148,149,150,152,153,155,156,157,159,162,163,164,165,166,168,171,172,173,175,178,179,180,181,182,183,184,185,186,189,190,191,192,193,194,195,196,197,200,201,202,204,207,208,209,210,211,212,215,234,235,236,237,239,240,241,245,246,247,248,249,250,251,252,253,254,255,256,259,260,263,265,266,267,270,271,272,275,277,280,281,282,283,284,287,288,289,290,294,295,296,297,298,300,308,309,310,311,312,314", "uncoveredLines": "20,21,22,23,24,25,26,29,30,32,35,36,37,38,39,40,41,42,44,219,220,221,224,225,226,227,228,229,230,243,303,304,305"}, "DistributedJournalBatchableJobTest": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DistributedJournalItemSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3"}, "DomainProcessCoordinator": {"coverageRate": "0.99", "coveredLines": "4,6,13,14,18,19,22,23,25,26,27,28,29,32,35,36,39,44,45,46,47,48,49,50,51,52,53,54,55,61,62,65,66,69,70,71,73,76,77,78,79,81,84,85,88,89,90,92,93,98,99,104,105,115,120,121,131,135,138,139,140,142,144,146,147,148,149,152,153,154,156,157,158,159,160,161,162,163,164,165,166,167,171,172,173,174,178,179,181,182,183,184,185,186,187,188,191,193,196,197,198,199,201,204,206,208", "uncoveredLines": "175"}, "DraftContractNotificationJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "DynamicSelector": {"coverageRate": "0.50", "coveredLines": "2,10,11", "uncoveredLines": "5,6,7"}, "EgenciaExpenseFactory": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,9,11,12,13,14,15,16,17,20", "uncoveredLines": ""}, "EgenciaRawDataService": {"coverageRate": "0.91", "coveredLines": "2,3,4,5,6,7,8,11,12,13,14,15,16,18,19,21,22,24,25,28,29,30,31,32,34,37,42,43,45,50,51,52,53,54,57,58,59,60,63,64,65,66,71,72,74,75,77,78,79,81,82,85,89,90,91,92,93,94,96,97,98,101,102,103,105,108,109,110,113,114,115,128,129,130,131,132,134,137,138,140,143,144,146,147,148,149,150,151,153,154,165,166,167,168,169,171,175,176,177,178,179,181,182,184,186,187,191,192,193,194,195,197,198,200,202,203", "uncoveredLines": "38,39,111,112,120,121,122,124,135,157,158"}, "EmailActivity": {"coverageRate": "0.61", "coveredLines": "3,7,16,19,31,32,33,34,35,36,37,38,39,40", "uncoveredLines": "42,43,44,45,46,47,48,49,50"}, "EmailMessageAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "EmailMessageBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "EmailMessageTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "EmailMessageTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,15,16,17,18,19", "uncoveredLines": "11,21"}, "EmailSelector": {"coverageRate": "0.50", "coveredLines": "11,12,14", "uncoveredLines": "3,4,7"}, "EmailSendBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EmailSenderQueueable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EmailTableGenerator": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,10,13,16,17,20,21", "uncoveredLines": ""}, "EnrichQueueableJob": {"coverageRate": "0.82", "coveredLines": "2,3,4,5,6,7,8,9,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,38,39,40,41,43,47,48,51,52,53,54,57,58,59,60,61,69,70,71,72,73,74,75,76,78,79,86,89,93,94,98,99,100,101,104,105,106,107,114,115,116,117,118,119,120,121,125,127,128,132,133,137,138,142,143,144,145,146,147,148,152,153,154,157,159,160,161,162,163,164,167,169,170,171,186,187,190,191,192,193,194,195,196,197,198,205,212,216,220,221,222,223,224,225,228,229,230,231,234,235,237,241,242,243,244,245,247,248,250,251,252,253,254,256,259,260,261,262,263,267,270,271,272,273,279,280,281,283,286,287,289,292,293,294,295,296,297,302,303,305,308,309,310,311,312,313,314,315,316,317,319,320,321,322,323,324,325,327,331,332,333,334,335,336,338,339,341,344,346,351,352,353,357,358,359,360,361,362,363,364,365,366,367,368,369,376,380,381,385,390,391,395,396,397,398,400,401", "uncoveredLines": "63,64,65,80,81,82,83,84,87,95,108,109,123,134,139,149,172,173,174,175,176,177,180,181,184,199,200,201,203,206,207,208,210,213,214,217,218,264,276,284,298,299,340,342,374,375,386,399"}, "EnrichedMemberInfoDto": {"coverageRate": "1.00", "coveredLines": "2,4,6,8,10,12,14,16,20,24,25,26,27,28,29,30,31,32", "uncoveredLines": ""}, "EnrichmentController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EnrichmentHistorySelector": {"coverageRate": "0.71", "coveredLines": "2,8,9,12,13,14,18,19,24,25", "uncoveredLines": "4,5,20,26"}, "EnrichmentHistoryService": {"coverageRate": "0.97", "coveredLines": "2,4,7,8,11,12,13,14,15,16,17,18,19,20,22,25,28,29,30,33,34,35,36,37,39,40,42,43,46,50,51,52,53,54,55,56,57,58,59,60,62,64,65,66,68,69,70,71,72,73,74,75,76,77,80,83,84,85,86,88,89,92,93,96,97,99,100,102,105,106,107", "uncoveredLines": "67,94"}, "EstVsActualsSelector": {"coverageRate": "1.00", "coveredLines": "2,3,7,8,9,10", "uncoveredLines": ""}, "EstimatedHoursFromAssignment": {"coverageRate": "1.00", "coveredLines": "5,13,14,15,18,19,20,21,24,26,27,28,29,30,32,33,34,37,38,39,43,46,47,48,49,51,52,53,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,72,77", "uncoveredLines": ""}, "EstimatedHoursFromHoliday": {"coverageRate": "1.00", "coveredLines": "2,8,16,17,18,21,22,23,24,26,27,29,31,32,33,34,35,37,38,40,42", "uncoveredLines": ""}, "EstimatedHoursFromLeave": {"coverageRate": "1.00", "coveredLines": "2,8,16,17,18,21,22,23,24,26,27,28,29,30,31,32,35,36,37,38,39,41,42,44,47", "uncoveredLines": ""}, "EstimatedHoursFromNonAssignment": {"coverageRate": "0.98", "coveredLines": "8,19,20,21,22,23,24,27,28,29,30,31,32,34,35,36,37,40,41,44,45,46,47,49,53,56,57,58,59,60,61,62,64,65,66,67,68,70,71,72,73,74,79,82,83,84,85,86,88,89,90,91,92,93,95,96,97,99,100,102,103,105,106,108", "uncoveredLines": "38"}, "EstimatedHoursFromSavedTimecard": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,11,15,22,23,24,27,28,29,30,31,32,33,34,36,37,38,39,40,41,42,43,44,45,46,48,49,51,54", "uncoveredLines": ""}, "EstimatedHoursGenerator": {"coverageRate": "0.99", "coveredLines": "2,3,4,5,6,7,8,9,11,18,19,20,21,22,25,26,29,30,31,32,33,34,35,36,37,39,40,41,42,44,45,47,48,49,50,53,54,55,60,61,62,68,69,70,71,72,73,74,75,76,77,78,79,80,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "uncoveredLines": "51"}, "EventAfterDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EventAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "EventAfterUpdateTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EventBeforeDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EventBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "EventBeforeUpdateTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EventPublisher": {"coverageRate": "0.62", "coveredLines": "2,44,45,48,49,52,53,56,57,60,63,64,65,66,70,75,76,77,78,96,97,98,99,103,104,105,106,110,111,112,113,117,118,119,120,124,125,126,127,131,132,133,134,138,139,140,141,145,146,147,148,152,153,154,155,156,165,170,171,172,173,174,175,176,180,181", "uncoveredLines": "5,8,9,10,13,15,16,17,18,21,22,26,27,28,31,33,34,35,36,39,40,46,58,61,67,71,82,83,84,85,89,90,91,92,157,158,159,160,162,166"}, "EventSelector": {"coverageRate": "0.75", "coveredLines": "6,7,10,11,14,15", "uncoveredLines": "2,3"}, "EventService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,9,10,11", "uncoveredLines": ""}, "EventTrackingRetryBatchableJob": {"coverageRate": "0.94", "coveredLines": "2,3,4,6,7,8,11,12,13,14,16,17,22,23,24,25,26,27,28,29,30,31,35,36,37,38,39,40,41,48,49,50,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,73", "uncoveredLines": "42,43,44"}, "EventTrackingRetryScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EventTrackingSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6", "uncoveredLines": ""}, "EventTrackingService": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,11,12,13,15,18,19,20,21,22,23,24,25,26,27,28,30,33,34,35,36,37,38,39,40,41,42,43,45,48,50,51,52,53,55,58,59,60,63,64,65,68,69,70,73,74,75,78,79,80,83,84,85,88,89,90,92,93,94,96,99,100,101,102,104,105,108,109,110,111,112,114,115,118,119,120,121,122,124,125", "uncoveredLines": ""}, "EventTrackingStatus": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "EventTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "EventTriggerDispatcher": {"coverageRate": "0.38", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,29,30,31,32,33", "uncoveredLines": "15,19,20,21,22,23,25,35,39,40,41,42,43,45,49,50,51,52,53,55,59,60,61,62,63,65"}, "EventUtils": {"coverageRate": "0.92", "coveredLines": "2,4,5,6,7,8,9,12,15,16,17,18,19,20,21,24,27,28,29,30,31,32,34,35,38,46,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,64,66,67,70,72,75,76", "uncoveredLines": "79,80,83,84"}, "ExceptionalExpenseCodingJSONforTest": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9", "uncoveredLines": ""}, "ExceptionalExpenseCodingRuleSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ExchangeRateController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ExpReptStatusSyncToExpensifySchedule": {"coverageRate": "0.71", "coveredLines": "2,3,4,6,7,8,9,17,18,21,22,23,24,26,38,39,40,41,43,44,49,50,51,52,53,56,59,60,61,62,65,68", "uncoveredLines": "5,11,12,13,14,19,27,31,32,33,34,35,63"}, "ExpenseAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,8,9", "uncoveredLines": ""}, "ExpenseAfterInsertTriggerHandler": {"coverageRate": "0.93", "coveredLines": "2,3,5,6,7,9,11,12,13,14,16,17,18,22", "uncoveredLines": "19"}, "ExpenseAfterUnDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,10,11", "uncoveredLines": ""}, "ExpenseAfterUpdateTriggerHandler": {"coverageRate": "0.88", "coveredLines": "2,3,5,6,7,9,10,12,13,15,16,18,23,24", "uncoveredLines": "19,20"}, "ExpenseAmountItemSelector": {"coverageRate": "0.75", "coveredLines": "2,3,4", "uncoveredLines": "5"}, "ExpenseAttachmentService": {"coverageRate": "0.97", "coveredLines": "7,8,9,12,13,14,15,18,21,22,23,26,27,28,29,30,31,32,36,37,40,42,43,44,45,46,53,55,56,57,58,59,60,61,62,64,65,70,71,74,76,77,78,79,80,82,83,84,85,87,89,91,94,98,99,100,101,102,105,106,109,110,111,112,113,117,121,123,124,125,126,127,128,129,138,139,140,144,146,147,148,149,150,157,158,159,160,163,164,165,168,169,170,173,174,175,176,177,178,184,185,188", "uncoveredLines": "118,141,186"}, "ExpenseAutoShare": {"coverageRate": "0.86", "coveredLines": "10,11,15,24,25,26,27,28,29,30,34,35,36,37,38,40,41,42,44,45,46,47,52,53,54,60,64,76,77,79,83,85,87,88,92,93,95,96,97,100,103,104,106,107,111,114,115,117,118,120,121,123,124,128,131,132,133,134,138,139,140,141,142,146,147,148,149,150,151,152,153,154,155,160", "uncoveredLines": "48,50,55,57,61,67,68,69,70,72,80,108"}, "ExpenseBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,11,12,13,14", "uncoveredLines": ""}, "ExpenseBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10", "uncoveredLines": ""}, "ExpenseBeforeUpdateTriggerHandler": {"coverageRate": "0.98", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,15,16,17,18,19,20,21,24,25,26,28,29,30,31,32,33,36,37,38,43,44,46,47,48,49,50,51,52,53,54,55,57,60,64,65,69,70,73,74,75,76,77", "uncoveredLines": "34"}, "ExpenseBigShareRevokeBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ExpenseCreationController": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,8,9,10,11,14,15,16,17,21", "uncoveredLines": ""}, "ExpenseEventLog": {"coverageRate": "1.00", "coveredLines": "6,7,11,17,18,19,20,23,24,26,28,29,31,32,33,35,36,37,38,41,42,43,45,46,47,49,50,51,52,56,57,62,63,65,66,67,68,69,70,71,72,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,100,101,102,103,107,111,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,138,139,141,142,144,148,149,150,151,154,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178,181,182,183,184,185,186,187,191", "uncoveredLines": ""}, "ExpenseEventLogSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ExpenseFactory": {"coverageRate": "0.84", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19", "uncoveredLines": "22,23,25"}, "ExpenseManagementHomePageController": {"coverageRate": "0.96", "coveredLines": "2,3,4,14,18,19,20,24,25,26,28,29,30,31,35,36,37,38,41,42,43,44,45", "uncoveredLines": "6"}, "ExpenseRawDataAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,8,9,10,11,14,15", "uncoveredLines": ""}, "ExpenseRawDataAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6", "uncoveredLines": ""}, "ExpenseRawDataBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9", "uncoveredLines": ""}, "ExpenseRawDataBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "uncoveredLines": ""}, "ExpenseRawDataFilter": {"coverageRate": "0.98", "coveredLines": "3,5,7,9,10,11,12,13,14,15,18,20,21,22,23,24,25,26,29,31,32,33,34,36,37,38,45,46,48,49,50,51,53,55,58,59,60,61,62,63,65,66,67,71,72", "uncoveredLines": "42"}, "ExpenseRawDataProcessCheckSchedulable": {"coverageRate": "0.85", "coveredLines": "2,3,4,5,6,14,21,22,23,24,25,28,29,30,31,34,35,36,38,42,43,44,45,46,48,50,51,52,66,67,68,69,72,76,77,78,79,80,84,85,86,87,91,92,96,97,98,99,100,101,104,105,108,109,110,111,113", "uncoveredLines": "39,56,57,58,59,60,62,70,81,88"}, "ExpenseRawDataProcessQueueable": {"coverageRate": "0.93", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,22,23,24,26,27,31,32,36,43,44,45,46,47,50,51,55,56,57,58,59,60,61,62,63,64,65,66,67,68,70,74,75,76,77,78,79,80,81,83,85,88,89,94,95,96,97,98,99,100,108,109,110,112,113,114,115,116,117,118,119,120,121,124,126,129,135,136,137,138,139,140,141,142,143,144,145,147,148,149,150,152,158,159,162,166,169,170,171,172,176,177,178,179,180,181,182,183,185,187,190,195,196,197,198,199,200,201,202,203,204,205,207,208,209,210,211,212,213,214,215,217,220,221,222,224,227,228,229,231", "uncoveredLines": "86,101,102,103,104,153,154,155,156,163,164"}, "ExpenseRawDataProcessSchedulable": {"coverageRate": "0.89", "coveredLines": "2,3,4,10,12,13,14,15,16,17,18,19,21,22,23,25,26,27,31,32,33,43,44,45,46,49,50,53,54,55,58,59,60,62,63,64,65,69,70,71,72,75,79,80,81,82,84,88,89,90,91,92,94,95,98,99,102,104,105,106,110,114,115,116,117,118,120,121,124,125,127,129,130,131,134,137,138,139,140,142,148,149,150,152,153", "uncoveredLines": "36,37,38,39,51,73,96,122,143,144"}, "ExpenseRawDataSelector": {"coverageRate": "0.57", "coveredLines": "2,3,29,31,43,44,45,49,50,51,54,78,79", "uncoveredLines": "35,36,39,59,60,74,82,83,84,87"}, "ExpenseRawDataService": {"coverageRate": "0.95", "coveredLines": "2,3,4,5,7,9,11,12,13,15,18,19,28,29,30,33,34,36,37,40,41,42,46,47,48,49,51,52,54,56,68,71,72,83,90,91,92,93,94,95,100,101,102,104,107,114,115,116,117,118,123,124,125,126,128,129,130,133,136,137,138,139,142,145,146,147,148,149,150,151,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,189,190,191,192,195,198,199,200,201,202,203,205,206,209,212,213,214,215,217,218,220,221,223,226,227,228,229,230,233,234,237,238,239,240,241,242,243,244,245,246,247,249,250,251,252,254,255,258,259,263,264,265,268,269,270,271,272,273,276,277,278,279,280,282,283,284,285,286,287,288,291,293,294,297,299,300,301,302,303,304,305,306,309,310,311,312,313,314,318,319,320,321,322,323,324,325,326,327,328,329,330,331,333,334,335,336,338,339,340,341,343,344,345,346,348,349,350,351,353,354,355,356,357,358,360", "uncoveredLines": "22,23,24,59,62,65,75,76,79,80,103,106,108"}, "ExpenseRawDataTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ExpenseReportAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "ExpenseReportAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "ExpenseReportAutoShare": {"coverageRate": "1.00", "coveredLines": "9,10,11,12,13,14,15,19,26,27,28,29,30,33,34,35,36,37,41,42,43,44,45,49,50,51,52,53,54,55,56,57,58,63,68,69,70,71,72,74,76,77,79,80,81,82,83,85,87,88,89,90,92,95,96,99,102,103,104,105,107,111,112,114,115,116,118,119,121,122,124,125,129,130", "uncoveredLines": ""}, "ExpenseReportBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,8,9,10,11,12,15,16,17,18,19,21,22", "uncoveredLines": ""}, "ExpenseReportBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ExpenseReportBigShareRevokeBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ExpenseReportClearScheduleJob": {"coverageRate": "0.64", "coveredLines": "2,11,12,13,14,17,18", "uncoveredLines": "4,5,6,7"}, "ExpenseReportSelector": {"coverageRate": "0.58", "coveredLines": "2,3,4,7,12,13,15,19,20,21,24,25,26,27,29,32,33,34,37,47,48,78,79,80,86,108,109,110", "uncoveredLines": "41,42,43,51,52,53,54,57,58,61,63,69,70,73,91,92,93,101,103,112"}, "ExpenseReportService": {"coverageRate": "0.88", "coveredLines": "2,3,4,5,6,7,8,9,11,15,16,19,20,21,22,23,24,25,26,29,32,33,34,36,37,38,39,41,42,45,48,51,52,53,54,55,58,59,61,63,65,66,68,70,73,74,75,76,77,79,80,81,82,84,85,86,89,92,93,94,95,96,97,98,99,100,101,103,108,109,110,111,116,117,118,119,124,125,126,127,128,129,130,131,134,135,140,141,145,146,147,148,149,153,154,155,156,157,160,161,167,168,169,170,171,173,174,177,178,179,180,181,184,188,189,190,191,192,193,194,196,197,200,201,202,203,204,205,208,212,213,214,215,216,217,218,220,221,224,225,226,227,228,229,232,236,237,238,239,240,243,244,245,246,247,248,251,255,256,257,258,259,260,261,262,263,265,266,267,268,269,270,271,272,273,275,276,277,278,279,280,281,282,284,288,289,290,291,292,293,294,295,298,299,300,301,302,304,307,308,309,310,311,312,315,316,317,318,319,320,321,322,323,327,349,350,355,356,357,358,361,364,369,370,371,373,374,376,377,379,380,381,382,405,406,407,408,409,410,417,418,419", "uncoveredLines": "175,198,222,241,305,328,329,330,331,332,333,334,335,339,340,341,342,343,346,359,383,384,385,386,387,388,389,390,391,392,393,394"}, "ExpenseReportShareBatchableJob": {"coverageRate": "0.87", "coveredLines": "2,8,10,11,12,13,14,15,18,19,21,22,24,29,30,31,32,33,34,35,39,40,44,47,48,51,52", "uncoveredLines": "25,26,42,53"}, "ExpenseReportShareRevokeBatchableJob": {"coverageRate": "0.69", "coveredLines": "2,3,4,15,17,24,25,26,27,28,32,35,36,37,38,39,40,42,43,45,47,48,52,53,57,58,61,62,65,71,75,76,77,78", "uncoveredLines": "29,30,49,50,63,66,67,68,80,81,83,84,85,86,89"}, "ExpenseReportShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,12,13,17,18,21,22,23,24,27,28,29,30,33,34,35,36,39", "uncoveredLines": ""}, "ExpenseReportTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ExpenseSelector": {"coverageRate": "0.54", "coveredLines": "2,3,6,7,8,21,63,64,65,68,69,70,109,110,123,124,125,128,134,135,139,140,141,147,148,156,157,158,164,165,166,169", "uncoveredLines": "26,27,28,30,31,52,58,74,75,78,79,91,94,97,98,99,103,104,105,106,113,114,115,117,118,136,160"}, "ExpenseService": {"coverageRate": "0.98", "coveredLines": "2,4,14,15,16,17,18,19,20,21,22,24,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,68,69,70,71,72,73,74,75,76,77,78,79,82,83,84,85,86,87,88,89,90,91,92,93,95,97,98,99,100,101,102,103,104,105,106,110,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,138,139,141,142,143,144,145,146,149,151,152,153,157,158,159,160,161,162,163,167,172,173,174,175,176,177,178,180,181,186,187,188,192,193,195,196,197,199,200,201,202,203,204,205,209,210,214,215,217,218,219,223,224,228,229,230,231,232,233,234,238,239,240,241,242,244,249,250,251,252,256,257,258,259,260,261,263,264,265,266,267,268,269,270,271,272,273,274,276,278,279,280,281,282,285,288,297,298,300,303,304,305,306,309,310,311,312,314,315,319,322,323,324,325,328,329,330,331,334,340,341,342,343,344,346,347,348,349,352,353,354,355,357,358,359,361,363,366,367,368,369,372,373,374,375,377,378,380,383,384,385,386,388,389,391,394,395,396,397,398,399,400,401,403,404,405,406,409,410,411,412,414,415,416,417,418,419,421,422,423,426,427,428,429,433,434,436,437,439,441,443,447,454,456,457,458,459,462,463,464,465,468,470,474,475,476,477,478,479,480,482,483,484,488,489,490,491,493,494,495,497,500,501,502,504,505,507,508,511,512,513,514,515,516,517,518,519,522,523,528,529,530,531,534,535,536,537,538,543,544,545,546", "uncoveredLines": "164,168,313,317,335,336,337,485,520"}, "ExpenseShareBatchableJob": {"coverageRate": "0.88", "coveredLines": "2,9,11,12,13,14,15,16,19,20,22,23,25,30,31,32,33,34,35,36,40,41,45,48,49,52,53,56", "uncoveredLines": "26,27,43,54"}, "ExpenseShareRevokeBatchableJob": {"coverageRate": "0.67", "coveredLines": "2,3,4,15,17,24,25,26,27,28,32,35,36,37,38,39,41,42,44,48,49,53,54,57,58,61,67,71,72,73,74", "uncoveredLines": "29,30,45,46,59,62,63,64,76,77,79,80,81,82,84"}, "ExpenseShareSelector": {"coverageRate": "0.95", "coveredLines": "2,3,8,9,12,13,17,18,19,20,22,23,24,25,29,30,31,32,35,38,39", "uncoveredLines": "4"}, "ExpenseTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ExpenseTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,7,8,10,11,12,13,14,19,20,21,22,23,28,29,30,31,32,37,38,39,40,41,46,47,48,49,50,55,56,57,58,59,64,65,66,67,68", "uncoveredLines": "16,25,34,43,52,61,70"}, "Expense_Raw_DataTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,20,25,26,27,28,29,34,35,36,37,38", "uncoveredLines": "13,22,31,40"}, "Expense_ReportTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,20,25,26,27,28,29,34,35,36,37,38", "uncoveredLines": "13,22,31,40"}, "ExpensesUnapprovedCountingScheduleJob": {"coverageRate": "0.73", "coveredLines": "2,3,4,5,11,12,13,21,22,23,24", "uncoveredLines": "7,16,17,18"}, "ExpensifyIntegrationService": {"coverageRate": "0.85", "coveredLines": "2,3,4,5,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,33,34,35,36,37,38,41,42,43,46,47,48,49,51,56,57,58,60,61,62,64,65,66,67,68,69,70,71,72,73,74,78,79,80,83,84,85,86,87,88,95,96,99,100,101,102,104,105,123,124,125,126,128,129,130,133,134,135,136,138,142,143,146,147,148,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,172,173,179,180,181,182,183,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204", "uncoveredLines": "75,89,90,91,92,97,106,109,110,111,112,113,114,115,116,117,118,131,139,140,206,207,208"}, "ExpensifySyncHistorySelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "FFCurrencyERAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "FFCurrencyERService": {"coverageRate": "0.83", "coveredLines": "2,5,6,11,12", "uncoveredLines": "13"}, "FFCurrencyExchangeRateTrigger": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16", "uncoveredLines": ""}, "FFCurrencyExchangeRateTriggerDispatcher": {"coverageRate": "0.88", "coveredLines": "2,4,7,8,9,10,11", "uncoveredLines": "13"}, "FF_Expense_Report_Update_Schedule": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,8,11,12,13,22,23,24,25,27,28,29,30,31,32,33,34,35,37,39,52,53,54,55,56,57,58,59,60,63,64,65,66,68,72,75,76,77,78,79,85,86,87,89,90,91,93,94,95,96,97,99,100,102,103,104,105,106,107,108,111,112,114,115", "uncoveredLines": "16,17,18,19,40,45,46,47,48,49,69"}, "FF_Missed_Card_Prev_Week": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FF_Missed_TimeSheet_Schedulable": {"coverageRate": "0.51", "coveredLines": "2,10,11,12,13,18,40,41,42,43,44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,61,62,63,64,65,68,69,70,71,76", "uncoveredLines": "20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,79,80,81,82,85,86,87,88,89,91,93,94,96,97,98,99,100,102"}, "FF_Missed_Time_Utils": {"coverageRate": "0.84", "coveredLines": "2,6,9,10,11,12,14,16,17,18,19,20,24,25,26,27,28,31,32,33,34,35,37,38,39,42,45,46,47,48,49,50,51,52,53,56,74,75,76,79,80,81,84,85,87,90,94,95,96,97,98,99,100,101,103,104,105,106,107,109,110,111,112,113,114,115,116,120,121,124,127,129,137,138,139,140,141,143,144,148,151,152,153,154,155,156,157,160,161,162,163,167,172,173,174,175,176,178,182,183,184,185,187,188,189,190,195,196,197,198", "uncoveredLines": "59,60,61,62,63,64,65,66,67,68,71,88,118,122,130,131,132,133,142,145,146"}, "FF_ResourceOfficeRate": {"coverageRate": "0.87", "coveredLines": "6,7,8,9,10,11,12,13,14,15,18,19,20,21,23,24,25,28,29,30,32,33,36,37,41,42,43,44,45,46,47,48,49,50,54,55,57,65,66,68", "uncoveredLines": "58,59,60,61,62,67"}, "FailedEnrichedInfoDto": {"coverageRate": "1.00", "coveredLines": "2,4,6,8,9,10,11", "uncoveredLines": ""}, "FieldDefinitionSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "FieldUpdateEventService": {"coverageRate": "0.93", "coveredLines": "2,3,4,6,9,10,11,14,15,16,17,20,21,22,23,25,26,28,33,34,35,37,38,39,40,41,42,47,48,51,52,55,56,58,59,65,66,67,69,72,82,83,86,87,88,89,91,93,96,97,98,99,100,101,104,105,106,107,108,109,110,111,114,117,118,119,122,123,124,126,127,128,129,133,134,135,136", "uncoveredLines": "44,53,60,61,62,63"}, "FieldUpdateEventTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "FillDateTimecardEnteredScheduleJob": {"coverageRate": "1.00", "coveredLines": "2,4,5,7,8,11,12,13,15,16,17,18,19,23", "uncoveredLines": ""}, "FinanceInvoicingEventService": {"coverageRate": "0.96", "coveredLines": "2,4,6,8,10,12,13,16,17,18,21,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,104,105,106,107,108,109,110,111,112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137,138,139,140,141,142,143,146,147,149,150,151,152,153,154,155,156,157,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,182,184,185,186,188,192,195,196,197,198,199,200,202,204,205,208,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,227,228,229,230,233,235,236,237,238,239,242,245,248,249,250,251,252,253,254,255,258,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,278,279,282,285,286,287,288,289,290,291,292,293,296,299,300,302,303,304,306,307,309,311,314,315,316,317,318,319,320,321,322,323,326,327,330,331,332,333,335,338,339,344,345,346,347,350,351,352,354,355,356,359,362,363,365,366,367,368,371,372,373,378,379,380,381,382,383,384,385,392,395,396,397,398,399,402,403,404,405,406,407,408,409,410,412,413,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,433,438,439,440,441,442,443,448,449,451,452,453,454,455,456,458,459,460,461,462,463,466,469,470,471,472,473,474,475,476,478,479,481,483,484,485,486,487,488,489,492,497,498,499,500,501,502,503,505,506,508,509,510,511,513,518,521,522,523,525,526,527,529,530,531,532,533,534,535,536,539,541,544,545,546,547,548,549,550,551,553,554,555,558,561,562,563,564,566,569,572,573,575,576,577,578,579,580,581,582,586,589,592,593,594,596,597,599,600,601,603,604,605,606,610,611,612,613,614,615,616,617,620,623,624,625,626,627,628,629,632,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,655,658,659,662,663,664,665,667,670,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,695,698,701,702,703,704,705,706,709,710,711,712,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,746,749,750,753,754,755,756,757,758,761,762,763,765,768,769,770,771,772,775,778,779,780,783,784,785,786,787,790,793,794,795,796,797,798,799,800,805,806,810,813,814,815,816,818,821,824,825,826,827,828,831,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,854,856,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,877,879,882,883,884,885,886,887,888,889,890,891,892,895,897,900,901,902,903,904,905,907,909,912,913,914,915,916,917,918,919,920,921,922,927,928,931,932,933,941,942,945,946,947,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,972,975,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,999,1002,1005,1006,1007,1008,1009,1010,1011,1015,1018,1019,1020,1021,1022,1023,1034,1035,1042,1043,1046,1047,1052,1053,1054,1056,1057,1058,1059,1060,1061,1064,1067,1068,1069,1070,1071,1072,1073,1074,1075,1082,1083,1084,1085,1086,1087,1088,1089,1090,1094,1095,1100,1101,1102,1103,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1120,1121,1122,1128,1129,1130,1131,1132,1133,1134,1135,1136,1138,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1173,1174,1177,1178,1179,1181,1182,1183,1186,1189,1190,1191,1192,1193,1194,1197,1198,1199,1203,1204,1205,1208,1209,1210,1213,1216,1219,1220,1223,1224,1225,1226,1230,1231,1232,1233,1236,1237,1238,1242,1243,1244,1245,1246,1247,1248,1249,1250,1253,1257,1258,1259,1260,1262,1266,1267,1270,1271,1272,1275,1278,1279,1282,1283,1284,1287,1290,1291,1292,1293,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1307,1310", "uncoveredLines": "158,159,160,161,162,328,340,341,374,387,801,802,803,937,938,1026,1027,1028,1029,1030,1031,1038,1039,1044,1048,1079,1092,1097,1124,1125,1206,1211,1268,1280"}, "FirstlyCreatedProjectInChinaPredicate": {"coverageRate": "0.76", "coveredLines": "2,13,14,15,18,19,20,23,24,25,27,30,31", "uncoveredLines": "6,7,8,9"}, "FixContactAssignedAndLifecyleDateJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FixLeadAssignedAndLifecyleDateJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FixTimecardLogsBatchable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FixedBidCalculateOpportunityBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FixedBidCalculationQueueable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FixedBidCalculationScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "FixedBidCalculationScheduleJobSelector": {"coverageRate": "1.00", "coveredLines": "2,3,5", "uncoveredLines": ""}, "FixedBidCalculationService": {"coverageRate": "0.43", "coveredLines": "2,3,11,12,13,15,20,21,22,23,24,25,27,29,30,31,32,34,35,36,41,43,44,45,47,48,49,53,55,58,59,61,62,63,64,66,69", "uncoveredLines": "37,67,72,73,76,77,78,79,81,84,85,86,89,90,92,93,97,98,99,103,104,105,106,110,111,112,115,117,119,120,121,124,125,127,128,129,131,132,133,135,136,137,138,141,142,143,144,145,147"}, "FixedBidFinalReportReminderScheduleJob": {"coverageRate": "0.83", "coveredLines": "4,22,23,24,25,27,42,43,44,45,46,47,52,53,54,56,57,58,59,61,64,65,66,68,69,70,72,73,74,75,76,77,78,79,83,84,85,89,90,92,93,94,96,97,98,99,102,103,105,108,112,113,114,115,117,120,121,122,125,126,127,128,129,132,135,136,137,138", "uncoveredLines": "13,14,15,16,18,30,32,33,34,37,38,86,106,141"}, "FixedBidFinishedJobSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6,7"}, "FixedBidReportItemSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6,10,11"}, "FixedBidRuningTime": {"coverageRate": "0.94", "coveredLines": "2,3,4,5,6,7,8,9,10,11,18,19,20,23,24,25,28,29,30,31,33,34,38,39,40,41,43,46,47,48,49,53,54,55,60,61,62,63,66,69,70,71,72,73,74,75,76,77,78,79,80,81,87,88,90,91,94,95,96", "uncoveredLines": "13,14,15,84"}, "FixedBidScheduleBuilder": {"coverageRate": "1.00", "coveredLines": "4,5,6,7,8,9,10,11,12,13,15,18,20", "uncoveredLines": ""}, "FixedBidScheduleController": {"coverageRate": "0.86", "coveredLines": "7,10,15,16,17,18,19,20,24,25,30,31,32,33,34,35,37,38,39,42,46,47,49,50,51,52,53,56,57,62,64,67,68,69,72,73,77,78,79,80,81,88,92,93,94,95,96,105,114,115,116,117,118,119,120,121,125,128,129,130,131,136,137,140,144,146,154,159,160,161,165,166,167,168,169,172,175,176,177", "uncoveredLines": "13,58,59,74,75,82,83,84,97,98,100,106,133"}, "FixedBidScheduleRedirectController": {"coverageRate": "0.89", "coveredLines": "5,6,10,14,15,16,17,18", "uncoveredLines": "8"}, "FlatExpenseAutomation": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "5,7,8,11,13,15,16,17,21,22,23,24,25,26,27,28,29,33"}, "FocusAccountProcessQueueable": {"coverageRate": "0.86", "coveredLines": "2,5,6,9,10,13,14,15,16,22,23,24", "uncoveredLines": "17,18"}, "ForecastingQuotaController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ForecastingQuotaSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6,11,12,13,14,16,17,19,22,23"}, "ForecastingQuotaService": {"coverageRate": "0.71", "coveredLines": "2,7,8,11,12,13,14,15,17,19,20,23", "uncoveredLines": "4,27,28,31,32"}, "FutureMethodShouldRun": {"coverageRate": "1.00", "coveredLines": "2,4,5,8,9", "uncoveredLines": ""}, "GDSController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GDSRequestSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,10,11", "uncoveredLines": ""}, "GDSService": {"coverageRate": "0.81", "coveredLines": "2,3,4,6,7,8,9,10,12,15,16,17,18,19,22,23,24,26,27,28,29,30,32,33,34,35,36,39,40,41,42,43,46,98,99,100,102,103,105,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126,127,128,129,130,131,132,133,134,135,136,143,146,147,148,149,150,153,154,156,157,158,161,162,163,164,165,168,169,171,172,173,176,177,178,179,182,185,186,187,188,191,195,196,197,198,200,201,202,203,204,206,208,209,210,211,212,213,214,215,216,217,218,222,223,224,225,228,232,233,234,235,237,238,239,240,241,243,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,267,268,269,270,274", "uncoveredLines": "47,48,49,53,54,55,56,57,58,59,60,62,65,66,68,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,94,138,139,140"}, "GLMappingHelper": {"coverageRate": "0.99", "coveredLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,51,53,54,55,57,61,62,65,66,67,68,69,70,72,74,75,76,77,81,82,85,86,89,90,93,94,97,98,101,102,103,104,105,107,108,109,110,111,112,113,114,115,116,117,118,120,122,123,124,125,126,127,128,133,134,136,137,138,140,142,143,146,147,148,149,151,152,153,154,155,156,157,159,160,162,163,164,165,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,209,210,211,212,213,214,215,219,220,221,222,226,229,230,231,232,233,234,235,236,237,238,241,242,243,245", "uncoveredLines": "130"}, "GLMappingSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "GenerateEVAByAssignmentScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GenerateEVAByAssignmentService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GenerateReportItemBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GenerateReportItems": {"coverageRate": "0.97", "coveredLines": "2,3,4,5,6,11,12,13,16,17,18,19,20,22,23,24,25,28,30,31,34,36,37,39,40,42,43,45,46,47,48,49,50,53,55,56,57,58,59,60,62,63,64,66,67,68,69,72,73,74,75,76,77,78,79,80,82,84,85,86,87,88,89,90,91,92,95,97,100,101,103,104,105,106,107,108,109,110,112,115,116,117,118,119,120,122,123,125,128,129,131,132,133,134,137,138,139,141,142,143,146,149,150,151,152,153,154,157,160,161,162,163,164,165,166,168,169,170,173,176,177,178,179,182,183,186,187,188,189,192,193,196,197,200,201,202,203,204,208,211,220,221,223,224,225,226,227,228,229,230,231,234,235,236,237,238,239,241,243,244,246,247,248,249,253,255,258,259,260,263,264,265,266,268,270,271,273,274,275,276,280,282,285,288,297,298,300,301,302,303,305,306,307,308,309,310,311,312,315,316,317,318,319,320,321,322,324,325,326,327,330,331,332,333,334,337,338,339,340,341,345,346,347,348,351,352,355,356,357,360,361,364,365,366,367,368,369,371,372,373,374,377,378,379,380,381,384,385,386,387,388,392,393,394,395,398,399,402,403,404,407", "uncoveredLines": "180,190,194,206,261,358,362"}, "GenerateReportItemsSelector": {"coverageRate": "1.00", "coveredLines": "2,3,14,18,19,22,23,26,27,30,31", "uncoveredLines": ""}, "GenerateShareEntity": {"coverageRate": "1.00", "coveredLines": "14,15,16,17,18,19,22,23,24,25,26,27,30,31,32,33,34,35,38,39,40,41,42,43,46,47,48,49,50,51,54,55,56,57,58,59,63,64,65,66,67,68,71,72,73,74,75,76,79,80,81,82,83,84,87,88,89,90,91,92,95,96,97,98,99,100,103,104,105,106,107,108,112,113,114,115,116,117,118,121,122,123,124,125,126,127,130,131,132,133,134,135,138,139,140,141,142,143", "uncoveredLines": ""}, "GenerateTimePeriodBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,7,8,9,12,13,14,16,17,18,19,20,21,22,24,25,28,31,32,33,37,38", "uncoveredLines": ""}, "GenerateTotalCostBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GenerateTotalCostItems": {"coverageRate": "0.86", "coveredLines": "2,4,5,9,10,25,27,30,32,34,35,36,39,40,42,43,44,46,47,48,49,50,51,52,53,58,62,63,64,65,67,68,69,70,71,73,78,79,80,81,82,83,84,87,88,89,90,91,98,99,100,101,104,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151,152,153,155,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,223,224,225,226,227,229,230,233,234,235,236,237,240,241,242,243,245", "uncoveredLines": "54,55,56,92,93,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126"}, "GetActivityDataService": {"coverageRate": "0.81", "coveredLines": "11,12,13,14,15,25,26,27,28,29,30,34,35,36,37,38,52,53,54,55,56,57,58,59,60,61,62,63,64,65,67,70,71,72,73,74,75,76,77,78,79,80,82,85,86,88,89,92,95,99,100,101,102,104,105,106,107,108,110,111,113,117,121,122,124,125,128,134,136,137,138,141,142,143,144,145,146,147,148,151,159,167,169,173,174,175,176,180,181,182,183,184,185,187,188,190,191,192,193,195,197,201,202,203,204,205,206,207,211,212,216,217,219,220,221,222,223,224,226,228,231,232,233,234,237,240,241,245,246,247,248,252,255,256,257,258,259,260,261,262,263,264,265,266,269,270,273,277,278,279,280,284,285,286,287,288,290,292,293,294,295,296,299,355,356,357,359,362,363,365,366,367,368,369,370,372,373,374,377,378,379,380,381,383,386,387,388,389,390,391,393,396,397,398,399,400,401,402,403,404,406,407,410,413,414,415,417,420,421,423,424,425,426,429,430,431,432,433,434,435,436,449,450,451,452,453,454,455,458,459,474,475,476,477,480,481,483,486,487,489,492,493,494,495,496,499,502,503,504,505,506,507,508,509,510,511,512,515,519,522,523,524,525,526,527,528,529,530,531,532,533,534,535,538,541,545,546,547,549,550,551,552,553,554,555,556,557,558,559,561,562,563,565,566,569,572,575,576,577,578,579,580,581,582,583,584,585,586,587,590,594,595,596,597,598,600,601,602,603,604,605,606,608,611,612,613,614,615,617,618,620,624,627,628,629,630,631,632,635,638,639,640,641,642,645,646", "uncoveredLines": "41,42,43,44,45,46,47,48,49,90,114,115,129,130,131,139,152,153,154,155,156,160,161,162,163,164,198,235,242,302,303,304,305,306,310,311,312,314,315,316,317,320,321,324,325,328,332,333,334,335,336,340,341,342,343,344,345,347,348,350,352,360,418,439,440,441,442,443,444,445,446,462,463,465,466,467,468,469,471,484"}, "GetDealCoachDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GlobalDealDeskDelegationService": {"coverageRate": "0.90", "coveredLines": "9,10,11,24,25,26,27,29,30,31,33,34,36,37,39,40,44,45,46,47,51,52,53,54,55,59,60,61,62,63,70,71,72,73,75,76,83,84,85,87,88,89,90,91,92,94,95,96,97,98,99,101,103,104,107,110,113,114,115,116,117,118,119,120,121,122,123,125,128,129,130,131,133,137,138,140,141,143,144", "uncoveredLines": "14,15,16,17,19,20,65,74,78"}, "GlobalDealDeskDelegationTrigger": {"coverageRate": "1.00", "coveredLines": "6", "uncoveredLines": ""}, "GlobalDefaultRateController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GlobalDefaultRateSelector": {"coverageRate": "0.40", "coveredLines": "11,12", "uncoveredLines": "2,3,6"}, "GlobalDefaultRateService": {"coverageRate": "0.55", "coveredLines": "4,5,7,8,9,10", "uncoveredLines": "14,15,18,19,20"}, "GlobalDefaultRateTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "GlobalGroup": {"coverageRate": "1.00", "coveredLines": "17,18", "uncoveredLines": ""}, "GlobalProjectTCShareRevokeHandler": {"coverageRate": "0.85", "coveredLines": "5,6,7,10,17,18,19,20,21,22,23,26,27,30,31,32,34,35,36,37,44,45,46,47,48,49,52,53,54,55,56,59,63,64,65,66,67,69,72,73,74,76,77,78,79,80,81,82,83,85,87,93,96,99,100,101,102,103,104,105,108,113,124,126,129,130,131,132,133,136,149,150,151,153", "uncoveredLines": "24,106,114,115,116,117,119,139,140,141,142,143,145"}, "Global_Default_RateTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,14,15,16,17,18", "uncoveredLines": "11,20"}, "GovernLimitsMonitor": {"coverageRate": "0.96", "coveredLines": "2,3,4,5,6,7,8,9,11,12,13,18,19,20,21,22,23,24,26,27,30,31,32,36,37,38,39,40,41,43,45,46,54,55,58,68,69,70,71,74,77,78,79,82,83,84,85,87,93,94,97,98,101,102,105,106,111,112,115,116,119,120,123,124,129,130,133,134,137,138,141,142,147,148,151,152,155,156,159,160,165,166,169,170,173,174,177,178,183,184,187,188,191,192,195,196", "uncoveredLines": "15,48,49,72"}, "GroupAndGroupMemberQueueableJob": {"coverageRate": "1.00", "coveredLines": "5,7,8,9,10,13,14,15,18,19,22,23", "uncoveredLines": ""}, "GroupMemberController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GroupMemberDeletionQueueableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GroupMemberQueueableJob": {"coverageRate": "0.89", "coveredLines": "6,11,12,13,16,17,18,20", "uncoveredLines": "21"}, "GroupMemberSelector": {"coverageRate": "0.73", "coveredLines": "2,4,5,8,9,12,13,14", "uncoveredLines": "18,19,20"}, "GroupMemberService": {"coverageRate": "1.00", "coveredLines": "2,7,8,9,13,14,15,18,19,20,21,22,24,27,28,30,31,32,33,34,35,38", "uncoveredLines": ""}, "GroupMembersInsertBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GroupQueueableJob": {"coverageRate": "1.00", "coveredLines": "2,4,6,7,10,11", "uncoveredLines": ""}, "GroupSelector": {"coverageRate": "0.79", "coveredLines": "2,4,5,8,9,12,13,16,17,18,19,20,21,28,29,32,33,35,36,39,52,53", "uncoveredLines": "22,42,43,45,46,49"}, "GroupService": {"coverageRate": "0.66", "coveredLines": "2,3,4,16,17,18,19,20,21,22,23,24,46,47,49,50,53,57,58,59,61,62,64,69,70,71,72,73,76,77,79,82,83,86,87,91,92,93,94,95,97,98,101,102,104,105,106,107,114,117,118,119,120,121,123,124,127,128,130,131,132,192,193,194,195,198,199,201,202,207,208,209,210,213,214,215,217,218,219,220,221,224,225,230,231,232,233,238,239,240,242,245,246,250,251,253,254,257,261,262,263,265,266,268,269,272,273,277,278,279,280,282,283,286,287,288,289,292,294,297,298,299,302,303,304,305,306,308,309,311,312,316,317,321,322,324,325,326,327,328,333,336,337,338,339,340,342,343,345,348,349,350,351,352,353,356,357,359,360,361,421,422,424,425,430,431,432,433,436,437,440,442,443,444,445,446,447,448,453,456,457,458,461,462,463,466,467,470,471,472,479,482,483,485,486,487,488,490,493,494,495,498,499,500,501,502,505,506,507,508,509,512,513,515,516,518,519,522,545,546,547,549,550,551,552,553,557", "uncoveredLines": "28,37,38,39,40,41,42,43,51,54,65,66,74,108,109,135,136,137,138,139,140,142,145,146,147,148,149,152,153,154,155,156,157,160,161,162,163,164,165,166,167,169,170,171,173,174,175,176,177,178,179,181,182,189,204,227,235,243,255,258,290,313,318,364,365,366,367,368,373,375,376,377,378,379,380,382,383,384,385,387,388,389,390,391,392,394,395,399,400,403,404,406,407,408,409,410,411,418,427,468,473,474,475,525,526,527,528,529,530,533,536,537,538,539,542"}, "GuestStayBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,8,9", "uncoveredLines": ""}, "GuestStayBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,8,9", "uncoveredLines": ""}, "GuestStayClearBatchJob": {"coverageRate": "1.00", "coveredLines": "2,4,7,8,11,12,13,14,15,18,21", "uncoveredLines": ""}, "GuestStayController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "GuestStayNotificationJob": {"coverageRate": "0.62", "coveredLines": "2,3,9,10,19,20,21,22", "uncoveredLines": "5,13,14,15,16"}, "GuestStaySelector": {"coverageRate": "0.62", "coveredLines": "27,28,29,69,79,80,81,130,137,138,145,146,147", "uncoveredLines": "2,3,4,151,152,153,159,160"}, "GuestStayService": {"coverageRate": "1.00", "coveredLines": "2,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,28,29,30,31,32,33,34,35,39,40,41,42,43,44,52,53,54,55,59,60,61,62,64,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,90,91,93,94,97,98,100,102,103,105,109,110,111,112,114,115,116,118,119,120,121,122,123,124,125,132,134,137,138,139,140,141,142,143,145,147,148,149,150,151,153,156,157,158,159,161,162,165,166,167,168,169,170,171,172,173,174,176,177,181,182,183,184,185,186,190,191,193,194,195,196,201,202,206,213,214,215,216,217,218,219,220,221,230,232,233,234,235,236,237,238,239,240,241,242,243,244,246,247,248,249,251,252,253,254,256,257,258,259,261,262,264,265,267,268,269,270,271,277,278,280,281,283,284,286,287,289,290,292,293,296,297,298,299,300,302,306,307,308,309,310,311,312,313,314,315,317,318,319,320,322,323,330,331,332,333,334,336,337,340,341,344,345,347,350,351,352,353,355,356,357,360,361,363,364,365,366,368,369,370,373,374,377,380,381,382,383,384,385,386,387,388,389,390,392,396,397,398,399,400,403,404,405,406,407,408,409,412,413,417,418,419,420,421,422,431,432,433,434,435,436,437,439,440,441,442,443,445,446,448,449,451,452,456,457,458,459,460,461,462,464,465,466,467", "uncoveredLines": "469"}, "GuestStayTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "GuestStayValidationJob": {"coverageRate": "0.50", "coveredLines": "2,8,9,18,19", "uncoveredLines": "4,12,13,14,15"}, "Guest_StayTriggerDispatcher": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,6,7,8,10,11,12,13,14,19,20,21,22,23", "uncoveredLines": "16,25"}, "HEMAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "HEMBeforeDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "HEMTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "HEMTriggerDispatcher": {"coverageRate": "0.50", "coveredLines": "2,3,5,6,7,8,9", "uncoveredLines": "11,15,16,17,18,19,21"}, "HistoryDTO": {"coverageRate": "1.00", "coveredLines": "13,14,15,16,17,18", "uncoveredLines": ""}, "HistoryWrapper": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "HistoryWrapperService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "HolisticEngagementModelSelector": {"coverageRate": "0.67", "coveredLines": "6,7,10,11", "uncoveredLines": "2,3"}, "HolisticEngagementSelector": {"coverageRate": "0.41", "coveredLines": "2,3,4,8,9,10,14,15,18,19,23,26,27,28,34,35,36,39,42,43,50,51,54,55,56,60,61,64", "uncoveredLines": "20,31,46,47,68,69,70,73,74,77,78,79,80,81,82,83,84,85,86,88,89,91,92,93,94,95,96,99,100,101,103,106,107,109,112,113,114,119,120,121"}, "HolisticEngagementService": {"coverageRate": "0.95", "coveredLines": "2,3,5,8,9,10,13,14,16,17,18,19,20,21,22,24,25,26,27,28,29,30,31,32,37,40,41,43,44,47,48,51,52,53,54,55,56,58,62,63,66,67,68,70,71,72,73,76,77,81,82,83,84,85,89,90,91,92,93,95,98,99,101,102,105,106,107,110,111,112,114,115,116,118,119,120,122,124,127,128,129,133,134,135,137,138,144,145,148,149,150,153", "uncoveredLines": "130,136,139,140,146"}, "HomePageFeedbackDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "HomepageHoursToSubmitController": {"coverageRate": "0.95", "coveredLines": "2,3,6,9,10,11,15,16,17,18,19,20,21,22,23,24,25,26,27,31,34,35,36,37,38,41,42,43,44,46,49,52,53,54,55,56,74,76", "uncoveredLines": "29,39"}, "HomepageInvoicesToApproveController": {"coverageRate": "0.78", "coveredLines": "2,4,5,10,11,12,16,17,31,34,37,38,44,45,46,47,50,51,56,58,59,60,61,62,63,64,75,76,77,78,79,82,84,86,87,89,90,91,92,93,94,95,99", "uncoveredLines": "6,35,39,40,48,52,66,68,69,70,96,97"}, "HomepageNotificationController": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,10,11,12,13,15,19,20,21,23,27,28,29,31", "uncoveredLines": ""}, "HomepageShortcutsController": {"coverageRate": "0.95", "coveredLines": "3,14,15,16,17,20,21,24,25,27,28,29,31,32,34,35,38,40,43,44,45,46,47,48,49,50,51,53,57,58,59,60,61,63,64,65,70,71,72,73", "uncoveredLines": "9,10"}, "HomepageTaskListController": {"coverageRate": "1.00", "coveredLines": "2,3,5,9,10,11,13,18,19,24,25,30,31,36,37", "uncoveredLines": ""}, "HourlySyncExpensifyPoliciesSchedulable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "HoursToDaysRuleSelector": {"coverageRate": "1.00", "coveredLines": "2,5,6,9,10,11", "uncoveredLines": ""}, "InactiveOwnerTransferAssignmentSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "InactiveOwnerTransferDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "InfluenceAttributionDetailSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "InfluenceAttributionValueScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "InitProjectInfoService": {"coverageRate": "0.97", "coveredLines": "6,7,8,9,20,21,22,23,24,27,28,29,32,33,34,36,39,40,41,43,46,47,48,50,53,54,55,57,60,61,63,64,65,68,71,72,74,75,76,79,82,83,85,86,87,90,93,94,96,97,98,101,104,105,106,107,108,109,111,114,115,116,117,119,120,122,123,126,129,130,131,133,134,137,140,141,142,145,146,147,148,149,152,155,156,157,158,159,160,161,163,164,165,166,167,168,169,173,176,177,178,179,180,185,188", "uncoveredLines": "181,182,183"}, "InsertOppHistoryBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "InsertOpportunityPermissionAuditLog": {"coverageRate": "0.46", "coveredLines": "41,42,47,48,49,52,56,57,61,62,63,64,65,69,70,71,72,73", "uncoveredLines": "2,3,5,6,7,15,16,17,18,19,20,22,23,24,25,26,27,29,30,33,36"}, "InterestingMoment": {"coverageRate": "0.53", "coveredLines": "3,5,7,9,10,11,12,13,14", "uncoveredLines": "16,17,18,19,20,21,22,23"}, "InviteUserToExpensifyScheduledJob": {"coverageRate": "0.57", "coveredLines": "2,3,4,5,6,21,22,23,24,25,27,29,30,32,33,34,40,43,44,45,46,47,49", "uncoveredLines": "9,10,11,12,15,16,17,36,41,53,54,55,56,57,58,59,61"}, "InvoiceDownloadController": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,10,13,14,17,18,19,20,21,22,23,24,25,26,30,31,32,41,42,44,45,46,47,48,50", "uncoveredLines": ""}, "InvoiceOverDueDTO": {"coverageRate": "1.00", "coveredLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "uncoveredLines": ""}, "InvoiceReviewController": {"coverageRate": "0.80", "coveredLines": "4,5,6,7,8,9,10,11,13,15,17,18,20,39,41,46,47,48,49,52,53,56,58,59,60,61,64,67,71,72,73,79,90,91,92,93,94,96,97,101,103,106,107,108,109,114,115,116,118,119,120,121,123,124,129,130,132,133,134,137,138,139,140,142,149,150,153,156,157,161,162,163,167,168,169,170,171,172,176,177,178,179,183,184,185,187,189,190,199,200,202,203,204,205,209,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,227,229,231,233,234,235,236,237,238,239,241,244,248,249,250,251,252,255,258,259,260,273,279,280,281,284,290,291,293,294,295,296,297,300,301,302,305,306,307,308,309,310,311,315,316,317,318,319,320,321,322,324,325,327,328,330,331,333,334,336,337,339,340,345,346,347,348,349,350,352,353,356,360,361,364,365,368,369,375,380,381,382,383,384,385,386,394,395,396,398,399,400,412,427,428,429,430,431,432,433,434,435,436,440,441,442,459,462,463,465,466,467,468,471,472,473,474,475,476,477,480,481,482,485,486,487,489,494,498,499,500,501,502,505,509,510,511,512,516,519,526,528,529,530,533,534,535,538,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,560,561,562,563,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,613,614,615,616,617,618,619,620,621,622,623,624,625,626,628,629,632,633,634,635,637,638,639,641,642,643,645,646,647,649,650,651,653,654,655,657,658,659,661,662,663,665,666,667,669,670,671,673,674,675,677,680,681,682,683,684,685,686,687,688,689,690,691,692,697,698,699", "uncoveredLines": "22,23,28,29,34,35,62,84,85,86,98,99,104,125,126,143,144,145,146,151,354,366,387,388,389,401,402,403,404,407,408,415,416,417,418,419,420,421,424,443,444,446,447,448,449,451,452,453,483,513,566,567,569,570,572,573,576,577,580,581,703,704,705,706,707,710,711,712,713,714,717,718,719,720,721,724,725,726,728,730,731,732,735,736,737,739,742,743,744,746,749,750,751"}, "JSONComparator": {"coverageRate": "0.95", "coveredLines": "6,7,8,9,10,11,13,17,18,19,21,22,23,25,26,27,28,31,34,35,36,38,39,40,43,46,47,48,50,53,54,55,56,57,58,59,60,61,62", "uncoveredLines": "51,64"}, "LeadAddToAccountController": {"coverageRate": "0.96", "coveredLines": "2,3,4,5,6,10,11,12,13,17,18,20,21,22,25,27,28,29,30,31,33,38,39,41,42,44,46,47,49,53,54,55,56,60,61,62,65,66,68,70,71,72,73,74,77,81,82,84,85,87", "uncoveredLines": "14,57"}, "LeadAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4", "uncoveredLines": ""}, "LeadAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "4,5,7,8,9,12,13,14,15,16,17,18,19", "uncoveredLines": ""}, "LeadAfterUpdateTriggerHandler": {"coverageRate": "0.92", "coveredLines": "2,3,6,7,8,9,11,12,13,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "uncoveredLines": "17,18"}, "LeadBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,10,12,13,14,15,16,17,18,19,20,21,22", "uncoveredLines": ""}, "LeadBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,10,11,12,13,14,15,16,17,18,19,20,21,24,25,26,27,31,32,33", "uncoveredLines": ""}, "LeadBeforeUpdateTriggerHandler": {"coverageRate": "0.92", "coveredLines": "3,4,6,7,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,46,47,48", "uncoveredLines": "11,12,49"}, "LeadContactAssignmentController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LeadContactAssignmentService": {"coverageRate": "0.99", "coveredLines": "3,6,9,11,12,13,16,17,18,19,20,21,23,26,27,29,32,33,34,37,38,39,40,43,44,45,48,49,50,52,53,54,55,56,61,62,65,67,68,69,71,72,76,77,78,80,81,85,88,89,93,94,97,98,99,101,103,104,106,110,111,114,116,118,120,122,125,126,127,132", "uncoveredLines": "90"}, "LeadContactAutoRecycledBatchJob": {"coverageRate": "0.97", "coveredLines": "2,3,4,5,6,8,9,10,11,12,15,16,17,19,20,21,23,27,28,30,32,36,37,38,40,41,42,44,46,47,48,52,53,54,55,59,60,62,63,66,69,70,71,73,74,76,77,78,80,81,83,84,85,86,87,88,92,93,94,95,98,101,102,105,106", "uncoveredLines": "29,33"}, "LeadContactAutoRecycledScheduleJob": {"coverageRate": "0.67", "coveredLines": "2,3,9,10", "uncoveredLines": "5,6"}, "LeadContactStatusSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,11,12,13", "uncoveredLines": ""}, "LeadContactStatusService": {"coverageRate": "0.97", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,14,15,16,17,18,19,20,21,22,23,24,25,28,36,37,38,39,41,42,43,44,45,46,48,49,50,53,54,55,56,57,58,59,60,61,62,63,66,69,70,71,72,73,74,76,78,79,80,81,82,84,85,87,88,89,90,94,95,97,98,102,103,104,105,107,110,111,112,114,115,116,117,118,119,120,121,123,124,125,128,130,131,132,133,135,138,140,142,144,145,147,148,150,151,152,153,157,160,161,162,163,164,165,167,170,171,172,173,174,175,176,177,178,179,182,183,184,185,186,187,188,189,190,195,198,201,202,203", "uncoveredLines": "31,32,33,136,192"}, "LeadConvertContext": {"coverageRate": "0.94", "coveredLines": "2,3,4,8,9,10,11,12,17,18,21,22,25,26,29,30", "uncoveredLines": "5"}, "LeadConvertController": {"coverageRate": "0.88", "coveredLines": "2,3,4,17,18,20,21,22,23,24,25,26,27,29,30,31,32,33,34,35,38,42,43,45,46,48,49,51,52,59,60,61,62,64,66,67,68,72,73,74,75,76,78,80,81,82,83,88,89,90,91,97,98,99,100,114", "uncoveredLines": "7,8,9,10,11,13,93,102"}, "LeadConvertResource": {"coverageRate": "1.00", "coveredLines": "3,4,11,12,16,17,18,20,22,23,25,26,28,29,30,31,32", "uncoveredLines": ""}, "LeadHVOOrAQLToMQLBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LeadHistorySelector": {"coverageRate": "0.18", "coveredLines": "2,3", "uncoveredLines": "6,7,8,9,11,12,15,16,19"}, "LeadHistoryService": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8", "uncoveredLines": ""}, "LeadMQLToSALBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LeadSalesFollowupToHandoverNotesJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LeadSelector": {"coverageRate": "0.46", "coveredLines": "2,3,6,7,10,11,12,29,34,35,38,39,42,46,47,50,54,55,75,76,127,128,131,132,133,146,147,152,154,160,161,201,213,214,217,220,228,229,232", "uncoveredLines": "58,59,62,66,67,68,69,71,72,79,90,91,92,94,95,97,98,100,101,103,104,106,107,109,110,112,115,116,119,120,123,137,138,139,140,141,205,206,209,210,224,225,230,235,236,253"}, "LeadService": {"coverageRate": "0.95", "coveredLines": "2,7,9,10,11,12,13,14,15,16,17,18,19,20,21,24,25,26,27,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,48,51,65,66,67,68,69,70,71,72,73,74,75,76,79,80,81,82,84,85,86,91,92,93,96,97,102,103,104,107,108,113,114,115,116,118,119,120,121,129,130,131,132,134,135,143,144,145,149,150,151,152,153,154,155,157,167,168,171,172,174,176,177,178,179,182,183,185,186,187,188,189,190,191,192,193,195,196,202,203,204,205,206,207,208,210,211,212,213,214,218,219,220,221,222,223,224,227,229,233,234,235,236,237,238,240,241,246,249,250,253,254,257,258,260,261,262,263,266,267,268,270,271,272,273,274,280,281,284,285,287,288,289,290,295,296,299,300,302,303,304,305,306,308,309,315,316,317,318,319,320,321,322,323,324,325,326,327,328,331,334,335,336,337,338,340,346,347,348,349,354,355,357,359,361,362,363,364,365,366,368,369,370,371,372,374,377,380,383,384,385,387,388,389,393,396,397,398,399,400,402,403,404,405,409,410,411,412,413,416,419,420,421,422,423,424,425,426,427,432,433,434,435,441,442,445,446,447,448,449,450,455,456,457,458,459,461,462,464,469,470,471,472,473,475,476,482,483,484,486,487,488,490,491,492,494,498,499,500,502,503,504,507,511,512,513,515,520,521,522,526,527,532,533,534,536,537,540,544,545,546,547,548,549,550,551,552,555,556,560,561,563,564,565,566,567,568,569,570,574,575,576,577,582,583,584,585,586,588,589,590,591,592,593,594,595,600,601,602,604,605,606,608,609,610,611,612,613,614,616,617,618,620,621,622,624,626,629,630,632,633,636,637,638,640,641,642,645,646,647,649,650,653,654,659,660,661,662,663,664,666,671,672,673,674,675,676,678,679,682,683,684,685,688,689,690,693,694,695,698,699,700,701,703,704,706,711,712,713,716,717,718,719,722,723,726,727,728,729,730,733,734,735,737,743,744,745,746,747,751,752,753,754,758,759,760,762,763,764,765,767,768,769,770,771,773,774,775,777,778,779,780,781,782,784,786,787,788,794,795,796,797,798,799,800,802,803,804,805,809,810,812,813,816,817,818,819,820,821,824,826,827,829,830,831,833,838,839,840,842,843,844,845,846,851,852,853,854,855,858,859,861,862,865,866,869,870,871,872,875,877,878,879,880,881,882,886,887,888,889,890,892,896,901,902,903,905,906,907,908,909,914,915,916,917,918,919,920,923,924,926,929,930,931,932,935,936,937,938,939,940,941,942,943,944,947,948,951,952,953,954,959,960,961,962,963,964,965,966,967,970,973,974,975,976,977,978,979,980,981,982,984,987,990,991,992,993,994,996,1001,1002,1003,1004,1005,1008,1009,1011,1012,1018,1019,1020,1021,1022,1023,1026,1027,1028,1032,1033", "uncoveredLines": "94,105,123,124,125,159,160,161,169,225,243,255,282,297,436,443,516,517,523,528,529,738,814,822,848,863,883,897,911,1006"}, "LeadTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "LeadTriggerDispatcher": {"coverageRate": "0.88", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,18,19,20,21,22,27,28,29,30,31,33,36,37,38,39,40,45,46,47,48,49,54,55,56,57,58", "uncoveredLines": "15,24,42,51,60"}, "LeadWithEngagementsDTO": {"coverageRate": "0.75", "coveredLines": "11,12,13", "uncoveredLines": "8"}, "LeaveBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "LeaveBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "LeaveSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,10,11,12,13,15,16,17,18,20,24", "uncoveredLines": ""}, "LeaveService": {"coverageRate": "0.97", "coveredLines": "2,4,5,6,7,8,10,14,15,20,21,22,23,24,28,29,30,31,32,33,35,41,42,44,45,46,47,48,49,50,53,54,55,60,61,62,63,66", "uncoveredLines": "11"}, "LeaveTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "LeaveTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,14,15,16,17,18", "uncoveredLines": "11,20"}, "LeaveTypeSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7", "uncoveredLines": ""}, "LicenseAssignmentCacheEventTrigger": {"coverageRate": "0.79", "coveredLines": "2,4,5,6,8,9,10,11,19,21,22", "uncoveredLines": "12,13,15"}, "LicenseAssignmentCacheSelector": {"coverageRate": "0.50", "coveredLines": "2,3", "uncoveredLines": "6,7"}, "LightningCustomSearchController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LightningMTController": {"coverageRate": "0.28", "coveredLines": "42,43,44,45,47,51,52,53,55", "uncoveredLines": "3,4,5,6,9,12,15,18,19,20,21,22,23,24,27,28,29,30,32,33,34,36,38"}, "LightningMissingTimecardsController": {"coverageRate": "0.94", "coveredLines": "3,5,8,9,10,13,17,18,22,23,24,25,27,28,29,30,32,33,34,35,36,44,45,46,47,48,50,53,54,55,56,57,61,62,63,64,65,66,68,74,76,78,80,82", "uncoveredLines": "37,38,40"}, "LineItemAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "LineItemBeforeDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LogController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "LogService": {"coverageRate": "1.00", "coveredLines": "5,7,10,12,13,17,19,20,24,26,27,31,32,33,34,35,36,37,38,39,40,41,42,43,45,54,55,58,59,60,61,62,68,70,71,73,75,76,77,78,81,82,85,88,89,90,91,92,93,94,95,96,97,98,99,100,102,105", "uncoveredLines": ""}, "Logger": {"coverageRate": "0.82", "coveredLines": "7,8,9,12,13,14,17,18,21,22,29,30,33,34,35,38,39,40,43,44,47,48,49", "uncoveredLines": "25,26,52,53,54"}, "LongAbsenceAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "LongAbsenceAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "LongAbsenceTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "Long_AbsenceTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,15,16,17,18,19", "uncoveredLines": "11,21"}, "MAPersonAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "MAPersonFunnelAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "MAPersonFunnelBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "MAPersonFunnelTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MA_PersonTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "MA_Person_Funnel_TriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,15,16,17,18,19", "uncoveredLines": "11,21"}, "MKTAccountController": {"coverageRate": "0.69", "coveredLines": "2,3,12,13,14,15,16,18,19,20,23", "uncoveredLines": "6,7,8,27,28"}, "MKTContactAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,14,15,16", "uncoveredLines": ""}, "MKTContactAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "uncoveredLines": ""}, "MKTContactBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,12,13,14,15,16,17,18,19,20,21,22,23,24", "uncoveredLines": ""}, "MKTContactBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,9,10,11,12,13,14,15,17,18,19,20,21,22,23", "uncoveredLines": ""}, "MKTContactBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,16,18,19,20,21,23,25,26,27,28,29,30,31,32,33,34,35,36,37", "uncoveredLines": ""}, "MKTContactController": {"coverageRate": "0.51", "coveredLines": "2,3,4,5,6,7,8,36,37,46,47,93,94,95,96,97,98,99,100,102,105,106,107,108,109,110,111,114,115,118,119,120,124,125,126,127,131", "uncoveredLines": "11,12,16,17,21,22,26,27,31,32,41,42,51,52,56,57,58,60,64,65,69,70,71,75,76,77,78,80,84,85,86,87,89,128,129"}, "MKTContactSelector": {"coverageRate": "0.44", "coveredLines": "2,4,5,6,9,14,15,16,19,24,25,26,39,40,41,68,82,83,109,113,114,117,118,119,135,140,141,142,158,167,168,175,176,179,182,183,186,190,191,224,225,226,229,317,318,323,324,330,331,334,335,372,376,377,380,383,384,446,447,448,524,529,530,531,567,572,573", "uncoveredLines": "30,31,32,33,34,73,74,75,76,78,79,163,164,171,172,194,195,198,202,203,204,205,206,207,209,210,211,212,213,215,218,219,220,234,235,236,238,239,241,242,243,246,249,250,253,257,258,261,265,266,269,273,274,277,281,292,293,294,296,297,299,300,302,303,305,306,308,309,311,312,314,378,387,388,389,441,580,581,584,588,589,607,611,612"}, "MKTContactService": {"coverageRate": "0.90", "coveredLines": "2,5,6,8,9,10,11,13,17,18,19,20,23,24,25,26,31,32,34,35,36,41,42,43,44,46,47,48,53,54,55,56,61,62,63,64,69,70,71,72,77,78,79,80,81,83,89,90,91,92,97,98,99,100,101,104,107,108,109,110,111,112,116,117,120,121,122,123,124,127,128,129,130,131,132,135,136,140,141,142,143,144,145,146,147,148,149,150,151,152,153,156,157,158,159,160,161,164,165,166,169,170,171,172,173,174,176,177,182,183,184,185,186,187,188,189,194,197,198,199,200,201,202,203,204,205,206,211,212,213,214,216,217,218,220,221,223,228,229,230,231,237,238,239,240,241,243,244,250,251,252,253,254,255,256,257,259,260,261,262,263,265,271,272,273,274,275,276,277,278,280,283,286,287,288,290,291,292,293,294,295,296,297,298,299,303,321,322,323,324,325,327,328,361,362,363,364,366,367,369,370,375,376,377,378,379,384,385,386,387,388,389,392,393,394,398,399", "uncoveredLines": "232,306,307,308,311,312,313,314,315,318,332,334,336,341,345,346,348,351,352,353,354,355,356,357,358"}, "MKTFrontEndContext": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "MKTUserAfterUpdateHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9,10,11,12,13,14", "uncoveredLines": ""}, "MKTUserBeforeInsertHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9,10", "uncoveredLines": ""}, "MKTUserBeforeUpdateHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9,10,11,12,13,14", "uncoveredLines": ""}, "MKTUserController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MKTUserSelector": {"coverageRate": "0.71", "coveredLines": "2,5,6,9,10", "uncoveredLines": "13,14"}, "MKTUserService": {"coverageRate": "0.75", "coveredLines": "10,11,12,13,17,18,19,20,25,27,28,29,30,31,32,37,38,39,40,41,42,43,44,49,50,51,52,53,58,59,62,64,65,66,68,69,71,72,73,74,78,80,81,83,84,85,89,93,94,95,96,98,102,103,104,105,107,108,109,110,111,112,113,116,119,120,121,131,136,137,138,139,140,142,147,148,183,184,185,187,190", "uncoveredLines": "86,90,122,123,125,126,132,143,151,154,155,156,157,159,160,163,164,165,168,169,170,171,174,175,179,180,188"}, "MSAContractDTO": {"coverageRate": "0.62", "coveredLines": "3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33,35,37,39,41,43,45,59", "uncoveredLines": "47,48,49,50,51,52,53,54,55,56,64,66,68,70"}, "ManageOpportunityOnProjectController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MapUtils": {"coverageRate": "0.88", "coveredLines": "2,3,6,7,8,10,11,13,17,18,21,22,25,26,32,33,36,37,40,41,47,48,49,50,52,55,56,57,58,60,63,64,65,66,68,69,70,75", "uncoveredLines": "23,28,38,43,72"}, "MarginToolControllerExtension": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MarketOrgController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MarketOrgSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3"}, "MarketOrgService": {"coverageRate": "0.83", "coveredLines": "2,7,8,11,12", "uncoveredLines": "4"}, "MarketingActivatedPersonTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MarketingActivatedSelector": {"coverageRate": "0.78", "coveredLines": "2,3,18,19,22,23,26,27,30,42,43,46,47,60,72,73,76,80,81,84,88,89,92,93,94,105,110,111,112,127,132,133,134,149,154,155,156,159,160,161,164,165,166,170,171,172,176,177,178", "uncoveredLines": "6,7,10,11,14,34,35,38,64,65,68,182,183,196"}, "MarketingActivatedService": {"coverageRate": "0.98", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,17,18,19,21,22,23,24,25,28,29,30,31,32,33,35,38,39,40,41,43,44,46,47,48,49,50,51,52,53,54,60,61,62,66,67,68,69,71,74,75,76,77,79,80,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,103,104,107,108,109,112,115,116,117,119,120,121,122,123,124,126,127,129,130,131,132,137,138,141,142,144,145,149,150,151,152,153,154,155,158,161,162,163,165,166,167,168,169,173,177,178,182,183,184,185,187,188,189,191,192,193,195,199,200,203,205,206,207,211,213,214,215,216,217,218,221,224,225,226,227,228,229,232,233,234,235,236,240,241,242,243,245,247,248,249,251,254,257,258,259,260,262,263,264,265,266,267,270,273,274,275,278,279,280,281,283,284,287,288,289,290,293,294,295,296,297,300,301,304,305,306,307,308,309,311,314,315,316,317,318,319,320,321,322,326,327,331,332,333,334,335,336,337,338,339,340,342,343,344,345,349,350,353,354,355,356,359,361,363,364,367,369,370,371,372,373,374,375,376,377,378,379,380,382,383,384,387,388,389,390,391,393,394,397,398,401,403,404,409,410,413,414,416,417,418,419,424,425,428,429,430,431,434,435,438,439,440,441,442,445,448,449,452,453,456,457,460,461,462,465,466,467,468,469,470,471,472,474,475,476,477,478,479,480,481,483,485,488,489,490,491,492,494,495,498,501,502,503,504,505,506,507,508,509,510,513,516,521,522,523,524,526,527,528,529,530,531,532,533,535,536,537,539,540,544,547,548,549,551,552,553,554,555,556,560,561,564,565,566,568,569,570,573,574,577,578,580,581,582,585,587,588,591,596,597,598,599,600,601,602,603,604,605,606,607,608,610,615,616,617,618,619,620,625,626,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,648,649,652,653,654,655,657,658,661,662,663,664,665,666,667,668,670,671,672,673,674,675,677,682,683,684,685,686,687,688,692,693,694,695,698,699,700,701,706,707,710,711,715,716,717,718,719,722,723,724,726,728,730,731,732,733,734,735,736,737,739,742,743,746,747,751,752,756,757,758,759,760,761,762,764,765,766,767,771,772,773,774,777,778,779,781,784,786,787,788,790,792,793,794,795,796,799,801,802,803,807,808,809,813,814,818,819,820,821,822,823,826,827,829,832,833,834,835,836,837,839,842,845,846,849,850,853,854,855,857,858,860,861,862,865,868,869,870,871,872,873,874,875,876,877,878,879,880,881,883,884,885,886,890,891,894,895,896,897,900,901,904,905,907,908,909,910,911,913,914,915,916,917,918,919,920,922,923,924,925,926,928,929,930,931,936,937,938,940,942,945,949,950,951,952,953,954,956,957,958,959,960,964,967,968,969,971,972,974,976,978,980,981,982,983,984,987,989,990,992,994,995,999,1002,1003,1004,1007,1009,1012,1018,1020,1021,1022,1023,1024,1025,1027,1028,1029,1030,1031,1032,1034,1036,1037,1038,1042,1043,1044,1045,1047,1048,1049,1051,1052,1053,1056,1057,1058,1062,1069,1070,1071,1072,1073,1076,1077,1078,1081,1082,1083", "uncoveredLines": "105,208,458,583,589,744,782,797,804,847,985,1054,1059,1060,1064,1085"}, "MarketingActivity": {"coverageRate": "0.91", "coveredLines": "3,5,7,9,11,16,17,18,19,22", "uncoveredLines": "20"}, "MarketingEngagementDetailDTO": {"coverageRate": "0.85", "coveredLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,68,69,71,74,77,78,80,83", "uncoveredLines": "70,72,79,81"}, "MarketingKPIToolHistoryDTO": {"coverageRate": "1.00", "coveredLines": "3,5,7,9,11,12,13,14,15", "uncoveredLines": ""}, "MarketingKPIToolService": {"coverageRate": "0.80", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19,20,22,23,24,25,27,28,29,30,31,32,33,35,36,37,38,39,42,90,99,101,102,104,105,107,108,109,110,112,114,115,117,118,119,122,127,128,130,131,132,134,135,136,138,139,141,142,144,145,146,147,149,150,154,155,156,159,160,163,165,167,168,169,170,172,173,181,182,183,184,190,191,192,193,196,197,198,199,203,204,205,206,210,211,212,213,214,217,247,252,253,256,257,258,260,261,262,264,265,269,271,272,273,274,275,276,277,281,284,289,293,294,296,297,298,299,300,301,302,303,304,306,309,310,311,312,313,314,315,317,319,321,323,325,326,327,328,332,337,338,339,342,348,349,350,351,352,357,362,363,366,367,368,369,370,373,374,376,380,385,386,387,389,390,391,393,394,395,396,397,399,400,401,402,403,406,407,410,411,412,413,414,415,416,419,420,425,426,427,430,431,432,433,434,437,438,443,448,449,451,452,453,455,456,457,458,459,462,463,467,468,469,470,471,472,473,476,477,482,488,489,492,493,494,495,496,499,500,503,506,507,508,509,512,513,514,517,520,521,522,523,524,527,528,532,533,536,537,539,540,541,544,545,546,547,551,552,554,555,556,558,559,561,562,564,565,566,567,569,571,573,575,578,579,580,581,583,584,586,589,590,591,594,595,596,599,613,615,616,617,618,619,620,621,622,624,625,626,630,633,634,635,637,638,640,641,643,644,645,647,650,653,654,658,659,660,661,662,663,666,670,671,672,673,677,678,680,681,683,686,688,689,691,694,695,698,699,702,703,713,714,716,717,718,719,720,723,724,726,727,730,732,733,736,737,738,739,740,741,742,746,747,748,751,752,753,754,757,758,765,766,768,769,776,777,778,779,780,781,784", "uncoveredLines": "44,45,46,47,48,50,51,53,56,57,58,60,61,62,63,64,65,66,68,69,71,73,74,76,77,78,79,81,83,85,87,151,152,171,174,175,176,177,178,180,186,187,220,221,222,223,225,226,227,228,229,230,231,232,239,240,241,242,244,254,266,279,282,285,290,364,428,490,510,529,534,548,592,602,603,604,605,607,608,610,648,651,655,667,696,704,705,706,707,708,709,710,715,728,734,743,744,755,761,762,772,773"}, "MarketoActivitySyncQueueableJob": {"coverageRate": "0.51", "coveredLines": "2,3,4,5,6,7,8,10,11,13,14,15,16,18,20,21,22,23,24,32,37,42,43,44,45,46,47,48,49,50,51,52,55,56,57,58,62,63,149,150,151,152,153,154,155,198,199,200,201,202,204,206,207,208,209,211,212,213,217,220,221,222,223,231,279,280,281,282,283,284,285,286,287,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,322,323,324,326,327,332,335,336,337,340,341,342,343,344,346,347,348,349,351,354,357,358,361,364,368,371,372,374,377,381,382,383,384,385,387,391,392,393,394,395,396,397,398,399,402,405,406,407,408,409,411,412,413,416,418,420,425,430,435,441,442,443,444,447,448,450,451,452,453,455,456,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,494,495,496,497,498,499,500,501,504,542,545,546,547,552,553,554,555,556,557,558", "uncoveredLines": "9,12,53,66,67,68,69,70,73,74,76,78,79,80,81,82,83,85,86,89,90,92,93,94,95,98,100,101,102,106,107,108,110,111,114,115,117,118,119,122,123,124,126,127,128,129,130,132,135,136,140,141,142,143,145,146,158,159,160,161,163,164,165,167,170,171,172,173,174,175,176,177,178,179,180,182,186,189,190,191,192,193,195,224,225,226,227,234,235,236,237,238,239,240,241,242,243,244,246,247,248,250,253,254,257,258,260,263,264,266,267,268,269,270,273,274,276,289,290,291,292,295,296,299,300,302,329,352,362,365,369,375,378,421,422,426,427,431,432,436,437,457,458,459,460,461,462,463,464,465,468,469,470,471,472,473,474,476,505,506,507,508,509,510,511,514,515,516,517,518,519,522,524,525,526,527,528,529,531,532,533,534,536,539,549"}, "MarketoActivitySyncScheduledJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MarketoClient": {"coverageRate": "0.09", "coveredLines": "2,4,5,6,10,11,12,14,15,16,17", "uncoveredLines": "19,20,21,23,24,26,27,28,29,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,57,58,60,61,63,65,67,70,71,72,74,75,76,77,78,79,80,81,82,83,85,86,89,90,93,94,96,99,100,101,102,103,104,105,106,107,108,109,111,115,116,118,121,122,123,124,125,126,129,130,131,132,133,134,136,137,138,139,140,144,145,146,149,150,152,155,156,157,158,159,160,162,163,165,166,168,169,171,174,175,176,179,180,181,182"}, "MarketoSalesInsightController": {"coverageRate": "0.03", "coveredLines": "269,270,271,272,273,732,733,734,914,915,916,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998", "uncoveredLines": "6,7,8,12,13,14,18,19,20,28,29,30,31,32,33,34,39,40,41,42,46,47,48,52,53,54,61,62,63,64,65,66,70,71,72,76,77,78,81,82,89,90,91,92,93,94,98,99,100,107,108,109,110,111,112,116,117,118,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,158,159,160,161,165,166,167,172,173,174,175,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,215,216,217,218,219,220,221,225,226,227,232,233,234,235,239,240,241,245,246,247,253,254,255,256,257,261,262,263,277,278,279,290,291,292,293,294,295,296,297,298,299,303,304,305,309,310,311,315,316,317,321,322,323,327,328,329,334,335,336,337,342,343,344,345,353,354,355,356,357,358,359,363,364,365,369,370,371,375,376,377,381,382,383,387,388,389,393,394,395,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,446,447,448,456,457,458,459,460,461,462,467,468,469,470,481,482,483,484,485,486,487,488,489,490,494,495,496,501,502,503,504,509,510,511,512,517,518,519,520,524,525,526,531,532,533,534,538,539,540,545,546,547,548,554,555,556,557,558,563,564,565,566,574,575,576,577,578,579,580,587,588,589,590,591,592,597,598,599,600,604,605,606,610,611,612,616,617,618,621,622,626,627,628,632,633,634,638,639,640,644,645,646,656,657,658,659,660,661,662,663,664,669,670,671,672,676,677,678,682,683,684,690,691,692,693,694,701,702,703,704,705,706,710,711,712,717,718,719,720,725,726,727,728,738,739,740,744,745,746,750,751,752,758,759,760,761,762,770,771,772,773,774,775,776,780,781,782,786,787,788,795,796,797,798,799,800,804,805,806,810,811,812,816,817,818,824,825,826,827,828,832,833,834,839,840,841,842,846,847,848,852,853,854,858,859,860,864,865,866,873,874,875,876,877,878,882,883,884,888,889,890,895,896,897,898,902,903,904,908,909,910,921,922,923,924,928,929,930,940,941,942,943,944,945,946,947,948,954,955,956,957,958,1005,1006,1007,1008,1009,1017,1018,1019,1020,1021,1022,1023,1027,1028,1029,1033,1034,1035,1042,1043,1044,1045,1046,1047,1051,1052,1053,1062,1063,1064,1065,1066,1067,1068,1069,1073,1074,1075,1079,1080,1081,1087,1088,1089,1090,1091,1100,1101,1102,1103,1104,1105,1106,1107,1111,1112,1113,1117,1118,1119,1126,1127,1128,1129,1130,1131,1141,1142,1143,1144,1145,1146,1147,1148,1149,1156,1157,1158,1159,1160,1161,1165,1166,1167,1171,1172,1173,1177,1178,1179,1186,1187,1188,1189,1190,1191,1195,1196,1197,1200,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1235,1236,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1259,1260,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1282,1283,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1305,1306,1308,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1342,1343,1345,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1379,1380,1382,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1412,1413,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1434,1435,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1459,1460,1462,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1490,1491,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1513,1514,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1536,1537,1539,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1573,1574,1576,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1606,1607,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1633,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1659,1660,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1683,1684,1686,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1716,1717,1719,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1747,1748,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1771,1772,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1794,1795,1797,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1827,1828,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1853,1854"}, "MaskAccountFieldsBatchableJob": {"coverageRate": "0.76", "coveredLines": "2,8,11,12,17,21,22,26,27,28,30,31,33,34,36,37,39,40,42,43,45,46,49,50,51,52,53,60,65,68,69,72", "uncoveredLines": "4,5,13,14,23,54,55,56,61,70"}, "MaskUserFieldsBatchableJob": {"coverageRate": "0.77", "coveredLines": "2,5,6,11,12,13,19,20,24,25,26,28,29,31,32,34,35,38,39,40,41,42,49,53,56,57,60", "uncoveredLines": "8,15,21,43,44,45,50,58"}, "MassConvertLeadService": {"coverageRate": "0.94", "coveredLines": "2,4,5,7,8,9,10,11,14,16,19,20,21,22,23,24,27,28,29,30,31,34,35,38,39,40,42,43,47,48,49,50,52,55,56,57,58,59,60,66,67,68,70,71,73,75,76,77,79,82,85,86,87,88,89,90,91,92,94,97,98,99,101", "uncoveredLines": "61,62,64,80"}, "MassTimecardAfterInsertTriggerHandler": {"coverageRate": "0.92", "coveredLines": "2,4,5,7,9,10,13,15,16,17,18,20,21,22,23,27,28,30,31,32,35,36,37,38,39,40,41,42,43,44,45,48,50,51,52,53,56,57,63,64,66,67,69,71,73,74,76,77,78,79,80,81,82,84,87,88,90,91,92,94,99,106,107,110,111,112,113,114,115,117,118,119,120,122,123,124,126,127,130,133,134,135,136,137", "uncoveredLines": "95,96,97,100,101,102,103"}, "MassTimecardTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "Mass_TimecardTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "MergeDuplicateContact": {"coverageRate": "0.69", "coveredLines": "2,3,5,7,9,10,11,12,13,14,15,16,17,18,19,22,24,25,26,27,28,29,30,31,32,33,34,35,36,37,42,43,44,45,46,47,48,51,68,69,70,71,72,73,74,75,78,80,81,82,83,84,85,86,87,88,89,90,92,96,98,99,100,101,102,111,112,113,114,115,117,119,122,123,124,125,126,134,138,139,140,141,159,160,161,165,168,169,170,171,175,178,179,180,181,182,185,188,189,190,191,192,193,197,200,201,202,203,204,206,209,210,211,212,213,214,215,220,223,224,225,226,229,232,233,234,235,239,281,285,286,297,298,299,300,301,302,303,304,322,323,324,326,328,331,333,336,339", "uncoveredLines": "53,54,55,56,59,60,61,62,63,103,104,127,128,129,130,144,145,147,148,149,150,151,156,162,172,242,243,244,247,248,249,250,251,252,255,256,257,258,259,261,263,266,267,273,274,275,289,290,291,305,306,307,308,310,313,314,315,319,320,330,335,343,344,345,346,347,348"}, "MigrateClusterAndServicesJob": {"coverageRate": "1.00", "coveredLines": "2,4,5,7,8,9,12,15,18,19,20", "uncoveredLines": ""}, "MigrateDamoProportionJob": {"coverageRate": "0.96", "coveredLines": "2,5,6,7,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,27,29,30,34,35,36,37,38,39,41,42,43,44,45,47,50,51,52,56,59,61,62,63,66,67,68,69,71,72,73,74,75,76,77,79,80,81,82,83,87,88,89,90,94,96,97,98,100,103,105,107,110,111,114,115", "uncoveredLines": "57,99,101"}, "MilestoneAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "MilestoneAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "MilestoneAfterUnDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10", "uncoveredLines": ""}, "MilestoneAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "MilestoneBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4", "uncoveredLines": ""}, "MilestoneBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": ""}, "MilestoneBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "MilestoneController": {"coverageRate": "1.00", "coveredLines": "2,5,6", "uncoveredLines": ""}, "MilestoneSelector": {"coverageRate": "0.80", "coveredLines": "2,3,4,16,37,38,45,46,47,50,51,52,55,60,61,64,68,69,70,74,75,80,81,82,83,85,88,89,90,94,95,98,99,102,103,122,123,124,127,132,133,144,145,146,150,151,169,173,174,175,178,180,193,194,197,198,201,202,203,204,205,206,208,211,212,222,232,233,236,240,241,244", "uncoveredLines": "21,22,41,42,76,106,107,108,112,113,114,117,136,137,140,183,186,188"}, "MilestoneService": {"coverageRate": "0.96", "coveredLines": "2,3,4,13,14,15,16,17,18,19,23,30,31,32,33,34,37,38,39,40,41,42,44,45,46,49,51,52,53,56,57,58,60,61,63,65,66,68,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,93,96,97,98,100,101,102,103,104,105,108,111,112,113,114,117,118,119,120,122,125,129,130,131,132,133,135,139,142,143,144,145,146,150,153,154,155,156,159,160,161,162,163,166,168,169,170,171,172,173,174,175,176,182,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,206,209,210,212,215,216,218,222,225,226,227,228,229,231,234,235,236,237,241,242,243,244", "uncoveredLines": "123,136,148,207,213,219"}, "MilestoneShareBatchableJob": {"coverageRate": "0.66", "coveredLines": "2,9,11,12,13,14,15,16,19,20,22,23,25,30,31,40,41,45,52,53,56", "uncoveredLines": "26,27,32,33,34,35,36,43,48,49,54"}, "MilestoneShareRevokeBatchableJob": {"coverageRate": "0.76", "coveredLines": "2,3,12,14,21,22,23,24,25,31,32,33,34,35,37,38,39,42,43,46,47,53,57,58,59,60,62,63,64,68,69,70,71,74,77,78,79,80", "uncoveredLines": "26,27,48,49,50,82,83,85,86,87,88,90"}, "MilestoneShareSelector": {"coverageRate": "0.83", "coveredLines": "2,3,8,9,12", "uncoveredLines": "4"}, "MilestoneTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "MilestoneTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,11,12,13,14,15,20,21,22,23,24,29,30,31,32,33,38,39,40,41,42,47,48,49,50,51,56,57,58,59,60,65,66,67,68,69", "uncoveredLines": "17,26,35,44,53,62,71"}, "MiscAdjustmentSelector": {"coverageRate": "0.50", "coveredLines": "2,21,22,23,27,28,29,40,41,58,62,63,64", "uncoveredLines": "5,6,7,10,13,14,17,18,33,34,35,37,66"}, "MiscAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MiscAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "MiscAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "MiscBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MiscBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MiscService": {"coverageRate": "0.98", "coveredLines": "2,3,4,9,10,11,12,13,17,18,19,20,21,24,25,26,27,28,32,33,36,38,44,45,46,48,49,54,55,56,57,58,59,61,62,63,65,66,67,68,69,70,71,73,74,76,77,79,80,81,86,89,92,93,94,95,96,97,98,99,100,102,106,107,109,110,112,113,117,118,119,121,122,124,125,127,128,132,133,134,135,138,139,142,144,147,148,149,150,153,154,158,159,160,164,165,166,167,168,169,170,172,173,174,175,176,177,178,180,186,187,188,189,190,191,196,200,201,204,206,207,208,209,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,233,234,235,236,240,241,243,244,245,246,248,249,251,252,253,254,259,260,262,263,266,267,268,269,273,275,276,278,279,282,286,287,288,289,290,294,296,297,298,299,300,301,302,303,304,309,313,314,315,319,320,321,322,324", "uncoveredLines": "39,40,193"}, "MiscShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,12", "uncoveredLines": ""}, "MiscTriggerDispatcher": {"coverageRate": "0.94", "coveredLines": "2,3,5,6,7,9,12,13,14,15,16,21,22,23,24,25,27,30,31,32,33,34,36,39,40,41,42,43,45,48,49,50,51,52", "uncoveredLines": "18,54"}, "MiscellaneousAdjustmentSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,4"}, "MiscellaneousAdjustmentTrigger": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16", "uncoveredLines": ""}, "MiscellaneousShareBatchableJob": {"coverageRate": "0.85", "coveredLines": "2,9,11,12,13,14,15,16,19,20,22,23,25,30,31,32,33,34,35,38,39,45,46,50,53,54,57,58,61", "uncoveredLines": "26,27,36,48,59"}, "MiscellaneousShareRevokeBatchableJob": {"coverageRate": "0.92", "coveredLines": "2,3,5,12,13,15,23,24,25,26,27,28,31,32,33,35,37,38,39,40,46,47,53,57,58,59,60,61,62,63,64,65,67,68,69,70,73,74,75,76,77,80,81,82,83,84,87,88,91,92,95,100,101,102,103,104,105,106,108,109,113,116,117,121,122", "uncoveredLines": "43,48,49,50,118,119"}, "MissingTimecard": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MissingTimecardInfo": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "MissingTimecardSelector": {"coverageRate": "0.24", "coveredLines": "25,26,43,44,45", "uncoveredLines": "2,3,10,11,12,13,21,33,34,35,36,39,49,50,51,54"}, "MktgEngagementController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MonitorAsyncApexJob": {"coverageRate": "0.77", "coveredLines": "2,3,4,10,11,19,20,21,22,23,25,31,32,33,34,35,36,41,42,43", "uncoveredLines": "6,14,15,16,26,27"}, "MonthEndTcService": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,10,11,13,15", "uncoveredLines": ""}, "MonthEndTimecardBatchableJob": {"coverageRate": "0.88", "coveredLines": "2,3,7,8,11,12,15,16,19,20,21,22,25,26,27", "uncoveredLines": "28,29"}, "MonthEndTimecardCleanUpTask": {"coverageRate": "1.00", "coveredLines": "4,5,7,8,11,12,13,14,15,18,21,22", "uncoveredLines": ""}, "MonthEndTimecardCreateActualTcTask": {"coverageRate": "1.00", "coveredLines": "4,5,7,8,11,12,13,14,15,18,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,53", "uncoveredLines": ""}, "MonthEndTimecardCreateEstimatedTcTask": {"coverageRate": "0.97", "coveredLines": "4,6,7,8,9,10,11,12,13,14,17,20,21,22,25,26,27,28,30,31,32,35,38,39,40,41,46,47,48,49,52,55,56,57,58,59,60,61,64,65,66,67,68,69,70,71,73,76,77,78,83,84,85,86,87,88,89,90,91,92,94,95,97,100,103,104,105,106,107,108,109,110,111,112,114,119,123,126,127,128,129,130,131,132,134,140,141,142,146,147,149,150,152,154,155,157,158,159,160,161,162,163,164,165,166,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,188,189,190,191,193,195,196,197,200,201,202,203,204,208", "uncoveredLines": "33,42,43,120"}, "MonthEndTimecardCreationSchedulable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MonthEndTimecardSelector": {"coverageRate": "0.65", "coveredLines": "2,3,4,16,17,18,32,36,37,38,39,40,42,43,44,45,47,49,52,53,60,61,66,67,68,71", "uncoveredLines": "6,9,11,74,75,76,87,88,92,96,97,98,99,102"}, "MonthEndTimecardTask": {"coverageRate": "0.77", "coveredLines": "4,5,13,14,21,24,25,27,28,29,30,31,32,33,34,38,39,41,42,43,47,48,52,53,54,57,58,59,60,66,73,74,75,76,78,84,85,88,89,90,98,99,102,103,107,108,113,114,115", "uncoveredLines": "36,45,62,63,64,80,91,92,94,95,104,109,116,117,118"}, "MonthlyClearDataJob": {"coverageRate": "0.82", "coveredLines": "2,3,4,5,13,14,15,18,19,20,21,22,25,27,30,31,32,33", "uncoveredLines": "7,8,9,23"}, "MonthlyCurrencyExchangeRateSelector": {"coverageRate": "0.77", "coveredLines": "18,19,27,29,32,35,36,37,39,42,43,44,46,47,49,52,53,54,55,57,58,60,61", "uncoveredLines": "2,4,7,11,12,13,15"}, "MonthlyCurrencyExchangeRateTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "MonthlyCurrencyExchangeRatesSelector": {"coverageRate": "0.68", "coveredLines": "2,3,6,7,10,11,14,15,22,23,24,27,28,31,34", "uncoveredLines": "18,19,37,38,43,46,47"}, "MonthlyCurrencyExchangeRatesService": {"coverageRate": "0.75", "coveredLines": "2,3,6,7,14,15", "uncoveredLines": "10,11"}, "MonthlyCurrencyExchangeRatesServiceImpl": {"coverageRate": "0.83", "coveredLines": "4,5,7,8,9,12,13,16,18,19,22,24,26,27,30,54,55,57,58,59,60,62,63,66,69,70,71,72,73,75,76,78,80,83,84,85,87,89,90,91,92,95,96,97,98,99,100,102,103,104,105,107,108,109,112", "uncoveredLines": "33,34,35,37,39,40,43,45,47,48,51"}, "MonthlyRateAfterInsertTriggerHandler": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,8,9,10,11,16,17,21,22", "uncoveredLines": "12,13"}, "MonthlyRateTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": "10"}, "MyAssignmentsController": {"coverageRate": "0.96", "coveredLines": "2,3,4,7,8,9,11,12,14,15,17,19,20,21,22,23,25,28,32,33,34,35,36,37,40,41", "uncoveredLines": "44"}, "MyDomainDiscoveryLoginHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MyPersonalUtilizationController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MySalesInsigntController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "MyUtilizationController": {"coverageRate": "1.00", "coveredLines": "2,5,7,9,11,13,15,16,17,18,19,23,24,25,26,27,43,44,46,48,50,52,53,54,55,57,61,62,63,64,65,66,67,68,71,72,73,74,75,78,79,80,81,84,88,89,91,94,95,97,98,99,100,103,104,105,106,109,110,111,112,116,120,121,122,123,125,128,129,130,131,132,133,134,135,136,137,138,141,142,143,144,145,148,151,152,153,154,155,158", "uncoveredLines": ""}, "NetPromoterScore": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "NewlyCreatedBEIsShareBatchableJob": {"coverageRate": "1.00", "coveredLines": "6,8,9,10,11,14,15,17,18,19,21,22,26,29,30,33,36,37,38,39,41", "uncoveredLines": ""}, "NextInvoiceDueDateBatchJob": {"coverageRate": "0.95", "coveredLines": "2,3,4,6,8,9,10,11,14,15,16,19,20,21,22,23,24,29,30,35,36,39,40,41,43,47,48,51,54,55,56,57,58,59,60,61,65,66,67,68,69,72,73,75,76,77,81,83,86,87,88,89,90,91,92,93,96,98", "uncoveredLines": "26,32,49"}, "NextInvoiceDueDateFixedBidScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "NextInvoiceDueDateScheduleJob": {"coverageRate": "0.60", "coveredLines": "2,16,17,18,19,20,22,23,25", "uncoveredLines": "5,6,9,10,11,12"}, "NoMovementDTO": {"coverageRate": "1.00", "coveredLines": "3,6,8,9,10", "uncoveredLines": ""}, "NotAllowedException": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "NotificationWhenPaymentOverdue": {"coverageRate": "0.57", "coveredLines": "56,57,59,60,61,64,66,67,68,70,71,72,74,76,91,92,95,96,97,98,99,100,101,102,104,105,107,110", "uncoveredLines": "2,3,4,7,8,9,11,12,40,41,47,49,50,51,77,80,81,83,84,85,87"}, "NotifyCorpApartDepositNotReturnOver6M": {"coverageRate": "0.73", "coveredLines": "2,3,4,5,13,14,37,38,39,40,41,42,43,44,45,46,47,48,49,50,52,55,59,60,61,62,63,65,69,70,71,72,73,74,75,78,79,81", "uncoveredLines": "8,9,17,18,21,22,24,25,26,27,29,30,31,32"}, "ObjectsReassignment": {"coverageRate": "0.71", "coveredLines": "3,8,9,10,11,14,15,16,17,20,21,22,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,59,60,61,62,63,66,67,69,72,74,83,84,85,86,87,88,90,91,92,93,94,95,97,98,99,100,101,102,104,105,106,107,108,112,113,114", "uncoveredLines": "24,28,29,30,31,32,75,76,78,79,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132"}, "OfferingServiceMapSelector": {"coverageRate": "1.00", "coveredLines": "6,7,8,9,10,11,12,13,16,17", "uncoveredLines": ""}, "OperateApprovalProcessEventService": {"coverageRate": "0.86", "coveredLines": "2,3,5,8,9,10,11,12,14,15,24,25,26,28,29,30,31,32,33,37,38,42,43,44", "uncoveredLines": "18,19,34,35"}, "OperateApprovalProcessEventTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "OperatorDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OppContactRoleOperationService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,10,11,12,15,16,17,20,21,23,24,25,27,28,29,30,31,33,34,35,37,38,39,40,41,42,43,45,47,50,52,54,55,56,57,58,59,60,61,64,65,69,70,71,72,73,74,75,78,79", "uncoveredLines": ""}, "OppContactRoleSelectController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OppContactRoleWrapper": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OppControllerExtension": {"coverageRate": "1.00", "coveredLines": "4,6,9,10,13,14,15,17,21,22", "uncoveredLines": ""}, "OppDeliveryDistRegionDTO": {"coverageRate": "1.00", "coveredLines": "3,5,7,8,9,12", "uncoveredLines": ""}, "OppDeliveryDistributionRegionController": {"coverageRate": "1.00", "coveredLines": "3,6,7,11,12", "uncoveredLines": ""}, "OppDeliveryDistributionRegionSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7", "uncoveredLines": ""}, "OppDeliveryDistributionRegionService": {"coverageRate": "0.98", "coveredLines": "2,3,4,5,6,7,9,10,11,12,14,15,16,17,18,19,20,23,26,27,28,30,31,32,36,38,39,42,43,44,45,46,47,48,49,53,54,55,56,57", "uncoveredLines": "60"}, "OppEstimatedPursuitCostBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OppPartnershipAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "OppPartnershipAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "OppPartnershipAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "OppSLAssignmentItem": {"coverageRate": "0.68", "coveredLines": "6,7,8,9,24,25,26,27,29,30,32,35,36", "uncoveredLines": "12,13,16,17,20,21"}, "OppTagRelationAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "OppTagRelationAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "Opp_Tag_RelationTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,14,15,16,17,18", "uncoveredLines": "11,20"}, "OppoTMemberAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "5,7,8", "uncoveredLines": ""}, "OppoTMemberAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9,10", "uncoveredLines": ""}, "OppoTMemberAfterUpdateTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "Opportunities": {"coverageRate": "0.83", "coveredLines": "6,7,14,16,20,21,25,26,27,28,29,30,31,32,37,38,40,41,42", "uncoveredLines": "2,3,10,11"}, "OpportunityAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,10,11,12,13,14,17", "uncoveredLines": ""}, "OpportunityAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,9,10,11,12,13", "uncoveredLines": ""}, "OpportunityAfterUnDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,10,11,12,14,15,16,17,18,19,20,21,22,24,25,26,27,28,29,30", "uncoveredLines": ""}, "OpportunityBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,9,10,11,12,13,14,15,16,17,18", "uncoveredLines": ""}, "OpportunityBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,11,12,13,14,15,16,17,18,19,20,21,22,23,26,27,28", "uncoveredLines": ""}, "OpportunityBeforeUpdateTriggerHandler": {"coverageRate": "0.80", "coveredLines": "5,6,7,8,9,10,12,13,14,15,16,18,19,21,22,23,24,25,26,27,28,29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,51,52,54,55,56,57,63,64,65,68,69,70,71,75,76,78,79,82,88,89,90,93,95,96,100,101,131,132,133,134,140,141,142,143,144,145,146,148,153,154,155,156,161,162,163,164,167,173,174,175,177,178,179,181,182,184,185,190,191,192,193,194,195,196,197,198,199,203,204", "uncoveredLines": "91,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,120,121,122,123,124,125,126,127,165,168"}, "OpportunityCRAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,9,10,11,12", "uncoveredLines": ""}, "OpportunityCRAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,11", "uncoveredLines": ""}, "OpportunityCRAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "3,4,5,7,8,9", "uncoveredLines": ""}, "OpportunityCRBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,7", "uncoveredLines": ""}, "OpportunityCRBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "OpportunityCRBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,8,9,10", "uncoveredLines": ""}, "OpportunityChatterService": {"coverageRate": "0.98", "coveredLines": "2,5,6,7,8,9,10,12,15,16,17,19,20,21,22,23,25,26,27,28,29,31,33,35,36,38,39,40,43,46,49,52,53,55,57,59,61,62,64,65,66,67", "uncoveredLines": "41"}, "OpportunityCompetitorAssignmentSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "OpportunityCompetitorInfoDTO": {"coverageRate": "1.00", "coveredLines": "2,5,6,7,8,12,15,18", "uncoveredLines": ""}, "OpportunityContactRoleSelector": {"coverageRate": "0.53", "coveredLines": "2,3,6,7,10,11,29,34,35,58,112,113,114,124,125,126,156,157,160,168,169,172,176,177,178,197", "uncoveredLines": "63,64,78,83,84,100,104,105,108,118,119,120,130,131,147,152,153,164,165,203,204,207,208"}, "OpportunityContactRoleService": {"coverageRate": "0.99", "coveredLines": "2,6,7,8,9,10,12,15,21,22,23,24,27,28,29,30,31,32,33,34,36,37,38,42,43,44,45,46,47,48,49,51,52,56,57,59,62,63,65,68,72,73,74,75,76,79,80,82,85,86,87,88,89,90,92,93,94,95,96,97,98,99,100,103,104,106,109,110,111,112,113,116,117,120,121,122,123,128,129,130,131,132,133,135,136,139,140,141,144,145,146,147,152,153,154,156,157,159,160,163,166,170,171,172,173,174,179,180,181,182,183,185,186,187,188,189,190,192,193,196,199,200,203,204,206,207,208,211,212,214,215,216,217,219,222,225,226,227,228,229,231,232,233,234,236,237,238,242,243,244,245,246,247,251,252,253,254,258,259,260,261,263,264,265,269,270,271", "uncoveredLines": "161,167"}, "OpportunityContactRoleTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "OpportunityContactRoleTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,18,19,20,21,22,27,28,29,30,31,36,37,38,39,40,45,46,47,48,49,54,55,56,57,58", "uncoveredLines": "15,24,33,42,51,60"}, "OpportunityController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityEventSelector": {"coverageRate": "0.40", "coveredLines": "2,3", "uncoveredLines": "6,7,10"}, "OpportunityEventService": {"coverageRate": "0.78", "coveredLines": "6,7,14,15,16,17,19,20,28,29,30,31,32,33,34,35,36,38,39,42,44,45,46,47,48,49,50,51,52,54,56", "uncoveredLines": "10,11,24,25,60,61,62,63,66"}, "OpportunityFieldHistoryController": {"coverageRate": "0.75", "coveredLines": "3,9,10", "uncoveredLines": "5"}, "OpportunityFieldHistorySelector": {"coverageRate": "0.73", "coveredLines": "2,5,6,9,12,13,14,16", "uncoveredLines": "19,20,23"}, "OpportunityHistoryController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityHistorySelector": {"coverageRate": "0.20", "coveredLines": "2", "uncoveredLines": "4,5,6,52"}, "OpportunityHistoryService": {"coverageRate": "0.99", "coveredLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,52,55,56,57,60,61,64,65,66,67,68,70,71,74,75,78,79,80,81,82,83,84,85,86,87,88,89,90,95,98,99,100,101,103,105,106,107,109,110,113,114,115,116,119,122,124,125,126,127,129,130,131,132,133,134,135,136,140,141,142,143,144,145,146,147,148,149,150,152,153,154,156,157,158,160,161,162,163,165,166,167,169,170,171,173,174,175,179,180,181,182,183,184,185,186,187,189,192,193,195,196,197,198,199,200,201,202,203,205,206,207,208,209,210,211,212,213,216,217,218,219,222,225,226,228,229,231,232,233,235,236,238,239,244,245,246,248,249,250,251,252,254,255,256,258,259,261,262,266,267,268,270", "uncoveredLines": "138,168"}, "OpportunityNextStepController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityNextStepSelector": {"coverageRate": "0.14", "coveredLines": "2", "uncoveredLines": "5,6,9,10,13,14"}, "OpportunityNextStepService": {"coverageRate": "0.92", "coveredLines": "5,10,11,14,15,16,17,19,21,24,25", "uncoveredLines": "7"}, "OpportunityOfferingAndServiceDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityOfferingService": {"coverageRate": "0.98", "coveredLines": "6,7,9,12,13,16,17,19,20,21,22,26,28,29,33,34,35,36,41,42,43,44,45,49,50,53,54,55,56,57,58,59,61,62,64,67,70,71,72,73,74,75,76,78,81,82,85,86,87,90,91,92,93,94,96,97,98,99,101,102,103,104,106,107,108,109,110,112,113,114,115,116,120,121,122,123,127,129,130,131,132,133,134,138,142,145,146,147,148,149,152,153,154,155,156,157,158,159,161,163,166,167,168,169,170,171,172,173,174,175", "uncoveredLines": "14,136"}, "OpportunityOfferingServiceSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,10,11,14,15", "uncoveredLines": ""}, "OpportunityOperationService": {"coverageRate": "0.83", "coveredLines": "2,13,14,15,16,17,19,20,21,22,23,24,25,28,31,32,33,34,35,36", "uncoveredLines": "4,5,6,7"}, "OpportunityOperationTrigger": {"coverageRate": "1.00", "coveredLines": "6,7", "uncoveredLines": ""}, "OpportunityPartnershipTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "OpportunityProgressBarDTO": {"coverageRate": "1.00", "coveredLines": "7,9,11,13,15,17,19,21", "uncoveredLines": ""}, "OpportunityProgressBarService": {"coverageRate": "0.68", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,16,19,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,46,47,48,53,54,55,56,57,58,59,60,61,62,66,67,69,70,71,73,74,75,76,77,78,79,80,81,82,83,90,91,92,93,94,96,131,132,133,136,137,138,139,141,156,157,158,172,173,174,175,178,179,180,181,182,183,184,185,187,188,191,192,196,197,200,201,204,205,208,211", "uncoveredLines": "85,86,99,100,101,102,103,104,105,106,107,108,109,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126,127,143,144,145,150,151,152,153,159,160,161,164,165,166,167,168,212,216,217,218,219,220"}, "OpportunityProjectService": {"coverageRate": "0.84", "coveredLines": "2,5,6,7,8,9,10,11,12,13,14,16,20,29,30,31,32,33,34,35,38,39,40,41,42,43,46,47,49,50,52,53,54,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,84,91,92,93,94,95,97,102,107,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,134,135,137,139,140,141,142,143,146,149,150,151,152,153,158,159,160,161,180,181,184,185,187,188,189,193,194,195,196,199,202,203,204,205,206,207,211,214,215,225,226,227,228,229,232,235,236,237,238,241,244,247,248,249,250,253,256,259,260,261,262,267,270,273,279,280,281,282,284,285,286,288,289,290,293,294,297,298,299,300,303,305,306,307,308,314,315,317,318,320,321,326,327,328,329,330,331,333,334,338,339,340,341,342,343,344,351,352,353,354,355,356,358,359,360,367,368,369,370,380,381,383,384,385,386,393,394,402,404,405,409,410,411,412,413,416,419,420,423,425,428,429,430,431,434,437,441,442,443,444,445,446,447,448,449,450,451,453,454,456,459,460,463,464,467,468,469,470,471,472,473,474,475,476,477,478,479,480,483,484,485,486,491,492,493,508,509,510,513,516,517,518,524,525,526,527,528,529,532,533,536,537,538,539,540,543,546,547,549,550,551,552,554,555,556,557,558,559,560,561,562,564,565,566,568,569,570,576,579,580,581,582,584,587,588,589,592,593,594,595,596,597,598,599,601,602,603,604,606,607,609,610,611,614,615,616,618,619,620,621,622,623,625,626,627,629,630,634,637,640,641,642,644,645,646,648,649,650,651,652,653,654,659,661,662,663,664,665,666,668,669,674,675,676", "uncoveredLines": "85,86,87,88,98,99,103,104,108,132,154,162,163,164,165,166,169,170,171,174,175,176,177,216,217,218,219,221,239,251,263,264,268,295,345,346,348,361,362,363,364,371,372,373,375,376,387,388,389,390,395,396,397,414,417,421,432,438,487,488,495,496,497,499,500,501,503,505,511,520"}, "OpportunityRedirectVFController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunitySelector": {"coverageRate": "0.45", "coveredLines": "2,4,88,89,90,161,166,167,170,174,175,176,196,197,198,201,202,203,262,265,266,267,268,269,272,275,276,277,301,306,307,310,311,320,321,322,323,325,328,329,332,333,336,337,338,357,362,363,364,379,393,394,395,411,431,432,435,436,439,440,443,444,509,510,513,514,517,521,522,525,543,544,547,585,586,590,621,622,624,625,626,628,639,640,651,706,707,737,739,742,743,762,766,767,770,771,774,775,778,779,812,813,816,820,821,829,881,882,883,884,885,886,887,888,890,891,892,893,894,897,900,901,903,904,905,907,914,915,916,917,918,919,920,921,922,923,925,926,927,948,953,956,957,958,963,964,965,966,991,997,998,999,1000,1001,1002,1006,1024,1025,1026,1027,1076,1077,1078,1079,1080,1081,1082,1083,1084,1086,1087,1088,1089,1092,1093,1094,1095,1096,1097,1098,1100,1101,1102,1103,1104,1105,1256,1257,1258,1261", "uncoveredLines": "7,8,9,12,17,18,19,22,27,28,29,32,37,38,39,42,47,54,55,56,57,58,59,61,62,63,64,65,66,69,77,78,79,80,81,82,83,84,85,180,181,184,185,186,189,193,314,315,316,385,386,389,390,416,417,418,427,428,529,530,534,535,536,537,538,539,551,552,571,572,575,576,577,581,582,600,601,602,604,605,607,610,611,613,616,631,632,635,655,656,668,672,673,702,704,782,783,784,785,786,787,788,789,792,793,796,799,800,805,807,808,822,823,824,827,832,833,834,835,836,840,844,852,853,854,855,856,857,858,860,861,863,864,865,866,867,868,870,871,873,874,876,877,878,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1039,1042,1043,1063,1064,1067,1069,1070,1071,1072,1108,1109,1113,1114,1116,1118,1119,1120,1121,1123,1124,1127,1128,1148,1149,1151,1153,1155,1156,1157,1161,1162,1166,1167,1169,1171,1173,1174,1175,1177,1178,1181,1182,1186,1187,1189,1191,1193,1194,1195,1197,1198,1201,1209,1210,1211,1213,1215,1216,1247,1248,1251,1253,1266,1267,1270"}, "OpportunityService": {"coverageRate": "0.90", "coveredLines": "2,4,5,6,7,8,14,17,18,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,96,97,98,99,100,102,105,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160,161,162,163,165,166,167,168,169,172,173,175,176,177,180,181,182,188,189,190,191,193,194,195,196,198,201,202,203,204,208,209,212,213,216,217,219,221,222,223,226,227,229,231,232,233,235,236,237,239,242,243,244,245,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,282,283,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,307,310,313,314,315,316,318,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,367,368,369,372,375,376,377,378,383,384,386,387,388,390,391,396,397,399,400,401,402,404,405,408,412,413,415,416,417,420,423,424,426,427,428,430,431,436,437,439,440,441,442,444,445,448,452,453,455,456,457,460,463,464,466,467,468,470,471,476,477,479,480,481,482,484,485,488,492,493,495,496,497,500,503,504,505,507,508,509,511,512,514,515,516,518,519,520,521,525,526,527,528,530,531,532,536,537,539,540,541,543,544,547,548,550,553,554,555,556,557,558,559,562,563,564,565,566,567,568,569,570,627,628,629,630,633,634,635,636,637,638,641,642,643,644,646,647,648,649,661,662,663,664,666,667,670,671,672,673,676,677,679,680,681,682,684,685,686,687,688,689,697,698,699,700,701,704,705,706,708,711,712,715,718,719,721,724,725,727,728,730,731,732,733,734,735,736,738,739,740,742,744,745,746,747,748,750,752,753,756,757,758,760,761,762,767,768,769,770,771,772,773,774,775,776,777,778,780,781,785,786,788,789,790,791,794,815,816,817,821,822,823,824,825,829,830,833,834,835,836,839,840,841,846,847,848,849,850,851,852,854,858,859,862,863,865,866,867,870,871,872,878,879,880,881,882,884,887,888,889,890,891,892,897,898,899,900,901,906,907,908,909,910,915,916,920,921,922,923,925,926,927,928,932,934,935,937,939,942,943,945,946,947,951,952,953,954,955,956,957,963,964,965,966,969,972,973,974,975,977,978,982,983,984,985,987,988,989,990,994,995,998,999,1001,1002,1003,1004,1006,1007,1008,1011,1012,1014,1015,1019,1020,1021,1023,1024,1025,1026,1030,1031,1034,1035,1036,1040,1043,1044,1045,1047,1048,1050,1051,1052,1055,1056,1057,1060,1063,1064,1067,1068,1069,1070,1071,1073,1074,1080,1081,1082,1084,1085,1087,1088,1089,1093,1094,1097,1098,1099,1100,1101,1105,1108,1109,1110,1111,1112,1113,1115,1116,1122,1123,1126,1127,1128,1129,1130,1132,1133,1134,1139,1140,1141,1142,1144,1145,1150,1151,1152,1153,1154,1156,1157,1162,1163,1164,1165,1167,1168,1173,1174,1175,1176,1177,1178,1179,1181,1182,1183,1184,1188,1189,1190,1191,1192,1193,1194,1196,1199,1200,1201,1205,1206,1207,1208,1209,1210,1212,1216,1217,1218,1222,1223,1224,1244,1245,1246,1247,1248,1249,1250,1252,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1267,1275,1276,1277,1278,1279,1280,1282,1283,1286,1287,1289,1290,1292,1293,1294,1298,1299,1300,1304,1305,1306,1307,1311,1312,1313,1316,1317,1318,1320,1322,1325,1326,1327,1328,1332,1333,1334,1335,1337,1338,1339,1340,1341,1342,1345,1350,1351,1352,1354,1355,1357,1361,1362,1363,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1379,1380,1383,1384,1385,1386,1387,1388,1389,1392,1396,1397,1398,1399,1400,1405,1406,1407,1408,1409,1413,1414,1415,1416,1417,1422,1423,1424,1426,1427,1428,1429,1443,1444,1445,1446,1447,1448,1449,1450,1451,1454,1455,1457,1458,1459,1460,1461,1463,1464,1465,1466,1467,1471,1472,1474,1475,1479,1480,1481,1482,1483,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1498,1501,1502,1503,1504,1506,1507,1508,1509,1510,1511,1512,1514,1518,1519,1521,1526,1527,1528,1529,1530,1531,1532,1534,1535,1536,1537,1538,1539,1541,1545,1546,1547,1549,1550,1551,1552,1553,1554,1556,1559,1563,1564,1565,1566,1568,1569,1570,1571,1573,1576,1577,1578,1581,1584,1585,1586,1587,1590,1593,1594,1595,1597,1600,1601,1602,1603,1604,1605,1606,1609,1611,1614,1615,1616,1617,1618,1619,1620,1621,1622,1624,1627,1628,1631,1632,1633,1635,1636,1637,1638,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1655,1656,1657,1659,1660,1661,1662,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1683,1684,1691,1692,1694,1695,1697,1698,1699,1701,1702,1704,1708,1709,1710,1711,1712,1713,1717,1721,1722,1723,1724,1725,1726,1730,1734,1735,1736,1738,1739,1740,1741,1742,1745,1746,1747,1748,1749,1750,1752,1765,1766,1769,1770,1773,1774,1775,1776,1789,1790,1791,1792,1793,1794,1795,1797,1799,1800,1801,1802,1805,1809,1810,1811,1812,1813,1814,1815,1817,1818,1819,1820,1821,1822,1823,1824,1829,1833,1834,1835,1836,1837,1838,1839,1841,1844,1852,1853,1854,1855,1858,1859,1860,1862,1863,1869,1872,1873,1874,1877,1878,1879,1882,1883,1884,1887,1888,1891,1893,1896,1900,1901,1902,1903,1908,1909,1911,1913,1915,1916,1917,1918,1919,1920,1922,1923,1924,1925,1926,1927,1929,1931,1932,1933,1934,1937,1940,1943,1950,1951,1953,1954,1955,1956,1957,1958,1959,1961,1963,1965,1984,1985,2004,2005,2006,2007,2009,2010,2012,2013,2014,2017,2018,2019,2021,2022,2023,2032,2036,2037,2038,2039,2040,2041,2042,2044,2046,2047,2048,2049,2051,2052,2053,2054,2055,2056,2058,2062,2063,2067,2068,2069,2070,2072,2081,2082,2083,2084,2085,2086,2089,2092,2093,2096,2097,2100,2101,2102,2105,2106,2107,2111,2112,2113,2114,2115,2116,2121,2122,2123,2124,2125,2130,2131,2132,2136,2137,2138,2139,2143,2144,2145,2148,2161,2162,2163,2164,2166,2167,2171,2172,2173,2174,2175,2178,2179,2180,2181,2182,2183,2184,2187,2188,2189,2192,2193,2199,2200,2201,2202,2203,2204,2205,2209,2210,2213,2214,2217,2218,2219,2222,2224,2225,2226,2227,2228,2229,2233,2234,2235,2236,2241,2242,2244,2245,2246,2247,2248,2249,2250,2253,2256,2257,2258,2259,2260,2262,2263,2264,2265,2268,2271,2272,2273,2274,2275,2276,2277,2281,2282,2285,2287,2289,2290,2291,2294,2298,2299,2300,2301,2302,2306,2308,2309,2331,2332,2333,2334,2336,2337,2338,2341,2342,2343,2344,2348,2349,2351,2352,2354,2355,2357,2358,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2373,2374,2375,2377,2379,2380,2383,2384,2386,2389,2390,2392,2393,2394,2398,2399,2401,2402,2403,2405,2406,2407,2410,2411,2416,2417,2418,2419,2420,2423,2424,2425,2426,2430,2433,2434,2435,2436,2437,2440,2444,2445,2446,2447,2449,2453,2454,2455,2456,2458,2461,2462,2464,2465,2466,2467,2472,2473,2474,2475,2476,2478,2482,2483,2484,2487,2488,2490,2491,2492,2493,2494,2496,2497,2498,2499,2502,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2521,2522,2523,2524,2525,2527,2528,2529,2530,2531,2532,2537,2540,2541,2542,2543,2544,2546,2547,2549,2550,2552,2555,2556,2557,2558,2559,2562,2563,2564,2565,2570,2571,2572,2573,2574,2575,2576,2577,2578,2586,2587,2588,2589,2590,2591,2592,2594,2595,2601,2602,2606,2607,2609,2610,2611,2612,2613,2615,2616,2617,2618,2619,2620,2622,2623,2624,2625,2626,2628,2629,2630,2631,2632,2636,2638,2639,2640,2641,2658,2659,2660,2664,2665,2666,2669,2670,2671,2672,2673,2675,2676,2679,2682,2683,2684,2685,2688,2690,2692,2694,2696,2699,2700,2701,2702,2703,2705,2706,2707,2708,2709,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2731,2732,2734,2737,2738,2740,2741,2742,2743,2745,2746,2750,2753,2754,2755,2756,2757,2758,2759,2760,2762,2763,2764,2766,2767,2768,2772,2773,2777,2778,2779,2780,2782,2785,2786,2788,2833,2834,2836,2837,2838,2842,2845,2846,2847,2848,2849,2853,2854,2857,2858,2860,2864,2865,2866,2867,2868,2869,2872,2873,2874,2875,2876,2877,2880,2881,2883,2884,2887,2888,2889,2892,2893,2896,2897,2898,2899,2902,2903,2904,2908,2909,2913,2914,2915,2919,2920,2921,2923,2924,2927,2928,2930,2931,2932,2933,2934,2935,2936,2937,2943,2944,2945,2946,2948,2949,2950,2951,2956,2957,2958,2959,2963,2964,2965,2966,2969,2970,2971,2972,2973,2975,2976,2981,2982,2983,2984,2985,2986,2988,2989,2990,2991,2993,2994,2995,2996,2997,3002,3004,3007,3008,3009,3010,3011", "uncoveredLines": "572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,593,594,595,596,597,598,599,600,601,602,605,606,607,608,609,610,611,612,613,614,615,617,618,619,620,621,622,623,631,668,713,716,798,799,800,801,802,805,807,808,809,812,1323,1432,1435,1436,1437,1438,1440,1542,1560,1561,1663,1687,1688,1755,1756,1757,1758,1759,1761,1762,1778,1779,1780,1782,1783,1784,1847,1848,1849,1865,1894,1968,1969,1972,1973,1976,1977,1980,1981,1989,1997,1998,2000,2024,2025,2026,2027,2028,2075,2076,2077,2078,2141,2146,2151,2152,2153,2154,2155,2157,2215,2292,2314,2315,2318,2319,2320,2322,2323,2327,2328,2421,2580,2603,2645,2648,2649,2652,2653,2654,2793,2794,2796,2797,2798,2799,2800,2801,2802,2804,2805,2806,2807,2808,2810,2811,2812,2813,2815,2816,2818,2819,2820,2821,2822,2823,2824,2825,2826,2828,2829,2850,2998,2999,3000,3015,3016,3017,3018,3019,3020,3021,3022,3025,3026,3027,3029,3030,3031,3032,3036,3037,3038,3040,3041,3044,3047,3048"}, "OpportunitySyncEvent": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "OpportunitySyncService": {"coverageRate": "0.17", "coveredLines": "2,3,4,5,11,17", "uncoveredLines": "7,18,19,23,24,25,26,27,29,30,31,32,33,34,35,36,38,41,42,43,44,45,46,47,51,52,56,57,58"}, "OpportunityTagController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityTagSelector": {"coverageRate": "0.36", "coveredLines": "10,11,14,15", "uncoveredLines": "2,3,6,7,18,19,36"}, "OpportunityTagService": {"coverageRate": "0.86", "coveredLines": "4,5,8,9,25,26,29,30,37,38,39,40,43,44,45,46,49,50,51,52,53,54,55,58,59,60,61,62,63,66,69,70,73,74,75,76,81,82,83,85,86,88,89,90,91,92,93,95,99,100,101,102,103,105", "uncoveredLines": "12,13,14,17,18,21,22,33,34"}, "OpportunityTagTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "OpportunityTeamMemberSelector": {"coverageRate": "0.50", "coveredLines": "2,3,32,33,35,36,39,42,46,47,48,49,51,53", "uncoveredLines": "6,7,10,11,12,13,16,19,21,22,23,24,26,29"}, "OpportunityTeamMemberTrigger": {"coverageRate": "1.00", "coveredLines": "5", "uncoveredLines": ""}, "OpportunityTeamMemberTriggerDispatcher": {"coverageRate": "0.62", "coveredLines": "6,7,8,10,11,12,13,14,30,31,32,33,34", "uncoveredLines": "16,20,21,22,23,24,26,36"}, "OpportunityTrendDTO": {"coverageRate": "1.00", "coveredLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "uncoveredLines": ""}, "OpportunityTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "OpportunityTriggerDispatcher": {"coverageRate": "0.78", "coveredLines": "2,3,4,5,6,7,8,10,11,12,13,14,19,20,21,22,23,28,29,30,31,32,37,38,39,40,41,46,47,48,49,50,52,55,56,57,58,59", "uncoveredLines": "16,25,34,43,61,64,65,66,67,68,70"}, "OpportunityWarningSignBatchCount": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityWarningSignBatchResult": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityWarningSignDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OpportunityWithSharingSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7", "uncoveredLines": ""}, "Opportunity_PartnershipTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,15,16,17,18,19,24,25,26,27,28", "uncoveredLines": "12,21,30"}, "OrgUnitController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OrgUnitDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,14,15,16,17,18", "uncoveredLines": ""}, "OrgUnitSelector": {"coverageRate": "0.25", "coveredLines": "2,3", "uncoveredLines": "6,7,10,11,14,15"}, "OrgUnitService": {"coverageRate": "0.87", "coveredLines": "9,10,13,14,15,18,19,20,23,24,25,26,28", "uncoveredLines": "4,5"}, "OrgWideEmailAddressSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "OrganizationInfo": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,10,11", "uncoveredLines": ""}, "OrganizationalUnitSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,10,11", "uncoveredLines": ""}, "OtherAuthorisedCoachBeforeDeleteHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OtherAuthorisedCoachBeforeInsertHandler": {"coverageRate": "1.00", "coveredLines": "8,9,12,13,14", "uncoveredLines": ""}, "OtherAuthorisedCoachBeforeUpdateHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "OtherAuthorisedCoachListService": {"coverageRate": "0.36", "coveredLines": "2,9,10,11,12,13,16,17,18,21,22,23,24,25,27,28,29,32,33,34,35,38,39,40,44,45,49,50,51,52,53,54,55,56,57,58,59,61,69,70,71,73,74,75,77,78,79,80,81,82,85,88,91,92,93,96,97,98,99,101,105,106,107,108,110,111", "uncoveredLines": "64,116,117,118,119,120,121,122,123,127,128,129,130,131,133,134,135,136,138,142,143,144,145,146,147,150,151,152,155,156,157,160,161,162,163,164,165,166,168,169,170,172,173,175,177,180,181,182,183,185,186,187,190,191,193,194,195,197,198,199,201,203,204,205,206,209,210,211,212,213,214,217,220,222,225,228,229,230,232,233,238,239,242,244,245,248,249,250,251,252,255,258,259,260,261,262,263,266,267,268,271,272,273,276,277,278,279,280,281,282,283,285,286,289,290,291"}, "OtherAuthorisedCoachListTrigger": {"coverageRate": "1.00", "coveredLines": "6", "uncoveredLines": ""}, "OtherContractDTO": {"coverageRate": "1.00", "coveredLines": "25,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "uncoveredLines": ""}, "PEE_AggregateResultProxy": {"coverageRate": "0.60", "coveredLines": "2,9,10,11,14,15", "uncoveredLines": "4,5,6,7"}, "PEE_Constants": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PEE_Currency": {"coverageRate": "1.00", "coveredLines": "11,12,13,14,17,18,19,20", "uncoveredLines": ""}, "PEE_ExpenseAttachment": {"coverageRate": "0.79", "coveredLines": "27,30,31,32,33,34,35,37,38,39,40", "uncoveredLines": "26,29,36"}, "PEE_ExpenseAttachmentController": {"coverageRate": "0.63", "coveredLines": "3,8,10,16,18,20,22,24,26,32,34,36,38,42,43,44,45,46,53,54,55,62,63,67,68,69,72,74,77,79,80,83,86,88,89,92,96,97,98,101,105,109,110,112,113,114,115,116,119,120,122,126,127,128,129,130,131,132,135,147,150,151,152,153,155,159,160,164,165,170,171,172,173,174,175,178,179,182,186,190,191,194,196,197,199,203,204,205,208,210,211,212,216,217,218,219,220,221,224", "uncoveredLines": "5,7,13,15,17,19,21,23,29,31,33,35,39,47,48,56,57,70,84,106,111,117,118,121,125,143,146,148,149,156,161,166,167,168,169,176,177,180,181,183,184,185,187,188,193,198,200,202,206,207,209,213,214,225,226,227,228"}, "PEE_ExpenseAttachmentSelector": {"coverageRate": "0.36", "coveredLines": "2,3,6,9,21,41,42,45,48,49,50,53,65,70,71,83", "uncoveredLines": "25,26,27,28,29,31,32,33,34,35,36,37,38,40,46,47,51,55,56,58,61,62,68,69,73,85,90,91,103"}, "PEE_ExpenseAttachmentService": {"coverageRate": "0.74", "coveredLines": "3,5,11,12,13,14,15,16,17,19,21,22,25,26,27,28,29,30,32,34,37,38,40,41,42,44,47,48,51,52,53,54,55,56,62,63,64,65,66,68,72,73,74,77,78,79,80,83,84,86,90,92,93,94,95,96,97,98,100,101,103,104,105,106,108,109,110,111,115,116,117,118,121,124,128,129,132,133,134,135,138,141,144,145,147,148,149,152,154,157,158,159,161,162,165,166,167,168,169,170,171,175,176,179,180,181,182,183,184,186", "uncoveredLines": "6,7,8,9,10,20,23,24,31,35,36,45,46,49,50,57,58,59,60,70,71,76,82,85,87,88,89,91,99,102,113,122,123,127,130,136,137,143"}, "PEE_ExpenseCurrencyController": {"coverageRate": "1.00", "coveredLines": "3,6,7,8,9,10,15,16,17,18,19,24,25,26,27,28", "uncoveredLines": ""}, "PEE_ExpenseCurrencySelector": {"coverageRate": "0.14", "coveredLines": "37,38,39,42", "uncoveredLines": "2,3,4,5,8,9,10,11,14,18,19,21,24,25,27,28,29,30,31,32,33,34,35,36,40"}, "PEE_ExpenseCurrencyService": {"coverageRate": "0.75", "coveredLines": "3,4,6,7,8,9,10,12,15,16,17,18,19,20,21,23,26,27", "uncoveredLines": "5,11,13,22,24,28"}, "PEE_ExpenseEmailHandler": {"coverageRate": "0.96", "coveredLines": "3,5,7,9,11,13,15,16,17,20,21,22,23,24,25,26,29,30,32,34,35,36,39,40,41,42,45,46,47,50,51,54,55,60,63,64,65,66,67,68,69,73,74,75,76,77,78,79,80,83,86,87,88,89,90,91,92,93,94,95,96,97,99,102,103,104,105,106,107", "uncoveredLines": "56,57,58"}, "PEE_ExpenseEntryController": {"coverageRate": "0.61", "coveredLines": "3,6,9,12,15,18,21,23,25,30,32,38,40,46,48,54,56,58,60,62,66,67,70,73,74,106,114,115,116,119,120,121,122,123,161,164,165,172,173,174,175,176,177,215,217,218,220,221,227,228,229,230,231,232,240,244,248,249,253,255,256,257,258,261,262,263,264,266,267,268,269,270,271,279,283,284,285,289,290,291,292,293,294,296,297,300,301,302,304,305,306,307,308,313,315,322,323,324,326,330,331,332,334,335,336,338,339,342,343,344,345,346,347,349,351,352,353,354,355,356,358,359,361,363,364,366,367,368,369,373,374,377,379,380,382,383,384,385,386,387,388,389,390,391,392,393,394,395,406,407,408,409,410,411,412,413,415,416,417,432,433,435,439,445,446,447,448,449,450,451,452,453,454,457,459,463,464,465,468,470,473,476,477,480,485,486,492,493,495,496,497,498,499,501,502,504,505,510,511,512,515,517,519,521,522,524,525,526,528,529,530,531,533,534,535,536,537,538,539,542,543,544,546,547,548,549,550,551,552,557,559,566,567,568,569,570,571,572,578,579,580,583,584,585,586,587,588,589,590,594,595,596,599,600,601,603,604,605,607,608,609,612,613,614,616,617,618,619,620,622,623,627,628,635,639,640,641,642,644,645,650,652,655,656,657,658,661,662,665,668,669,670,706,711,712,714,715,716,717,718,723", "uncoveredLines": "5,7,13,27,33,36,39,42,45,49,52,53,86,89,94,95,96,97,99,100,101,102,103,109,117,140,143,144,145,150,151,152,153,155,156,157,158,159,166,167,196,198,199,201,202,203,210,212,214,219,222,223,225,226,233,234,236,237,238,239,241,242,243,247,250,254,259,260,272,273,274,276,277,281,282,286,288,298,309,310,311,312,316,317,318,319,320,325,327,328,333,337,341,348,357,360,362,365,371,372,375,376,396,397,398,399,400,401,402,405,419,420,421,422,423,424,426,427,428,429,430,431,434,436,440,441,442,443,444,458,461,462,466,467,469,475,478,481,482,483,484,487,488,490,491,500,503,506,507,509,513,514,518,520,523,532,541,545,554,555,556,558,560,561,563,564,573,574,576,581,582,592,593,624,629,630,631,636,646,647,648,663,726,727,729,730,731,732,733,734,740,741"}, "PEE_ExpenseLimitRateController": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "3,6,9,10,11,12,14,15,16,19,21"}, "PEE_ExpenseLimitRateService": {"coverageRate": "0.85", "coveredLines": "3,9,10,11,13,14,15,16,17,20,23", "uncoveredLines": "5,6"}, "PEE_ExpenseProjectController": {"coverageRate": "0.81", "coveredLines": "3,6,7,8,9,10,15,16,17,18,19,33,34,35,36,37,42,43,44,45,46", "uncoveredLines": "24,25,26,27,28"}, "PEE_ExpenseProjectSelector": {"coverageRate": "0.07", "coveredLines": "113,114,115,118", "uncoveredLines": "2,3,4,8,13,18,19,20,21,23,25,28,29,30,31,34,35,36,40,41,44,45,46,49,50,51,65,66,67,70,71,72,73,76,77,79,82,83,85,92,95,96,97,98,100,101,102,103,106,109,110,116"}, "PEE_ExpenseProjectService": {"coverageRate": "0.64", "coveredLines": "3,4,6,7,8,9,10,12,13,16,17,18,19,20,22,23,45,46,47,49,50,51,52,54,55,58,59,60,61,62,63,64,67,69,72,73,74,76,77,78,79,81", "uncoveredLines": "26,27,28,29,30,31,32,33,34,35,36,37,39,42,43,44,48,56,57,65,66,70,71,75"}, "PEE_ExpenseSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "PEE_ExpenseService": {"coverageRate": "1.00", "coveredLines": "3,5,6", "uncoveredLines": ""}, "PEE_ExpenseSubmitEmailService": {"coverageRate": "0.85", "coveredLines": "7,9,10,11,13,17,18,19,20,21,22,23,26,27,28,29,30,31,34,35,36,37,38,39,40,41,42,43,44,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,70,71,72,73,74,76,82,83,86,87,88,94,95,96,97,99,100,101,102,103,104,105,106,107,108,111,112", "uncoveredLines": "6,8,14,15,16,24,33,45,46,57,58,68,89"}, "PEE_ExpenseTaxController": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "3,6,9,10,11,12,14,15,16,18,19"}, "PEE_ExpenseTaxService": {"coverageRate": "0.82", "coveredLines": "3,9,10,12,13,14,15,17,20", "uncoveredLines": "5,6"}, "PEE_ExpenseTypeController": {"coverageRate": "1.00", "coveredLines": "3,6,7,8,9,10,15,16,17,18,19", "uncoveredLines": ""}, "PEE_ExpenseTypeSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,4,5,6,9,10,11,12,15,19,20,22"}, "PEE_ExpenseTypeService": {"coverageRate": "1.00", "coveredLines": "3,4,6,7,8,9,10,12,15,16,17,18,19,20,21,23", "uncoveredLines": ""}, "PEE_ExpenseValidationService": {"coverageRate": "0.73", "coveredLines": "2,3,6,7,8,9,10,11,15,16,17,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,44,45,46,47,48,49,50,52,56,57,58,62,63,64,66,67,72,73,75,76,77,81,82,86,87,88", "uncoveredLines": "18,19,20,33,35,36,37,51,53,54,55,68,69,70,78,79,83,92,93,94"}, "PEE_Project": {"coverageRate": "0.56", "coveredLines": "15,16,17,18,19", "uncoveredLines": "13,14,22,23"}, "PEE_RegionService": {"coverageRate": "0.80", "coveredLines": "3,5,6,7,11,12,13,17", "uncoveredLines": "8,14"}, "PEE_SubProject": {"coverageRate": "0.38", "coveredLines": "11,12,13", "uncoveredLines": "8,9,10,16,17"}, "PEE_Type": {"coverageRate": "1.00", "coveredLines": "9,10,11", "uncoveredLines": ""}, "PEE_Utils": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "PEE_WithoutSharingSelector": {"coverageRate": "0.70", "coveredLines": "29,30,31,50,56,57,58,59,61,64,65,77,78,81,86,87", "uncoveredLines": "2,3,4,23,68,69,72"}, "PMA_AccountTenureBatchJob": {"coverageRate": "1.00", "coveredLines": "6,9,10,13,14,17,18,19,20,22,23,24,25,27,28,29,30,33,34,38,39", "uncoveredLines": ""}, "PMA_AccountTenureDeleteBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_BaseCallout": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_Constants": {"coverageRate": "1.00", "coveredLines": "10,11,12,13,14,15,16,17,18,22,32,34", "uncoveredLines": ""}, "PMA_HolidayDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_InvoicedBurnDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveManagementController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveManagementSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveManagementService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveRequestComparator": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveRequestSortColumnEnum": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveRequestsDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_LeaveRequestsResponse": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_MismatchedAssignmentDTO": {"coverageRate": "1.00", "coveredLines": "3,5,7,9,11,13,16,18,20,22,24,26,28,30,32,35,37,39,41,43,45,47,49,51,53,55,57,59,61,63,64,65,66,67,68,70,71,72,73,74,75,76,77,78,79,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121", "uncoveredLines": ""}, "PMA_ProjectAccessController": {"coverageRate": "0.87", "coveredLines": "2,3,4,5,6,7,8,14,16,20,21,24,26,30,31,32,33,34,37,38,39,50,51,52,55,58,60,61,64,65,68,71,73", "uncoveredLines": "27,40,43,45,46"}, "PMA_ProjectAlertCardController": {"coverageRate": "0.97", "coveredLines": "13,14,15,16,20,21,24,26,30,31,32,35,37,38,41,42,44,45,46,47,49,50,51,53,55,56,57,58,59,61,64,65,66,67,68,69,70,71,72,75,79,80,81,83,84,87,88,92,94,95,99,102,103,107,108,109,112,114,117,118,121,125,126,128,131,132,133,134,138,139,145,146,147,148,149,150,151", "uncoveredLines": "73,135"}, "PMA_ProjectBurnController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectBurnSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectBurnService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectBurnSummaryDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectCostCallout": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectCostController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectCostSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,11,12,15,20,21,48,52,53,56,60,61,84,85,86,91,92,93,96,98,99,101,104,105,108,113,114,117,118", "uncoveredLines": ""}, "PMA_ProjectCostService": {"coverageRate": "0.98", "coveredLines": "6,7,8,9,12,13,14,15,17,18,19,20,21,24,25,26,29,30,31,32,35,36,37,40,43,44,45,46,47,48,49,50,51,52,55,56,57,58,59,60,61,64,65,67,70,71,72,73,74,77,80,81,82,83,84,85,88,91,92,93,95,96,97,98,100,101,102,105,106,108,109,110,111,112,114,115,121,122,125,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,149,150,151,154,157,162,163,164,165,166,167,168,170,171,173,175,178,179,180,182,185,186,187,189,192,193,194,195,196,197,198,200,203,204,205,206,207,209,210,211,214,217,218,219,220,229,230,231,232,233,234,236,239,240,241,242,245,246,247,248,249,251,254,257,258,259,260,261,263,265,268,269,270,271,272,273,274,276,277,280,283,284,285,286,287,288,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,318,320,321,322,323,325,328,329,330,331,333,336,337,338,339,342,345,348,349,350,351,352,353,354,355,357,361,362,363,366,367,368,371,372,373,374,375,376,377,379,382,383,384,385,388,389,392,393,394,395,396,399,402,403,404,405,407,410,411,412,413,415,418,419,420,421,422,425", "uncoveredLines": "27,38,222,223,224,226,340"}, "PMA_ProjectLeaveCallout": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectLeaveController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectMarginCallout": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectMarginController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectMonthlyBurnDataDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectRevenueCallout": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectRevenueController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_ProjectUtils": {"coverageRate": "0.80", "coveredLines": "2,4,5,12,13,16,17,18", "uncoveredLines": "8,9"}, "PMA_RotationPlanQueueJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_RotationPlanScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_SortOrderEnum": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMA_StaffingRotationPlanController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PMTimecardManagementController": {"coverageRate": "0.69", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,21,25,27,31,33,35,37,39,70,72,74,76,114,116,118,120,124,194,195,196,197,198,199,200,201,202,208,209,210,211,222,223,232,233,234,235,236,240,260,261,262,263,265,267,268,269,270,373,374,375,376,380,381,382,385,386,387,389,391,392,393,395,396,397,398,417,418,419,420,421,422,423,424,427,430,433,434,435,436,437,438,440,441,443,446,447,449,450,451,455,456,457,458,459,461,462,463,464,467,472,473,474,475,476,477,478,480,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,507,508,509,511,512,513,514,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,546,547,548,549,551,552,556,557,560,561,562,563,566,567,568,571,572,573,575,576,577,578,579,580,582,585,586,587,588,589,590,592,593,594,596,599,605,606,607,608,609,611,612,614,615,616,617,618,619,620,622,623,624,625,628,629,632,633,634,635,636,637,638,639,640,698,699,700,701,702,703,704,705,707,708,709,710,712,715,716,717,718,719,720,721,722,723,724,727,730,731,732,733,734,735,740,741,742,759,760,761,763,764,767,768,770,773,774,776,777,779,780,782,783,785,786,789,792,793,794,795,796,797,799,800,803,805,806,807,809,810,811,813,816,820,821,822,823,824,825,826,828,830,831,832,835,836,838,839,840,841,842,844,845,846,850,851,853,855,858,859,860,861,862,863,864,865,866,867,868,883,884,885,886,887,888,889,890,891,892,893,900,904,905,906,907,908,911,940,945,947,948,949,951,952,953,954,955,958,959,961,981,982,983,984,985,987,990,991,992,994,995,996,998,999,1001,1002,1006,1007,1008,1013", "uncoveredLines": "215,216,217,218,227,228,237,238,244,245,246,247,248,249,251,255,256,271,272,273,274,275,277,278,279,280,281,282,284,289,290,291,292,293,295,297,298,299,300,301,302,303,304,305,307,308,309,310,311,312,314,319,320,321,325,326,327,328,329,330,331,332,333,334,336,337,338,340,341,342,344,345,349,350,359,361,362,364,368,369,370,377,401,402,404,405,406,408,409,411,413,414,425,452,468,553,558,597,645,646,647,648,650,651,652,655,656,657,658,659,661,662,663,664,665,666,667,668,669,670,673,674,675,676,680,682,685,687,689,691,693,695,736,745,746,747,748,749,750,751,755,756,771,814,871,872,873,874,875,878,879,896,897,909,915,916,917,918,919,920,921,924,925,927,931,932,936,937,964,965,966,967,969,970,971,972,975,976,978,1009,1010"}, "POContractDTO": {"coverageRate": "1.00", "coveredLines": "22", "uncoveredLines": ""}, "PRS_AutoQuoteCallbackClass": {"coverageRate": "1.00", "coveredLines": "3,4,6,7,8,9,10,11,15,16,17,18", "uncoveredLines": ""}, "PRS_OpportunityAutoQuoteCreationSelector": {"coverageRate": "0.87", "coveredLines": "2,3,6,7,19,23,24,31,32,35,36,39,43,44,60,68,69,72,75,76", "uncoveredLines": "64,65,70"}, "PRS_OpportunityAutoQuoteCreationService": {"coverageRate": "0.67", "coveredLines": "2,3,4,9,10,11,12,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,44,45,46,47,48,50,51,52,53,54,55,56,60,61,62,63,64,65,66,67,71,72,73,74,75,76,81,95,96,97,98,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133,134,139,140,141,142,150,154,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,178,179,180,188,189,190,191,192,193,198,199,200,201,223,224,225,226,227,228,229,231,236,237,238,239,240,241,242,243,244,246,247,248,249,253,256", "uncoveredLines": "77,78,82,83,84,85,86,90,91,136,137,145,146,148,151,152,155,181,182,183,202,203,204,205,206,207,208,211,212,213,214,216,217,250,254,265,266,267,268,269,270,272,273,274,275,276,277,278,279,280,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,299,304,305,310,311,314,315"}, "PRS_OpportunityAutoQuoteEventTrigger": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "PRS_QuoteAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "PRS_QuoteAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "PRS_QuoteBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "PRS_QuoteBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,9", "uncoveredLines": ""}, "PRS_QuoteItemAfterDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PRS_QuoteItemAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "PRS_QuoteItemAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,10,11", "uncoveredLines": ""}, "PRS_QuoteItemBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "PRS_QuoteItemBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "PRS_QuoteItemTrigger": {"coverageRate": "1.00", "coveredLines": "8,9", "uncoveredLines": ""}, "PRS_QuoteItemTriggerDispatcher": {"coverageRate": "0.74", "coveredLines": "7,8,9,10,11,13,14,15,16,17,23,24,25,26,27,43,44,45,46,47,49,53,54,55,56,57", "uncoveredLines": "19,29,33,34,35,36,37,39,59"}, "PRS_QuoteTrigger": {"coverageRate": "1.00", "coveredLines": "8,9", "uncoveredLines": ""}, "PRS_QuoteTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "7,8,9,10,11,12,13,14,15,21,22,23,24,25,30,31,32,33,34,40,41,42,43,44", "uncoveredLines": "17,27,36,46"}, "PRS_QuoteTriggerSelector": {"coverageRate": "0.88", "coveredLines": "2,3,8,9,17,18,19", "uncoveredLines": "4"}, "PRS_QuoteTriggerService": {"coverageRate": "0.89", "coveredLines": "2,7,8,19,20,26,27,28,29,31,32,33,34,37,38,39,40,51,54,55,56,57,59,60,61,62,63,68,69,70,71,73,77,78,81,83,84,86,89,90,91,92,93,94,95,98,99,101,104,105,106,107,109,112,113,116,119,120,123,124,125,126,127,128,129,132,133,135,136,137,138,140,145,146,147,149,150,151,152,153,154,155,156,157,158,165,166,170,171,175,176,177,178,179,180,185,186,190,191,195,196,200,201,202,203,204,209,210,211,212,213,216,219,220,221,222,223,224,227,230,231,232,234,235,236,237,241,242,245,247,248,249,250,251,257,258,259,260", "uncoveredLines": "11,12,15,16,41,42,43,44,45,47,110,121,130,172,192,197,253"}, "PRS_SendQuoteToCollaborationController": {"coverageRate": "1.00", "coveredLines": "9,10,14,15", "uncoveredLines": ""}, "PRS_SendQuoteToCollaborationSelector": {"coverageRate": "0.56", "coveredLines": "2,3,10,11,14,32,33,36,37", "uncoveredLines": "18,19,24,25,26,27,28"}, "PRS_SendQuoteToCollaborationService": {"coverageRate": "1.00", "coveredLines": "7,11,12,13,16,17,18,19,21,22,25,26,27,28,29,30,31,34,35", "uncoveredLines": ""}, "PRS_StackedDiscountsController": {"coverageRate": "1.00", "coveredLines": "4,5,10,11,16,17", "uncoveredLines": ""}, "PRS_StackedDiscountsSelector": {"coverageRate": "0.77", "coveredLines": "2,3,6,11,12,13,16,17,20,21", "uncoveredLines": "18,22,23"}, "PRS_StackedDiscountsService": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,8,11,12", "uncoveredLines": ""}, "PSAAccountOperationWithSubscriberTrigger": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,11,12", "uncoveredLines": ""}, "PSAConstants": {"coverageRate": "1.00", "coveredLines": "3,4,5,7,8,11,13", "uncoveredLines": ""}, "PSAContactAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "PSAContactAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": ""}, "PSAContactAfterUndeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PSAContactAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,9,10,11,12", "uncoveredLines": ""}, "PSAContactBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,7", "uncoveredLines": ""}, "PSAContactBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": ""}, "PSAContactBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,11", "uncoveredLines": ""}, "PSAContactSelector": {"coverageRate": "0.71", "coveredLines": "2,4,5,6,14,15,16,18,19,31,36,37,38,49,54,55,98,99,102,103,104,107,108,109,111,112,113,116,117,120,121,124,125,128,129,132,133,147,151,152,165,169,170,173,174,177,178,179,195,200,201,213,218,219,222,223,224,251,256,257,260,261,262,265,270,271,272,273,274,276,279,280,283,284,291,292,302,303,304,305,348,349,352,359,363,364,401,405,411,412,421,422,423,427,428,431,432,435,436,470,471,474,475,476,477,489,490,492,558,559,578,579,582,586,587,590,591,592,595,596,599,600,603,604", "uncoveredLines": "10,11,58,59,60,68,69,70,76,77,78,79,80,82,83,92,93,95,415,416,418,439,440,466,494,498,507,508,510,513,514,526,527,528,529,530,532,534,538,547,548,549,550,551,553,562,563,571,572,573"}, "PSAContactService": {"coverageRate": "0.99", "coveredLines": "2,4,5,6,8,12,13,14,17,18,19,20,21,24,25,27,29,30,31,32,33,34,35,39,40,41,43,47,48,49,50,51,52,53,54,55,62,63,64,65,66,67,68,70,71,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,95,96,98,99,104,105,107,108,110,111,113,114,118,119,120,121,122,123,125,126,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,147,153,154,158,159,160,161,162,164,165,166,167,169,173,174,175,176,177,178,180,181,182,183,185,189,190,193,194,195,196,199,200,203,204,207,208,209,212,213,216,217,218,219,220,222,223,224,226,227,228,233,234,235,236,237,238,239,240,244,245,249,250,252,253,254,256,257,262,263,264,265,266,269,270,271,272,273,275,279,280,281,282,283,286,289,290,292,294,295,297,298,299,305,306,307,308,309,313,314,317,318,319,322,323,324,327,328,329,330,334,335", "uncoveredLines": "57,255"}, "PSAEmail": {"coverageRate": "0.63", "coveredLines": "2,4,5,6,8,9,10,11,12,13,17,19,22,23,24,25,26,27,28,29,30,31,34,35,40,41,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,69,72,73,75,76,77,79,80,83,89,91,92,94,97,98,99,100,104,105,106,107,108,111,112,113,114,115,116,118,120,121,122,124,131,136,283,284,285,286,287,288,289,290,291,294,295,296,297,299,302,303,304,305,312,313,314,315,316,317,318,319,320,322,323,325,326,328,329,332,333,334,335,336,337,339,340,341,342,343,344,346,347,348,349,350,351,353,354,355,356,357,358,360,361,362,365,369,370,371,372,373,374,375,376,377,378,379,392,393,394,395,396,397,399,403,404,407,408,409,410,411,412,413,414,415,418,419,420,421,424,425,426,427", "uncoveredLines": "15,36,37,84,85,86,90,125,126,128,134,139,140,141,142,143,146,147,148,149,150,151,164,167,168,169,170,171,172,173,175,177,179,180,183,184,185,186,187,188,190,191,199,200,203,206,207,208,209,210,211,213,215,216,217,218,219,220,222,223,227,228,229,230,231,232,236,237,238,240,242,245,246,247,248,249,250,252,254,255,256,257,258,260,261,265,266,267,268,269,270,274,275,276,278,280,307,382,383,384,386,387,398,400,401"}, "PSAOpportunityOperationWithSubscriberTrigger": {"coverageRate": "1.00", "coveredLines": "6,7,8,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "uncoveredLines": ""}, "PSAUserAfterInsertHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9", "uncoveredLines": ""}, "PSAUserAfterUpdateHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9,10,11,12,13,14,15", "uncoveredLines": ""}, "PSAUserBeforeUpdateHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9,10,11,12", "uncoveredLines": ""}, "PSAUserService": {"coverageRate": "1.00", "coveredLines": "12,13,14,15,16,17,21,22,23,24,27,28,29,30,31,32,33,35,36,39,40,41,44,45,46,51,52,55,56,57,59,64,65,66,67,68,69,73,74,78,79,80,82,83,84,85,86,87,90,91,93,94,98,99,100,101,102,107,108,109,110,112,115,116,118,119,120,121,125,128,129,131,132,135,139,140,141,142,143,144,146,148,149,150,151,155,156,159,161,162,163,164,165,170,171,174,176,178,180,181,182,185,188,189,190,191,192,194,197,198,199,200,203,204,205,206,207,208,209,210,212,213,215,218,219,220,221,222,223,224,225,226,227,228,230,233,236,237,238,239,240,242,245,246,247,248,249,251,254,255,256,257,258,259,260,261,262,263", "uncoveredLines": ""}, "PackageLicenseManagementService": {"coverageRate": "0.89", "coveredLines": "7,8,9,10,12,14,15,16,17,18,21,24,25,26,27,28,30", "uncoveredLines": "3,4"}, "PackageLicenseSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "PartnershipFundingController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PartnershipFundingService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PartnershipsController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PartnershipsSelector": {"coverageRate": "0.63", "coveredLines": "2,12,13,15,16", "uncoveredLines": "5,6,9"}, "PartnershipsService": {"coverageRate": "0.97", "coveredLines": "5,6,7,10,11,12,19,20,23,24,25,26,29,30,33,34,38,39,40,41,42,43,44,45,46,47,49,52,55,56,57,58,63,64,65,66,69,70,72,74,76,77,82,83,84,85,88,91,92,93,94,95,96,98,99,101,102,106,109,110,111,112", "uncoveredLines": "15,16"}, "PeopleEventSelector": {"coverageRate": "0.67", "coveredLines": "2,3,4,5,7,10,11,12,13,15", "uncoveredLines": "18,19,20,21,23"}, "PeopleEventTrigger": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2"}, "PermissionContext": {"coverageRate": "0.94", "coveredLines": "2,4,5,8,11,12,13,14,15,18,21,22,23,24,25,28", "uncoveredLines": "6"}, "PermissionControlSelector": {"coverageRate": "1.00", "coveredLines": "2,5,6,9,14,15,18,21,22,23,24,25,26,28,31,32", "uncoveredLines": ""}, "PermissionSetAssignService": {"coverageRate": "0.87", "coveredLines": "2,3,4,5,8,9,10,11,13,14,15,18,19,20,21,25,26,29,31,32,33,36,37,38,39,43,44,46,48,49,51,55,56,57,58,59,60,61,64,65,68,69,70,71,73,77,78,79,80,81,82,83,84,86,89,90,91,92,93,94,95,112,113,114,116,117,118,120,123,124,125,128,129,130,131,132,133,134,137,140,141,142,147,148,149,151,152,155,156,159,160,161,162,166,167,168,173,176,177,178,179,180,184,185,190,191,192,193,194,198,199,204,205,206,207,208,209,210,211,212,214", "uncoveredLines": "100,101,102,104,105,106,108,126,135,153,157,169,170,171,181,187,195,201"}, "PermissionSetAssignmentSelector": {"coverageRate": "0.85", "coveredLines": "2,3,6,7,10,11,14,26,27,30,34,35,38,39,42,43,46,47,50,51,54,55,58,59,62,63,68,69", "uncoveredLines": "18,19,22,64,70"}, "PermissionSetGroupComponentSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3"}, "PermissionSetLicenseAssignSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "PermissionSetSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,10,11,14,15", "uncoveredLines": ""}, "PersonAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "PersonBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7", "uncoveredLines": ""}, "PersonFieldHistorySelector": {"coverageRate": "0.43", "coveredLines": "2,3,6,10,11,16,17,18,23,24,29,30,31,32,87,88,89,102,103,106,110,111", "uncoveredLines": "37,38,42,44,45,50,51,55,57,58,59,63,64,67,72,73,76,81,82,83,93,94,97,118,119,122,127,128,131"}, "PersonFieldHistoryService": {"coverageRate": "0.86", "coveredLines": "2,4,7,8,11,12,13,14,15,17,18,19,21,22,25,26,30,31,32,33,34,35,37,38,40,44,45,47,48,52,53,54,55,56,57,58,60,61,62,64,65,66,67,68,69,70,72,74,78,89,90,94,95,96,97,98,99,100,102,103,104,105,106,107,108,110,112,113,114,115,116,117,118,120,122,126,138,139,142,143,144,145,148,149,150,151,153,157,158,159,160,161,163,164,165,166,168,169,171,174,175,176,177,178,179,180,181,182,184,186,189,190,191,192,194,195,197,198,199,200,204,206,209,210,211", "uncoveredLines": "41,42,75,76,79,80,81,82,83,84,86,123,124,127,128,129,130,131,132,134"}, "PersonSelector": {"coverageRate": "0.65", "coveredLines": "2,3,4,8,9,10,14,15,18,19,20,44,45", "uncoveredLines": "24,25,32,33,36,37,40"}, "PersonService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,9,10,12,15,16,19,20,22,23,24,26,27,29,30,32,35,36,37,38,40,41,42,43,44,45,47,48,50,52,54,57,58,60,61,62,63,64,65,67,68,69,70,71,74,76,77,78,79,81,82,83,84,85,86,87,88,89,90,91,92,95,96,97,98,99,102,103,105,106,109,110,113,114,115,117,118,119,120,121,122,123,126,127,128,131,132,133,136,137,138,141,142,143,144,148,149,152,153,157,158,159", "uncoveredLines": ""}, "PersonTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "PersonTriggerDispatcher": {"coverageRate": "0.93", "coveredLines": "2,3,5,6,7,8,9,14,15,16,17,18,20", "uncoveredLines": "11"}, "PersonWithOpportunityCRDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,15,18,21,24,27,30", "uncoveredLines": ""}, "PopulateMiscAdjustIdOnRelatedBEI": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,10,11,12,15,16", "uncoveredLines": ""}, "PrepareForcetalkTestData": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PrepareTestDataForAutoShare": {"coverageRate": "0.84", "coveredLines": "18,19,22,23,24,25,26,27,29,30,32,35,36,37,40,41,42,43,44,45,47,50,52,53,54,55,56,59,61,62,63,64,65,66,67,70,72,73,74,75,76,77,78,81,82,83,85,86,87,88,89,90,91,92,93,94,95,96,97,98,101,111,113,114,115,116,117,118,119,122,124,125,126,127", "uncoveredLines": "105,106,108,130,132,133,134,135,136,139,140,141,142,144"}, "ProcessAbsenceApprovalJob": {"coverageRate": "0.70", "coveredLines": "2,6,7,10,11,14,15", "uncoveredLines": "12,16,17"}, "ProcessInstanceAction": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ProcessInstanceSelector": {"coverageRate": "0.67", "coveredLines": "2,3,10,15,16,19", "uncoveredLines": "24,25,32"}, "ProcessInstanceService": {"coverageRate": "0.84", "coveredLines": "2,7,8,9,12,13,14,15,17,18,19,20,21,22,25,26,27,30,33,34,35,36,38,39,45,48,49,50,51,56,59,60,61,63,64,65,73,76", "uncoveredLines": "40,41,52,53,67,68,74"}, "ProcessInstanceStepSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,11,12,15,20,21,24", "uncoveredLines": ""}, "ProcessInstanceStepStatus": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ProcessInstanceWorkItemSelector": {"coverageRate": "0.54", "coveredLines": "2,4,5,8,12,13,16", "uncoveredLines": "21,22,25,27,31,32"}, "ProcessorControl": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ProfileSelector": {"coverageRate": "0.90", "coveredLines": "2,3,6,7,11,12,15,21,24,25,28,29,32,33,34,36,37,40", "uncoveredLines": "43,44"}, "ProjTriggerDispatcher": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,6,8,9,10,11,12,17,18,19,20,21,26,27,28,29,30,35,36,37,38,39,41,44,45,46,47,48", "uncoveredLines": "14,23,32,50"}, "ProjectAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ProjectAfterInsertTriggerHandler": {"coverageRate": "0.86", "coveredLines": "5,6,7,10,11,12,13,14,15,16,17,21,25,26,29,30,31,32,33,37,38,42,43,44", "uncoveredLines": "18,22,23,45"}, "ProjectAfterUpdateTriggerHandler": {"coverageRate": "0.93", "coveredLines": "17,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,38,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,71,72,73,75,76,77,78,79,80,81,82,83,85,86,87,88,89,91,92,93,94,95,96,97,98,99,100,102,103,104,105,106,107,108,109,110,111,112,113,115,116,118,119,121,124,125,127,128,130,131,133,134,136,137,140,141,143,144,146,147,149,152,153,156,158,159,160,162,165,166,167,168,170,171,176,177,178,180,181,182,185,186,190,191,192,194,195,196,197,198,202,203,204,208,209,210,211,212,216,218,219,220,221,222,223,224,228,231,232,236,237,240,241,242,243,244,248,249,252,253,255,261,273,274,275,276,277,279,280,281,285,292,293,295", "uncoveredLines": "122,150,256,257,262,263,267,268,269,270,286,287"}, "ProjectBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,26,27,28", "uncoveredLines": ""}, "ProjectBeforeUpdateTriggerHandler": {"coverageRate": "0.99", "coveredLines": "2,3,4,7,8,9,10,11,12,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,40,41,42,43,44,45,50,51,52,53,58,59,60,61,62,67,68,69,70,71,72,73,74,77,78,79,83,84,86,87,88,89,93,96,97,98,99,100,105,106,107,108,109,112,113,114,120,121,122,123,124,125,126,127,132,133,134,135,136,137,138,143", "uncoveredLines": "144"}, "ProjectBillingController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ProjectCodeGenerationQueueableJob": {"coverageRate": "0.23", "coveredLines": "6,8,9,20,21,24,25,39,41,42,43,46,54,55,65,114,124", "uncoveredLines": "12,14,16,47,48,49,56,59,61,66,67,68,71,72,73,74,75,76,79,80,83,84,85,86,87,88,89,90,91,94,96,97,98,100,102,103,106,107,108,110,111,115,116,117,119,125,126,130,131,132,134,135,136,137,138,139,140"}, "ProjectController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ProjectControllerExtension": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ProjectDataPrepareService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,9,10,11,16,17,18,19,20,21,22,23,24,25,26,27,29,33,41,42,43,44,45,46,49,50,51,53,56,57,58,60,63,64,65,68,69,70,71,72,73,74,75,77,78,79,81,82,83,86,87,88,89,91,92,93,94,96,97,98,100,101,102,103,105,106,107,108,109,110,117,120,121,122,123,124,125,126,129,130,132,133,134,136,137,138,139,140,143,144,145,147,148,149,150,151,152,153,154,157,158,163,164,165,166,167,168,169,170,171", "uncoveredLines": ""}, "ProjectDeactivateReactivateService": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,12,13,17,18,21,22,25,26,29,30,33,34,37,38,41,42,45,46", "uncoveredLines": ""}, "ProjectFieldEnforce": {"coverageRate": "0.93", "coveredLines": "2,3,4,5,7,8,13,14,15,19,20,23,24,25,27,30,33,34,35,36,37,38,39,40,41,44,45", "uncoveredLines": "21,46"}, "ProjectFieldEnforceService": {"coverageRate": "0.93", "coveredLines": "2,7,14,16,17,18,19,20,21,22,23,24,25,30,40,41,42,43,44,45,46,47,48,50,51,54,56,57,58,59,60,61,63,64,66,67,71,72,74,75,76,77,78,79,81,82,85,86,87,89,90,94,95,97,98,99,100,102,106,107,111,112,113,114,115,118,119,121,122,123,124,127,128,129,131,132,133,138,139,143,144,145,146,152,153,154,157,158,164,165,166,167,168,170,175,179,180,181,182,183,184,185,186,187,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,207,208,211,212,213,214,215,216,222,223,224,225,226,227,228,229,233,234,236,237,240,241,242,244,245,246,248,249,250,251,252,253,256,258,259,260,261,262,263,264,266,267,268,269,270,290,291,292,293,294,295,301,308,309,310,311,312,313,314,320,321,322,323", "uncoveredLines": "172,173,254,272,273,275,276,277,279,282,283,285,326,327"}, "ProjectInitialLoadController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ProjectInvoicingController": {"coverageRate": "0.76", "coveredLines": "6,7,8,11,13,16,17,20,24,25,28,32,33,36,40,41,44,45,47,48,50,53,54,55,57,61,69,70,74,75,82", "uncoveredLines": "18,26,34,58,62,65,71,76,79,83"}, "ProjectLogSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "ProjectLogService": {"coverageRate": "1.00", "coveredLines": "5,6,7,11,12,13,16,17,18,19,20,21,22,26,27,31,32,33,34,35,36,37,38,40,42", "uncoveredLines": ""}, "ProjectOperationEventTrigger": {"coverageRate": "0.61", "coveredLines": "2,3,4,6,8,9,10,11,12,13,14", "uncoveredLines": "15,16,17,18,19,20,22"}, "ProjectPMIAChangeHandler": {"coverageRate": "0.95", "coveredLines": "2,3,4,5,6,7,8,9,10,12,13,18,19,20,21,24,25,27,28,29,30,31,35,36,37,38,40,41,44,45,46,50,52,53,56,57,64,65,66,67,68,69,73,74,81,82,84,86,87,88,95,96,97,98,99,102,105,106,107,108,111,114,115,116,118,119,120,123,126,127,128,129,131,134,135,136,137,138,139,142,143,145,146,148,149,150,151,152,153,156,157,159,161,164,165,166,167,168,169,170,171,172,173,174,175,176,179", "uncoveredLines": "58,59,75,76,89,90"}, "ProjectRegionLocationPredicate": {"coverageRate": "1.00", "coveredLines": "2,5,6,10,11,14,15,16,19,20,21,23", "uncoveredLines": ""}, "ProjectSelector": {"coverageRate": "0.79", "coveredLines": "2,3,4,5,7,8,15,39,40,41,63,72,73,76,77,80,81,84,85,86,90,91,92,93,95,98,99,102,106,107,113,121,122,129,135,136,137,150,155,156,157,185,198,199,200,212,213,214,215,218,221,222,225,226,227,230,235,236,239,240,283,284,292,296,297,300,301,307,308,309,310,311,314,315,321,322,323,324,325,326,327,333,334,335,343,360,364,369,370,371,380,383,384,385,386,388,389,390,391,392,393,394,397,400,401,412,416,417,418,421,426,427,428,447,450,497,498,499,500,509,519,522,527,528,529,547,550,555,556,559,560,561,569,570,573,575,579,580,581,596,598,603,604,607,608,609,612,617,618,619,634,644,645,648,649,650,674,675,682,683,687,690,691,695,696,707,710,711,715,716,719,723,724,727,728,729,737,758,759,778,782,783,801,805,806,809,810,813,814,825,826,829,833,834,835,838,843,844,859,860,863,864,871,872,875,879,880,896", "uncoveredLines": "20,21,24,25,26,34,68,69,190,191,194,195,204,205,206,207,209,303,304,455,456,457,458,459,462,463,481,482,489,490,491,639,640,641,652,656,657,670,678,679,742,743,746,750,751,754,817,818,821,847,848,852,854,855"}, "ProjectService": {"coverageRate": "0.87", "coveredLines": "2,22,23,24,25,26,27,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,68,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,107,108,109,112,113,114,115,116,117,120,121,122,123,124,125,126,127,129,132,133,134,135,136,137,138,139,140,143,144,145,147,148,149,150,152,153,157,158,159,160,163,164,165,166,167,168,169,170,171,172,173,174,176,177,179,180,181,182,185,186,188,189,192,193,195,196,198,199,201,202,204,205,207,208,210,211,216,217,218,222,223,224,226,227,228,229,230,231,234,235,236,238,239,241,242,243,245,246,247,249,250,251,253,258,262,263,264,265,266,268,269,270,271,272,273,274,276,279,280,281,282,283,284,304,307,308,309,310,311,312,314,315,316,318,319,322,323,325,326,329,330,332,333,334,335,337,338,342,344,346,347,348,349,352,353,356,362,365,366,367,368,370,371,372,373,374,378,379,381,382,383,385,386,387,388,389,390,391,392,394,395,396,397,398,403,404,405,406,411,412,413,414,415,417,418,423,424,427,430,431,435,437,440,441,443,444,445,446,450,453,454,455,456,459,462,463,465,466,467,468,469,471,472,476,479,480,481,482,484,487,488,495,496,497,498,499,500,501,504,505,506,508,511,512,513,515,516,520,521,522,523,527,528,529,530,531,534,535,537,538,539,540,542,543,544,548,551,552,553,555,556,557,559,560,561,562,563,564,566,569,574,575,576,577,579,580,582,583,588,589,590,591,592,595,598,599,600,601,603,604,605,606,607,608,609,612,613,618,619,620,621,622,623,625,627,628,631,632,633,635,636,638,643,644,645,646,647,648,650,655,656,657,658,659,661,662,665,671,672,673,674,675,678,679,683,684,685,686,687,688,689,691,692,694,695,701,702,703,705,706,707,708,709,710,711,712,713,716,717,719,720,723,724,725,726,727,729,730,731,732,733,735,736,737,739,740,742,743,745,752,753,754,755,756,757,759,760,772,773,774,775,780,781,782,783,784,785,786,794,795,800,801,802,803,805,806,807,808,809,810,812,815,819,820,821,822,823,824,825,826,827,828,829,832,833,834,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,860,861,863,866,867,869,872,875,878,881,882,885,889,897,905,906,909,913,916,919,920,921,922,923,924,925,926,927,929,930,931,932,934,935,937,938,939,942,943,944,945,946,947,949,951,955,956,957,958,961,963,964,965,969,972,973,974,975,976,977,978,979,983,987,988,990,991,992,993,994,995,996,997,998,999,1000,1002,1004,1005,1006,1007,1009,1015,1019,1020,1022,1023,1024,1025,1027,1031,1032,1033,1034,1035,1036,1037,1038,1044,1048,1049,1051,1052,1053,1054,1058,1062,1063,1064,1066,1067,1068,1069,1071,1074,1075,1076,1077,1082,1085,1086,1087,1088,1096,1108,1109,1110,1111,1112,1114,1115,1116,1117,1119,1124,1125,1126,1127,1129,1130,1131,1137,1138,1139,1140,1141,1142,1143,1147,1148,1149,1150,1151,1155,1156,1158,1159,1160,1161,1162,1163,1164,1166,1170,1173,1174,1175,1179,1180,1181,1182,1183,1184,1185,1186,1189,1193,1194,1196,1197,1198,1199,1200,1201,1204,1208,1209,1210,1211,1214,1215,1218,1219,1220,1221,1225,1226,1227,1228,1229,1233,1234,1236,1237,1239,1240,1242,1243,1244,1248,1251,1252,1253,1256,1257,1260,1261,1265,1266,1269,1270,1271,1273,1277,1278,1279,1292,1293,1294,1295,1298,1299,1302,1303,1304,1305,1306,1308,1309,1312,1313,1314,1317,1318,1321,1325,1326,1327,1330,1331,1333,1346,1350,1351,1354,1355,1357,1358,1361,1365,1368,1369,1370,1373,1380,1395,1396,1397,1398,1399,1402,1406,1407,1408,1411,1413,1414,1415,1416,1417,1419,1420,1423,1424,1425,1472,1473,1475,1476,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1506,1573,1574,1575,1576,1577,1578,1580,1583,1584,1586,1587,1588,1589,1590,1591,1593,1594,1598", "uncoveredLines": "183,357,358,425,432,491,492,502,581,634,639,746,762,763,765,766,864,870,873,876,879,883,890,891,893,898,899,901,907,914,1089,1090,1091,1099,1100,1102,1103,1105,1132,1133,1167,1187,1222,1262,1334,1336,1337,1339,1341,1342,1359,1374,1375,1376,1381,1385,1386,1387,1388,1389,1392,1403,1429,1430,1431,1434,1435,1437,1438,1440,1441,1442,1445,1446,1447,1448,1449,1450,1454,1455,1456,1457,1458,1459,1462,1463,1467,1468,1509,1510,1511,1512,1515,1518,1519,1520,1522,1523,1525,1526,1528,1531,1532,1533,1536,1537,1538,1541,1542,1543,1545,1546,1549,1550,1553,1554,1555,1558,1559,1560,1562,1563,1565,1568,1569,1570"}, "ProjectShareBatchableJob": {"coverageRate": "0.88", "coveredLines": "2,8,10,11,12,13,14,15,18,19,21,22,24,29,30,31,32,33,34,35,40,41,45,48,49,52,53,56", "uncoveredLines": "25,26,43,54"}, "ProjectShareRevokeBatchableJob": {"coverageRate": "0.98", "coveredLines": "2,11,13,21,22,23,24,25,26,27,28,32,33,34,35,37,39,41,42,45,48,49,50,51,52,55,59,60,61,62,64,66,67,68,69,70,73,76,77,78,79,81,82,84,85,86,87,89,92,93,94,95,96,97,98,101,106", "uncoveredLines": "99"}, "ProjectShareSelector": {"coverageRate": "0.40", "coveredLines": "8,9", "uncoveredLines": "2,3,4"}, "ProjectTimecardExpenseService": {"coverageRate": "0.98", "coveredLines": "2,3,8,9,10,11,12,13,14,17,20,21,22,23,24,25,27,30,31,33,34,36,37,38,39,40,41,42,44,46,47,53,54,55,57,58,59,60,62,63,64,67,68,69,72,73,77,80,81,82,83,85", "uncoveredLines": "5"}, "ProjectTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "PublicHolidayController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PublicHolidaySelector": {"coverageRate": "0.52", "coveredLines": "2,3,4,7,10,11,12,13,15,17,18,20,23", "uncoveredLines": "26,27,30,36,37,39,40,41,44,47,48,51"}, "PublicHolidayService": {"coverageRate": "0.68", "coveredLines": "12,13,24,25,26,27,28,29,30,32,33,34,35,36,38,39,40,42,43,47,50", "uncoveredLines": "7,8,16,17,18,20,53,54,55,57"}, "PursuitRequestAfterDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PursuitRequestAfterInsertTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PursuitRequestAfterUpdateTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PursuitRequestController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "PursuitRequestSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6"}, "PursuitRequestService": {"coverageRate": "0.80", "coveredLines": "2,13,20,21,22,23,24,59,60,61,63,64,65,66,70,71,72,73,74,76,79,81,82,83,84,85,87,90,97,98,100,101,102,103,104,105,106,107,110,111,112,113,114,115,117,118,119,121,122,124,127,128,129,130,131,135,142,143,144,145,146,147,148,149,150,151,152,153,154,158,159,160,161,162,165,166,167,172,173,174,175,176,177,182,183,184,186,187,190,191,192,195,196,199,200,201,203", "uncoveredLines": "9,10,27,28,31,32,35,36,37,38,39,43,44,45,46,47,48,50,51,52,53,56,193,197"}, "PursuitRequestTrigger": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2"}, "PursuitTeamMemberService": {"coverageRate": "0.66", "coveredLines": "6,13,14,15,16,17,18,21,28,29,30,31,32,35,36,37,40,41,42,43,44,45,46,47,49,50,53,54,55,57,58,59,60,61,62,63,66,69,70,71,75,77,78,79,80,81,83,86,87,88,89,92,95,96,97,98,99,101,103,106,107,108,109,110,111,114,117,118,119,120,121,122,123,124,128,129,130,131,132,133,134,135,137,139,142,143,144,147,148,151,152,153,154,157,158,159,160,161,207,208,209,210,212,213,214,216,218,219,227,276,277,278,279,281,284,285,286,287,288,289,291,293", "uncoveredLines": "51,64,149,164,165,166,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,194,195,196,197,198,199,200,203,220,221,222,224,234,235,237,238,239,240,241,242,247,249,250,251,252,254,257,258,259,260,261,262,263,264,265,267,268,273"}, "Pursuit_RequestTriggerDispatcher": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "QualificationHistorySelector": {"coverageRate": "0.79", "coveredLines": "2,5,6,13,14,15,19,20,25,26,27", "uncoveredLines": "9,10,21"}, "QualificationHistoryService": {"coverageRate": "0.89", "coveredLines": "2,3,4,6,7,8,9,10,14,15,19,20,21,22,23,27,28,32,33,34,35,37,38,41,42,43,44,45,46,47,48,49,50,51,52,54,55,58,59,60,63,64,65,68,69,72,73,76,77,78,79,80,91,92,93,96,99,100,101", "uncoveredLines": "83,84,85,86,87,88,94"}, "QueueSobjectHandlerFactory": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "QueueSobjectsSelector": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "QueueableJobSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,8", "uncoveredLines": ""}, "QuoteAutoCreationService": {"coverageRate": "0.39", "coveredLines": "6,22,23,24,25,26,29,30,34,35,36,37,40,41,42,44,45,47,48,49,50,51,55,58,59,60,61,62,63,64,65,73,74,76,77,78,143,146,147,148,149,150,151,152,154,158,161,162,163,164,165,166,168,171,174,175,176,177,179,180,181,184,185,186,187,191,194,195,196,197,202,205,206,207,208,209,210,213,216,265,266,267,271,300,301,304,305,306,307,358,359,370,381,382,383,384,385,390,391,394,395", "uncoveredLines": "31,32,67,68,69,79,81,82,83,84,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,104,107,108,109,110,111,112,113,116,117,119,120,121,122,123,124,125,126,127,128,129,130,131,134,137,138,139,188,198,199,211,219,220,221,222,223,224,225,227,228,229,230,231,232,233,234,236,237,240,241,242,243,244,245,247,248,249,250,251,252,253,254,255,256,257,258,259,261,262,268,272,273,274,276,277,278,279,280,281,282,283,284,286,288,289,290,291,292,293,294,295,296,309,310,312,313,314,315,318,320,325,329,330,331,332,333,338,339,340,341,342,345,349,350,351,354,355,360,361,362,363,364,365,366,371,372,373,374,375,387"}, "QuoteController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "QuoteItemDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "QuoteSelector": {"coverageRate": "0.60", "coveredLines": "15,16,19,39,40,44,47,51,52,55,59,60,63,67,68", "uncoveredLines": "23,24,27,31,32,35,41,71,72,81"}, "QuoteService": {"coverageRate": "0.73", "coveredLines": "7,12,13,14,15,18,19,20,21,24,25,26,27,35,36,47,48,69,70,71,72,73,74,75,76,79,80,81,83,86,87,88,89,90,93,94,95,96,99,100,101,103,107,108", "uncoveredLines": "39,40,43,44,51,52,53,54,55,58,59,60,61,62,65,66"}, "QuoteTemplateMappingSelector": {"coverageRate": "1.00", "coveredLines": "6,7", "uncoveredLines": ""}, "ReassignObjectsSchedulable": {"coverageRate": "0.84", "coveredLines": "2,3,4,14,15,16,17,20,21,22,23,24,26,28,29,30,31,35,36,37,38", "uncoveredLines": "6,9,10,11"}, "ReassignTimecards": {"coverageRate": "0.85", "coveredLines": "21,22,23,24,27,28,29,32,33,37,39,40,41,43,45,48,49,50,51,52,57,58,61,62,66,69,74,75,76,77,78,79,80,81,82,83,84,85,86,91,94,101,102,103,104,105,106,107,108,109,111,112,113,115,116,117,118,119,120,122,126,127,129,130,131,134,137,138,143,144,146,147,148", "uncoveredLines": "5,6,7,8,9,13,14,15,16,17,53,54,63"}, "RecalculateFirstDateOfProjBatchableJob": {"coverageRate": "0.94", "coveredLines": "2,3,5,6,7,11,12,13,14,17,18,19,20,24,27,28,29,30,31,32,34,35,36,37,42,43,46,47,48,49,51,52,55,56", "uncoveredLines": "22,44"}, "RecalculateInfluenceMarketingPipelineJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RecalculateMAPersonFunnelJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RecentEnrichmentDto": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,14,15,16,17,18", "uncoveredLines": ""}, "RecentMktgEngagementContactDto": {"coverageRate": "1.00", "coveredLines": "21,27,28,29,30,31,32", "uncoveredLines": ""}, "RecentMovementOppDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,15,18,21,24,27,30,32,41,42,43,44,45,46,47,48,49,50", "uncoveredLines": ""}, "RecordTypeSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,11,12", "uncoveredLines": ""}, "RecycleBinSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,10,14,15", "uncoveredLines": ""}, "RecycleBinService": {"coverageRate": "0.97", "coveredLines": "2,3,4,6,9,10,11,14,16,17,19,20,21,22,25,28,29,30,31,34,35,36,38,39,40,42,45,46,47,48,51,52,53,56", "uncoveredLines": "49"}, "RedirctPageController": {"coverageRate": "0.95", "coveredLines": "4,6,7,11,12,13,16,17,18,21,22,23,24,27,28,29,30,35,36", "uncoveredLines": "32"}, "RefreshAbsenceRequestApproverJob": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,14,15,17,19,20,21,22,23,25,26,28,29,30,33,34,35,39,40,51,54,57,58", "uncoveredLines": "41,42,43,44"}, "RefreshPersonSubstageBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RegionMappingAfterDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RegionMappingAfterInsertTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RegionMappingAfterUnDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RegionMappingAfterUpdateTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RegionMappingSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "RegionMappingTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "RegionSelector": {"coverageRate": "0.77", "coveredLines": "2,5,6,13,14,21,22,25,26,29,30,31,32,33,35,44,45,48,49,52,53,56,57,59,60,61,64,66,69,70,73,74,81,82", "uncoveredLines": "9,10,17,18,38,39,40,62,77,78"}, "Region_MappingTriggerDispatcher": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RegionalInvoiceSquenceSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,10,11,14", "uncoveredLines": ""}, "ReimbursementStatusUpdateSchedulable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RemovePSELicense": {"coverageRate": "0.77", "coveredLines": "7,8,9,10,15,16,18,21,24,25", "uncoveredLines": "2,3,4"}, "RenameAndCleanOfferingJob": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,32,33,35,36,37,38,39,40,42,43,44,45,46,47,48,50,55,57,58,59,63,67,68,70,71,75,76,77,80,83,84,85,89,90,91,92,93,94,95,96,97,98,101,103,104,105,106,107,108,110,111,112,113,115,116,117,118,120,121,122,123,125,126,127,128,130,131,132,133,134,136,137,138,139,141,142,143,144,146,147,148,149,150,152,153,154,155,157,158,159,160,162,163,164,165,167,168,169,170,172,173,174,175,176,178,179,180,181,183,184,185,186,188,189,190,191,193,194,195,196,197,199,200,201,202,204,205,206,207,209,210,211,212,214,215,216,217,219,220,221,222,224,225,226,227,229,230,231,232,233,235,236,237,238,240,241,242,243,245,246,247,248,250,251,252,253,255,256,257,258,260,262,263,265", "uncoveredLines": ""}, "ReportController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ReportNotFoundException": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ReportNotUniqueException": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ReportSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,4,7"}, "ReportService": {"coverageRate": "0.85", "coveredLines": "12,13,16,17,18,19,20,22,23,24,26", "uncoveredLines": "8,9"}, "RequestAbsenceApprovalQueueableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ResourceRequestTrigger": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2"}, "RiskAlarmingController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RiskAlarmingCriticalErrorsDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "RiskAlarmingService": {"coverageRate": "0.53", "coveredLines": "2,13,14,24,25,28,29,32,33,34,35,36,39,40,41,42,43,45,46,47,50,51,54,55,56,58,59,61,64,65,68,69,70,71,72,74,77,78,81,150,151,154,155,158,159,160,161,163,166,167,170,171,173,174,175,178,179,181,184,185,186,187,188,189,192,193,196,197,198,200,202,205,206,209,210,211,212,213,215,218,219,222,223,224,225,226,229,230,232", "uncoveredLines": "20,21,48,52,66,79,84,85,86,88,89,90,91,93,96,97,98,99,101,102,104,105,107,110,111,112,113,114,116,117,118,119,120,121,122,123,126,127,131,132,136,137,138,139,140,142,143,147,152,156,168,176,190,194,207,220,227,235,243,244,248,249,250,251,254,255,258,261,262,264,265,266,268,270,271,275,279,281"}, "SALSummaryReminderScheduleJob": {"coverageRate": "0.87", "coveredLines": "7,8,9,10,16,17,18,19,20,26,27,28,31,32,34,35,36,37,38,39,40,43,44,47,48,49,50,51,53,55,58,59,60,61,62,64,66,69,70,73,74,75,76,79,82,83,86,87,88,89,90,91,92,94,97", "uncoveredLines": "12,13,23,29,41,45,71,84"}, "SFCurrencyExchangeRateUpdateSchedulable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SG_Leave_Calc_Controller": {"coverageRate": "0.84", "coveredLines": "2,3,4,5,6,7,8,9,10,11,18,19,20,22,23,27,28,29,35,36,40,73,74,75,78,79,83,86,87,92,93,96,99,100,101,102,103,117,120,121,122,123,127,130,131,136,137,141,144,145,146,147,148,149,150,153,156,157,158,159,160,164,167,168,169,170,171,172,184,187,188,189,194,195,209,212,213,217,219,224,225,228,231,232,236,238,239,244,245,248,249,250,252,253,255,257,258,263,264,267,270,274,275,276,277,278,294,295,297,298,299,304,305,306,307,308,309,310,311,312,313,314,315,317,318,319,320,321,322,323,326,329,332,335,338,341,344,347,348,350,351,352,355,356,359,360,361,364,365,366,367,370,371,372,375,376,377,378,381,382,385,386,387,388,389,391,392,395,396,397,399,401,404,405,407,408,409,410,411,412,414,415,417,418,419,420,421,422,428,429,430,431,434,435", "uncoveredLines": "14,15,25,31,37,38,44,45,46,47,48,94,104,105,107,110,111,113,138,196,197,198,199,202,203,206,226,246,260,283,284,285,286,287,288,289,413,425"}, "SIAccountLevelRevokeHandler": {"coverageRate": "1.00", "coveredLines": "2,9,10,11,12,13,16,17,18,20,22,25,26,27,29,31,35,36,37,38,39,40,43,47,48,49,50,51,52,53,57", "uncoveredLines": ""}, "SILIRevokeHandler": {"coverageRate": "1.00", "coveredLines": "2,6,7,9,10,11,14,15,16,18,20,23,24,25,27,29,32,33,34,35,36,37,41,44,45,46,47,48,49,50,54", "uncoveredLines": ""}, "SIPendingApprovalScheduleJob": {"coverageRate": "0.87", "coveredLines": "2,3,4,5,6,7,8,12,13,14,15,24,25,26,29,30,31,32,35,36,37,38,41,42,43,45,46,47,48,49,50,51,52,53,54,56,62,63,68,69,70,71,72,73,75,76,77,81,84,85,86,87", "uncoveredLines": "10,18,19,20,21,57,58,59"}, "SIProjectLevelRevokeHandler": {"coverageRate": "1.00", "coveredLines": "2,6,7,9,10,11,14,15,16,18,20,23,24,25,27,29,32,33,34,35,36,37,41,44,45,46,47,48,49,50,54", "uncoveredLines": ""}, "SOWContractDTO": {"coverageRate": "1.00", "coveredLines": "25", "uncoveredLines": ""}, "SalesAppAccessControlService": {"coverageRate": "0.83", "coveredLines": "10,11,12,16,17,18,21,22,23,24,25,26,27,28,29,30,33,36,37,38,39,42,45,46,50,51,52,89,90,91,92,93,94,96,97,99,102,103,104,106,107,109,110,111,112,113,115,116,117,134,135,136,137,138,139,143,144,145,147,148,153,154,155,157,160,161,162,163,164,165,168,171,172,173,175,176,177,178,179,181,183,185,189,190,191,196,197,198,200,201,203,206,207,208,209,211,212,213,217,218,221,222,223,224,225,226,227,228,229,232,235,238,239,240,241,242,243,244,245,246,247,248,249,253,254,257,258,262,263,266,267,268,269,270,272,274,275,276,277,279,280,282,285,286,287,288,290,291,292,295,296,298,299,300,301,303,304,305,307,310,312,315,316,317,318,320,321,322,325,326,328,329,330,331,333,334,336,338,341,342,345,346,361,362,363,364,366,369,370,371,372,374,388,389,390,391,392,393,395,398,399,400,401,402,404,407,408,409,410,411,414,415,416,417,419,423,424", "uncoveredLines": "40,56,57,58,60,61,62,63,64,65,66,67,69,70,71,74,75,76,78,84,85,122,123,125,126,128,129,146,230,278,302,332,349,350,351,353,355,356,358,377,378,379,380,382,383,385"}, "SalesAppAccessEmail": {"coverageRate": "0.95", "coveredLines": "5,6,7,8,9,10,11,12,14,15,17,18,19,20,21,22,23,29,30,31,32,34,37,38,39,40,42,45,46,47,48,49,51,53,56,57,58,59,60,62,64,67,68,69,70,72,74,75,78,79,81,82,83,84,93,94,95,96,110,111,112,122,123,124,125,126,132,133,134,135,138,141,142,143,144,146,147,170,173,174,175,176,178,179,181,182,190,191,193,196,197,198,199,201,202,204,205,208,209,212,213,214,220,221,224,225,226,227,229,230,231,232,234,235,237,238,251,252,253,254,263", "uncoveredLines": "24,97,98,108,109,216"}, "SalesAppScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesContactAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "SalesContactAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,9,10", "uncoveredLines": ""}, "SalesContactBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "SalesContactController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesContactSelector": {"coverageRate": "0.25", "coveredLines": "20,21,24,65,66", "uncoveredLines": "2,3,4,12,13,14,28,29,42,46,47,61,69,70,73"}, "SalesContactService": {"coverageRate": "0.62", "coveredLines": "3,5,6,7,8,9,14,15", "uncoveredLines": "18,19,20,21,23"}, "SalesContentDocumentLinkService": {"coverageRate": "0.90", "coveredLines": "15,16,17,18,19,20,21,22,23,24,25,26,27,30,44,45,46,47,48,49,50,51,52,53,54,55,58,66,67,68,78,79,80,85,86,88,95,96,97,98,99,100,101,102,104,108,114,116,123,124,125,126,127,128,129,130,132,136,137,138,139,140,141,142,143,158,166,167,169,170,171,172,173,174,175,176,177,178,179,180,183,184,186,190,198,199,201,202,203,204,205,206,207,208,209,210,211,212,215,216,218,222,223,224,225,226,227,228,229,232,236,238,239,240,241,245,246,247,250,251,252,253,254,255,256,259,263,265,266,267,270,271,272,273,274,275,276,279,283,288,289,290,291,293,294,295,296,297,298,301,305,310,311,312,313,315,316,317,318,319,320,322,326,327,328,329,330,332,334,338,339,340,343,344,345,346,348,349,350,353,356,357,358,359,360,363,366,367,368,369,370,371,374,375,377,378,380,381,382,383,384,385,386,387,388,389,391,394,395,396,398,399,401,402,404,405,407,409,410,411,412,413,414,415,418,419,420,421,422,423,424,427,428,429,430,431,432,433,434,437,440,442,443,444,446,449,450,451,452,453,457,458,461,463,465,466,467,468,476,478,481,484,485,488,490,491,492,493,497,498,499,500,501,502,508,509,559,560,561,562,564,565,566,571,572,573,574,575,576,577,578,579,580,584,588,589,590,591,593,594,596,600,602,603,604,605,607,608,611,612,613,614,615,617,621,622,623,625,626,629,632,634,637,638,640,641,642,643,644,645,646,647,648,652,653,654,683,684,687,688,691,692,694,695,698,699,702,703,704,705,706,708,710,711,712,713,715,716,720,721,722,723,724,725,727,728,730,731,732,733,735,736,738,739,740,741,743,744,748,749,750,751,752,754,756,757,758,759,761,762,766,767,769,770,771,772,774,776,777,778,779,781,782,786,787,788,789,790,791,792,793,794,797,800,801,802,803,804,805,807,808,810,811,812,813,815,816,818,819,820,821,823,828,829,830,831,832,835,838,839,840,841,842,843,844,847,850,855,856,857,858,859,860,861,862,865,868,869,870,871,872,875,878,879,880,881,883,884,885,888,891,896,897,898,899,901,902,903,904,907,911,916,918,919,920,921,922,924,925,926,927,928,934,938,943,945,946,947,948,949,951,952,953,954,955,956,959,960,961,962,963,969,973,974,975,978,979,980,981,982,984,986,990,991,993,994,995,998,999,1001,1002,1003,1004,1005,1006,1008,1009,1016,1019,1022,1023,1024,1025,1027,1030,1031,1032,1033,1035,1038,1039,1040,1041,1043,1046,1047,1048,1049,1051,1054,1055,1056,1057,1058,1060,1062,1065,1066,1067,1068,1069,1071,1073,1076,1077,1080,1081,1082,1083,1084,1086,1088,1091,1092,1093,1094,1095,1096,1098,1100", "uncoveredLines": "89,91,92,117,119,120,147,148,149,150,151,152,153,154,341,469,470,503,504,505,511,512,514,515,516,517,520,521,523,525,526,527,530,531,532,533,537,538,539,543,544,545,546,547,548,550,551,554,556,658,663,665,666,667,668,669,670,672,673,674,675,677,680,824,1013,1017,1078"}, "SalesContractOperationSubscriberTrigger": {"coverageRate": "0.47", "coveredLines": "2,3,4,5,7,8,9,10", "uncoveredLines": "11,12,13,14,15,16,17,18,19"}, "SalesContractOppAllocSubscriberTrigger": {"coverageRate": "0.47", "coveredLines": "2,3,4,5,7,8,9,10", "uncoveredLines": "11,12,13,14,15,16,17,18,19"}, "SalesDealDeskDelBeforeDeleteHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesDealDeskDelBeforeInsertHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesDealDeskDelBeforeUpdateHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesEmail": {"coverageRate": "0.98", "coveredLines": "2,4,6,7,8,10,11,12,14,15,16,17,19,25,26,27,28,29,31,34,35,36,37,38,39,40,41,42,44,46,49,50,51,52,53,54,55,57,58,59,61,62,63,68,69,70,71,73,74,75,77,78,79,82,83,84,87,88,89,93,94,95,96,97,98,102,103,104,105,106,107,110,113,114,115,116,118,119,120,123,124,125,126,128,129,130,133,134,135,136,138,139,140,143,144,145,146,148,152,153,154,155,156,158,161,162,165,166,167,169,170,171,172,175,176,177,178,179,180,183,184", "uncoveredLines": "20,21"}, "SalesInsightSelector": {"coverageRate": "0.32", "coveredLines": "7,8,9,10,11,22,23,34,35,36,37,38,39,41,44,47,48,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,161", "uncoveredLines": "14,15,18,19,26,27,28,29,30,31,42,45,51,52,53,55,56,57,58,59,61,62,65,66,68,71,76,77,80,82,83,84,86,89,90,91,92,93,94,96,97,99,100,101,102,103,105,108,109,110,111,113,114,116,117,119,122,123,124,125,127,128,130,131,132,134,137,138,141"}, "SalesInvoiceAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6", "uncoveredLines": ""}, "SalesInvoiceAfterUpdateTriggerHandler": {"coverageRate": "0.93", "coveredLines": "2,4,5,6,7,8,9,10,11,12,13,16,17,18,20,22,23,24,25,26,27,28,36,37,38,39,41,43,44,45,46,47,52,53,54,56,57,58,59,60,61,62,63,64,70,71,72,74,75,76,77,78,79,84,85,86,87,88,89,90,92,93,94,99,101,106,107,108,109,110,112,116,121,122,123,124,127", "uncoveredLines": "29,30,96,102,113,117"}, "SalesInvoiceApprovalEmailController": {"coverageRate": "0.95", "coveredLines": "4,5,6,8,12,13,16,18,19,25,26,30,31,32,33,35,39,40,44,45", "uncoveredLines": "21"}, "SalesInvoiceBeforeDeleteTriggerHandler": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesInvoiceBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "4,5,8,9", "uncoveredLines": ""}, "SalesInvoiceBeforeUpdateTriggerHandler": {"coverageRate": "0.97", "coveredLines": "2,3,4,8,9,10,11,12,13,14,15,16,17,19,20,21,22,25,26,27,28,31,32,33,36,37,40,41,42,43,44,45,46,48,49,50,51,52,53,57,58,60,63,64,65,66,67,68,70,71,72,73,74,75,76,77,78,79,88,89,90,91,92,93,94,95,97,100,101,105,106,107,108,109,110,112,113,114,115,116,117,118,119,121,122,126,127,128,129,130,131,132,133", "uncoveredLines": "80,81,82"}, "SalesInvoiceController": {"coverageRate": "0.85", "coveredLines": "8,10,11,12,15,16,17,18,19,20,22,24,28,29,31,32,33,36,37,38,39,40,41,47,48,49,52,53,54,55,56,57,59,60,62", "uncoveredLines": "25,26,43,44,66,67"}, "SalesInvoiceGenerator": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,15,16,18,20,21,22,23,24,26,27,29,30,32,33,34,36,37,38,41,42,46,47,48,51,52,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,99,100,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,138,140,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,160,161,162,163,164,165,166,167,168,169,170,171,172,174,175,176,177,178,180,181,183,184,185,187,188,189,191,192,193,194,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,220,221,222,223,226,227,228,229,230,231,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,257,258,259,260,264,273,274,275,276,277,281,282,283,284,285,286,289,290,291,292,293,295,296,297,298,301,302,305,306,307,308,309,310,312,313,314,317,318,319,320,321,322,323,324,325,327,328,329,330,331,332,333,334,335,336,337,339,341,342,343,344,345,346,348,351,356,357,358,359,360,361,362,365,366", "uncoveredLines": "136"}, "SalesInvoiceLineItemCalculator": {"coverageRate": "0.99", "coveredLines": "13,19,20,21,22,25,33,34,35,36,38,41,43,45,48,49,50,51,54,55,56,58,59,60,64,67,68,70,71,72,73,74,75,76,78,81,82,84,85,86,87,88,89,90,92,95,96,97,98,99,100,101,103,104,108,116,117,118,119,120,121,122,123,124,125,127,128,129,132,133,134,135,137,139,140,142,145,146,148,149,151,152,154,155,157,158,161,162,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,198,199,202,210,211,213,214,215,217,218,219,220,221,222,223,224,225,226,227,228,230,231,232,233,235,236,240,241,242,243,246,249,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,282,283,284,287,288,289,290,291,292,294,295,297,298,300,302,304,305,307,308,309,311,312,313,317,318,320,321,323,324,325,326,328,329,332,333,337,338,340,341,343,344,345,346,348,349,355,356,358,359,361,362,363,364,366,367,371,372,374,375,377,379,381,382,383,384,385,386,390,391,395,397,398,400,401,403,404,406,407,408,410", "uncoveredLines": "61,130,143"}, "SalesInvoiceLineItemSelector": {"coverageRate": "0.50", "coveredLines": "2,3,23,27,28,29,33,34,35,36,37,39,42,43,44,47,49", "uncoveredLines": "52,55,57,62,63,70,71,72,77,78,82,83,84,99,100,105,106"}, "SalesInvoiceLineItemService": {"coverageRate": "0.91", "coveredLines": "8,10,11,12,13,14,18,23,24,25,26,29,30,31,32,36,37,39,40,41,43,45,47,48,49,50,51,52,53,54,55,60,65,66,67,68,69,70,71,72,74,75,77,80,81,82,83,85", "uncoveredLines": "88,89,90,91,92"}, "SalesInvoiceLineItemShareBatchableJob": {"coverageRate": "0.91", "coveredLines": "2,11,13,14,15,16,17,18,19,20,23,24,26,27,29,34,35,36,37,41,44,45,48,49,52,56,57,58,59,60,61,62,64,65,66,67,68,69,74", "uncoveredLines": "30,31,39,50"}, "SalesInvoiceLineItemShareRevokeBatchJob": {"coverageRate": "0.89", "coveredLines": "2,3,10,17,25,26,27,28,29,30,31,34,35,36,38,39,41,42,44,46,52,53,56,57,58,59,60,61,62,63,64,66,67,68,69,72,73,77,78,82,83,88", "uncoveredLines": "49,74,75,84,85"}, "SalesInvoiceLineItemShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,12", "uncoveredLines": ""}, "SalesInvoiceLineItemTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "SalesInvoiceReassignSchedulable": {"coverageRate": "0.63", "coveredLines": "2,3,4,5,6,7,32,33,34,35,36,37,39,40,45,50,51,54,55,56,58,59,60,61,66,67,70", "uncoveredLines": "9,10,13,14,15,21,22,23,24,25,26,27,28,41,42,43"}, "SalesInvoiceSelector": {"coverageRate": "0.58", "coveredLines": "2,3,22,37,38,39,41,42,44,47,48,49,108,109,110,111,113,116,129,130,131,134,139,140,141,154,159,160,161,173,174,187,191,192,195,196,197,198,199,200,201,204,205,206,207,209,220,221,222,225,230,231,232,235,240,241", "uncoveredLines": "26,27,28,31,32,33,53,54,57,58,64,65,68,72,73,89,90,104,121,122,123,125,163,166,168,212,213,216,217,244,245,266,267,271,272,276,277,278,279,280,284"}, "SalesInvoiceService": {"coverageRate": "0.80", "coveredLines": "4,25,32,34,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,54,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,88,89,90,91,92,93,94,95,97,98,100,103,104,105,106,108,111,112,113,114,117,118,119,120,121,122,123,124,125,126,127,128,129,131,138,139,140,141,143,144,145,146,147,148,149,150,151,152,154,155,157,158,159,164,165,166,167,169,170,171,172,173,174,176,177,178,179,180,181,215,233,234,235,237,239,240,241,243,247,248,249,250,253,254,255,256,257,261,264,265,266,268,272,274,277,280,284,286,287,291,292,293,294,295,297,310,314,315,333,335,336,337,338,341,342,343,344,348,356,358,359,361,364,366,367,368,371,372,374,375,376,377,378,379,380,382,383,384,386,389,390,391,392,395,396,397,399,402,403,404,405,406,407,409,410,411,414,415,418,419,422,423,430,431,439,440,441,442,443,445,446,447,448,449,452,454,455,458,460,461,462,463,464,465,467,468,470,471,473,476,480,481,482,486,487,493,494,495,496,498,499,501,502,503,504,507,508,509,512,513,514,517,518,519,520,522,523,524,525,526,527,528,529,530,531,532,533,538,539,540,541,544,545,549,550,551,552,553,554,555,556,557,560,561,564,565,566,569,570,571,574,575,576,578,581,584,585,586,588,591,592,593,594,597,598,599,606,607,609,610,611,613,615,616,617,618,619,620,621,622,623,628,641,642,643,644,645,648,651,652,653,654,655,658,661,662,663,664,665,667,668,669,670,671,673,675,676,677,678,679,680,682,683,685,687,690,691,692,694,695,696,698,702,703,706,707,709,710,711,715,718,719,720,723,724,725,726,727,732,733,734,737,738,739,740,741,746,747,750,751,754,761,762,763,765,766,767,768,769,771,773,774,775,776,777,778,780,781,783,785,788,789,790,791,795,800,801,802,804,807,808,809,810,811,812,813,816,817,818,819,820,821,822,824,825,826,827,828,830,831,834,835,836,837,838,840,841,842,843,845,870,871,872,873,874,875,899,900,924,925,928,929,932,933,936,937,938,939,940,941,942", "uncoveredLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,201,203,205,206,207,208,209,216,217,218,219,220,221,222,223,224,225,226,227,269,298,299,300,301,302,303,304,305,316,318,319,320,321,322,323,324,325,326,330,424,425,427,432,433,435,474,477,521,535,542,600,632,633,634,635,636,752,757,758,792,796,848,849,850,856,857,858,860,861,864,865,866,867,878,879,880,881,882,886,887,888,889,890,891,892,893,896,903,904,907,908,909,911,912,913,914,915,916,917,918,920,921,926"}, "SalesInvoiceShareBatchableJob": {"coverageRate": "0.89", "coveredLines": "2,11,13,14,15,16,17,18,19,20,23,24,26,27,29,34,35,37,38,43,46,47,50,51,54,56,57,59,65,66,67,68,70,71,72,74,75,76,77,78,79,84", "uncoveredLines": "30,31,40,52,60"}, "SalesInvoiceShareRevokeBatchableJob": {"coverageRate": "0.71", "coveredLines": "2,3,14,25,37,38,39,40,41,42,43,44,45,46,47,50,51,52,53,55,56,58,59,61,63,64,65,67,68,70,71,73,76,82,83,86,87,88,89,90,91,92,93,96,97,98,99,100,101,102,103,104,106,107,108,109,112,113,117,118,122,123,124,140,143,144,160", "uncoveredLines": "79,114,115,125,126,127,128,129,130,131,132,133,134,135,136,145,146,147,148,149,150,151,152,153,154,155,156"}, "SalesInvoiceShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,12,16,17,20", "uncoveredLines": ""}, "SalesInvoiceTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "SalesTargetDTO": {"coverageRate": "1.00", "coveredLines": "13,14,15,16,17,18,21,22,23,24,25,26,27", "uncoveredLines": ""}, "SalesUserAfterInsertHandler": {"coverageRate": "1.00", "coveredLines": "6,8,9", "uncoveredLines": ""}, "SalesUserAfterUpdateHandler": {"coverageRate": "1.00", "coveredLines": "6,7,9,10,11,12,13,14,15,16,17,18", "uncoveredLines": ""}, "SalesUserBeforeInsertHandler": {"coverageRate": "1.00", "coveredLines": "6,7,8,10,11,12,13", "uncoveredLines": ""}, "SalesUserBeforeUpdateHandler": {"coverageRate": "1.00", "coveredLines": "6,7,9,10,11,12,13,14,15", "uncoveredLines": ""}, "SalesUserController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesUserDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SalesUserSelector": {"coverageRate": "1.00", "coveredLines": "2,5,6", "uncoveredLines": ""}, "SalesUserService": {"coverageRate": "0.87", "coveredLines": "6,7,8,9,10,30,31,32,36,37,38,39,40,45,46,47,49,50,51,57,58,59,60,61,62,65,66,70,71,72,74,75,76,78,79,80,81,82,86,87,90,91,94,95,96,98,99,100,102,103,104,105,106,110,111,114,115,119,120,121,123,124,125,131,137,138,140,145,146,147,149,150,151,153,154,156,157,158,159,161,162,166,167,173,174,177,178,181,182,183,184,185,186,189,190,194,195,197,198,199,202,203,204,206,207,208,209,213,214,217,220,221,225,226,227,229,230,231,232,233,234,235,239,240,243", "uncoveredLines": "12,13,14,15,16,18,21,22,23,24,25,27,126,127,128,132,141,163,170"}, "SalesWorkCalendarSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3"}, "Sales_InvoiceTriggerDispatcher": {"coverageRate": "0.74", "coveredLines": "2,3,4,5,6,8,9,10,11,12,14,18,19,20,21,22,28,29,30,31,32,48,49,50,51,52", "uncoveredLines": "24,34,38,39,40,41,42,44,54"}, "Sales_Invoice_Line_ItemTriggerDispatcher": {"coverageRate": "0.50", "coveredLines": "2,3,5,6,7,8,9", "uncoveredLines": "11,15,16,17,18,19,21"}, "SalesfunnelAccessHistoryService": {"coverageRate": "0.93", "coveredLines": "2,4,5,6,7,8,9,16,17,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,39", "uncoveredLines": "10,11"}, "ScheduleAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,13,14,27,29", "uncoveredLines": ""}, "ScheduleTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "ScheduleTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": "9"}, "ScoreActivity": {"coverageRate": "0.54", "coveredLines": "3,5,7,8,10,13,14,15,16,17,18,19,20", "uncoveredLines": "23,24,25,26,27,28,29,30,31,32,33"}, "SendEmailToNewlyAssignedPMAndIA": {"coverageRate": "0.55", "coveredLines": "2,17,18,19,20,21,22,25,27,29,30,32,33,34,35,36,50,51,52,53,54,55,56,58,59,63,64,65,66,67,70", "uncoveredLines": "4,5,7,8,9,10,11,12,13,14,37,39,40,41,43,46,47,73,74,75,76,77,78,79,83"}, "SendEmailToTravelTeamScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SendErrorEmail": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10,11,12,13,16,17,18,19,20,21,22,23", "uncoveredLines": ""}, "ServiceLineDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SetInfluenceAttributionValueJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SetSupervisingRegionOfLeadAndContactJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "ShareBatchableJobService": {"coverageRate": "0.88", "coveredLines": "2,3,4,5,6,8,9,10,11,13,14,15,16,17,19,20,21,22,23,24,25,26,27,29,30,31,32,33,35,36,37,38,39,40,41,43,44,45,46,47,48,49,51,52,53,54,55,56,57,59,60,61,62,63,64,65,67,68,69,70,71,72,73,75,76,77,78,79,80,81,83,84,85,86,87,89,90,91,92,93,106,107,108,109,110,111,112,113,114,118,125,126,127,128,129,132,133,134,137,138,140,143,144,145,148,149,151,154,155,156,157,158,159,160,161,163,164,165,166,167,169,173,174,175,176,178,182,184,187,188,189,190,191,192,193,194,196,201,204,205,206,207,209,212,213,214,215,216,217,220,223,224,225,229,230,231,232,234,238,239,240,241,244,245,246,249,250,252,253,256,257,258,259,260,261,264,268,271,274,275,276,281,282,283,284,286,288,289,290,293,297,298,299,302,307,308,309,310,311,312,315,322,323,324,325,331,332,333,334,335,337,346,350,351,352,353,356,357,360,361,362,363,364,366,367,368,370,372,374,375,377,380,384,385,393,394,395,398,402,403,404,407,411,412,413,414,416,420,421,422,423,425,429,430,440,441,442,443,444,445,446,447,448,450,456,457,458,459,460,462,471,472,473,474,475,476,477,478,490,491,492,495,496,501,504,505,506,509,510,513,514,515,516,517,519,522,523,524,527,530,536,537,538,539,540,541,542,543,545,548,549,552,559,566,567,568,569,570,571,572,574,577,578,579,581,588,589,592,593,594,595,597,600,603,604,605,606,608,611,612,613,623,626,627,628,629,631,634,635,636,637,638,640,641,642,645,648,658,661,662,664,665,666,667,668,670,673,676,679,683,686,687,688,689,698", "uncoveredLines": "226,262,277,291,300,313,338,339,341,378,386,387,389,396,405,431,432,434,479,480,482,493,525,550,598,609,616,617,619,620,643,646,649,650,651,652,671,674,677,680,690,691,692,694,701,702,703,704,705,707"}, "ShareDMLUtils": {"coverageRate": "0.92", "coveredLines": "2,4,5,9,10,12,17,18,19,22,23,25,30,31,32,35,36,38,43,44,45,48,49,51,52,56,57,58,60,61,62,63,64,65,67,68,72,75,76,79,80,83,84,85,87,88,89,90,91,92,94,95,96,102,105,106,107,109,110,111,112,113,114,116,117,118,125,128,129,130,131,133", "uncoveredLines": "6,13,26,39,98,120"}, "ShareRecordProcessSchedulable": {"coverageRate": "0.65", "coveredLines": "2,3,4,5,13,14,15,22,27,30,31,32,33,34,35", "uncoveredLines": "7,8,9,10,23,24,25,37"}, "ShareRecordRevokeProcessSchedulable": {"coverageRate": "0.45", "coveredLines": "2,6,7,19,20,25,28,35,38,39,44,46,47", "uncoveredLines": "9,10,11,12,13,15,29,30,31,33,49,50,51,52,54,55"}, "ShareRecordService": {"coverageRate": "0.91", "coveredLines": "2,4,5,6,8,9,11,14,19,20,21,22,23,24,25,27,29,31,32,35,38,39,42,43,46,47,48,49,50,51,52,53,54,55,56,57,60,64,73,76,77,78,80,81,83,85,86,89,92,93,94,96,97,98,99,104,105,108,109,110,111,116,117,120,121,122,123,128,129,130", "uncoveredLines": "12,15,26,28,30,106,118"}, "SkillSelector": {"coverageRate": "1.00", "coveredLines": "2,3,6", "uncoveredLines": ""}, "SquadAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "SquadAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,8,9,10,11", "uncoveredLines": ""}, "SquadAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,8,9,10,12,13,14,15,16,17,18,21,22,23,25", "uncoveredLines": ""}, "SquadAssignmentShareBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,7,8,9,12,13,17,18,19,23,24,25,26,27,30,33,34", "uncoveredLines": ""}, "SquadBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,11,12,14", "uncoveredLines": ""}, "SquadBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6", "uncoveredLines": ""}, "SquadBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "SquadController": {"coverageRate": "0.96", "coveredLines": "3,5,7,9,11,14,15,16,17,20,21,22,23,26,27,30,31,32,35,36,37,39,43,44", "uncoveredLines": "28"}, "SquadEmailService": {"coverageRate": "0.98", "coveredLines": "11,13,14,15,16,20,21,22,23,26,27,28,29,30,31,34,35,37,38,41,42,43,44,45,46,47,48,49,50,52,53,54,56,57,61,62,63,65,66,68,69,72,75,76,77,78,79,80,81,82,85,88,89,90,91,92,93,94,97,98,99,100,101,102,103,104,106,107,108,110,111,114,117,118,119,120,121,122,123,124,125,126,127,130,131,132,133", "uncoveredLines": "134,135"}, "SquadMemberAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "SquadMemberBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,11,12,14", "uncoveredLines": ""}, "SquadMemberController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SquadMembershipAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,11,12,14", "uncoveredLines": ""}, "SquadMembershipSelector": {"coverageRate": "0.58", "coveredLines": "22,23,26,30,31,34,38,39,42,53,54,57,61,62,65", "uncoveredLines": "2,3,6,7,10,14,15,18,46,47,50"}, "SquadMembershipService": {"coverageRate": "0.72", "coveredLines": "6,7,8,9,32,33,34,37,38,41,42,43,44,45,46,47,48,51,55,58,59,60,61,62,64,67,69,70,71,72,73,74,76,80,83,84,87,88,89,92,95,97", "uncoveredLines": "12,13,14,15,16,18,19,20,23,26,27,28,29,35,49,90"}, "SquadMembershipTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "SquadSelector": {"coverageRate": "0.65", "coveredLines": "16,17,18,21,34,35,51,55,56,59,62,65,66,67,70,75,76,79,87,88,91,107,108,121", "uncoveredLines": "2,3,12,26,27,30,83,84,95,96,99,100,103"}, "SquadService": {"coverageRate": "0.97", "coveredLines": "2,3,4,5,6,7,8,9,10,14,18,19,20,21,22,25,26,27,28,32,33,34,35,37,41,42,43,44,45,46,49,52,53,54,55,58,59,63,64,67,68,69,72,73,74,77,78,79,80,83,84,87,88,89,90,91,92,95,96,97,99,100,101,102,103,104,106,107,112,113,116,117,119,120,121,122,123,124,126,127,131,132,134,135,139,140,144,145,146,147,148,158,159,161,162,163,164,165,166,168,169,172,173,174,175,179,180,181,182,185,186,187,189,190,193,194,195,196,199,200,201,202,203,205,206,207,209,212,213,214,215,216,217,218,221,224,225,226,227,228,231,234,235,236,237,238,240,241,244,247,248,249,250,251,254,257,258,259,260,261,262,263,264,268,271,272,273,274,275,278,281,282,283,284,285,286,287,288,289,292,295,296,297,298,299,300,301,303,305,306,307,308,310,313,316,317,318,319,320,323,326,327,330,331,332,333,334,335,336,339,342,343,344,345,347,350,351,352,355,356,357,358,361,362,363,364,366,367,368,369,371,372,373,374,377,378,381,382,384,385,386,387,390,393,395,396,397,400,403,404,405,406,408,409,410,411,412,413,414,417,418,419,420,421,425,428,429,430,433,434,437,438,439,440,441,442,443,444,445,447,450,451,455,456,457,458,459,460,461,464", "uncoveredLines": "38,60,81,149,150,151,152,154,379"}, "SquadShareRevokeBatchableJob": {"coverageRate": "0.32", "coveredLines": "2,3,4,5,15,16,19,26,27,28,29,30,33,34,35,37,58,78,79,91,92,116,117", "uncoveredLines": "36,38,39,40,41,42,43,44,45,47,49,50,51,52,53,55,61,62,63,64,65,66,67,69,70,71,72,75,80,81,82,83,84,85,86,88,96,97,98,101,102,103,104,105,108,112,120,121"}, "SquadShareSelector": {"coverageRate": "0.33", "coveredLines": "10,11,12", "uncoveredLines": "2,3,6,16,17,18"}, "SquadTimecardRevokeBatchableJob": {"coverageRate": "0.87", "coveredLines": "2,10,11,12,13,17,18,19,20,22,23,26,32,35,42,43,44,45,46,49,50,51,52,53,54,55,60,63,65,66,67,69,70,71,72,74,75,76,77,81,82,83,98,99,100,103,105,106,107,108,109,110,111,112,113,115,116,121,122,123,124,125,126,127,128,130,131,132,134,135,136,138,139,140,141,142,144,146,149,150,151,153,154,155,168,169,170,182,185,188,189,190,191,192,193,196,203,204,207,208,209,210,211,212,213,214,215,216,220,221,222,227,228,231,233,234,235,236,237,238,239,240,241,242,246,247,248,254,255,258,260,261,262,263,266,267,268,269,270,271,277,278,279,292,293,294,296,297,298,299,300,301,303,304,307,308,309,310,311,312,314,317,318,320,321,326,327,330,332,333,334,335,336,338,339,340,341,344,345,347", "uncoveredLines": "57,86,87,88,89,90,92,94,157,158,159,160,161,163,171,172,173,174,175,176,177,179,199,200,218,244,315"}, "SquadTimecardShareBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,3,9,10,13,14,15,18,19,20,21,25,26,27,31,32,33,35,36,39,45,46,47,48,49,50,54,55,56,57,58,59,61,66,69,70,73,74,75,77,80,81,82", "uncoveredLines": ""}, "SquadTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "SquadTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,19,20,21,22,23,29,30,31,32,33,39,40,41,42,43,49,50,51,52,53,59,60,61,62,63", "uncoveredLines": "15,25,35,45,55,65"}, "Squad_MembershipTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,16,17,18,19,20,26,27,28,29,30", "uncoveredLines": "12,22,32"}, "StaffingAssignmentsDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "StaffingRequestController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "StaffingRequestDTO": {"coverageRate": "0.88", "coveredLines": "3,5,7,9,11,13,15,17,19,21,27,33,37,39,41,43,45,47,51,53,55,60,61,62,63,67,70,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "uncoveredLines": "23,25,29,31,35,57"}, "StaffingRequestDeleteDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "StaffingRequestService": {"coverageRate": "0.87", "coveredLines": "11,12,13,14,15,16,17,18,21,28,29,30,31,32,33,34,41,42,43,45,47,48,49,50,51,53,56,60,61,64,85,86,87,90,91,92,93,95,96,97,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,121,124,129,130,132,133,134,137,138,139,140,141,142,143,144,145,146,147,148,153,154,155,157,158,159,161,164,165,166,167,168,169,170,171,173,176,179,180,183,184,185,186,187,188,190,193,196,197,198,200,201,202", "uncoveredLines": "37,38,54,67,68,71,72,73,74,75,77,80,81,131,181,205"}, "StaffingRequestSimpleDTO": {"coverageRate": "0.46", "coveredLines": "3,5,7,9,11,13,15,17,19,21,23,25", "uncoveredLines": "27,30,44,45,46,47,48,49,50,51,52,53,54,55"}, "StaffingSectionSalesFunnelController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "StaffingWorkingOfficeDTO": {"coverageRate": "0.90", "coveredLines": "3,5,7,9,14,15,16,17,18", "uncoveredLines": "11"}, "StandarCostRateCalculator": {"coverageRate": "0.99", "coveredLines": "7,9,11,15,16,17,20,21,23,24,25,26,30,31,32,33,34,35,37,38,39,40,42,43,44,45,47,48,49,50,52,54,57,58,60,61,62,63,64,65,68,69,71,74,75,76,77,78,79,81,82,83,85,86,87,88,89,94,95,96,97,98,100,102,103,104,105,107,111,112,113,114,117,118,119,122,126,127,128,129,130,133,134,135,136,137,138,142,143,144,145,146,147,148,151", "uncoveredLines": "2"}, "StandarCostRateSchedualeJob": {"coverageRate": "0.70", "coveredLines": "2,3,5,7,15,16,17", "uncoveredLines": "10,11,12"}, "StandarCostRateSelector": {"coverageRate": "1.00", "coveredLines": "2,5,6,12,13,19,23,28,29,32,33,36,37", "uncoveredLines": ""}, "StandarCostRateService": {"coverageRate": "1.00", "coveredLines": "5,6,7,10,11,12,15,16,17,18,19,22,23,25,26,27,29,31,32,33,36,38,41,42,43,44,45,48", "uncoveredLines": ""}, "StringExtension": {"coverageRate": "1.00", "coveredLines": "2,3,4,6,7,9,12,13,14,16", "uncoveredLines": ""}, "StringUtils": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,7,8,9,11,13", "uncoveredLines": ""}, "SubMapIterator": {"coverageRate": "0.94", "coveredLines": "9,11,12,13,14,15,18,19,22,23,26,27,28,29,31,32", "uncoveredLines": "24"}, "SupervisedJournalBatchableJob": {"coverageRate": "0.81", "coveredLines": "2,4,5,6,7,8,10,42,50,51,52,53,54,55,56,79,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,109,110,111,112,114,115,117,118,120,123,124,127,130,131,132,135,138,140,141,143,144,145,147,150,151,152,154,157,158,161,162,165,166,169,170,173,174,175,177,178,181,182,185,186,189,190,191,192,193,196,197,198,199,200,201,203,204,206,207,208,210,213,214,215,216,217,220,223,224,225,226,227,228,229,232,233,234,235,236,237,261,262,263,264,265,266,270,271,272,275,276,277,278,279,280,281,282,283,284,285,286,289,290,293,295,296,297,300,301,302,305,306,307,308,310,313,314,315,316,317,319,320,324,327,328,329,330,331,332,333,334,337,338,339,340,343,344,347", "uncoveredLines": "19,20,21,22,23,25,26,27,30,33,34,35,36,37,38,125,128,133,241,242,243,246,247,248,249,250,252,253,254,255,256,257,267,273,322,341,345,350,351,352"}, "SupervisedJournalItemSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SupplierExpenseTypeSelector": {"coverageRate": "0.75", "coveredLines": "2,3,6", "uncoveredLines": "4"}, "SurveyRespAfterDeleteTrigger": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "SurveyRespAfterInsertTrigger": {"coverageRate": "0.48", "coveredLines": "2,3,4,6,7,8,9,10,13,14,15,16,17,51,52,53,54,55,56,62,66", "uncoveredLines": "20,21,22,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,43,46,47,58,63"}, "SurveyRespAfterUpdateTrigger": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,8,9,10,11", "uncoveredLines": ""}, "SurveyRespBeforeInsertTrigger": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7", "uncoveredLines": ""}, "SurveyResponseCategories": {"coverageRate": "1.00", "coveredLines": "11,12,13,14,15", "uncoveredLines": ""}, "SurveyResponseController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SurveyResponseDetail": {"coverageRate": "1.00", "coveredLines": "19,20,21,22,23,24,25,26,27", "uncoveredLines": ""}, "SurveyResponseSelector": {"coverageRate": "0.65", "coveredLines": "2,3,6,7,10,16,17,36,37,44,45,48,52,53,56,57,60", "uncoveredLines": "20,21,24,25,28,29,32,40,41"}, "SurveyResponseService": {"coverageRate": "0.92", "coveredLines": "5,7,8,9,10,11,12,13,15,19,26,27,28,29,30,33,34,35,36,41,42,43,45,46,47,48,49,50,52,53,56,57,58,59,60,62,66,67,68,69,70,73,74,75,80,81,82,83,84,85,87,88,94,95,96,97,98,102,105,108,111,112,113,114,116,118,119,120,121,123,125,126,127,128,129,131,133,136,137,138,144,145,148,149,150,151,152,153,155,156,158,159,173,174,175,177,178,181,182,185,188,189,190,191,192,193,195,196,198,199,213,214,215,217,218,221,222,224,226,228,230,232,234,237,238,239,240,246,247,248,249,255,256,259,260,261,262,263,264,265,266,268,269,273,274,275,276,277,281,282,297,298,299,300,301,302,303,304,305,306,307,309,310,312,313,314,315,317,320,321,322,323,324,325,328,330,333,334,335,336,337,338,342,350,351,352,353,355,356,357,359,361,362,363,364,368,370,371,372,373,374,375,376,377,378,379,381,383,386,388,389,390,391,392,395,396,399,400,401,402,403,404,408,410,411,412,414,415,416,417,418,419,420,421,422,424,426,429,430,431,439,441,443,444,445,447,448,449,450,452,453,454,455,456,457,459,461,462,464,468,469,470,471,472,473,475,478,480,481,484,486,487,490,492,495,497,498,502,503,504,505,507,508,509,510,511,512,513,514,515,516,517,519,521", "uncoveredLines": "71,99,103,109,139,183,242,251,286,287,288,291,292,293,294,326,340,345,346,347,393,434,435,482,488,499"}, "SurveyResponseTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "Survey_ResponseTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,20,26,27,28,29,30,36,37,38,39,40", "uncoveredLines": "13,22,32,42"}, "SyncCampaignMemberToMarketingPerson": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SyncContactRollupOnOppJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SyncMarketingPersonToPersonFunnel": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SyncMarketoIdBatchableJob": {"coverageRate": "0.89", "coveredLines": "2,3,4,5,6,7,8,13,21,22,23,24,25,26,39,40,43,44,45,46,47,48,49,51,52,56,57,59,60,61,63,64,65,66,67,68,70,71,72,73,74,75,77,78,80,86,87,89,90,91,92,93,97,101,102,104,105,109,110,112,113,114,115,116,117,118,119,121", "uncoveredLines": "10,29,30,31,41,83,98,106"}, "SyncMarketoIdScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SyncOppoPermAuditLogMonthlyScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SyncQuoteDTO": {"coverageRate": "1.00", "coveredLines": "3,5,7,8,9", "uncoveredLines": ""}, "SyncRecentMarketoActivityQueueableJob": {"coverageRate": "0.54", "coveredLines": "2,3,4,5,9,10,11,12,14,15,16,17,19,20,21,32,33,41,44,45,46,47,48,50,51,53", "uncoveredLines": "22,23,24,25,26,27,29,34,35,36,37,38,54,55,56,57,58,59,60,61,62,63"}, "SyncRespondedDateTimeOfCampaignMemberJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "SyncStaffingRequestDTO": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TCVApplication": {"coverageRate": "1.00", "coveredLines": "3,4,5,6,7,8,9,10,11,16,17,24,25,26,27,28,40,41,42,43", "uncoveredLines": ""}, "TEHelpPageController": {"coverageRate": "1.00", "coveredLines": "3,5,6,8,9,10,11,12,13,14,15,16,17,20,23,24,27,28", "uncoveredLines": ""}, "TWPSA_TestDataFactory": {"coverageRate": "0.88", "coveredLines": "8,11,12,13,14,15,16,17,18,41,42,43,44,47,48,49,50,78,79,81,82,84,85,86,88,90,91,92,96,100,101,104,105,106,107,108,109,111,114,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133,134,137,139,156,157,158,159,160,161,162,163,164,165,166,169,171,175,176,178,179,182,183,186,187,190,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,223,225,229,230,231,232,233,234,235,236,238,239,240,241,242,243,244,245,246,248,249,250,251,252,253,254,255,256,259,260,261,264,266,270,271,275,276,277,278,280,282,285,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,304,305,306,309,310,313,314,317,319,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,356,357,360,362,400,401,402,404,407,408,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,434,435,438,440,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,464,465,468,470,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,496,497,500,501,504,505,508,509,512,514,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,541,542,544,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,586,587,589,596,597,600,601,602,603,605,609,610,611,612,613,614,615,616,617,618,619,622,624,627,628,629,630,631,632,633,634,635,636,637,640,644,645,646,647,648,649,652,653,654,655,656,657,658,659,660,663,665,672,673,674,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,693,694,695,698,699,701,739,740,741,743,744,745,746,747,748,749,751,752,753,754,755,756,757,759,760,762,763,764,765,766,767,768,769,770,772,773,775,776,777,778,781,782,784,789,792,793,803,804,805,806,807,808,809,810,813,815,819,820,821,822,823,824,825,826,829,830,832,836,837,838,839,840,841,842,845,847,859,860,862,863,865,869,870,872,873,874,877,893,894,895,896,897,898,899,900,901,902,905,906,907,908,910,913,914,915,916,917,918,919,920,921,922,923,924,927,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,949,952,953,954,955,956,957,958,962,963,964,965,966,967,968,969,971,974,975,976,977,978,979,980,981,982,983,987,988,989,990,991,992,994,997,998,999,1002,1003,1004,1005,1008,1009,1010,1011,1012,1013,1015,1016", "uncoveredLines": "22,23,25,26,28,29,30,32,33,37,54,55,59,60,62,63,65,66,67,69,70,74,142,143,145,146,147,150,152,366,367,368,370,375,376,377,378,383,385,386,387,388,389,391,392,393,394,395,396,397,548,549,550,551,552,553,554,555,558,559,562,592,593,797,798,799,850,851,852,853,855,880,881,882,883,884,885,886,887,888,889,890"}, "TWRegionSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "TagAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7", "uncoveredLines": ""}, "TagBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "TagTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "TagTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,5,6,7,8,9,14,15,16,17,18", "uncoveredLines": "11,20"}, "TaskAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "TaskAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "TaskAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": ""}, "TaskBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "TaskBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5", "uncoveredLines": ""}, "TaskBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "TaskController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TaskCountVO": {"coverageRate": "1.00", "coveredLines": "3,5,7,8,9", "uncoveredLines": ""}, "TaskSelector": {"coverageRate": "0.91", "coveredLines": "6,7,10,11,14,15,18,19,22,24,28,29,30,33,34,37,38,41,42,45,46", "uncoveredLines": "2,3"}, "TaskService": {"coverageRate": "0.79", "coveredLines": "4,6,7,18,19,20,21,26,27,30,31,34,35,38,39,42,43,46,47", "uncoveredLines": "10,11,12,13,14"}, "TaskTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "TaskTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,7,9,10,11,12,13,19,20,21,22,23,29,30,31,32,33,39,40,41,42,43,49,50,51,52,53,59,60,61,62,63", "uncoveredLines": "15,25,35,45,55,65"}, "TaxAmountDetailSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "TaxTypeInitService": {"coverageRate": "0.89", "coveredLines": "10,16,17,18,19,22,23,24,25,26,27,28,29,30,33,34,35,38,39,43,44,45,47,48,52,53,54,55,56,57,58,62,63,64,65,66,68,77,80,81,85,86,90,91,92,93,95,96,97,99,100,103,104,107,108,109,110,111,112,113,115,120,121", "uncoveredLines": "59,69,70,71,73,74,78,116"}, "TaxTypeSelector": {"coverageRate": "0.72", "coveredLines": "2,3,4,6,15,18,19,22,23,24,25,29,30", "uncoveredLines": "7,8,9,10,12"}, "TeamName": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TerritoryController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TerritoryDTO": {"coverageRate": "1.00", "coveredLines": "14,16,17,18,19", "uncoveredLines": ""}, "TerritorySelector": {"coverageRate": "0.20", "coveredLines": "14,15", "uncoveredLines": "2,3,6,7,10,11,18,19"}, "TerritoryService": {"coverageRate": "0.79", "coveredLines": "3,5,11,12,16,17,18,25,26,28,29,30,31,32,37,38,39,40,45,46,49,51,53,54,57,69,70,73,76,77,78,79,81,83,95,96,100,101,102,105,106,107,110,112,113,114,118,119,125,126,127,130,131,132,133,137,138,140,141,142,145,148,149,150,151,153,156,157,158,159,162,163,165,166,167,171,172,173,174,175,178,181,182,188,189,190,192,195,196,197,199,200", "uncoveredLines": "14,21,22,41,42,50,52,60,61,62,63,64,66,71,86,87,88,89,90,91,92,97,128,183,184"}, "TimeUtils": {"coverageRate": "1.00", "coveredLines": "8,9,12,13", "uncoveredLines": ""}, "Timecard": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "TimecardAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8", "uncoveredLines": ""}, "TimecardAutoShareService": {"coverageRate": "0.96", "coveredLines": "12,13,14,15,16,17,18,19,20,21,22,26,37,38,39,40,41,42,43,44,45,48,49,51,52,53,55,56,57,58,59,62,66,67,68,69,70,73,74,75,77,79,80,81,82,83,84,88,95,97,98,99,100,101,106,111,112,113,115,116,117,118,120,121,122,123,125,126,127,128,132,133,134,137,138,141,142,144,145,146,149,152,153,157,158,159,160,162,166,168,169,170,171,173,174,176,177,179,180,181,184,185,188,189,191,192,193,196,200,201,202,203,204,206,208,209,211,212,213,214,215,217,220,221,222,223,225,228,229,232,235,236,237,238,240,243,244,245,246,247,250,253,254,255,256,258,259,260,261,265,268,269,270,271,272,273,274,275,276,277,280,282,283,284,285,286,291,292,296,297,298,299,300,301,302,303,306,309,312,313,314,315,318,321", "uncoveredLines": "89,90,91,163,287,288,304,316"}, "TimecardBeforeDeleteTriggerHandler": {"coverageRate": "0.77", "coveredLines": "8,9,10,12,13,16,17,19,20,21", "uncoveredLines": "2,3,4"}, "TimecardBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,6,7,8", "uncoveredLines": ""}, "TimecardBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6", "uncoveredLines": ""}, "TimecardControllerExtension": {"coverageRate": "0.67", "coveredLines": "4,23,24,25,32,33,34,35,36,37,38,41,42,43,44,46,47,48,50,52,63,64,65,67", "uncoveredLines": "6,7,8,11,14,17,28,29,55,56,57,59"}, "TimecardEditingReasonSelector": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "TimecardEditingReasonService": {"coverageRate": "0.94", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,120,122,123,124,125,128,129,130,144,145,160,161,168,169,170,171,174,177,183,184,187,188,189,190,191,193,195,198,204,208,211,212,213,216,217,218,220,221,223,226,227,230,231,234,235,238,239,242,249,250,251,252,253,254,256,257,258,259,261,265,266,267,268,272,273,274,276,277,281,284,285,287,288,289,290,293,294,296,297,298,300,303,304,305,306,307,308,309,310,311,312,313,314,315,316,318,322,323,324,325,326", "uncoveredLines": "149,150,151,155,156,157,164,165,205,262,263,269,270"}, "TimecardEmailController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TimecardEventLog": {"coverageRate": "0.95", "coveredLines": "13,14,18,26,27,28,29,30,31,34,35,46,47,48,49,50,51,53,55,56,57,58,59,60,61,62,63,66,67,68,69,72,75,76,77,78,79,80,81,82,86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151,152,155,158,159,160,161,162,163,164,166,168,169,171,172,173,175,177", "uncoveredLines": "38,39,40,41,42,84"}, "TimecardEventLogSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "TimecardHeader": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "TimecardHeaderAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "TimecardHeaderAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,8,10,11,12,13,15,17,19", "uncoveredLines": ""}, "TimecardHeaderAfterUndelTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,5,6,7,8,9", "uncoveredLines": ""}, "TimecardHeaderAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,7,8,9,10,11,12,14,15,16,17,18,19,20,21", "uncoveredLines": ""}, "TimecardHeaderBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,9,10", "uncoveredLines": ""}, "TimecardHeaderBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "uncoveredLines": ""}, "TimecardHeaderBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "uncoveredLines": ""}, "TimecardHistoryController": {"coverageRate": "1.00", "coveredLines": "2,3,5,10,12,13,14,15,16,17,18,19,20,21,22,23,24,28,29,30,33,35,37,41,42,43,46,50,53,54,55,58,60,61,62,65,67,71,72,73,74,76,80,81,82,83,84,85,86,88,89,90,91,94,98,99,101,102,103,106,116,118,120", "uncoveredLines": ""}, "TimecardHoursDaysRecaculateBatchableJob": {"coverageRate": "0.91", "coveredLines": "2,3,5,6,9,10,11,14,15,16,17,21,24,25,26,27,28,29,31,34,35", "uncoveredLines": "19,36"}, "TimecardSelector": {"coverageRate": "0.77", "coveredLines": "2,3,4,30,35,36,37,41,42,56,69,70,71,75,76,79,80,92,97,98,108,113,114,115,171,172,185,189,190,191,225,230,231,232,246,251,252,253,257,258,259,260,261,267,268,269,272,278,279,280,299,304,305,306,352,357,358,359,399,400,401,402,404,469,470,471,472,473,476,479,482,483,490,491,492,493,539,540,543,544,547,548,549,567,568,569,577,578,579,583,584,589,613,614,670,675,676,677,697,709,710,711,714,719,720,763,764,767,770,771,774,775,778,782,783,796,848,849,852,856,857,858,862,863,864,865,867,868,870,871,873,876,877,880,881,884,885,888,892,893,896,900,901,904,908,909,931", "uncoveredLines": "60,61,64,66,119,120,148,152,153,167,413,414,415,455,456,457,458,460,474,499,500,535,545,592,596,597,601,603,608,609,703,704,705,800,801,837,838,839,840,841,842,843,935,936,939"}, "TimecardService": {"coverageRate": "0.82", "coveredLines": "2,22,23,24,25,26,27,28,29,32,33,34,35,36,37,38,39,41,42,51,52,53,54,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,78,88,89,90,91,92,93,94,95,166,167,168,169,170,172,173,174,175,176,178,179,180,185,186,187,188,189,191,192,193,196,197,198,199,203,204,207,208,209,210,211,212,258,259,260,263,264,265,266,267,270,271,272,273,279,280,281,282,284,285,286,288,289,290,291,292,294,299,300,301,303,304,305,308,309,310,311,312,313,314,315,319,322,323,325,326,327,328,331,334,335,336,338,339,340,341,342,344,346,347,349,350,351,352,353,355,360,361,362,363,366,367,372,373,375,378,379,380,382,383,386,389,390,391,392,393,394,395,396,397,398,399,400,401,402,404,405,406,407,410,414,421,422,423,424,425,426,427,428,429,430,432,433,434,438,439,440,442,443,444,445,446,447,448,449,450,451,452,453,456,457,458,460,463,472,473,474,476,477,478,481,482,483,484,485,488,489,490,491,492,495,496,498,499,500,501,502,503,504,505,508,511,512,513,514,515,516,517,518,519,521,522,523,524,525,526,527,528,529,530,532,533,535,536,539,542,543,547,556,557,558,560,561,562,563,564,565,568,569,571,572,573,574,575,576,577,578,581,590,591,592,593,594,595,596,598,599,600,601,602,603,604,606,607,610,614,615,617,621,622,623,624,625,626,627,628,629,630,631,632,634,635,636,637,638,639,640,641,642,643,644,664,665,666,668,669,671,672,673,674,679,680,681,682,683,685,690,691,692,693,700,701,702,703,704,707,708,709,712,713,714,715,717,718,719,723,724,725,726,728,729,731,734,737,740,743,749,750,751,752,753,758,759,760,761,762,763,764,765,767,770,771,772,773,774,775,776,777,778,780,781,785,786,787,788,789,790,795,796,797,799,800,806,807,808,810,811,812,813,814,815,816,819,820,821,822,823,826,827,828,829,831,832,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,856,859,860,862,863,864,865,866,868,869,870,871,872,873,874,875,880,882,883,884,885,886,887,889,892,893,894,895,896,897,898,899,900,901,903,906,907,908,909,910,911,912,914,917,918,919,920,921,923,924,927,928,929,930,932,935,936,938,941,942,943,944,947,950,951,952,953,956,959,960,961,962,963,967,968,969,970,975,976,977,978,979,980,982,983,986,989,990,991,992,993,996,998,999,1000,1001,1005,1008,1009,1010,1011,1014,1016,1017,1018,1019,1020,1021,1033,1034,1035,1036,1037,1038,1039,1042,1043,1044,1045,1046,1047,1048,1053,1054,1056,1057,1058,1059,1060,1063,1064,1124,1125,1126,1127,1128,1129,1130,1132,1135,1136,1137,1138,1139,1141,1142,1143,1144,1145,1147,1148,1154,1155,1156,1157,1158,1159,1160,1164,1165,1166,1167,1168,1170,1173,1174,1175,1177,1178,1182,1183,1186,1187,1188,1189,1190,1191,1192,1193,1194,1197,1198,1199,1201,1204,1205,1206,1207,1208,1209,1210,1211,1223,1224,1225,1226,1227,1228,1229,1234,1235,1236,1238,1239,1242,1243,1244,1245,1246,1248,1249,1250,1251,1252,1253,1259,1260,1261,1262,1263,1264,1273,1277,1278,1279,1283,1284,1285,1286,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1303,1307,1310,1313,1314,1351,1352,1354,1355,1356,1357,1358,1360,1361,1362,1363,1367,1368,1369,1370,1371,1375,1376,1379,1380,1381,1382,1383,1387,1390,1391,1392,1393,1394,1395,1396,1398,1399,1400,1403,1404,1406,1407,1411,1412,1414,1415,1416,1417,1421,1422,1426,1427,1430,1431,1432,1433,1435,1436,1438,1439,1440,1442,1447,1448,1449,1451,1452,1454,1455,1456,1457,1458,1459,1460,1463,1464,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1480,1483,1484,1489,1490,1492,1493,1497,1498,1499,1503,1504,1505,1506", "uncoveredLines": "98,103,104,105,106,107,110,111,112,113,114,115,117,118,122,123,124,125,126,128,130,131,134,136,137,138,141,142,145,146,148,149,152,153,154,155,156,158,162,218,219,220,221,222,223,224,225,226,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,356,611,647,648,649,650,651,653,654,655,656,658,659,661,732,735,738,741,744,922,1026,1027,1028,1030,1068,1069,1070,1072,1073,1076,1077,1078,1079,1082,1083,1084,1086,1087,1088,1090,1092,1094,1095,1097,1098,1099,1101,1102,1103,1105,1106,1107,1110,1112,1113,1115,1117,1118,1121,1212,1213,1214,1215,1216,1218,1220,1256,1266,1267,1268,1269,1288,1302,1304,1305,1311,1318,1319,1320,1321,1322,1323,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1338,1341,1342,1343,1344,1346,1347,1443,1444"}, "TimecardShareBatchableJob": {"coverageRate": "0.92", "coveredLines": "2,9,12,13,17,25,26,27,28,29,30,33,39,40,41,42,43,44,47,48,50,51,53,58,59,60,61,62,65,66,67,68,69,70,73,74,75,76,78,79,80,81,87,88,92,95,96,99,100,103,108,109,110,111,112,114", "uncoveredLines": "54,55,90,101,115"}, "TimecardShareRevokeBatchableJob": {"coverageRate": "0.98", "coveredLines": "2,15,22,23,24,25,26,27,28,32,41,42,43,44,45,46,47,50,51,52,53,54,55,56,57,58,59,63,64,66,68,69,72,73,74,75,76,78,81,82,83,85,86,87,88,90,92,93,96,97,98,105,106,107,108,109,111,112,113,114,116,117,118,119,121,122,123,124,132,133,134,137,138,139,140,141,142,143,145,149,152,153,156,157,160,161,162,164", "uncoveredLines": "99,100"}, "TimecardShareSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,8,9,10,14,15,16,20,21,22,38,41,55,56,68", "uncoveredLines": ""}, "TimecardSplitsSelector": {"coverageRate": "0.50", "coveredLines": "15,16,17,27,28,31,35,36,48,52,53,62,66,67,68,72,81,82,83,87,157,158,166,171,172,176,179,180,181,182,213,214,215,217,309,310,311,312,313,343,344,345,390,391,392,394,395,396,397,399,402,406,407,480", "uncoveredLines": "2,3,7,21,22,23,98,99,105,108,110,114,115,116,117,118,120,123,126,127,139,140,144,148,149,150,154,220,251,252,253,254,255,256,258,262,291,294,295,296,297,298,300,301,303,349,376,378,381,382,384,385,485,486"}, "TimecardTriggerDataHolder": {"coverageRate": "0.85", "coveredLines": "6,7,8,9,12,14,16,18,20,21,22,23,30,31,32,34,35,37,38,40,41,43,44", "uncoveredLines": "25,26,27,28"}, "TimecardTriggerDispatcher": {"coverageRate": "0.71", "coveredLines": "2,3,4,5,7,8,9,10,11,16,17,18,19,20,34,35,36,37,38,40", "uncoveredLines": "13,22,25,26,27,28,29,31"}, "Timecard_HeaderTriggerDispatcher": {"coverageRate": "0.88", "coveredLines": "2,3,4,5,6,7,8,10,11,12,13,14,19,20,21,22,23,28,29,30,31,32,37,38,39,40,41,43,46,47,48,49,50,55,56,57,58,59,64,65,66,67,68", "uncoveredLines": "16,25,34,52,61,70"}, "TransferInactiveOwnershipController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TransferInactiveOwnershipService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TravelExpenseFactory": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,8,10", "uncoveredLines": ""}, "TravelRawDataService": {"coverageRate": "0.97", "coveredLines": "2,3,4,6,7,8,9,11,13,17,18,21,22,23,24,25,27,28,30,31,32,33,34,35,36,38,40,41,43,44,46,47,49,50,53,54,56,59,60,62,67,68,69,70,71,72,73,75,80,81,82,83,84,88,89,90,91,92,93,95,96,97,99,102,103,105,107,108,112,113,114,115,116,119,120,123,124,125,130,131,132,133,134,135,137,138,139,140,144,145,146,147,148,149,150,152,153,154,157,158,160,161,164,165,166,170,171,172,173,174,175,176,177,179,180,187,188,189,190,191,192,193,194,196,197,202,203,204,205,206,207,208,213,214,217,218,219,220,221,222,223,224,225,229,230,231,232,233,235,240,241,242,243,244,249,250,251,252,254,257,258,260,263,267,268,269,270,271,274,277,280,285,286,287,288,289,290,293,294,295,296,298,299,301,302,303,304,306,309,316,317,318,319,320,321,322,323,324,325,328,329,330,333,334,337,338,342,343,348,349,350,351,352,353,355,357,358,359,361,362,363,365,367,368", "uncoveredLines": "162,167,259,261,272,278,291,292"}, "TriggerDispatcherBase": {"coverageRate": "0.88", "coveredLines": "22,31,40,51,60,69,94,96,97,100,101,102,104,105,107,108,110,111,113,114,116,117,119,120,122,123,125,128,131,132,134,135,137,138,140,143", "uncoveredLines": "78,126,129,141,144"}, "TriggerException": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "TriggerFactory": {"coverageRate": "0.98", "coveredLines": "5,6,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,29,30,33,35,36,37,38,39,40,41,44,45,46,47,48,49,50,51,56,57,59,60,61,64,65,66,67,68,70", "uncoveredLines": "27"}, "TriggerHandlerBase": {"coverageRate": "0.80", "coveredLines": "9,27,35,36", "uncoveredLines": "37"}, "TriggerParameters": {"coverageRate": "1.00", "coveredLines": "22,24,25,26,27,28,29,31,32,33,34,35,36,37,38,57,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,90,91", "uncoveredLines": ""}, "TriggerToggle": {"coverageRate": "1.00", "coveredLines": "2,4,5,8,9,12,13", "uncoveredLines": ""}, "UTTargetBatchableJob": {"coverageRate": "0.90", "coveredLines": "2,5,7,8,9,13,14,15,18,19,20,21,24,25,26,27,28,29,30,33,34,35,38,43,44,47", "uncoveredLines": "36,39,45"}, "UTTargetChangeConfigSelector": {"coverageRate": "0.67", "coveredLines": "2,3,6,10,11,14,27,28,35,36", "uncoveredLines": "18,19,22,31,32"}, "UTTargetScheduleJob": {"coverageRate": "0.67", "coveredLines": "2,3,9,10", "uncoveredLines": "5,6"}, "UpdateAccountOwnerResultDTO": {"coverageRate": "1.00", "coveredLines": "13,14,15,16", "uncoveredLines": ""}, "UpdateAssignmentCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateCampaignMemberEngagedJob": {"coverageRate": "0.91", "coveredLines": "2,3,8,9,10,13,14,15,17,18,19,24,25,26,27,29,30,33,36,39,40", "uncoveredLines": "21,28"}, "UpdateClientStatusScheduleJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateContractAcceptDetailBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,4,6,7,10,11,12,13,14,15,16,19,22,23,26,29,30,33,34,39,43,44", "uncoveredLines": ""}, "UpdateContractAmendatoryTypeBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateContractApprovalDetailBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,4,6,7,10,11,12,13,14,15,16,19,22,23,26,29,30,33,34,39,43,44", "uncoveredLines": ""}, "UpdateExpenseCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateMilestoneCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateMiscellaneousCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOppIsExtendedBatchable": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOppMonthlyExchangeRateBatchableJob": {"coverageRate": "0.87", "coveredLines": "2,6,7,14,15,18,19,20,21,22,23,24,28", "uncoveredLines": "10,11"}, "UpdateOppOverallSkillNameBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOppoContractValueBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOppoContractValueBatchableJob2": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOpporEmailFieldBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOpportunityGMD": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOpportunityOwnerResultDTO": {"coverageRate": "1.00", "coveredLines": "11,12,13", "uncoveredLines": ""}, "UpdateOthContractAmountTypeBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateOwnerResultDTO": {"coverageRate": "1.00", "coveredLines": "15,16,17,18,19", "uncoveredLines": ""}, "UpdateProjectCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateRecordsAfterConvertQueueableJob": {"coverageRate": "1.00", "coveredLines": "3,4,5,7,8,11,12,13,14", "uncoveredLines": ""}, "UpdateRecordsAfterMergeQueueableJob": {"coverageRate": "0.73", "coveredLines": "2,7,8,9,10,11,13,14,15,16,17,20,21,22,23,24,25,27,28,30,31,33,34,35,36,37,38,39,41,42,43,44", "uncoveredLines": "46,47,48,49,50,51,52,53,54,55,56,57"}, "UpdateResReqCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateSalesInvoiceStatusBatchableJob": {"coverageRate": "1.00", "coveredLines": "2,3,5,7,8,9,10,13,14,17,18,19,20,21,23,24,25,26,29", "uncoveredLines": ""}, "UpdateTimecardCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UpdateTimecardSplitCurrencyBatchableJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UserExitScheduleJob": {"coverageRate": "0.87", "coveredLines": "2,3,4,5,6,12,13,15,16,20,21,22,24", "uncoveredLines": "8,9"}, "UserFieldsResetScheduleJob": {"coverageRate": "0.79", "coveredLines": "2,3,4,5,6,7,8,9,10,11,19,20,21,22,23,24,28,34,35,36,38,39,40,43,44,45,46,49,50,51,56,57,59,60,62,64,65,67,69,70,71,73,76,79,80,81,82,83,86,89,90,91,92,120,121,122,123,124,128,129,130,131,137,138,139,140,145,146,147,148,149,150,153,156,157,159,160,161,162,167,168,169,170,171,172,173,175,177,181,182,183,184,186,187,188,190,191,192,195,197,199,207,208,211,212,213,218,219,220,221,225,228,229,230,231,232,233,234,235,236,237,238,239,243,244,250,251,252,253,254,255,256,258", "uncoveredLines": "13,14,15,16,25,26,29,95,96,98,99,100,103,104,105,106,108,109,110,111,112,114,117,132,200,201,203,209,214,215,262,263,264,265,266,267"}, "UserHierarchyDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,15,18,21,24,27", "uncoveredLines": ""}, "UserHierarchySelector": {"coverageRate": "1.00", "coveredLines": "2,3,6,7", "uncoveredLines": ""}, "UserHierarchyService": {"coverageRate": "0.85", "coveredLines": "2,3,4,6,8,30,31,32,35,36,39,40,48,49,50,51,52,54,57,58,59,62,64,66,67,71,75,76,80,84,85,86,89,90,91,94,98,102,107,108,112,115,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,148,149,153,155,156,157,161,163,165,167,170,171,172,173,174,175,176,177,180,181,183,187,188,196,197,198,201,202,203,204,205,208,209,210,211,212,213,214,215,216,217,218,220,221,222,224,227,230,231,232,235,236,237,238,239,242,243,245,246,247,250,251,253,254,255,256,257,260,261,262,263,264,265,266,267,268,269,270,271,272,273,276,277,278,279,280,281,282,283,284,285,290", "uncoveredLines": "10,11,12,13,14,15,16,18,19,20,21,23,24,26,33,37,41,42,43,45,68,109,126,164,206,258,274"}, "UserHierarchyWithPOHDTO": {"coverageRate": "1.00", "coveredLines": "3,6,9,12,15,18,21,25", "uncoveredLines": ""}, "UserInfoService": {"coverageRate": "0.82", "coveredLines": "4,8,9,10,20,21,24,25,28,29,32,33,36,37,39,40,41,44", "uncoveredLines": "13,14,15,17"}, "UserLicenseSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3"}, "UserMarketOrgAfterDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "UserMarketOrgAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "UserMarketOrgAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8", "uncoveredLines": ""}, "UserMarketOrgBeforeInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,7,8,9", "uncoveredLines": ""}, "UserMarketOrgBeforeUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,8,9,10,11,13,14", "uncoveredLines": ""}, "UserMarketOrgController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UserMarketOrgSelector": {"coverageRate": "0.50", "coveredLines": "6,7", "uncoveredLines": "2,3"}, "UserMarketOrgService": {"coverageRate": "0.95", "coveredLines": "2,3,4,6,9,10,13,14,15,18,19,20,21,23,25,26,27,28,29,31,35,36,37,38,39,40,41,42,43,47,50,53,54,55,56", "uncoveredLines": "59,60"}, "UserMarketOrgTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "UserPackageLicenseSelector": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,7,12,13,16,17,20", "uncoveredLines": ""}, "UserQueueableJob": {"coverageRate": "1.00", "coveredLines": "2,6,7,8,11,12", "uncoveredLines": ""}, "UserRecordAccessController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UserRecordAccessSelector": {"coverageRate": "0.25", "coveredLines": "2,3,6,10,11,14,17", "uncoveredLines": "4,12,15,20,21,22,23,25,26,29,30,31,32,33,34,36,38,39,41,46,47"}, "UserRecordAccessService": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UserRoleSelector": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "UserSearchBar": {"coverageRate": "1.00", "coveredLines": "3,4,5,7,8,9,13,14,16,17,18,19,21,22,24,25,26,27,28,29,30,31,32,34,38,39,40,41,42,43", "uncoveredLines": ""}, "UserSelector": {"coverageRate": "0.62", "coveredLines": "2,9,10,13,14,17,18,21,22,33,34,37,38,45,46,49,50,53,54,65,66,67,76,77,84,85,88,89,92,93,96,97,100,101,102,103,105,107,108,111,112,115,116,130,131,138,139,146,147,150,151,154,155,158,159,162,163,166,167,190,191", "uncoveredLines": "5,6,25,26,29,30,41,42,57,58,59,60,62,80,81,119,120,122,123,124,125,127,134,135,142,143,170,171,174,175,176,177,179,182,183,184,185,187"}, "UserTerritory2AssociationSelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6"}, "UserTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "UserTypeTerritoryDTO": {"coverageRate": "0.43", "coveredLines": "11,12,13", "uncoveredLines": "16,17,18,19"}, "User_Market_OrgTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,5,6,8,9,10,11,12,17,18,19,20,21,26,27,28,29,30,35,36,37,38,39,44,45,46,47,48", "uncoveredLines": "14,23,32,41,50"}, "Utility": {"coverageRate": "0.82", "coveredLines": "2,10,11,12,13,14,15,20,23,24,25,26,27,28,31,34,35,36,39,40,43,44,45,46,49,50,51,52,54,68,69,70,72,74,75,77,78,80,81,83,84,85,86,89,90,91,94,95,96,99,100,101,103,104,105,109", "uncoveredLines": "4,5,6,7,17,18,57,58,59,60,61,65"}, "UtilityClass": {"coverageRate": "1.00", "coveredLines": "15,16", "uncoveredLines": ""}, "UtilityJson": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,11,12,13,14,15,19", "uncoveredLines": ""}, "VFControllerForSIPendingAlert": {"coverageRate": "0.86", "coveredLines": "2,3,8,9,10,13,14,15,18,19,20,27,28,29,30,31,32,35", "uncoveredLines": "4,23,24"}, "VoiceOfCustomerController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "VoiceOfCustomerSelector": {"coverageRate": "0.38", "coveredLines": "15,16,17", "uncoveredLines": "2,3,6,11,12"}, "VoiceOfCustomerService": {"coverageRate": "1.00", "coveredLines": "2,4,5,6,7,8,11,12,14,17,18,19", "uncoveredLines": ""}, "WebActivity": {"coverageRate": "0.50", "coveredLines": "3,5,7,11,12,13,14,15,16,17", "uncoveredLines": "9,19,20,21,22,23,24,25,26,27"}, "WebActivityAfterInsertTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5,6", "uncoveredLines": ""}, "WebActivityAfterUpdateTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "WebActivityBatchJob": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "WebActivityBeforeDeleteTriggerHandler": {"coverageRate": "1.00", "coveredLines": "2,4,5", "uncoveredLines": ""}, "WebActivityObjectTriggerDispatcher": {"coverageRate": "0.86", "coveredLines": "2,3,4,6,7,8,9,10,16,17,18,19,20,26,27,28,29,30", "uncoveredLines": "12,22,32"}, "WebActivitySelector": {"coverageRate": "0.00", "coveredLines": "", "uncoveredLines": "2,3,6,7"}, "WebActivityService": {"coverageRate": "0.96", "coveredLines": "2,3,4,5,6,8,9,11,12,13,14,17,18,19,20,21,22,23,24,26,29,30,31,32,35,36,37,38,39,40,41,42,43,46,47,48,49,50,51,52,53,56,58,61,62,63,64,65,66,67,68,69,70,71,72,73,76,79,80,81,82,85,86,87,88,91,92,95,98,99,101,102,103,104,105,109,113,114,115,117,118,120", "uncoveredLines": "83,89,110"}, "WebActivityTrigger": {"coverageRate": "1.00", "coveredLines": "2", "uncoveredLines": ""}, "WithSharingDMLUtils": {"coverageRate": "1.00", "coveredLines": "2,3,4", "uncoveredLines": ""}, "WorkAsRiskDTO": {"coverageRate": "1.00", "coveredLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "uncoveredLines": ""}, "WorkCalendarSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "WorkingDetailsDTO": {"coverageRate": "0.95", "coveredLines": "3,5,7,9,11,13,15,17,19,21,26,38,39,40,41,42,43,44,45,46,47", "uncoveredLines": "23"}, "WorkingOfficeController": {"coverageRate": "NaN", "coveredLines": "", "uncoveredLines": ""}, "WorkingOfficeDTO": {"coverageRate": "1.00", "coveredLines": "9,10,11,12", "uncoveredLines": ""}, "WorkingOfficeRate": {"coverageRate": "1.00", "coveredLines": "5,7,8,9,12", "uncoveredLines": ""}, "WorkingOfficeSelector": {"coverageRate": "1.00", "coveredLines": "2,3", "uncoveredLines": ""}, "WorkingOfficeService": {"coverageRate": "0.63", "coveredLines": "2,3,8,9,12,13,14,15,16,18", "uncoveredLines": "5,21,22,23,24,26"}, "ZoomInfoAuthProvider": {"coverageRate": "0.74", "coveredLines": "2,3,4,6,9,10,14,15,16,17,18,19,20,21,22,24,25,27,30,32,33,34,35,36,37,38,40,41,42,43,44,51,52,53", "uncoveredLines": "11,28,48,55,56,57,58,59,61,62,63,66"}, "ZoomInfoClient": {"coverageRate": "0.85", "coveredLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,17,18,19,20", "uncoveredLines": "22,25,28"}, "ZoomInfoFieldFormater": {"coverageRate": "0.94", "coveredLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,44,46,49,50,53,54,57,58,60,63,64,65,67,70,71,75,76,77,79,80,81,84,85,86,89,90,94,97,98,101,102,103,106,109,110,111,113,114,115,116,119,120,122,123,124,130,131,134", "uncoveredLines": "55,91,117,127,132"}, "ZoomInfoService": {"coverageRate": "0.70", "coveredLines": "2,3,4,7,10,11,14,15,16,17,22,23,24,27,29,34,35,40,41,42,43,44,45,46,47,50,51,60,61,62,63,64,65,66,68,71,74,75", "uncoveredLines": "18,19,20,25,30,31,32,36,37,38,52,53,54,55,56,76"}, "fflib_AnyOrder": {"coverageRate": "1.00", "coveredLines": "17,18,19,21,23,24,26,27,28,29,30,31,32,33,36,37,41,42,44,45,46,47,48,49,53,57,60,61,63,64,65,68,78,79,80", "uncoveredLines": ""}, "fflib_ApexMocks": {"coverageRate": "0.82", "coveredLines": "9,18,20,22,27,29,32,39,40,42,43,44,46,48,56,57,70,71,74,75,76,77,78,87,88,97,98,99,101,110,111,118,119,120,122,128,129,135,136,144,145,152,153,161,162,170,171,179,180,181,189,190,191,199,200,201,204,205,207,208,209,210,213,214,218,229,242,243,244,245,247,249,250,251,253,255,257,259,261,263,265,267,268,269,271,272,273,275,278,279,282,283,284,286,287,288,289,290,305,316,317,328,329,330,332,334,335,336,337,339,340,341,342,345,346,347,348,351,353,354,357,363,364,366,367,368,371,373,374,377,378,381,384,399,400,409,410,418,419,428,429,437,438,446,447,456,457,464,465,474,475,484,485", "uncoveredLines": "206,219,220,221,224,225,230,231,232,235,236,237,238,239,246,248,252,254,256,258,260,262,264,266,292,293,294,295,296,298,299,300,301,302"}, "fflib_ApexMocksUtils": {"coverageRate": "1.00", "coveredLines": "66,68,69,70,77,79,80,81,82,83,90,91,92,94,96,103,104,106,108,109,115,116,118,119,120,121,122,127,128,129,130,133,144,146,147,148,149,150,153,155,156,157,158,159,160,161,162,163,164,172,173,174,176,177,179,180,181,182,183,184,185,186,187,189,190,191,192,193,195,196,197,198,199,200,201,204,205,219,220,221,222,223,225,231,232,233,234,235,236,238", "uncoveredLines": ""}, "fflib_Application": {"coverageRate": "0.89", "coveredLines": "51,52,60,62,63,64,72,74,75,76,87,89,90,91,102,104,105,106,110,111,137,138,139,150,152,153,156,157,158,159,163,164,188,189,190,199,201,202,205,206,207,210,221,223,224,227,228,229,230,233,248,249,250,251,252,253,255,259,260,304,305,306,307,318,319,330,331,332,333,335,338,340,341,344,345,346,349,352,353,354,358,372,373,374,376,380,381,389,390,391,392,394", "uncoveredLines": "43,126,178,277,289,290,291,292,355,385,386"}, "fflib_ArgumentCaptor": {"coverageRate": "1.00", "coveredLines": "35,46,47,57,58,60,69,70,71,75,84,85,92,93,97,98,99,103,104", "uncoveredLines": ""}, "fflib_IDGenerator": {"coverageRate": "1.00", "coveredLines": "27,33,34,35,37,39", "uncoveredLines": ""}, "fflib_InOrder": {"coverageRate": "1.00", "coveredLines": "10,12,21,22,23,35,36,37,46,47,48,59,60,61,68,69,70,73,74,76,84,85,86,98,99,100,101,102,103,104,107,108,109,110,113,114,115,116,120,121,122,124,125,126,130,134,135,139,140,141,142,144,145,147,150,153,156,157,159,160,161,162,163,168,171,172,175,176,177,178,180,181,182,189,192,194,195,197,199,201,204,207,208,211,212,213,214,215,216,217,218,221,225,228,229,230,231,235,244,245,247,248,249,250,251,256,266,267,268", "uncoveredLines": ""}, "fflib_InvocationOnMock": {"coverageRate": "1.00", "coveredLines": "21,22,23,24,33,34,35,42,43,50,51,58,59,66,67,70,71,72", "uncoveredLines": ""}, "fflib_Match": {"coverageRate": "1.00", "coveredLines": "27,33,41,42,44,45,47,48,49,50,51,53,54,60,71,72,74,75,76,77,79,80,84,87,88,89,92,93,96,97,100,101,102,104,105,106,107,108,109,110,121,122,123,125,140,141,151,152,163,164,172,173,182,183,193,194,205,206,214,215,223,224,233,234,244,245,256,257,265,266,269,270,273,274,275,280,282,283,284,285,288,292,293,295,299,311,312,320,321,329,330,338,339,347,348,356,357,365,366,374,375,383,384,392,393,401,402,410,411,419,420,428,429,436,437,444,445,452,453,460,461,468,469,476,477,484,485,492,493,500,501,508,509,516,517,524,525,532,533,540,541,548,549,557,558,567,568,576,577,586,587,596,597,608,609,617,618,627,628,636,637,646,647,656,657,668,669,678,679,690,691,699,700,709,710,718,719,728,729,738,739,750,751,759,760,769,770,778,779,788,789,797,798,807,808,819,820,828,829,838,839,847,848,857,858,865,866,873,874,882,883,890,891,900,901,912,913,921,922,931,932,940,941,950,951,959,960,967,968,975,976,984,985,993,994,1001,1002,1010,1011,1019,1020,1027,1028,1035,1036,1044,1045,1053,1054", "uncoveredLines": ""}, "fflib_MatcherDefinitions": {"coverageRate": "1.00", "coveredLines": "60,61,62,65,66,67,68,70,71,73,75,77,85,88,89,90,93,96,97,98,101,104,105,106,107,109,111,112,113,115,116,119,140,141,144,145,148,149,164,165,168,169,172,173,185,186,189,190,198,199,202,203,211,212,215,216,224,225,228,229,237,238,241,242,250,251,254,255,263,264,267,268,276,277,280,281,289,290,293,294,302,303,306,307,315,316,319,320,328,329,332,333,341,342,345,346,354,355,358,359,367,368,371,372,393,394,395,398,399,400,401,404,407,408,409,411,429,430,431,434,435,436,437,440,443,444,445,447,469,470,471,472,473,476,477,478,479,480,484,487,488,489,490,516,517,518,519,520,523,524,525,527,528,532,535,536,537,538,556,557,558,561,562,563,564,567,570,571,572,574,592,593,594,597,598,599,600,603,606,607,608,610,630,631,634,635,638,639,642,643,655,656,659,660,668,669,672,673,692,693,696,697,698,699,700,705,708,709,717,718,721,722,741,742,745,746,747,748,751,754,755,774,775,778,779,780,781,782,784,787,790,791,792,795,798,799,830,832,842,843,844,846,847,848,850,851,852,853,856,857,860,861,862,865,866,867,869,872,873,875,876,879,882,883,888,889,890,891,899,900,901,904,907,910,911,912,915,918,919,920,922,931,932,933,935,936,937,939,944,947,961,962,965,966,967,968,971,974,975,990,991,994,995,996,997,998,999,1000,1005,1008,1009,1028,1029,1032,1033,1036,1037,1052,1053,1056,1057,1060,1061,1069,1070,1073,1074,1082,1083,1086,1087,1103,1104,1105,1108,1109,1112,1113,1128,1129,1132,1133,1136,1137,1145,1146,1147,1150,1153,1154,1155,1156,1157", "uncoveredLines": ""}, "fflib_MatchersReturnValue": {"coverageRate": "1.00", "coveredLines": "30,31,32", "uncoveredLines": ""}, "fflib_MethodArgValues": {"coverageRate": "1.00", "coveredLines": "35,36,44,45,46,49,50,57,58,59,61,63", "uncoveredLines": ""}, "fflib_MethodCountRecorder": {"coverageRate": "1.00", "coveredLines": "17,19,25,26,33,34,41,42,44,45,46,49,51", "uncoveredLines": ""}, "fflib_MethodReturnValueRecorder": {"coverageRate": "1.00", "coveredLines": "31,33,44,46,47,49,57,58,60,61,62,63,66,69,70,71,72,74,78,79,81,89,90,91,92,93,94,95,100,107,108", "uncoveredLines": ""}, "fflib_MethodVerifier": {"coverageRate": "0.96", "coveredLines": "15,16,18,43,44,45,46,51,62,63,68,69,71,72,73,78,79,81,83,86,87,88,89,90,91,92,93,94,95,96,100,103,104,105,106,109,112,113,114,115,116,120,123,124,125,126,129,135", "uncoveredLines": "130,131"}, "fflib_Objects": {"coverageRate": "0.86", "coveredLines": "27,30,36,37,40,41,44,45,48,49,52,53,56,59,60,63,64,67,70,71,74,75,78,81,82,85,86,89,92,93,96,97,100,101,104,105", "uncoveredLines": "54,61,65,76,83,87"}, "fflib_QualifiedMethod": {"coverageRate": "1.00", "coveredLines": "10,11,14,15,16,17,18,26,27,28,31,33,34,35,36,37,44,45,46,48,49,51,52,53,55,62,63,70,71", "uncoveredLines": ""}, "fflib_QualifiedMethodAndArgValues": {"coverageRate": "1.00", "coveredLines": "13,14,15,16,19,20,23,24,27,28,31,32", "uncoveredLines": ""}, "fflib_QueryFactory": {"coverageRate": "0.94", "coveredLines": "71,85,86,97,104,105,108,110,111,112,114,115,118,122,123,124,125,126,127,128,130,131,134,135,137,138,140,141,142,143,148,149,150,152,153,155,159,162,163,167,168,169,171,180,181,182,183,191,192,193,194,195,205,206,207,214,215,216,226,227,230,231,232,241,242,243,250,251,260,261,262,271,272,278,279,285,286,289,290,291,293,301,302,310,311,314,315,316,317,318,320,321,323,325,350,358,359,362,368,369,370,375,376,381,382,383,388,389,394,395,396,407,408,409,415,416,417,422,423,429,430,440,445,456,461,470,471,481,482,483,484,486,487,497,498,508,509,518,519,520,522,523,525,526,529,532,534,535,537,538,544,545,546,548,555,556,559,560,563,570,571,572,573,576,590,591,592,606,607,608,623,624,625,640,641,642,656,657,658,672,673,674,687,688,689,702,703,704,710,711,712,719,720,722,723,724,726,728,730,731,734,737,738,739,742,744,745,749,750,751,752,755,756,757,758,759,762,763,765,768,769,772,779,780,782,783,784,785,786,788,791,792,793,795,803,804,810,811,817,818,821,822,823,824,826,827,829,830,832,833,840,841,842,843", "uncoveredLines": "330,331,339,340,341,342,343,344,345,347,401,402,766,813,814"}, "fflib_SObjectDescribe": {"coverageRate": "0.91", "coveredLines": "43,45,46,47,51,53,54,55,59,61,62,63,67,69,70,72,77,78,80,81,88,89,94,95,102,103,105,108,113,114,115,117,123,124,125,126,127,128,132,138,139,145,146,148,149,151,152,155,157,158,159,163,165,166,168,177,179,180,181,185,186,187,188,189,190,191,194,196,198,199,200,201,202,203,204,206,207,208,209,210,212,214,215,216,221,222,224,225,229,230,231,247,249,250,265,266,267,270,271,272,273,274,275,280,281,283,284,285,289,290,292,293,299,300,302,303,304,305,306,308,310,320,321,324,325,327,328,330,331,339,340,343,344,346,347", "uncoveredLines": "79,107,110,192,211,217,253,254,259,260,277,288,349,350"}, "fflib_SObjectDomain": {"coverageRate": "0.93", "coveredLines": "45,47,55,57,58,60,65,73,78,83,96,98,100,102,111,112,124,126,129,135,141,147,153,159,165,171,177,183,189,195,196,197,203,204,210,211,219,220,223,224,232,233,236,237,238,239,247,248,251,259,260,263,277,278,279,280,281,285,286,287,288,289,293,300,301,302,303,304,307,308,309,310,311,315,350,352,354,364,376,377,379,380,381,384,385,386,387,388,389,390,391,394,396,400,401,405,406,407,408,409,410,411,413,414,415,416,417,418,419,420,427,428,429,430,431,437,438,439,440,441,443,444,447,448,449,452,456,457,458,460,461,462,463,465,466,470,471,472,474,475,476,478,479,480,483,484,485,487,488,489,491,492,493,497,498,499,501,502,503,505,506,507,509,510,511,514,515,516,518,519,520,522,523,524,526,527,528,531,532,535,536,539,540,543,544,547,548,551,552,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,573,584,588,593,598,599,600,601,607,608,609,639,640,641,662,663,669,670,677,679,682,683,686,687,688,689,690,691,692,695,696,699,700,701,702,703,704,705,706,709,710,713,714,724,734,750,752,760,761,762,763,764,765,767,770,772,775,778,779,780,781,782,783,786,787,788,789,790,791,792,795,796,797,798,799,800,803,804,805,806,807,808,811,812,822,824,827,829,832,834,837,838,842,844,847,848,849,854,856,859,860,861,862,867,869,872,873,877,879,882,884,887,889,897,898,908,909,912,915,917,920,921,923,926,927,931,933,934,938,940,941,942,950,951,959,960,963,964,967,969,977,978,986,987,995,996,1004,1005,1008,1010,1013,1015,1018,1020,1023,1025,1028,1030,1033,1035,1038,1040,1048,1049", "uncoveredLines": "62,221,234,249,261,269,270,282,305,339,340,341,342,343,357,615,616,617,623,624,625,631,632,633,647,648,649"}, "fflib_SObjectSelector": {"coverageRate": "0.83", "coveredLines": "41,43,44,46,54,59,64,71,77,82,84,85,86,93,94,109,117,118,121,122,131,132,144,145,148,149,150,151,152,153,159,160,167,168,169,170,171,173,174,175,183,207,208,209,217,218,221,222,223,226,232,233,239,240,246,247,250,251,260,261,279,280,289,290,296,297,308,309,320,321,338,339,353,354,373,375,383,385,386,389,390,397,398,404,405,406,412,413,419,420,421,427,428,434,436,437,439,440,442,446,447,448,449,450,451,455,458,460,462,463,468,469,473,474,475,476,477,478,479,480,481,482,483,484,487,489", "uncoveredLines": "176,178,179,190,191,192,198,199,200,212,213,214,270,329,330,331,346,347,360,361,368,370,452,464,465"}, "fflib_SObjectUnitOfWork": {"coverageRate": "0.75", "coveredLines": "55,57,59,61,62,64,66,67,68,70,73,93,94,96,97,99,100,102,103,104,109,110,111,122,123,127,128,147,148,151,152,154,156,159,161,165,167,170,172,175,177,180,182,185,187,195,197,206,207,210,211,212,213,214,216,217,218,221,227,228,234,235,243,244,245,247,255,256,257,266,267,275,276,277,289,290,291,292,294,295,297,298,299,310,311,313,314,316,327,328,357,358,367,368,369,379,380,381,382,384,385,388,390,393,395,396,399,422,423,424,433,434,435,437,446,447,448,457,458,460,462,463,465,473,474,475,484,485,486,576,577,578,579,580,581,582,583,584,586,590,591,592,593,594,596,597,598,599,600,601,602,604,605,606,607,610,611,612,614,616,619,620,621,622,625,626,627,628,631,632,633,637,638,639,640,644,645,646,650,651,652,653,657,658,659,660,664,665,668,669,670,671,675,676,677,681,682,683,688,689,690,691,692,693,700,701,702,707,708,709,714,716,718,720,759,761,762,763,764,765,768,769,770,771,772,800,801,809,810,830,831,838,839", "uncoveredLines": "107,114,131,132,134,135,137,138,190,192,344,346,347,348,349,411,412,413,414,459,494,495,496,504,505,507,508,510,518,519,520,529,530,532,533,535,543,544,545,553,554,556,557,559,567,568,569,724,725,726,729,730,731,734,735,737,738,739,741,742,745,746,749,750,751,752,753,754,755,756,788,789,790,791,834,835,840"}, "fflib_SObjects": {"coverageRate": "0.96", "coveredLines": "27,32,35,41,42,45,46,47,50,51,54,55,58,59,62,63,84,85,86,95,96,104,105,106,107,133,134,143,144,145,146,148,157,158,159,160,162,171,172,173,174,176,185,186,195,196,197,198,199,202,210,211,219,220,221,222,223,224,226,227,230,238,239,240,241,242,243,244,245,248,249,251,259,260,268,269,270,271,272,273,274,278,286,287,288,289,290,291,292,293,296,297,299,308,309,310,319,320,321,322,323,332,334,337,338,341,342,343,344,345,346,347,350,351,354,355,356,357,358,359,360,361,364,365,368,369,379,389", "uncoveredLines": "71,72,73,120,121"}, "fflib_SecurityUtils": {"coverageRate": "0.98", "coveredLines": "55,56,57,58,59,60,61,62,63,64,65,67,77,78,79,80,81,82,83,85,86,88,98,109,110,111,112,121,122,123,124,133,134,135,136,145,146,147,148,157,158,159,160,169,170,171,172,184,185,192,193,200,201,202,203,204,211,212,219,220,227,228,229,230,238,239,246,247,254,255,256,257,258,269,270,271,272,273,281,282,283,284,285,292,293,294,295,296,303,304,305,306,307", "uncoveredLines": "84,231"}, "fflib_StringBuilder": {"coverageRate": "0.93", "coveredLines": "33,38,44,45,51,52,58,59,62,63,69,70,77,78,80,83,84,87,88,91,92,95,96,97,100,101,109,110,113,115,116,118,129,130,133,134,137,138", "uncoveredLines": "119,120,121"}, "fflib_System": {"coverageRate": "1.00", "coveredLines": "16,17,27,28,29,30,31,32,33,36,37,38,39,40", "uncoveredLines": ""}, "fflib_VerificationMode": {"coverageRate": "1.00", "coveredLines": "11,12,13,26,27,28,29,30,45,46,47,48,56,57,58,67,68,69,71,79,80,81,83,91,92,93,95,104,105,106,107,109,116,117,118,120,129,130,131,132,134", "uncoveredLines": ""}, "milestoneFieldEnforce": {"coverageRate": "1.00", "coveredLines": "3,6,8,9,11,12,16,17,18,19,21,22", "uncoveredLines": ""}, "updateSubProjectCode": {"coverageRate": "1.00", "coveredLines": "2,3,4,5,6,8,9,10,11,12,14,20,21,22,24,25", "uncoveredLines": ""}}