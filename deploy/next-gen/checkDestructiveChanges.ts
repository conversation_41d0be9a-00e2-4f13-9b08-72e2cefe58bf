import { promises as fs } from "fs";
import { parseStringPromise } from "xml2js";
import path from "path";
import { execSync } from "child_process";
import { getPackagePathsFromSFDXProjectJson, logger } from "./src/utils";
import { exit } from "shelljs";

const DESTRUCTIVE_PRE = "deploy/delete/destructiveChangesPre.xml";
const DESTRUCTIVE_POST = "deploy/delete/destructiveChangesPost.xml";
const TEMP_DIR = "temp_manifest";

// 解析XML文件
async function parseXmlFile(filePath: string): Promise<any> {
    const xmlData = await fs.readFile(filePath, "utf-8");
    return parseStringPromise(xmlData);
}

// 从生成的package.xml中获取所有的metadata名称
async function getMetadataFromCodebase(): Promise<Set<string>> {
    const sourceDirPaths = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
    execSync(`sf project generate manifest --source-dir ${sourceDirPaths.join(",")} -d ${TEMP_DIR} -n "package.xml"`);

    const packageXmlPath = path.join(TEMP_DIR, "package.xml");
    const packageJson = await parseXmlFile(packageXmlPath);
    const metadataSet = new Set<string>();

    if (packageJson.Package.types) {
        packageJson.Package.types.forEach((type: any) => {
            if (type.name && type.members) {
                type.members.forEach((member: string) => {
                    metadataSet.add(`${type.name}.${member}`);
                });
            }
        });
    }

    return metadataSet;
}

// 从destructiveChanges文件中获取所有的metadata名称
async function getMetadataFromDestructive(filePath: string): Promise<Set<string>> {
    const destructiveJson = await parseXmlFile(filePath);
    const metadataSet = new Set<string>();

    if (destructiveJson.Package.types) {
        destructiveJson.Package.types.forEach((type: any) => {
            if (type.name && type.members) {
                type.members.forEach((member: string) => {
                    metadataSet.add(`${type.name}.${member}`);
                });
            }
        });
    }

    return metadataSet;
}

// 比较metadata是否同时存在于代码库和destructive文件中
async function compareMetadata() {
    const codebaseMetadata = await getMetadataFromCodebase();

    const preDestructiveMetadata = await getMetadataFromDestructive(DESTRUCTIVE_PRE);
    const postDestructiveMetadata = await getMetadataFromDestructive(DESTRUCTIVE_POST);

    const preInBoth = [...preDestructiveMetadata].filter((metadata) => codebaseMetadata.has(metadata));
    const postInBoth = [...postDestructiveMetadata].filter((metadata) => codebaseMetadata.has(metadata));

    if (!preInBoth.length && !postInBoth.length) {
        logger.infoGreen("Great! All metadata in destructiveChangesPre.xml and destructiveChangesPost.xml have been successfully removed from the codebase.");
    } else {
        if (preInBoth.length) {
            logger.errorRed(`Metadata found in both codebase and destructiveChangesPre.xml: ${preInBoth.join(", ")}`);
        }
        if (postInBoth.length) {
            logger.errorRed(`Metadata found in both codebase and destructiveChangesPost.xml: ${postInBoth.join(", ")}`);
        }
        throw new Error("Metadata conflicts detected. Please review destructiveChangesPre.xml and destructiveChangesPost.xml in the deploy/delete directory.");
    }
}

compareMetadata().catch((error) => {
    logger.errorRed(`An error occurred:  ${error.message}`);
    exit(-1);
});
