export class TestCase {
    public maxRetryTimes: number;
    public name: string;
    public status: "notStart" | "running" | "finished";
    public result: "succeed" | "failed" | undefined;
    public retryTimes: number;
    public failureMessages: string | undefined;

    constructor(name: string, maxRetryTimes: number) {
        this.name = name;
        this.status = "notStart";
        this.retryTimes = 0;
        this.maxRetryTimes = maxRetryTimes;
    }

    public markAsSucceeded() {
        this.result = "succeed";
        this.status = "finished";
    }

    public handleFailure(failureMessages: string) {
        if (this.meetMaxRetryTimes()) {
            this.result = "failed";
            this.status = "finished";
            this.failureMessages = failureMessages;
        } else {
            this.status = "notStart";
            this.retryTimes++;
        }
    }

    public meetMaxRetryTimes() {
        return this.retryTimes >= this.maxRetryTimes;
    }
}
