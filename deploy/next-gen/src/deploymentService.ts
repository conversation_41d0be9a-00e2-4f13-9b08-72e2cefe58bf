import { DeploymentConfig } from "./deploymentConfigParser";
import { SalesforceEnvironment } from "./salesforceEnvironment/salesforceEnvironment";
import { EnvironmentFactory } from "./salesforceEnvironment/environmentFactory";
import { TaskFactory } from "./task/taskFactory";
import { CircleCIService } from "./circleciService";

export class DeploymentService {
    private sfEnvironments: SalesforceEnvironment[] = [];
    private deploymentConfig: DeploymentConfig;
    private circleCIService: CircleCIService;

    constructor(deploymentConfig: DeploymentConfig) {
        this.sfEnvironments = deploymentConfig.deployTo.map((username) => EnvironmentFactory.build(username, deploymentConfig.needSetup));
        this.deploymentConfig = deploymentConfig;
        this.circleCIService = new CircleCIService();
    }

    public async run() {
        if (!this.deploymentConfig.skipConflictCheck) {
            await this.circleCIService.checkAndAbortConflict(this.deploymentConfig.deployTo);
        }

        const deploymentTask = TaskFactory.createTask(this.sfEnvironments, this.deploymentConfig);
        if (deploymentTask) {
            await deploymentTask.run();
        }
    }
}
