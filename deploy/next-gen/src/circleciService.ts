import { logger, sleep } from "./utils";
import { CircleCIPipeline } from "./pipelineService/circleCIPipeline";

export class CircleCIService {
    private circleCIPipeline;

    constructor() {
        this.circleCIPipeline = new CircleCIPipeline();
    }

    async checkAndAbortConflict(deployTo: string[]) {
        logger.infoBannerCyan(`Checking job conflict`);
        const conflictJob = await this.circleCIPipeline.getSpecificJobBuildUrl(deployTo);
        if (conflictJob) {
            logger.errorRed(`Conflicting job detected: ${conflictJob}`);
            logger.errorBannerRed(`Terminating program due to conflicting job`);
            await this.circleCIPipeline.cancelWorkflow();
            await sleep(60 * 1000);
            throw Error();
        } else {
            logger.infoGreen(`No conflicting job detected`);
        }
    }
}
