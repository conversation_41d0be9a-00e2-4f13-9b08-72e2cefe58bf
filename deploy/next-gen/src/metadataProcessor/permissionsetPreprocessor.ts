import { XMLBuilder, XMLParser } from "fast-xml-parser";
import { MetadataPreprocessor } from "./metadataPreprocessor";

const INDENT_BY_4_SPACES = "    ";

interface ObjectPermission {
    allowCreate: boolean;
    allowDelete: boolean;
    allowEdit: boolean;
    allowRead: boolean;
    modifyAllRecords: boolean;
    object: string;
    viewAllRecords: boolean;
}

export class PermissionsetPreprocessor extends MetadataPreprocessor {
    protected processXML(xmlContent: string): string {
        const parser = new XMLParser({ ignoreAttributes: false });
        const permissionsetObj = parser.parse(xmlContent);

        if (permissionsetObj.PermissionSet.objectPermissions) {
            permissionsetObj.PermissionSet.objectPermissions = this.filterSandboxNotSupportObjectPermissions(permissionsetObj.PermissionSet.objectPermissions);
        }

        const builder = new XMLBuilder({ ignoreAttributes: false, format: true, indentBy: INDENT_BY_4_SPACES });
        return builder.build(permissionsetObj);
    }

    private filterSandboxNotSupportObjectPermissions(objectPermissions: ObjectPermission | ObjectPermission[]): ObjectPermission[] {
        const permissionsArray = Array.isArray(objectPermissions) ? objectPermissions : [objectPermissions];
        const objectPermissionsNotSupport = ["ActiveScratchOrg", "NamespaceRegistry", "ScratchOrgInfo"];
        return permissionsArray.filter((objectPermission: ObjectPermission) => !objectPermissionsNotSupport.includes(objectPermission.object));
    }
}
