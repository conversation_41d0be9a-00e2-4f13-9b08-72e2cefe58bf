import fs from "fs";
import { logger } from "../utils";

export abstract class MetadataPreprocessor {
    public process(metadataDir: string): void {
        const metadataFiles = fs.readdirSync(metadataDir);
        metadataFiles.forEach((metadataFile) => this.processSingleMetadataFile(`${metadataDir}/${metadataFile}`));
    }

    private processSingleMetadataFile(metadataFilePath: string): void {
        const xmlContent = fs.readFileSync(metadataFilePath, { encoding: "utf8" });
        const xmlContentProcessed = this.processXML(xmlContent);
        fs.writeFileSync(metadataFilePath, xmlContentProcessed);
        logger.infoGreen(`Successfully processed metadata: ${metadataFilePath}`);
    }

    protected abstract processXML(xmlContent: string): string;
}
