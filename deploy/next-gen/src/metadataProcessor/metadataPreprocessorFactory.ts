import { DefaultPreprocessor } from "./defaultPreprocessor";
import { FlexipagePreprocessor } from "./flexipagePreprocessor";
import { MetadataPreprocessor } from "./metadataPreprocessor";
import { ProfilePreprocessor } from "./profilePreprocessor";
import { RemoteSiteSettingPreprocessor } from "./remoteSiteSettingPreprocessor";
import { WorkflowPreprocessor } from "./workflowPreprocessor";
import { QueuePreprocessor } from "./queuePreprocessor";
import { PermissionsetPreprocessor } from "./permissionsetPreprocessor";

const METADATA_PREPROCESSOR_MAP = {
    flexipages: FlexipagePreprocessor,
    profiles: ProfilePreprocessor,
    remoteSiteSettings: RemoteSiteSettingPreprocessor,
    workflows: WorkflowPreprocessor,
    queues: QueuePreprocessor,
    permissionsets: PermissionsetPreprocessor
};

export class MetadataPreprocessorFactory {
    public static create(metadataType: string): MetadataPreprocessor {
        if (Object.hasOwn(METADATA_PREPROCESSOR_MAP, metadataType)) {
            return new (METADATA_PREPROCESSOR_MAP as any)[metadataType]();
        } else {
            return new DefaultPreprocessor();
        }
    }
}
