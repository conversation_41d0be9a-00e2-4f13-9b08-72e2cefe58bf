import { MetadataPreprocessor } from "./metadataPreprocessor";
import { XML<PERSON>uilder, XMLParser } from "fast-xml-parser";
import { isIterable } from "../utils";

const INDENT_BY_4_SPACES = "    ";

export class WorkflowPreprocessor extends MetadataPreprocessor {
    protected processXML(xmlContent: string): string {
        const maskedEmailContent = this.maskEmail(xmlContent);
        return this.updateWorkflowAlerts(maskedEmailContent);
    }

    private maskEmail(xmlContent: string): string {
        return xmlContent.replace(/[^>]*@thoughtworks.com/g, "<EMAIL>");
    }

    private updateWorkflowAlerts(xmlContent: string): string {
        try {
            const parser = new XMLParser({ ignoreAttributes: false });
            const xmlObj = parser.parse(xmlContent);

            // if there are only 1 alerts tag, then the alerts will be object instead of array, which is not iterable.
            // So add some logic to handle it.
            const alerts = xmlObj.Workflow.alerts ? (isIterable(xmlObj.Workflow.alerts) ? xmlObj.Workflow.alerts : [xmlObj.Workflow.alerts]) : [];

            for (const alert of alerts) {
                if (alert.senderType && alert.senderType === "OrgWideEmailAddress") {
                    alert.senderType = "CurrentUser";
                    delete alert.senderAddress;
                }

                if (alert.recipients) {
                    delete alert.recipients;
                    const newRecipients = {
                        recipients: [{ recipient: "<EMAIL>", type: "user" }]
                    };
                    Object.assign(alert, newRecipients);
                }
            }

            const builder = new XMLBuilder({ ignoreAttributes: false, format: true, indentBy: INDENT_BY_4_SPACES });
            return builder.build(xmlObj);
        } catch (error) {
            console.error("Error processing XML:", error);
            throw error;
        }
    }
}
