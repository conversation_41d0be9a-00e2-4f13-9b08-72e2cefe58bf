import { XML<PERSON><PERSON><PERSON>, XMLParser } from "fast-xml-parser";
import { MetadataPreprocessor } from "./metadataPreprocessor";

interface ComponentInstance {
    componentName: string;
}

interface ItemInstance {
    componentInstance: ComponentInstance;
}

interface FlexipageRegion {
    itemInstances?: ItemInstance | ItemInstance[];
    name: string;
    type: string;
}

const INDENT_BY_4_SPACES = "    ";

export class FlexipagePreprocessor extends MetadataPreprocessor {
    protected processXML(xmlContent: string): string {
        return this.removeNotSupportTag(xmlContent);
    }

    removeNotSupportTag(xmlContent: string): string {
        const tagsNotSupport = ["desktopDashboards:embeddedDashboard", "flexipage:reportChart"];
        const parser = new XMLParser({ ignoreAttributes: false });
        const xmlObj = parser.parse(xmlContent);
        const flexipageRegions = Array.isArray(xmlObj.FlexiPage.flexiPageRegions) ? xmlObj.FlexiPage.flexiPageRegions : [xmlObj.FlexiPage.flexiPageRegions];
        const itemInstanceToKeep = (itemInstance: ItemInstance) => !(itemInstance.componentInstance && tagsNotSupport.includes(itemInstance.componentInstance.componentName));
        flexipageRegions.forEach((flexiPageRegion: FlexipageRegion) => {
            if (flexiPageRegion.itemInstances) {
                if (Array.isArray(flexiPageRegion.itemInstances)) {
                    const itemInstances: ItemInstance[] = flexiPageRegion.itemInstances.filter(itemInstanceToKeep);

                    if (itemInstances.length == 0) {
                        delete flexiPageRegion.itemInstances;
                    } else if (itemInstances.length == 1) {
                        flexiPageRegion.itemInstances = itemInstances[0];
                    } else {
                        flexiPageRegion.itemInstances = itemInstances;
                    }
                } else {
                    if (!itemInstanceToKeep(flexiPageRegion.itemInstances)) {
                        delete flexiPageRegion.itemInstances;
                    }
                }
            }
        });
        const builder = new XMLBuilder({ ignoreAttributes: false, format: true, indentBy: INDENT_BY_4_SPACES });
        return builder.build(xmlObj);
    }
}

// const flexipagePreprocessor = new FlexipagePreprocessor("./__tests__/data/not-support-tags-flexipage");
// flexipagePreprocessor.process();
