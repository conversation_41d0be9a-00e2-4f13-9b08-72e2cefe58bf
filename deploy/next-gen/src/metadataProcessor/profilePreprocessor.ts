import { XML<PERSON>uilder, XMLParser } from "fast-xml-parser";
import { MetadataPreprocessor } from "./metadataPreprocessor";

interface ApplicationVisibility {
    application: string;
    default: boolean;
    visible: boolean;
}

interface UserPermission {
    name: string;
    enable: boolean;
}

const INDENT_BY_4_SPACES = "    ";

export class ProfilePreprocessor extends MetadataPreprocessor {
    protected processXML(xmlContent: string): string {
        const parser = new XMLParser({ ignoreAttributes: false });
        const profileObj = parser.parse(xmlContent);

        if (profileObj.Profile.applicationVisibilities) {
            profileObj.Profile.applicationVisibilities = this.filterSandboxNotSupportApplicationVisibility(profileObj.Profile.applicationVisibilities);
        }

        if (profileObj.Profile.userPermissions) {
            profileObj.Profile.userPermissions = this.filterSandboxNotSupportUserPermissions(profileObj.Profile.userPermissions);
        }

        const builder = new XMLBuilder({ ignoreAttributes: false, format: true, indentBy: INDENT_BY_4_SPACES });
        return builder.build(profileObj);
    }

    private filterSandboxNotSupportApplicationVisibility(applicationVisibilities: ApplicationVisibility[]): ApplicationVisibility[] {
        const applicationsToDeploy = ["Time_Expense", "Time_Expense_Lightning", "ThoughtWorks_CRM"];
        if (!Array.isArray(applicationVisibilities)) {
            applicationVisibilities = [applicationVisibilities];
        }
        return applicationVisibilities.filter((applicationVisibility: ApplicationVisibility) => applicationsToDeploy.includes(applicationVisibility.application));
    }

    private filterSandboxNotSupportUserPermissions(userPermissions: UserPermission | UserPermission[]): UserPermission[] {
        const permissionsArray = Array.isArray(userPermissions) ? userPermissions : [userPermissions];
        const userPermissionsNotSupport = [
            "CreateDashboardFolders",
            "AICreateInsightObjects",
            "AIViewInsightObjects",
            "EmailAdministration",
            "EmailTemplateManagement",
            "ManageSandboxes",
            "Packaging2PromoteVersion",
            "ManageHubConnections",
            "RecordVisibilityAPI",
            "SalesforceIQInbox",
            "ManageOrchInstsAndWorkItems",
            "ClientSecretRotation",
            "ViewMLModels"
        ];
        return permissionsArray.filter((userPermission: UserPermission) => !userPermissionsNotSupport.includes(userPermission.name));
    }
}

// const profilePreprocessor = new ProfilePreprocessor("./__tests__/data/not-support-tags-profile");
// profilePreprocessor.process();
