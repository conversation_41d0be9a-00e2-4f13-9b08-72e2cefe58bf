import { MetadataPreprocessor } from "./metadataPreprocessor";

export class RemoteSiteSettingPreprocessor extends MetadataPreprocessor {
    protected processXML(xmlContent: string): string {
        return xmlContent.replace(/ecs-forcetalk.production.psatalk.thoughtworks.net/g, "ecs-forcetalk.uat2.psatalk.thoughtworks.net");
    }
}

// const remoteSiteSettingPreprocessor = new RemoteSiteSettingPreprocessor("./__tests__/data/remote-site-settings");
// remoteSiteSettingPreprocessor.process();
