import { getApexClassFromXml, getGitBranchName, getPackagePathsFromSFDXProjectJson } from "./utils";
import fs from "fs";
import path from "path";

export class ApexTestService {
    public async getAllApexTestClasses(): Promise<string[]> {
        const sourceDirPaths: string[] = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
        const testClassNames: Set<string> = new Set();

        for (const sourceDirPath of sourceDirPaths) {
            const classNames = this.searchApexClassesWithText(sourceDirPath, "@istest", false);
            classNames.forEach((className) => testClassNames.add(className));
        }

        return Array.from(testClassNames);
    }

    public async getAffectedApexTestClasses(): Promise<string[]> {
        const sourceDirPaths: string[] = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
        const testClassNames: Set<string> = new Set();

        for (const sourceDirPath of sourceDirPaths) {
            const classNames = this.searchApexClassesWithText(sourceDirPath, "@istest", false);
            classNames.forEach((className) => testClassNames.add(className));
        }

        const allTestClassNames = Array.from(testClassNames);

        const affectedTestClassNames: Set<string> = new Set();
        const gitBranchName = await getGitBranchName();
        const coverageDataFilePath = gitBranchName.startsWith("release") ? "deploy/next-gen/config/coverage-data-release.json" : "deploy/next-gen/config/coverage-data.json";
        const diffApexClassName = await getApexClassFromXml("package/package.xml");
        const relatedApexTestClassName = await this.getRelatedApexClass(coverageDataFilePath, diffApexClassName, 3);
        const combinedClassNames = new Set([...diffApexClassName, ...relatedApexTestClassName]);
        allTestClassNames.forEach((className) => {
            if (combinedClassNames.has(className)) {
                affectedTestClassNames.add(className);
            }
        });

        return Array.from(affectedTestClassNames);
    }

    private async getRelatedApexClass(jsonFilePath: string, apexClassMembers: string[], minCoveredLines: number): Promise<string[]> {
        const jsonData = fs.readFileSync(jsonFilePath, "utf-8");
        const jsonObject = JSON.parse(jsonData);

        const map = new Map<string, string[]>();
        for (const key in jsonObject) {
            if (jsonObject.hasOwnProperty(key)) {
                const classNames = jsonObject[key]
                    .filter((item: string) => {
                        const parts = item.split(":");
                        const coveredLines = parseInt(parts[2].split("/")[0]);
                        return coveredLines >= minCoveredLines;
                    })
                    .map((item: string) => item.split(":")[0]);
                map.set(key, classNames);
            }
        }

        const relatedKeysSet = new Set<string>();

        for (const apexClass of apexClassMembers) {
            for (const [key, value] of map) {
                if (value.includes(apexClass)) {
                    relatedKeysSet.add(key);
                }
            }
        }

        return Array.from(relatedKeysSet);
    }

    private searchApexClassesWithText(dirPath: string, searchText: string, includeExtension: boolean): string[] {
        let results: string[] = [];

        const files = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const file of files) {
            const filePath = path.join(dirPath, file.name);
            if (file.isDirectory()) {
                results = results.concat(this.searchApexClassesWithText(filePath, searchText, includeExtension));
            } else if (file.isFile() && file.name.endsWith(".cls")) {
                const content = fs.readFileSync(filePath, "utf8").toLowerCase();
                if (content.includes(searchText)) {
                    const filename = includeExtension ? file.name : file.name.replace(".cls", "");
                    results.push(filename);
                }
            }
        }

        return results;
    }
}
