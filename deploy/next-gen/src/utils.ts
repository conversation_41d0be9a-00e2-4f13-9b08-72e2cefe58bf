import { promises as fs } from "fs";
import path from "path";
import axios from "axios";
import { exec } from "shelljs";
import { createLogger, format, transports } from "winston";
import moment from "moment-timezone";
import { randomBytes } from "crypto";
import { XMLParser } from "fast-xml-parser";
import { parseStringPromise, Builder } from "xml2js";
import { EventEmitter } from "events";

EventEmitter.defaultMaxListeners = 50;

interface CommandResult {
    message: string;
    output: string;
    code: number;
}

declare module "winston" {
    interface Logger {
        errorRed(message: string): void;

        warnYellow(message: string): void;

        infoYellow(message: string): void;

        infoGreen(message: string): void;

        infoBlue(message: string): void;

        infoCyan(message: string): void;

        errorBannerRed(message: string, totalLength?: number): void;

        infoBannerGreen(message: string, totalLength?: number): void;

        infoBannerCyan(message: string, totalLength?: number): void;
    }
}

const colors = {
    red: "\u001b[91m",
    green: "\u001b[92m",
    warnYellow: "\u001b[93m",
    infoYellow: "\u001b[38;5;100m",
    blue: "\u001b[94m",
    cyan: "\u001b[96m",
    reset: "\u001b[0m"
};
const { combine, printf } = format;
const logFormat = printf(({ timestamp, level, message }) => {
    return `[${timestamp}] [${level}] : ${message} `;
});

export const logger = createLogger({
    level: "info",
    format: combine(
        format.colorize(),
        format.timestamp({
            format: () => moment().tz("Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss [UTC+8]")
        }),
        logFormat
    ),
    transports: [new transports.Console()]
});

logger.errorRed = function (message: string) {
    this.error(`${colors.red}${message}${colors.reset}`);
};

logger.infoGreen = function (message: string) {
    this.info(`${colors.green}${message}${colors.reset}`);
};

logger.infoYellow = function (message: string) {
    this.info(`${colors.infoYellow}${message}${colors.reset}`);
};

logger.warnYellow = function (message: string) {
    this.warn(`${colors.warnYellow}${message}${colors.reset}`);
};

logger.infoBlue = function (message: string) {
    this.info(`${colors.blue}${message}${colors.reset}`);
};

logger.infoCyan = function (message: string) {
    this.info(`${colors.cyan}${message}${colors.reset}`);
};

logger.errorBannerRed = function (message: string, totalLength: number = 120) {
    const paddingLength = totalLength - message.length;
    if (paddingLength <= 0) {
        return message;
    }
    const halfPaddingLength = Math.floor(paddingLength / 2);
    const padding = "=".repeat(halfPaddingLength);
    this.error(`${colors.red}${padding}${message}${padding}${paddingLength % 2 === 0 ? "" : "="}${colors.reset}`);
};

logger.infoBannerGreen = function (message: string, totalLength: number = 120) {
    const paddingLength = totalLength - message.length;
    if (paddingLength <= 0) {
        return message;
    }
    const halfPaddingLength = Math.floor(paddingLength / 2);
    const padding = "=".repeat(halfPaddingLength);
    this.info(`${colors.green}${padding}${message}${padding}${paddingLength % 2 === 0 ? "" : "="}${colors.reset}`);
};

logger.infoBannerCyan = function (message: string, totalLength: number = 120) {
    const paddingLength = totalLength - message.length;
    if (paddingLength <= 0) {
        return message;
    }
    const halfPaddingLength = Math.floor(paddingLength / 2);
    const padding = "=".repeat(halfPaddingLength);
    this.info(`${colors.cyan}${padding}${message}${padding}${paddingLength % 2 === 0 ? "" : "="}${colors.reset}`);
};

export const execPromise = (command: string, silent: boolean = true, maxBuffer: number = 1024 * 1024, env?: NodeJS.ProcessEnv): Promise<CommandResult> => {
    return new Promise((resolve, reject) => {
        exec(command, { async: true, silent, maxBuffer, env: env }, async (code, stdout, stderr) => {
            if (code === 0) {
                resolve({ message: `Command executed successfully: ${command}`, output: stdout, code });
            } else {
                const errorInfo = { message: `Command executed with error: ${command}`, output: stdout, code };
                if (!command.includes("grep")) {
                    try {
                        const logDirectory = path.join(__dirname, "..", "logs");
                        const fileName = `error-log-${randomBytes(8).toString("hex")}.txt`;
                        const fullPath = path.join(logDirectory, fileName);
                        const content = `Command: ${command}\nStdout: ${stdout}\nStderr: ${stderr}`;
                        await fs.mkdir(logDirectory, { recursive: true });
                        await fs.writeFile(fullPath, content);
                    } catch (fileError) {
                        logger.errorRed(`Failed to write error log: ${fileError}`);
                    }
                }
                reject(errorInfo);
            }
        });
    });
};

export const sleep = (ms: number) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};

export const validateArgs = (
    runDiffDeploy: boolean,
    runFullDeploy: boolean,
    runValidate: boolean,
    runValidatedDeploy: boolean,
    runDiffApexTest: boolean,
    runRelatedApexTest: boolean,
    runFullApexTest: boolean,
    runFullApexTestWithCoverage: boolean,
    runUITest: boolean,
    deployToUAT: boolean,
    deployToPROD: boolean,
    deployToPlayground: boolean,
    devSandboxName: string | null,
    groupName: string | null
) => {
    if (
        !runDiffDeploy &&
        !runFullDeploy &&
        !runDiffApexTest &&
        !runRelatedApexTest &&
        !runFullApexTest &&
        !runFullApexTestWithCoverage &&
        !runUITest &&
        !deployToUAT &&
        !deployToPROD &&
        !deployToPlayground &&
        !devSandboxName &&
        !groupName
    ) {
        logger.errorRed(
            "At least one option is required. Available options include:\n" +
                "--run-diff-deploy: Perform a diff deployment.\n" +
                "--run-full-deploy: Perform a full deployment.\n" +
                "--run-diff-apex-test: Execute diff Apex tests.\n" +
                "--run-related-apex-test: Execute related Apex tests.\n" +
                "--run-full-apex-test: Execute full Apex tests.\n" +
                "--run-full-apex-test-with-coverage: Execute full Apex tests with coverage.\n" +
                "--run-ui-test: Execute UI tests.\n" +
                "--deploy-to-uat: Deploy to UAT environment.\n" +
                "--deploy-to-prod: Deploy to PROD environment.\n" +
                "--deploy-to-playground: Deploy to Playground environment.\n" +
                "--dev-sandbox-name: Specify the development sandbox name.\n" +
                "--group-name: Specify the group name."
        );
        return false;
    }

    if (groupName) {
        const groupValues = Object.values(groupMapping);
        if (!groupValues.includes(groupName)) {
            logger.errorRed(`The provided group name "${groupName}" is not within the correct range: ${groupValues.join(", ")}`);
            return false;
        }
    }

    if (deployToUAT || deployToPROD || deployToPlayground) {
        return true;
    }

    if (!runDiffDeploy && !runFullDeploy) {
        logger.errorRed("Please specify at least one deployment option: --run-diff-deploy or --run-full-deploy.");
        return false;
    }

    if (runDiffDeploy && runFullDeploy) {
        logger.errorRed("Please specify exactly one deployment option: --run-diff-deploy or --run-full-deploy.");
        return false;
    }

    if (runDiffApexTest && runFullApexTest) {
        logger.errorRed("Please specify exactly one Apex test option: --run-diff-apex-test or --run-full-apex-test.");
        return false;
    }

    if (runRelatedApexTest && !runDiffDeploy) {
        logger.errorRed("--run-related-apex-test can only be used with --run-diff-deploy.");
        return false;
    }

    if ((runValidate || runValidatedDeploy) && !deployToPROD) {
        logger.errorRed("--run-validate or --run-validated-deploy can only be used with --deploy-to-prod");
    }

    return true;
};

export const createDirectory = async (path: string) => {
    await execPromise(`rm -rf ${path}`);
    return execPromise(`mkdir -p ${path}`);
};

export const getGitBranchName = async (): Promise<string> => {
    const { output } = await execPromise("git rev-parse --abbrev-ref HEAD", true);
    return output;
};

export const readOAuthClientConfigFile = async (): Promise<
    {
        username: string;
        passwordEnv: string;
        clientId: string;
        group: string;
        usage: string;
    }[]
> => {
    const configPath = path.join(__dirname, "..", "config", "oauth-client-config.json");
    const data = await fs.readFile(configPath, "utf8");
    return JSON.parse(data);
};

interface OAuthClientConfig {
    username: string;
    passwordEnv: string;
    clientId: string;
    group: string;
    usage: string;
}

interface QuickDeployConfig {
    validatedJobId: string;
    runLocalTests: boolean;
    runFullDeploy: boolean;
    baseCommit: string;
}

export const readQuickDeployConfigFile = async (): Promise<QuickDeployConfig> => {
    const configPath = path.join(__dirname, "..", "config", "quick-deploy-config.json");
    const data = await fs.readFile(configPath, "utf8");
    return JSON.parse(data);
};

export const writeQuickDeployConfigFile = async (config: QuickDeployConfig): Promise<void> => {
    const configPath = path.join(__dirname, "..", "config", "quick-deploy-config.json");
    const data = JSON.stringify(config, null, 4); // Pretty-print with 4 spaces for readability
    await fs.writeFile(configPath, data, "utf8");
};

const groupMapping: Record<string, string> = {
    sf: "sales",
    crm: "crm",
    psa: "psa",
    master: "master",
    release: "release"
};

export const getOAuthClientConfigByGitBranch = async (groupName: string | null): Promise<OAuthClientConfig[]> => {
    const oauthClientConfig = await readOAuthClientConfigFile();
    const branchName = await getGitBranchName();
    const mappedGroup = groupName || Object.entries(groupMapping).find(([key]) => branchName.startsWith(key))?.[1];

    return oauthClientConfig.filter((client) => client.group === mappedGroup);
};

export const getDevOAuthClientConfig = async (devSandboxName: string): Promise<OAuthClientConfig[]> => {
    const oauthClientConfig = await readOAuthClientConfigFile();
    const group = "dev";

    return oauthClientConfig
        .filter((client) => client.group === group)
        .map((client) => ({
            ...client,
            username: `${client.username}${devSandboxName}`
        }));
};

export const getApexClassFromXml = async (filePath: string): Promise<string[]> => {
    const xmlData = await fs.readFile(filePath, "utf-8");

    const options = {
        ignoreAttributes: false,
        attributeNamePrefix: "@_",
        parseAttributeValue: true,
        trimValues: true
    };
    const parser = new XMLParser(options);
    const jsonObj = parser.parse(xmlData);

    const packageObj = jsonObj["Package"];
    const types = packageObj["types"];

    let apexClassMembers: string[] = [];

    if (types) {
        if (Array.isArray(types)) {
            types.forEach((type: any) => {
                if (type["name"] === "ApexClass") {
                    if (Array.isArray(type["members"])) {
                        apexClassMembers = apexClassMembers.concat(type["members"]);
                    } else {
                        apexClassMembers.push(type["members"]);
                    }
                }
            });
        } else {
            if (types["name"] === "ApexClass") {
                if (Array.isArray(types["members"])) {
                    apexClassMembers = types["members"];
                } else {
                    apexClassMembers.push(types["members"]);
                }
            }
        }
    }

    return apexClassMembers;
};

export const getRelatedApexClass = async (jsonFilePath: string, apexClassMembers: string[], minCoveredLines: number): Promise<string[]> => {
    const jsonData = await fs.readFile(jsonFilePath, "utf-8");
    const jsonObject = JSON.parse(jsonData);

    const map = new Map<string, string[]>();
    for (const key in jsonObject) {
        if (jsonObject.hasOwnProperty(key)) {
            const classNames = jsonObject[key]
                .filter((item: string) => {
                    const parts = item.split(":");
                    const coveredLines = parseInt(parts[2].split("/")[0]);
                    return coveredLines >= minCoveredLines;
                })
                .map((item: string) => item.split(":")[0]);
            map.set(key, classNames);
        }
    }

    const relatedKeysSet = new Set<string>();

    for (const apexClass of apexClassMembers) {
        for (const [key, value] of map) {
            if (value.includes(apexClass)) {
                relatedKeysSet.add(key);
            }
        }
    }

    return Array.from(relatedKeysSet);
};

export const getSandboxName = (username: string): string => {
    const domain = "@thoughtworks.com";
    const domainIndex = username.indexOf(domain);

    if (domainIndex === -1) {
        return "production";
    }

    const sandboxPart = username.slice(domainIndex + domain.length + 1);
    return sandboxPart.length > 0 ? sandboxPart : "production";
};

export const readIgnoreMetadataConfigFile = async (configPath: string): Promise<string[]> => {
    const data = await fs.readFile(configPath, "utf8");
    return data.split(/\r?\n/).filter((line) => line.trim() !== "");
};

interface PackageDirectory {
    path: string;
    default?: boolean;
}

interface SFDXProjectConfig {
    packageDirectories: PackageDirectory[];
    namespace: string;
    sfdcLoginUrl: string;
    sourceApiVersion: string;
}

export const getPackagePathsFromSFDXProjectJson = async (projectJsonPath: string): Promise<string[]> => {
    try {
        const fileContent = await fs.readFile(projectJsonPath, "utf8");
        const config: SFDXProjectConfig = JSON.parse(fileContent);
        return config.packageDirectories.map((dir) => dir.path);
    } catch (error) {
        logger.errorRed(`Error reading SFDX project JSON file: ${error}`);
        throw error;
    }
};

export const extractInstanceUrl = (url: string): string => {
    const parsedUrl = new URL(url);
    return `${parsedUrl.protocol}//${parsedUrl.host}`;
};

export const isProdEnvironment = (username: string): boolean => {
    const prodPattern = /@thoughtworks.com$/;
    return prodPattern.test(username);
};

export const timeout = <T>(promise: Promise<T>, ms: number): Promise<T> => {
    return new Promise<T>((resolve, reject) => {
        const timer = setTimeout(() => {
            reject(new Error("Operation timed out"));
        }, ms);

        promise.then(
            (value) => {
                clearTimeout(timer);
                resolve(value);
            },
            (error) => {
                clearTimeout(timer);
                reject(error);
            }
        );
    });
};

export const getArgValue = (args: string[], prefix: string) => {
    const arg = args.find((arg) => arg.startsWith(prefix));
    return arg ? arg.split("=")[1] : null;
};

export const sendWechatNotification = async (
    isSuccess: boolean,
    sandboxName: string,
    baseCommit: string | null,
    isFullDeploy: boolean,
    duration?: number,
    isValidate: boolean = false
): Promise<void> => {
    const webhookUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=15bcd791-01bd-4010-a2f6-ebf32773813b";
    const headers = { "Content-Type": "application/json" };
    const buildUrl = process.env.CIRCLE_BUILD_URL || "This is a test environment 😋";
    const triggerUser = process.env.CIRCLE_USERNAME || "Unknown user";
    logger.infoGreen(`[WeChat Notification]    Sending notification to WeChat...`);

    if (sandboxName !== "prod") {
        return;
    }

    let messageContent: string;
    const deployModeText = isValidate ? "验证" : `部署`;
    if (isSuccess) {
        messageContent = `🎉 ${deployModeText}状态: **成功**\n\n📦 环境: ${sandboxName}\n\n🔗 任务链接: ${buildUrl}`;
        if (duration) {
            const hours = Math.floor(duration / (1000 * 60 * 60));
            const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((duration % (1000 * 60)) / 1000);
            messageContent += `\n\n⏱️ ${deployModeText}耗时: **${hours}小时 ${minutes}分钟 ${seconds}秒**`;
        }
    } else {
        messageContent = `❌ ${deployModeText}状态: **失败**\n\n📦 环境: ${sandboxName}\n\n🔗 任务链接: ${buildUrl}`;
    }

    if (isFullDeploy) {
        messageContent += `\n\n🚀 ${deployModeText}类型: **全量${deployModeText}**`;
    } else {
        messageContent += `\n\n🚀 ${deployModeText}类型: **增量${deployModeText}**`;
    }

    if (!isFullDeploy && baseCommit) {
        messageContent += `\n\n🔍 部署范围:\n`;

        try {
            const gitLogCommand = `git --no-pager log --pretty=format:"%h - %s (%an)" ${baseCommit}..HEAD`;
            const result: CommandResult = await execPromise(gitLogCommand, true);
            const commits = result.output.trim().split("\n");

            if (commits.length > 0) {
                const fromCommit = commits[0].split(" ")[0];
                const toCommit = commits[commits.length - 1].split(" ")[0];

                const cardNumbers = new Set<string>();
                const authors = new Set<string>();

                commits.forEach((commit) => {
                    const match = commit.match(/\[(.*?)\].*\((.*?)\)/);
                    if (match) {
                        cardNumbers.add(match[1]);
                        authors.add(match[2]);
                    }
                });

                messageContent += `FROM ${fromCommit} - TO ${toCommit}\n`;
                messageContent += `📌 部署卡号: ${Array.from(cardNumbers).join(", ")}\n`;
                messageContent += `👥 提交人: ${Array.from(authors).join(", ")}\n`;
                messageContent += `🚀 触发部署人: ${triggerUser}`;
            }
        } catch (error) {
            console.error(`Error fetching git commits: ${(error as any).message}`);
            messageContent += `\n\n⚠️ 无法获取提交记录`;
        }
    }

    const MAX_LENGTH = 3600;

    const sendMessages = async (content: string) => {
        const message = {
            msgtype: "markdown",
            markdown: {
                content
            }
        };

        try {
            const response = await axios.post(webhookUrl, message, { headers });
            logger.infoGreen(`${response.status}`);
            logger.infoGreen(`${response.statusText}`);
            if (response.status !== 200) {
                logger.errorRed(`Failed to send notification to WeChat: ${response.statusText}`);
            }
        } catch (error) {
            logger.errorRed(`Error sending notification to WeChat: ${error}`);
        }
    };

    let startIndex = 0;
    while (startIndex < messageContent.length) {
        const endIndex = Math.min(startIndex + MAX_LENGTH, messageContent.length);
        const messagePart = messageContent.substring(startIndex, endIndex);
        await sendMessages(messagePart);
        startIndex = endIndex;
    }
};

export const findFilePath = async (directories: string[], fileName: string): Promise<string> => {
    for (const directory of directories) {
        const files = await fs.readdir(directory);

        for (const file of files) {
            const fullPath = path.join(directory, file);
            const stat = await fs.stat(fullPath);

            if (stat.isDirectory()) {
                try {
                    const result = await findFilePath([fullPath], fileName);
                    if (result) {
                        return result;
                    }
                } catch (err) {
                    // Continue searching in other directories
                }
            } else if (file === fileName) {
                return fullPath;
            }
        }
    }

    throw new Error(`File "${fileName}" not found in the provided directories: ${directories.join(",")}`);
};

export const insertFakeCoverageMethod = async (apexClassFilePath: string, incrementCount: number = 3): Promise<void> => {
    try {
        const classFileContent = await fs.readFile(apexClassFilePath, "utf8");
        const apexClassName = path.basename(apexClassFilePath, ".cls");

        if (classFileContent.includes("fakeCoverage")) {
            throw new Error(`fakeCoverage method already exists in ${apexClassFilePath}.`);
        }

        // Calculate number of methods needed
        const maxIncrementsPerMethod = 3000;
        const methodCount = Math.ceil(incrementCount / maxIncrementsPerMethod);
        let allFakeCoverageMethods = "";

        for (let methodIndex = 0; methodIndex < methodCount; methodIndex++) {
            const currentMethodIncrementCount = Math.min(maxIncrementsPerMethod, incrementCount - methodIndex * maxIncrementsPerMethod);
            let incrementStatements = "";
            for (let i = 0; i < currentMethodIncrementCount; i++) {
                incrementStatements += "        i++;\n";
            }

            const methodName = methodIndex === 0 ? "fakeCoverage" : `fakeCoverage${methodIndex}`;
            const fakeCoverageMethod = `
                @TestVisible
                private static void ${methodName}() {
                    Integer i = 0;
                    ${incrementStatements}
                }
            `;
            allFakeCoverageMethods += fakeCoverageMethod;
        }

        const closingBraceIndex = classFileContent.lastIndexOf("}");
        if (closingBraceIndex === -1) {
            throw new Error(`No closing brace found in ${apexClassFilePath}.`);
        }

        const updatedClassFileContent = classFileContent.slice(0, closingBraceIndex) + allFakeCoverageMethods + classFileContent.slice(closingBraceIndex);
        await fs.writeFile(apexClassFilePath, updatedClassFileContent, "utf8");
        logger.infoGreen(`[Deploy Planned]   [0/1]    \tInserted ${methodCount} fakeCoverage method with ${incrementCount} lines into ${apexClassFilePath}.`);

        const sourceDirPaths = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
        const filePath = await findFilePath(sourceDirPaths, "HackCoverageTest.cls");
        await insertIntoHackCoverageMethod(filePath, apexClassName, methodCount);
    } catch (error) {
        const errorMessage = `Error processing ${apexClassFilePath}: ${(error as any).message}`;
        logger.errorRed(errorMessage);
        throw new Error(errorMessage);
    }
};

export const insertIntoHackCoverageMethod = async (hackCoverageTestFilePath: string, apexClassName: string, methodCount: number): Promise<void> => {
    try {
        const hackCoverageTestContent = await fs.readFile(hackCoverageTestFilePath, "utf8");

        // Generate the fakeCoverage calls for the new insertion
        let newFakeCoverageCalls = "";
        for (let i = 0; i < methodCount; i++) {
            const methodName = i === 0 ? "fakeCoverage" : `fakeCoverage${i}`;
            newFakeCoverageCalls += `        ${apexClassName}.${methodName}();\n`;
        }

        // Find existing hackCoverage methods
        const hackCoverageRegex = /static\s+void\s+(hackCoverage\d*)\s*\(\s*\)\s*{([\s\S]*?)}/g;
        let hackCoverageMethods: { name: string; content: string }[] = [];
        let match;
        while ((match = hackCoverageRegex.exec(hackCoverageTestContent)) !== null) {
            hackCoverageMethods.push({
                name: match[1], // method name (e.g., hackCoverage, hackCoverage1, etc.)
                content: match[2] // method content
            });
        }

        // If no hackCoverage methods exist, start with hackCoverage
        if (hackCoverageMethods.length === 0) {
            hackCoverageMethods.push({ name: "hackCoverage", content: "" });
        }

        // Calculate how many fakeCoverage calls are already in each hackCoverage method
        const maxLinesPerMethod = 3000;
        let fakeCoverageInserted = 0;

        // Function to count lines of fakeCoverage in a method
        const countExistingHackCoverageLines = (methodContent: string) => methodContent.split("\n").length;

        // Distribute new fakeCoverage calls into existing or new hackCoverage methods
        hackCoverageMethods = hackCoverageMethods.map((method, index) => {
            const hackCoverageExistingLines = countExistingHackCoverageLines(method.content);
            const remainingCapacity = maxLinesPerMethod - hackCoverageExistingLines;

            // Add as many new fakeCoverage calls as can fit into this method
            const linesToInsert = newFakeCoverageCalls
                .split("\n")
                .slice(fakeCoverageInserted, fakeCoverageInserted + remainingCapacity)
                .join("\n");
            fakeCoverageInserted += remainingCapacity;

            // Return updated method with new fakeCoverage lines
            return {
                name: method.name,
                content: method.content + linesToInsert
            };
        });

        // If there's still more fakeCoverage to insert, create new hackCoverage methods
        while (fakeCoverageInserted < newFakeCoverageCalls.split("\n").length) {
            const remainingFakeCoverageCalls = newFakeCoverageCalls
                .split("\n")
                .slice(fakeCoverageInserted, fakeCoverageInserted + maxLinesPerMethod)
                .join("\n");
            fakeCoverageInserted += maxLinesPerMethod;

            const newMethodName = `hackCoverage${hackCoverageMethods.length}`;
            hackCoverageMethods.push({
                name: newMethodName,
                content: remainingFakeCoverageCalls
            });
        }

        // Reconstruct the updated content for the test file
        let updatedHackCoverageTestContent = `
            @IsTest(IsParallel=true)
            private class HackCoverageTest {
            `;

        // 遍历 hackCoverageMethods，确保每个方法都有 @IsTest 注解
        hackCoverageMethods.forEach((method) => {
            updatedHackCoverageTestContent += `
            @IsTest
            static void ${method.name}() {
                ${method.content}
            }
            `;
        });

        updatedHackCoverageTestContent += "\n}";

        // Write the updated file
        await fs.writeFile(hackCoverageTestFilePath, updatedHackCoverageTestContent, "utf8");
        logger.infoGreen(`[Deploy Planned]   [0/1]    \tInserted and distributed fakeCoverage calls into HackCoverageTest.`);
    } catch (error) {
        const errorMessage = `Error processing HackCoverageTest file ${hackCoverageTestFilePath}: ${(error as any).message}`;
        logger.errorRed(errorMessage);
        throw new Error(errorMessage);
    }
};

export const calculateAdditionalCoverage = (coverageData: { coveredLines: string; uncoveredLines: string }, targetCoverageRate: number, fileLines: number): number => {
    const coveredLines = new Set(coverageData.coveredLines.split(",").map(Number));
    const uncoveredLines = new Set(coverageData.uncoveredLines.split(",").map(Number));

    let currentCovered = coveredLines.size;
    let currentUncovered = uncoveredLines.size;

    if (coverageData.coveredLines === "" && coverageData.uncoveredLines === "") {
        currentUncovered = fileLines;
    }

    const totalLines = currentCovered + currentUncovered;

    const additionalLinesNeeded = Math.ceil((Math.ceil(targetCoverageRate * totalLines) - currentCovered) / (1 - targetCoverageRate));

    return Math.max(0, additionalLinesNeeded) + 50;
};

export const getFileLineCount = async (filePath: string) => {
    const fileContent = await fs.readFile(filePath, "utf8");
    return fileContent.split("\n").length;
};

interface ApexFileType {
    isTest: boolean;
    isInterface: boolean;
    isEnum: boolean;
}

function removeComments(content: string): string {
    // 移除块注释
    let withoutBlockComments = content.replace(/\/\*[\s\S]*?\*\//g, " ");

    // 移除行注释
    let withoutLineComments = withoutBlockComments.replace(/\/\/.*$/gm, " ");

    return withoutLineComments.trim();
}

function getFirstEffectiveLine(content: string): string | null {
    // 将内容按行分割，并移除空行和仅包含空白字符的行
    const lines = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0);

    // 返回第一行有效代码
    return lines.length > 0 ? lines[0] : null;
}

export const checkApexClassType = async (filePath: string): Promise<ApexFileType> => {
    const fileContent = await fs.readFile(filePath, "utf-8");

    // 移除注释后的内容
    const cleanedContent = removeComments(fileContent);

    // 获取第一行有效代码
    const firstLine = getFirstEffectiveLine(cleanedContent);

    if (!firstLine) {
        return { isTest: false, isInterface: false, isEnum: false };
    }

    const lowerCaseLine = firstLine.toLowerCase();

    const isTest = /@istest\b/.test(lowerCaseLine);
    const isInterface = /\binterface\b/.test(lowerCaseLine) && !/\bclass\b/.test(lowerCaseLine);
    const isEnum = /\benum\b/.test(lowerCaseLine) && !/\bclass\b/.test(lowerCaseLine);

    return { isTest, isInterface, isEnum };
};

// Function to check and update the package.xml
export const addHackCoverageTestToPackageXml = async (filePath: string): Promise<void> => {
    try {
        const xmlData = await fs.readFile(filePath, "utf-8");
        const parsedXml = await parseStringPromise(xmlData);

        let apexClassFound = false;

        if (parsedXml.Package.types) {
            for (const type of parsedXml.Package.types) {
                if (type.name && type.name.includes("ApexClass")) {
                    apexClassFound = true;
                    if (!type.members) {
                        type.members = [];
                    }
                    if (!type.members.includes("HackCoverageTest")) {
                        type.members.unshift("HackCoverageTest"); // Add to the start of the members list
                    }
                }
            }
        }

        if (apexClassFound) {
            const builder = new Builder();
            const updatedXml = builder.buildObject(parsedXml);
            await fs.writeFile(filePath, updatedXml, "utf-8");
            console.log(`Updated ${filePath} successfully.`);
        } else {
            console.log(`No ApexClass found in ${filePath}, no changes made.`);
        }
    } catch (error) {
        console.error("Error updating package.xml:", error);
    }
};

export const isIterable = (obj: any): boolean => {
    if (obj == null) {
        return false;
    }
    return typeof obj[Symbol.iterator] === "function";
};

export const fileExists = async (filePath: string): Promise<boolean> => {
    try {
        // 使用 fs.access 来异步地检查文件是否存在
        await fs.access(filePath, fs.constants.F_OK);
        return true;
    } catch (err) {
        return false;
    }
};
