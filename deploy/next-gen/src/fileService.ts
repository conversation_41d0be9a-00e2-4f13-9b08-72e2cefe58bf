import { promises as fs } from "fs";
import path from "path";
import * as xml2js from "xml2js";
import { getGitBranchName } from "./utils";
import { CoverageData } from "./fakeCoverageService";

export default class FileService {
    public async findFiles(dir: string, extension: string, fileNames?: string[]): Promise<string[]> {
        const files = await fs.readdir(dir, { withFileTypes: true });
        let matchedFiles: string[] = [];

        for (const file of files) {
            const fullPath = path.join(dir, file.name);

            if (file.isDirectory()) {
                matchedFiles = matchedFiles.concat(await this.findFiles(fullPath, extension, fileNames));
            } else if (file.isFile() && path.extname(file.name) === extension) {
                const baseName = path.basename(file.name, extension);
                if (!fileNames || fileNames.includes(baseName)) {
                    matchedFiles.push(fullPath);
                }
            }
        }
        return matchedFiles;
    }

    public async filterNonSpecialApexFiles(filePaths: string[]): Promise<string[]> {
        const filteredFiles: string[] = [];

        for (const filePath of filePaths) {
            const apexFileType = await checkApexClassType(filePath);
            if (!apexFileType.isTest && !apexFileType.isInterface && !apexFileType.isEnum) {
                filteredFiles.push(filePath);
            }
        }

        return filteredFiles;
    }

    public async parseXmlFile(filePath: string) {
        const parser = new xml2js.Parser({ explicitArray: false, tagNameProcessors: [xml2js.processors.stripPrefix] });
        const xmlData = await fs.readFile(filePath, "utf-8");
        return await parser.parseStringPromise(xmlData);
    }

    public async getSpecificTypeMemberFromPackageXml(filePath: string, typeNames: string[]) {
        const membersOfType: { [key: string]: string[] } = {};
        const packageObj = (await this.parseXmlFile(filePath)).Package;
        let packageTypes = packageObj.types;

        if (!Array.isArray(packageTypes)) {
            packageTypes = [packageTypes];
        }
        if (!packageTypes || !packageTypes[0]) {
            return membersOfType;
        }

        packageTypes.forEach((packageType: any) => {
            const typeName = packageType.name;
            if (typeNames.includes(typeName)) {
                const members = Array.isArray(packageType.members) ? packageType.members : [packageType.members];
                if (!membersOfType[typeName]) {
                    membersOfType[typeName] = [];
                }
                membersOfType[typeName] = membersOfType[typeName].concat(members);
            }
        });

        return membersOfType;
    }
}

interface ApexFileType {
    isTest: boolean;
    isInterface: boolean;
    isEnum: boolean;
}

function removeComments(content: string): string {
    // 移除块注释
    let withoutBlockComments = content.replace(/\/\*[\s\S]*?\*\//g, " ");

    // 移除行注释
    let withoutLineComments = withoutBlockComments.replace(/\/\/.*$/gm, " ");

    return withoutLineComments.trim();
}

function getFirstEffectiveLine(content: string): string | null {
    // 将内容按行分割，并移除空行和仅包含空白字符的行
    const lines = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0);

    // 返回第一行有效代码
    return lines.length > 0 ? lines[0] : null;
}

export const checkApexClassType = async (filePath: string): Promise<ApexFileType> => {
    const fileContent = await fs.readFile(filePath, "utf-8");

    // 移除注释后的内容
    const cleanedContent = removeComments(fileContent);

    // 获取第一行有效代码
    const firstLine = getFirstEffectiveLine(cleanedContent);

    if (!firstLine) {
        return { isTest: false, isInterface: false, isEnum: false };
    }

    const lowerCaseLine = firstLine.toLowerCase();

    const isTest = /@istest\b/.test(lowerCaseLine);
    const isInterface = /\binterface\b/.test(lowerCaseLine) && !/\bclass\b/.test(lowerCaseLine);
    const isEnum = /\benum\b/.test(lowerCaseLine) && !/\bclass\b/.test(lowerCaseLine);

    return { isTest, isInterface, isEnum };
};

export const getApexCoverage = async (coverageFilePath: string): Promise<Map<string, CoverageData>> => {
    const coverageContent = await fs.readFile(coverageFilePath, "utf-8");
    const parsedObject = JSON.parse(coverageContent);

    return new Map<string, CoverageData>(Object.entries(parsedObject) as [string, CoverageData][]);
};

export function getBaseName(filePath: string): string {
    return path.basename(filePath, path.extname(filePath));
}
