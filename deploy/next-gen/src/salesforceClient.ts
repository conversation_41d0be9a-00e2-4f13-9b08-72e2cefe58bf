import { execPromise, getSandboxName, logger } from "./utils";
import SoapApi<PERSON>lient from "./soapApiClient";

export class SalesforceClient {
    // public async authorize(username: string, clientId: string) {
    //     const uniqueSuffix = `${new Date().getTime()}_${randomBytes(8).toString("hex")}`;
    //     const uniqueKeyFileName = `oauth_private_key_${uniqueSuffix}`;
    //     await execPromise(`echo $NEXT_GEN_OAUTH_PRIVATE_KEY | base64 -d > ${uniqueKeyFileName}`, true);
    //     await execPromise(`sf org login jwt --username ${username} --jwt-key-file ./${uniqueKeyFileName} --client-id ${clientId} --alias ${username}`, true);
    //     await execPromise(`rm ${uniqueKeyFileName}`, true);
    // }

    public async authorizeWithAccessToken(username: string, passwordEnv: string) {
        const password = process.env[passwordEnv] || "";
        const soapApiClient = new SoapApiClient(username, password);
        const { sessionId, instanceUrl } = await soapApiClient.login();
        const loginCommand = `sf org login access-token --instance-url ${instanceUrl} --alias ${username} --no-prompt --json`;
        await execPromise(loginCommand, true, 1024, { ...process.env, SF_ACCESS_TOKEN: sessionId });
    }

    // public async deployWithoutDestructive(username: string, packageFile: string, silent: boolean = true) {
    //     // const packageXMLPath = `${sourcePath}/package/package.xml`;
    //     // const destructiveChangesXMLPath = `${sourcePath}/destructiveChanges/destructiveChanges.xml`;
    //     await execPromise(`sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level NoTestRun --ignore-conflicts --wait 60`, silent, 1024 * 1024 * 100);
    // }

    // public async deployWithPostDestructive(username: string, packageFile: string, postDestructiveFile: string, silent: boolean = true) {
    //     // const packageXMLPath = `${sourcePath}/package/package.xml`;
    //     // const destructiveChangesXMLPath = `${sourcePath}/destructiveChanges/destructiveChanges.xml`;
    //     await execPromise(
    //         `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level NoTestRun --ignore-conflicts --wait 60 --ignore-warnings --post-destructive-changes ${postDestructiveFile}`,
    //         silent,
    //         1024 * 1024 * 100
    //     );
    // }

    // public async deployWithPostDestructiveAndRunTest(username: string, packageFile: string, postDestructiveFile: string, diffApexTestClassNameWithRelated: string[], silent: boolean = true) {
    //     let testClasses: string;
    //
    //     if (diffApexTestClassNameWithRelated.length > 0) {
    //         testClasses = diffApexTestClassNameWithRelated.map((test) => `"${test}"`).join(" ");
    //     } else {
    //         testClasses = `"TimeUtilsTest"`;
    //     }
    //
    //     const command = `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level RunSpecifiedTests --tests ${testClasses} --ignore-conflicts --wait 60 --ignore-warnings --post-destructive-changes ${postDestructiveFile}`;
    //
    //     await execPromise(command, silent, 1024 * 1024 * 500);
    // }

    // public async deployWithDestructive(username: string, packageFile: string, preDestructiveFile: string, postDestructiveFile: string, silent: boolean = true) {
    //     await execPromise(
    //         `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level NoTestRun --ignore-conflicts --wait 60 --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile}`,
    //         silent,
    //         1024 * 1024 * 100
    //     );
    // }

    public async asyncDeploy(username: string, packageFile: string, preDestructiveFile: string, postDestructiveFile: string): Promise<string> {
        let command = `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level NoTestRun --ignore-conflicts --async --json --ignore-warnings --purge-on-delete`;
        if (preDestructiveFile != "") {
            command += ` --pre-destructive-changes ${preDestructiveFile}`;
        }
        if (postDestructiveFile != "") {
            command += ` --post-destructive-changes ${postDestructiveFile}`;
        }
        const output = await execPromise(command, true);
        const result = JSON.parse(output.output);
        return result.result.id;
    }

    public async deployWithDestructiveAsyncAndRunTest(
        username: string,
        packageFile: string,
        preDestructiveFile: string,
        postDestructiveFile: string,
        diffApexTestClassNameWithRelated: string[],
        runLocalTests: boolean = false,
        runFullDeploy: boolean = false
    ): Promise<string> {
        let command: string;

        if (runLocalTests || runFullDeploy) {
            command = `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level RunLocalTests --ignore-conflicts --async --json --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile}`;
        } else {
            let testClasses: string;

            if (diffApexTestClassNameWithRelated.length > 0) {
                testClasses = diffApexTestClassNameWithRelated.map((test) => `"${test}"`).join(" ");
            } else {
                testClasses = `"No Run test"`;
            }

            command = `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level RunSpecifiedTests --tests ${testClasses} --ignore-conflicts --async --json --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile}`;
        }

        const output = await execPromise(command, true);
        const result = JSON.parse(output.output);
        return result.result.id;
    }

    public async asyncDeployWithRunSpecificTests(
        username: string,
        packageFile: string,
        preDestructiveFile: string,
        postDestructiveFile: string,
        testClasses: string[],
        isValidation = false
    ): Promise<string> {
        const testClassesString = testClasses.length > 0 ? testClasses.join(" ") : "NoTestToRun";
        const command = `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level RunSpecifiedTests --tests ${testClassesString} --ignore-conflicts --async --json --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile} ${
            isValidation ? "--dry-run" : ""
        }`;
        const output = await execPromise(command, true);
        const result = JSON.parse(output.output);
        return result.result.id;
    }

    public async asyncDeployWithRunLocalTests(username: string, packageFile: string, preDestructiveFile: string, postDestructiveFile: string, isValidation = false): Promise<string> {
        const command = `sf project deploy start --target-org ${username} --manifest ${packageFile} --test-level RunLocalTests --ignore-conflicts --async --json --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile} ${
            isValidation ? "--dry-run" : ""
        }`;
        const output = await execPromise(command, true);
        const result = JSON.parse(output.output);
        return result.result.id;
    }

    public async asyncDeployValidatedJob(username: string, jobId: string) {
        const output = await execPromise(`sf project quick deploy -i ${jobId} -o ${username} --async --json`, true);
        const result = JSON.parse(output.output);
        return result.result.id;
    }

    public async validateWithDestructiveAsyncAndRunTest(
        username: string,
        packageFile: string,
        preDestructiveFile: string,
        postDestructiveFile: string,
        diffApexTestClassNameWithRelated: string[],
        runLocalTests: boolean = false,
        runFullDeploy: boolean = false
    ): Promise<string> {
        let command: string = "sf project deploy validate ";
        if (runLocalTests || runFullDeploy) {
            command += ` --target-org ${username} --manifest ${packageFile} --test-level RunLocalTests  --async --json --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile}`;
        } else {
            let testClasses: string;

            if (diffApexTestClassNameWithRelated.length > 0) {
                testClasses = diffApexTestClassNameWithRelated.map((test) => `"${test}"`).join(" ");
            } else {
                testClasses = `"No Run test"`;
            }

            command += ` --target-org ${username} --manifest ${packageFile} --test-level RunSpecifiedTests --tests ${testClasses} --async --json --ignore-warnings --pre-destructive-changes ${preDestructiveFile} --post-destructive-changes ${postDestructiveFile}`;
        }

        const output = await execPromise(command, true);
        const result = JSON.parse(output.output);
        return result.result.id;
    }

    public async cleanApexCodeCoverageData(username: string) {
        try {
            const coverageIds = await this.getApexCodeCoverageData(username);
            if (coverageIds.length > 0) {
                logger.infoGreen(`Found ${coverageIds.length} Apex code coverage records for user: ${getSandboxName(username)}`);

                const { instanceUrl, accessToken } = await this.getInstanceUrlAndAccessToken(username);
                const soapApiClient = new SoapApiClient(instanceUrl, accessToken);

                await soapApiClient.deleteToolingRecords(coverageIds);

                const remainingCoverageIds = await this.getApexCodeCoverageData(username);
                if (remainingCoverageIds.length > 0) {
                    logger.warnYellow(`There are still ${remainingCoverageIds.length} Apex code coverage records remaining in ${getSandboxName(username)} after clean-up`);
                    return;
                }
            }
            logger.infoGreen(`Successfully cleaned Apex code coverage data in ${getSandboxName(username)}`);
        } catch (error) {
            logger.errorRed(`Error while cleaning Apex code coverage data in ${getSandboxName(username)}, error: ${error}`);
        }
    }

    public async getInstanceUrlAndAccessToken(username: string): Promise<{
        instanceUrl: string;
        accessToken: string;
    }> {
        const result = await execPromise(`sf org display -o ${username} --json`, true);
        const { instanceUrl, accessToken } = JSON.parse(result.output).result;
        return { instanceUrl, accessToken };
    }

    public async getApexCodeCoverageData(username: string): Promise<string[]> {
        const query = "SELECT Id FROM ApexCodeCoverage";
        const records = await this.queryAllRecords(username, query, true);
        return records.map((record) => record.Id);
    }

    public async queryAllRecords(username: string, baseQuery: string, isToolingObject: boolean = false, userWhereClause: string = ""): Promise<any[]> {
        const records: any[] = [];
        const limit = 50000;
        let lastId: string | null = null;

        while (true) {
            let whereClause = "";

            if (lastId) {
                whereClause = userWhereClause ? `WHERE Id > '${lastId}' AND (${userWhereClause})` : `WHERE Id > '${lastId}'`;
            } else {
                whereClause = userWhereClause ? `WHERE ${userWhereClause}` : "";
            }

            const query = `${baseQuery} ${whereClause} ORDER BY Id LIMIT ${limit}`;
            const toolingOption = isToolingObject ? "-t" : "";
            const queryCommand = `sf data query -q "${query}" -o ${username} --json ${toolingOption}`;

            const { output } = await execPromise(queryCommand, true, 1024 * 1024 * 500);
            const result = JSON.parse(output).result;

            if (result.totalSize === 0) {
                break;
            }

            records.push(...result.records);
            lastId = result.records[result.totalSize - 1].Id;

            if (result.totalSize < limit) {
                break;
            }
        }

        return records;
    }

    public async checkDeployStatus(username: string, jobId: string): Promise<any> {
        const output = await execPromise(`sf project deploy report --json --target-org ${username} --job-id ${jobId}`, true, 1024 * 1024 * 100);
        return JSON.parse(output.output);
    }

    public async checkDeploymentStatus(username: string): Promise<boolean> {
        try {
            const result = await execPromise(
                `sfdx force:data:soql:query -q "SELECT Id, Status, StartDate, CompletedDate FROM DeployRequest WHERE Status = 'InProgress'" -t --target-org ${username} --json`,
                true
            );
            const jsonResponse = JSON.parse(result.output);

            if (jsonResponse.result.totalSize > 0) {
                logger.infoYellow(
                    `Ongoing deployment detected for ${username}, deployment id: ${jsonResponse.result.records[0].Id}, deployment start date: ${jsonResponse.result.records[0].StartDate}`
                );
                return true;
            } else {
                return false;
            }
        } catch (error) {
            logger.error(`Failed to check deployment status for ${username}: ${error}`);
            return false;
        }
    }

    public async cancelPendingAndInProgressDeployments(username: string): Promise<void> {
        try {
            const queryResult = await execPromise(
                `sfdx force:data:soql:query -q "SELECT Id, Status, StartDate, CompletedDate FROM DeployRequest WHERE Status = 'InProgress' OR Status = 'Pending'" -t --target-org ${username} --json`,
                true
            );
            const jsonResponse = JSON.parse(queryResult.output);

            if (jsonResponse.result.totalSize > 0) {
                for (const record of jsonResponse.result.records) {
                    const deploymentId = record.Id;
                    try {
                        const cancelResult = await execPromise(`sf project deploy cancel --job-id ${deploymentId} --target-org ${username} --json`, true, 1024 * 1024 * 100);
                        const cancelResponse = JSON.parse(cancelResult.output);

                        if (cancelResponse.status === 0) {
                            logger.infoGreen(`Successfully cancelled deployment with id: ${deploymentId} for ${username}`);
                        } else {
                            logger.errorRed(`Failed to cancel deployment with id: ${deploymentId} for ${username}. Response: ${cancelResult.output}`);
                        }
                    } catch (cancelError) {
                        logger.errorRed(`Error cancelling deployment with id: ${deploymentId} for ${username}: ${JSON.stringify(cancelError)}`);
                    }
                }
            } else {
                // logger.infoGreen(`No deployments found with status 'InProgress' or 'Pending' for ${username}`);
            }
        } catch (error) {
            logger.error(`Failed to retrieve deployment status for ${username}: ${error}`);
        }
    }

    // public async waitForNoDeploymentsInProgress(username: string) {
    //     const checkInterval = 10000;
    //
    //     while (await this.checkDeploymentStatus(username)) {
    //         logger.info(`Waiting for 10 seconds due to ongoing deployment in ${username}...`);
    //         await new Promise((resolve) => setTimeout(resolve, checkInterval));
    //     }
    //
    //     // logger.infoGreen(`No ongoing deployments for ${username}. Proceeding with the current deployment.`);
    // }

    // public async checkApexTestStatus(username: string): Promise<boolean> {
    //     const firstResult = await execPromise(
    //         `sfdx force:data:soql:query -q "SELECT Id, ApexClass.Name, TestTimestamp FROM ApexTestResult ORDER BY TestTimestamp DESC limit 1" -t --target-org ${username} --json`,
    //         true
    //     );
    //     const fristJsonResponse = JSON.parse(firstResult.output);
    //
    //     if (fristJsonResponse.result.totalSize > 0) {
    //         const firstApexTestResultId = fristJsonResponse.result.records[0].Id;
    //
    //         await new Promise((resolve) => setTimeout(resolve, 30000));
    //
    //         const secondResult = await execPromise(
    //             `sfdx force:data:soql:query -q "SELECT Id, ApexClass.Name, TestTimestamp FROM ApexTestResult ORDER BY TestTimestamp DESC limit 1" -t --target-org ${username} --json`,
    //             true
    //         );
    //         const secondJsonResponse = JSON.parse(secondResult.output);
    //         const secondApexTestResultId = secondJsonResponse.result.records[0].Id;
    //
    //         if (firstApexTestResultId !== secondApexTestResultId) {
    //             logger.info(
    //                 `Ongoing Apex test detected in ${username}, latest Apex test result id: ${secondJsonResponse.result.records[0].Id}, latest Apex test result timestamp: ${secondJsonResponse.result.records[0].TestTimestamp}, latest Apex test class name: ${secondJsonResponse.result.records[0].ApexClass.Name}`
    //             );
    //             return true;
    //         } else {
    //             return false;
    //         }
    //     } else {
    //         return false;
    //     }
    // }

    // public async waitForNoApexTestsInProgress(username: string) {
    //     const checkInterval = 10000;
    //
    //     while (await this.checkApexTestStatus(username)) {
    //         // logger.info("Waiting for 10 seconds...");
    //         await new Promise((resolve) => setTimeout(resolve, checkInterval));
    //     }
    //
    //     // logger.info("No ongoing Apex tests. Proceeding with the current deployment.");
    // }

    // public async runOneApexTest(username: string, testClassName: string) {
    //     try {
    //         await execPromise(`sf apex run test --target-org ${username} --class-names ${testClassName} --result-format json -w 20`, true);
    //         return { className: testClassName, username, isSuccess: true, failedTestMethods: [] };
    //     } catch (error) {
    //         try {
    //             const output = (error as { output: string }).output;
    //             const testResult = JSON.parse(output);
    //             const failedTestMethods = testResult.result.tests
    //                 .filter((test: any) => test.Outcome === "Fail")
    //                 .map((test: any) => ({
    //                     methodName: test.FullName,
    //                     errorMessage: test.Message,
    //                     stackTrace: test.StackTrace
    //                 }));
    //             return { className: testClassName, username, isSuccess: false, failedTestMethods };
    //         } catch (e) {
    //             const output = (error as { output: string }).output;
    //             return {
    //                 className: testClassName,
    //                 username,
    //                 isSuccess: false,
    //                 failedTestMethods: [
    //                     {
    //                         methodName: testClassName,
    //                         errorMessage: "Program failed to parse JSON output from Salesforce cli",
    //                         stackTrace: output
    //                     }
    //                 ]
    //             };
    //         }
    //     }
    // }

    public async runApexTest(username: string, testClassNames: string[], includeCoverage: boolean = false): Promise<any> {
        const testClassNamesString = testClassNames.join(",");
        let command = `sf apex run test --target-org ${username} --class-names ${testClassNamesString} -l RunSpecifiedTests --json`;
        if (includeCoverage) {
            command += " --synchronous --code-coverage";
        }

        try {
            const { output } = await execPromise(command, true, 1024 * 1024 * 500);
            const parsedOutput = JSON.parse(output.trim());

            if (!includeCoverage) {
                const testRunId = parsedOutput.result.testRunId;
                await execPromise(`sf apex get test --target-org ${username} -i ${testRunId} --json`);
            }

            return {
                username,
                isSuccess: true
            };
        } catch (error) {
            try {
                const testResult: ApexTestResult = JSON.parse((error as any).output);
                const failedTestMethods = testResult.result.tests
                    .filter((test: any) => test.Outcome === "Fail")
                    .map((test: any) => ({
                        className: test.ApexClass.Name,
                        methodName: test.MethodName,
                        errorMessage: test.Message,
                        stackTrace: test.StackTrace
                    }));

                return {
                    username,
                    isSuccess: failedTestMethods.length === 0,
                    failedTestMethods
                };
            } catch (parseError) {
                throw new Error(`Failed to parse JSON output from Salesforce CLI, error: ${(error as any).output}`);
            }
        }
    }

    // public async runApexTestWithCoverage(username: string, testClassName: string) {
    //     try {
    //         const result = await execPromise(
    //             `sf apex run test --target-org ${username} --class-names ${testClassName} --result-format json --synchronous --code-coverage --detailed-coverage`,
    //             true,
    //             1024 * 1024 * 100
    //         );
    //         const jsonData: CoverageData = JSON.parse(result.output);
    //         const reverseDependencies = this.getTestDependencies(jsonData);
    //         const dirPath = path.join(__dirname, "..", "coverage-data");
    //         if (!fs.existsSync(dirPath)) {
    //             fs.mkdirSync(dirPath, { recursive: true });
    //         }
    //         const filePath = path.join(dirPath, `${testClassName}.json`);
    //         const processed: { [key: string]: Set<string> } = {};
    //         for (const [key, values] of Object.entries(reverseDependencies)) {
    //             const className = key.split(".")[0];
    //             if (!processed[className]) {
    //                 processed[className] = new Set<string>();
    //             }
    //             values.forEach((value) => processed[className].add(value));
    //         }
    //         const dependency: { [key: string]: string[] } = {};
    //         for (const [key, valueSet] of Object.entries(processed)) {
    //             dependency[key] = Array.from(valueSet);
    //         }
    //         fs.writeFileSync(filePath, JSON.stringify(dependency));
    //
    //         return { className: testClassName, username, isSuccess: true, failedTestMethods: [] };
    //     } catch (error) {
    //         try {
    //             const output = (error as { output: string }).output;
    //             const testResult = JSON.parse(output);
    //             const failedTestMethods = testResult.result.tests
    //                 .filter((test: any) => test.Outcome === "Fail")
    //                 .map((test: any) => ({
    //                     methodName: test.FullName,
    //                     errorMessage: test.Message,
    //                     stackTrace: test.StackTrace
    //                 }));
    //             return { className: testClassName, username, isSuccess: false, failedTestMethods };
    //         } catch (e) {
    //             const output = (error as { output: string }).output;
    //             return {
    //                 className: testClassName,
    //                 username,
    //                 isSuccess: false,
    //                 failedTestMethods: [
    //                     {
    //                         methodName: testClassName,
    //                         errorMessage: "Program failed to parse JSON output from Salesforce cli",
    //                         stackTrace: ""
    //                     }
    //                 ]
    //             };
    //         }
    //     }
    // }

    // private getTestDependencies(coverageData: CoverageData) {
    //     const { coverage: coverageClasses } = coverageData.result.coverage;
    //     const { tests } = coverageData.result;
    //
    //     const testDependencies: Record<string, string[]> = {};
    //
    //     for (const test of tests) {
    //         const testName = test.FullName;
    //         testDependencies[testName] = [];
    //
    //         for (const coverageClass of coverageClasses) {
    //             const className = coverageClass.name;
    //             const lines = coverageClass.lines;
    //
    //             if (Object.values(lines).some((value) => value > 0) && !testDependencies[testName].includes(className)) {
    //                 testDependencies[testName].push(className);
    //             }
    //         }
    //     }
    //
    //     return testDependencies;
    // }

    public async getCoverageData(username: string): Promise<{ [testClassName: string]: string[] }> {
        const records = await this.queryAllRecords(username, "SELECT Id, ApexClassOrTrigger.Name, ApexTestClass.Name, Coverage FROM ApexCodeCoverage", true, "NumLinesCovered != 0");

        const coverageSummary: { [testClassName: string]: { [classOrTriggerName: string]: Set<number> } } = {};
        const totalLines: { [classOrTriggerName: string]: Set<number> } = {};

        records.forEach((record: any) => {
            const testClassName = record.ApexTestClass.Name;
            const classOrTriggerName = record.ApexClassOrTrigger.Name;
            const coveredLines = new Set<number>(record.Coverage.coveredLines as number[]);
            const uncoveredLines = new Set<number>(record.Coverage.uncoveredLines as number[]);
            const allLines = new Set<number>([...coveredLines, ...uncoveredLines]);

            if (!coverageSummary[testClassName]) {
                coverageSummary[testClassName] = {};
            }
            if (!coverageSummary[testClassName][classOrTriggerName]) {
                coverageSummary[testClassName][classOrTriggerName] = new Set<number>();
            }
            if (!totalLines[classOrTriggerName]) {
                totalLines[classOrTriggerName] = new Set<number>();
            }

            coveredLines.forEach((line) => coverageSummary[testClassName][classOrTriggerName].add(line));
            allLines.forEach((line) => totalLines[classOrTriggerName].add(line));
        });

        const sortedCoverageSummary: { [testClassName: string]: string[] } = {};
        Object.keys(coverageSummary)
            .sort()
            .forEach((testClassName) => {
                sortedCoverageSummary[testClassName] = Object.keys(coverageSummary[testClassName])
                    .sort()
                    .map((classOrTriggerName) => {
                        const totalCoveredLines = coverageSummary[testClassName][classOrTriggerName].size;
                        const totalLinesCount = totalLines[classOrTriggerName].size;
                        const coverage = (totalCoveredLines / totalLinesCount).toFixed(2);
                        const coveredLinesArray = Array.from(coverageSummary[testClassName][classOrTriggerName]).sort((a, b) => a - b);
                        return `${classOrTriggerName}:${coverage}:${totalCoveredLines}/${totalLinesCount}:${coveredLinesArray.join(",")}`;
                    });
            });

        return sortedCoverageSummary;
    }

    public async getAggregateData(username: string): Promise<{ [className: string]: { coveredLines: Set<number>; uncoveredLines: Set<number> } }> {
        const commandResult = await execPromise(
            `sf data query --query "SELECT Id, ApexClassOrTrigger.Name, Coverage FROM ApexCodeCoverageAggregate" --use-tooling-api --result-format json --target-org ${username}`,
            true,
            1024 * 1024 * 200
        );
        const data = JSON.parse(commandResult.output);

        const coverageData: { [className: string]: { coveredLines: Set<number>; uncoveredLines: Set<number> } } = {};

        data.result.records.forEach((record: any) => {
            const className = record.ApexClassOrTrigger.Name;
            const coveredLines = new Set<number>(record.Coverage.coveredLines as number[]);
            const uncoveredLines = new Set<number>(record.Coverage.uncoveredLines as number[]);

            if (!coverageData[className]) {
                coverageData[className] = { coveredLines: new Set<number>(), uncoveredLines: new Set<number>() };
            }

            coveredLines.forEach((line) => coverageData[className].coveredLines.add(line));
            uncoveredLines.forEach((line) => coverageData[className].uncoveredLines.add(line));
        });

        return coverageData;
    }

    public async runApexScript(username: string, scriptPath: string, silent: boolean = true) {
        try {
            await execPromise(`sf apex run -f ${scriptPath} --target-org ${username} --json`, silent);
        } catch (error) {
            const output = (error as { output: string }).output;
            logger.error(output);
            throw new Error(`Failed to run Apex script: ${scriptPath}, error: ${output}`);
        }
    }
}

type ApexTestResult = {
    status: number;
    result: {
        summary: {
            failRate: string;
            failing: number;
            hostname: string;
            orgId: string;
            outcome: string;
            passRate: string;
            passing: number;
            skipped: number;
            testRunId: string;
            testStartTime: string;
            testsRan: number;
            userId: string;
            username: string;
            commandTime: string;
            testExecutionTime: string;
            testTotalTime: string;
        };
        tests: Array<{
            Id: string;
            QueueItemId: string;
            StackTrace: string | null;
            Message: string | null;
            AsyncApexJobId: string;
            MethodName: string;
            Outcome: string;
            ApexClass: {
                Id: string;
                Name: string;
                NamespacePrefix: string | null;
            };
            RunTime: number;
            FullName: string;
        }>;
    };
    warnings: any[];
};

// type CoverageData = {
//     result: {
//         summary: {
//             outcome: string;
//             testsRan: number;
//             passing: number;
//             failing: number;
//             skipped: number;
//             passRate: string;
//             failRate: string;
//             testStartTime: string;
//             testExecutionTime: string;
//             testTotalTime: string;
//             commandTime: string;
//             hostname: string;
//             orgId: string;
//             username: string;
//             testRunId: string;
//             testRunCoverage: string;
//             orgWideCoverage: string;
//         };
//         tests: Array<{
//             Id: string;
//             QueueItemId: string;
//             StackTrace: string;
//             Message: string;
//             AsyncApexJobId: string;
//             MethodName: string;
//             Outcome: string;
//             ApexClass: {
//                 Id: string;
//                 Name: string;
//                 NamespacePrefix: string | null;
//             };
//             RunTime: number;
//             FullName: string;
//         }>;
//         coverage: {
//             coverage: Array<{
//                 id: string;
//                 name: string;
//                 totalLines: number;
//                 lines: Record<number, number>;
//                 totalCovered: number;
//                 coveredPercent: number;
//             }>;
//             records: Array<{
//                 ApexTestClass: {
//                     Id: string;
//                     Name: string;
//                 };
//                 Coverage: {
//                     coveredLines: Array<number>;
//                     uncoveredLines: Array<number>;
//                 };
//                 TestMethodName: string;
//                 NumLinesCovered: number;
//                 ApexClassOrTrigger: {
//                     Id: string;
//                     Name: string;
//                 };
//                 NumLinesUncovered: number;
//             }>;
//             summary: {
//                 totalLines: number;
//                 coveredLines: number;
//                 orgWideCoverage: string;
//                 testRunCoverage: string;
//             };
//         };
//     };
// };
