import { SalesforceClient } from "../salesforceClient";
import { execPromise, getGitBranchName, getSandboxName, logger, sleep } from "../utils";
import SoapApiClient from "../soapApiClient";
import { promises as fs } from "fs";

export class SalesforceEnvironment {
    protected salesforceClient: SalesforceClient;
    protected username: string;
    private passwordEnv: string = "";
    protected authorized: boolean = false;

    protected deploymentJobId: string = "";
    protected deploymentStartTime: number = 0;
    protected deployStatus: "NotStart" | "Deploying" | "Deployed" | "Failed" = "NotStart";
    protected deployFailedReason: string = "";

    private isRunningTest: boolean = false;

    private testResultMap: Map<string, BaseTestResult> = new Map();

    constructor(
        username: string,
        private readonly needSetup: boolean = false
    ) {
        this.salesforceClient = new SalesforceClient();
        this.username = username;
        this.needSetup = needSetup;
    }

    private async setupNecessaryConfig() {
        logger.infoGreen(`[Setup Sandbox]                  Start to setup sandbox: ${this.username}`);
        await execPromise(`sf browserforce apply --target-org="${this.username}" --definitionfile="config/setup-sandbox.json"`, false);
    }

    initDeploymentInfo() {
        this.deploymentJobId = "";
        this.deploymentStartTime = Date.now();
        this.deployStatus = "NotStart";
        this.deployFailedReason = "";
    }

    getDeploymentJobId() {
        return this.deploymentJobId;
    }

    getDeployStartTime() {
        return this.deploymentStartTime;
    }

    getDeployFailedReason() {
        return this.deployFailedReason;
    }

    getDeployStatus() {
        return this.deployStatus;
    }

    getTimeoutMs() {
        return 2 * 3600 * 1000;
    }

    getTestResultMap() {
        return this.testResultMap;
    }

    async checkDeployStatus(): Promise<string> {
        if (!this.authorized) {
            await this.authorize();
        }
        if (!this.deploymentJobId) {
            this.deployStatus = "Deployed";
            return this.deployStatus;
        }

        try {
            const status = await this.salesforceClient.checkDeployStatus(this.username, this.deploymentJobId);

            logger.infoYellow(
                `[Deploy Checked]                  Sandbox: ${this.username}, status: ${status.result.status}, components: (${status.result.numberComponentsDeployed}+${status.result.numberComponentErrors}/${status.result.numberComponentsTotal}), tests: (${status.result.numberTestsCompleted}+${status.result.numberTestErrors}/${status.result.numberTestsTotal}), details: ${status.result.stateDetail}`
            );
            if (Date.now() - this.deploymentStartTime > this.getTimeoutMs()) {
                this.deployStatus = "Failed";
                this.deployFailedReason = "Timeout";
            }
            if (status.result.numberComponentErrors > 0 || status.result.numberTestErrors > 0) {
                const componentFailures = status.result.details.componentFailures;
                const componentFailureMessages = componentFailures
                    .map((failure: { fullName: any; componentType: any; problem: any; lineNumber: any; columnNumber: any }) => {
                        return `Component Failure in ${this.getSandboxName()}: ${failure.fullName} (${failure.componentType}) - ${failure.problem} at line ${failure.lineNumber}, column ${
                            failure.columnNumber
                        }`;
                    })
                    .join("\n");
                const runTestFailures = status.result.details.runTestResult.failures;
                const runTestFailureMessages = runTestFailures
                    .map((failure: { name: any; methodName: any; message: any; stackTrace: any }) => {
                        return `Test Failure in ${this.getSandboxName()}: ${failure.name}.${failure.methodName} - ${failure.message}\n${failure.stackTrace}`;
                    })
                    .join("\n");

                this.deployFailedReason = componentFailureMessages + "\n" + runTestFailureMessages;
            }
            if (status.result.status === "Failed") {
                this.deployStatus = "Failed";
            }
            if (status.result.status === "Canceled") {
                this.deployStatus = "Failed";
                this.deployFailedReason = "Canceled";
            }
            if (status.result.status === "Succeeded") {
                this.deployStatus = "Deployed";
            }
        } catch (e) {
            logger.errorRed(`Failed to check deploy status: ${e.message}`);
        }
        if (this.deployStatus === "Failed" || this.deployStatus === "Deployed") {
            await this.postDeploy();
        }
        return this.deployStatus;
    }

    async deploy(packageFile: string, preDestructiveFile: string, postDestructiveFile: string) {
        if (!this.authorized) {
            await this.authorize();
        }
        await this.preDeploy();
        try {
            this.initDeploymentInfo();
            this.deploymentJobId = await this.salesforceClient.asyncDeploy(this.username, packageFile, preDestructiveFile, postDestructiveFile);
            logger.infoGreen(`[Deploy Started]                  Successfully started deployment for ${this.getSandboxName()}, job id: ${this.deploymentJobId}`);
            this.deployStatus = "Deploying";
        } catch (e) {
            logger.errorRed(`Deployment failed: ${e.message}`);
            throw e;
        }
    }

    async preDeploy() {
        if (this.needSetup) {
            await this.setupNecessaryConfig();
        }
        await this.cancelPendingAndInProgressDeployments();
    }

    async postDeploy() {}

    public async authorize() {
        const sandboxName = this.getSandboxName();
        if (sandboxName === "prod" || sandboxName === "crmpg") {
            this.passwordEnv = "NEXT_GEN_ACCESS_TOKEN_PASSWORD_PROD";
        } else if (sandboxName === "sfdcuat2") {
            this.passwordEnv = "NEXT_GEN_ACCESS_TOKEN_PASSWORD_UAT";
        } else {
            this.passwordEnv = "NEXT_GEN_ACCESS_TOKEN_PASSWORD";
        }

        const sandbox = {
            username: this.username,
            passwordEnv: this.passwordEnv
        };
        try {
            await this.salesforceClient.authorizeWithAccessToken(sandbox.username, sandbox.passwordEnv);
            logger.infoGreen(`Successfully authorized ${this.getSandboxName()}`);
            this.authorized = true;
        } catch (error) {
            logger.warnYellow(`Authorization failed with ${sandbox.passwordEnv}, checking alternative password...`);
            if (sandboxName !== "prod" && sandboxName !== "crmpg" && sandboxName !== "sfdcuat2") {
                const tempPasswordEnv = "NEXT_GEN_ACCESS_TOKEN_PASSWORD_TEMP";

                try {
                    await this.salesforceClient.authorizeWithAccessToken(sandbox.username, tempPasswordEnv);
                    logger.infoGreen(`Successfully authorized ${this.getSandboxName()} using TEMP password`);
                    this.authorized = true;
                    return;
                } catch (tempError) {
                    logger.errorRed(`Failed to authorize ${sandbox.username} with both passwords, error: ${JSON.stringify(tempError)}`);
                    throw new Error(`Failed to authorize ${sandbox.username} with both passwords, error: ${JSON.stringify(tempError)}`);
                }
            }

            logger.errorRed(`Failed to authorize ${sandbox.username}, error: ${JSON.stringify(error)}`);
            throw new Error(`Failed to authorize ${sandbox.username}, error: ${JSON.stringify(error)}`);
        }
    }

    public getSandboxName = (): string => {
        const domain = "@thoughtworks.com";
        const domainIndex = this.username.indexOf(domain);

        if (domainIndex === -1) {
            return "prod";
        }

        const sandboxPart = this.username.slice(domainIndex + domain.length + 1);
        return sandboxPart.length > 0 ? sandboxPart : "prod";
    };

    public getIsRunningTest() {
        return this.isRunningTest;
    }

    public async runPlaywrightTest(testName: string) {
        this.isRunningTest = true;
        try {
            await execPromise(`cd playwright-tests && PLAYWRIGHT_USERNAME="${this.username}" npx playwright test ${testName} --output=./test-results/${testName} || true`);
            this.testResultMap.set(testName, {
                runEnvironment: this.username,
                isCorrect: true,
                failureMessages: ""
            });
        } catch (e) {
            this.handleTestException(e, [testName]);
            logger.errorRed(`Failed to run test: ${e.message}`);
        } finally {
            this.isRunningTest = false;
        }
    }

    public async runUITest(testName: string) {
        this.isRunningTest = true;
        try {
            await this.setupUITestEnvironment();
            await this.runCucumberUITest(testName);
        } catch (e) {
            this.handleTestException(e, [testName]);
            logger.errorRed(`Failed to run test: ${e.message}`);
        } finally {
            this.isRunningTest = false;
        }
    }

    private async runCucumberUITest(testName: string): Promise<void> {
        const logFileName: string = `${testName.split("/").pop()}.txt`;
        const logFilePath: string = `./logs/${logFileName}`;
        const command: string = `cd ui-tests && touch ${logFilePath} && npm run test -- ${testName} --world-parameters '${JSON.stringify({
            sandboxName: this.getSandboxName(),
            password: process.env["CI_TEST_SANDBOX_PASSWORD"]
        })}' 2>&1 | tee ${logFilePath}`;

        await execPromise(command, true);

        const logContent = await fs.readFile(`./ui-tests/logs/${logFileName}`, "utf-8");
        const isSuccessful = !logContent.includes("failed") && !logContent.includes("error");

        this.testResultMap.set(testName, {
            runEnvironment: this.username,
            isCorrect: isSuccessful,
            failureMessages: isSuccessful ? "" : logContent
        });
    }

    private async setupUITestEnvironment() {
        const gitBranchName = await getGitBranchName();
        switch (true) {
            case gitBranchName.startsWith("sf-"):
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/setup_sf_test_environment.apex", true);
                break;
            case gitBranchName.startsWith("crm-"):
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/setup_crm_test_environment.apex", true);
                break;
            case gitBranchName.startsWith("psa-"):
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/delete_account_team_member.apex", true);
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/delete_lightning_timecard.apex", true);
                break;
            case gitBranchName.startsWith("master"):
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/delete_account_team_member.apex", true);
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/delete_lightning_timecard.apex", true);
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/setup_sf_test_environment.apex", true);
                break;
            case gitBranchName.startsWith("release"):
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/delete_account_team_member.apex", true);
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/delete_lightning_timecard.apex", true);
                await this.salesforceClient.runApexScript(this.username, "./deploy/script/deploy_and_run_test/apex_scripts/setup_sf_test_environment.apex", true);
                break;
        }
    }

    public async runApexTest(testNames: string[]) {
        this.isRunningTest = true;
        try {
            const testClassNamesString = testNames.join(",");
            const command = `sf apex run test --target-org ${this.username} --class-names ${testClassNamesString} -l RunSpecifiedTests --json`;
            const { output } = await execPromise(command, true);
            const testRunId = JSON.parse(output.trim()).result.testRunId;
            try {
                const testResult: ApexTestResult = JSON.parse((await execPromise(`sf apex get test --target-org ${this.username} -i ${testRunId} --json`)).output.trim());
                this.testResultMap = this.extractApexTestResults(testNames, testResult);
            } catch (error) {
                try {
                    const testResult: ApexTestResult = JSON.parse((error as any).output);
                    this.testResultMap = this.extractApexTestResults(testNames, testResult);
                } catch (parseError) {
                    this.handleTestException(parseError, testNames);
                    logger.errorRed(`Failed to parse JSON output from Salesforce CLI when run test of ${testNames.join(",")}, error: ${error.message}`);
                }
            }
        } catch (e) {
            this.handleTestException(e, testNames);
            logger.errorRed(`Failed to run test: ${e.message}`);
        } finally {
            this.isRunningTest = false;
        }
    }

    public async runApexTestWithCoverage(testName: string) {
        this.isRunningTest = true;
        try {
            const command = `sf apex run test --target-org ${this.username} --class-names ${testName} -l RunSpecifiedTests --json --synchronous --code-coverage`;

            try {
                const { output } = await execPromise(command, true, 1024 * 1024 * 500);
                const testResult = JSON.parse(output.trim());
                this.testResultMap = this.extractApexTestResults([testName], testResult);
            } catch (error) {
                try {
                    const testResult: ApexTestResult = JSON.parse((error as any).output);
                    this.testResultMap = this.extractApexTestResults([testName], testResult);
                } catch (parseError) {
                    this.handleTestException(parseError, [testName]);
                    logger.errorRed(`Failed to parse JSON output from Salesforce CLI when run test of ${testName}, error: ${error.message}`);
                }
            }
        } catch (e) {
            this.handleTestException(e, [testName]);
            logger.errorRed(`Failed to run test: ${e.message}`);
        } finally {
            this.isRunningTest = false;
        }
    }

    private initTestResultMap(testNames: string[]) {
        const resultMap = new Map();
        testNames.forEach((testName) => {
            resultMap.set(testName, {
                runEnvironment: this.username,
                isCorrect: true,
                failureMessages: "",
                incorrectMethods: []
            });
        });
        return resultMap;
    }

    extractApexTestResults(testClassNames: string[], apexTestResult: ApexTestResult) {
        const resultMap = this.initTestResultMap(testClassNames);
        apexTestResult.result.tests.forEach((test) => {
            const apexClassName = test.ApexClass.Name;
            const classResult = resultMap.get(apexClassName)!;

            if (test.Outcome === "Fail") {
                classResult.isCorrect = false;
                classResult.incorrectMethods.push({
                    methodName: test.MethodName,
                    stackTrace: test.StackTrace,
                    message: test.Message
                });
            }
        });

        resultMap.forEach((classResult) => {
            if (classResult.incorrectMethods.length > 0) {
                classResult.failureMessages = classResult.incorrectMethods
                    .map(
                        (
                            method: {
                                methodName: string;
                                stackTrace: string | null;
                                message: string | null;
                            },
                            index: number
                        ) => {
                            return `${index + 1}. Method ${method.methodName}\nMessage: ${method.message}\nStackTrace: ${method.stackTrace}`;
                        }
                    )
                    .join("\n");
            }
        });
        return resultMap;
    }

    handleTestException = (e: any, apexTestClassNames: string[]) => {
        this.testResultMap = this.initTestResultMap(apexTestClassNames);
        this.testResultMap.forEach((classResult) => {
            classResult.isCorrect = false;
            classResult.failureMessages = `1. Method Unknown\nMessage: ${e.message || e.output.message || "Unknown error"} \nStackTrace: ${e.stack || e.output.stack || "Unknown stack"}`;
        });
    };

    async cancelPendingAndInProgressDeployments() {
        if (!this.authorized) {
            await this.authorize();
        }
        await this.salesforceClient.cancelPendingAndInProgressDeployments(this.username);
    }

    public async cleanApexCodeCoverageData() {
        if (!this.authorized) {
            await this.authorize();
        }
        try {
            const coverageIds = await this.salesforceClient.getApexCodeCoverageData(this.username);
            if (coverageIds.length > 0) {
                logger.infoGreen(`Found ${coverageIds.length} Apex code coverage records for user: ${this.username}`);

                const { instanceUrl, accessToken } = await this.salesforceClient.getInstanceUrlAndAccessToken(this.username);
                const soapApiClient = new SoapApiClient(instanceUrl, accessToken);

                await soapApiClient.deleteToolingRecords(coverageIds);

                const remainingCoverageIds = await this.salesforceClient.getApexCodeCoverageData(this.username);
                if (remainingCoverageIds.length > 0) {
                    logger.warnYellow(`There are still ${remainingCoverageIds.length} Apex code coverage records remaining in ${this.getSandboxName()} after clean-up`);
                    return;
                }
            }
            logger.infoGreen(`Successfully cleaned Apex code coverage data in ${this.getSandboxName()}`);
        } catch (error) {
            logger.errorRed(`Error while cleaning Apex code coverage data in ${this.getSandboxName()}, error: ${error}`);
        }
    }

    public async getCoverageData(): Promise<{ [testClassName: string]: string[] }> {
        if (!this.authorized) {
            await this.authorize();
        }
        const records = await this.salesforceClient.queryAllRecords(
            this.username,
            "SELECT Id, ApexClassOrTrigger.Name, ApexTestClass.Name, Coverage FROM ApexCodeCoverage",
            true,
            "NumLinesCovered != 0"
        );

        const coverageSummary: { [testClassName: string]: { [classOrTriggerName: string]: Set<number> } } = {};
        const totalLines: { [classOrTriggerName: string]: Set<number> } = {};

        records.forEach((record: any) => {
            const testClassName = record.ApexTestClass.Name;
            const classOrTriggerName = record.ApexClassOrTrigger.Name;
            const coveredLines = new Set<number>(record.Coverage.coveredLines as number[]);
            const uncoveredLines = new Set<number>(record.Coverage.uncoveredLines as number[]);
            const allLines = new Set<number>([...coveredLines, ...uncoveredLines]);

            if (!coverageSummary[testClassName]) {
                coverageSummary[testClassName] = {};
            }
            if (!coverageSummary[testClassName][classOrTriggerName]) {
                coverageSummary[testClassName][classOrTriggerName] = new Set<number>();
            }
            if (!totalLines[classOrTriggerName]) {
                totalLines[classOrTriggerName] = new Set<number>();
            }

            coveredLines.forEach((line) => coverageSummary[testClassName][classOrTriggerName].add(line));
            allLines.forEach((line) => totalLines[classOrTriggerName].add(line));
        });

        const sortedCoverageSummary: { [testClassName: string]: string[] } = {};
        Object.keys(coverageSummary)
            .sort()
            .forEach((testClassName) => {
                sortedCoverageSummary[testClassName] = Object.keys(coverageSummary[testClassName])
                    .sort()
                    .map((classOrTriggerName) => {
                        const totalCoveredLines = coverageSummary[testClassName][classOrTriggerName].size;
                        const totalLinesCount = totalLines[classOrTriggerName].size;
                        const coverage = (totalCoveredLines / totalLinesCount).toFixed(2);
                        const coveredLinesArray = Array.from(coverageSummary[testClassName][classOrTriggerName]).sort((a, b) => a - b);
                        return `${classOrTriggerName}:${coverage}:${totalCoveredLines}/${totalLinesCount}:${coveredLinesArray.join(",")}`;
                    });
            });

        return sortedCoverageSummary;
    }

    public async getAggregateCoverageData(): Promise<{ [className: string]: { coveredLines: Set<number>; uncoveredLines: Set<number> } }> {
        if (!this.authorized) {
            await this.authorize();
        }
        const commandResult = await execPromise(
            `sf data query --query "SELECT Id, ApexClassOrTrigger.Name, Coverage FROM ApexCodeCoverageAggregate" --use-tooling-api --result-format json --target-org ${this.username}`,
            true,
            1024 * 1024 * 200
        );
        const data = JSON.parse(commandResult.output);

        const coverageData: { [className: string]: { coveredLines: Set<number>; uncoveredLines: Set<number> } } = {};

        data.result.records.forEach((record: any) => {
            const className = record.ApexClassOrTrigger.Name;
            const coveredLines = new Set<number>(record.Coverage.coveredLines as number[]);
            const uncoveredLines = new Set<number>(record.Coverage.uncoveredLines as number[]);

            if (!coverageData[className]) {
                coverageData[className] = { coveredLines: new Set<number>(), uncoveredLines: new Set<number>() };
            }

            coveredLines.forEach((line) => coverageData[className].coveredLines.add(line));
            uncoveredLines.forEach((line) => coverageData[className].uncoveredLines.add(line));
        });

        return coverageData;
    }
}

export interface BaseTestResult {
    runEnvironment: string;
    isCorrect: boolean;
    failureMessages: string;
}

type ApexTestResult = {
    status: number;
    result: {
        summary: {
            failRate: string;
            failing: number;
            hostname: string;
            orgId: string;
            outcome: string;
            passRate: string;
            passing: number;
            skipped: number;
            testRunId: string;
            testStartTime: string;
            testsRan: number;
            userId: string;
            username: string;
            commandTime: string;
            testExecutionTime: string;
            testTotalTime: string;
        };
        tests: Array<{
            Id: string;
            QueueItemId: string;
            StackTrace: string | null;
            Message: string | null;
            AsyncApexJobId: string;
            MethodName: string;
            Outcome: string;
            ApexClass: {
                Id: string;
                Name: string;
                NamespacePrefix: string | null;
            };
            RunTime: number;
            FullName: string;
        }>;
    };
    warnings: any[];
};
