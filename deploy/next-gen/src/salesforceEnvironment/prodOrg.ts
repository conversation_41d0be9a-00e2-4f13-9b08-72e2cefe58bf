import { SalesforceEnvironment } from "./salesforceEnvironment";
import { logger } from "../utils";
import quickFakeDeployConfig from "../../config/specified-tests-for-release-without-tests.json";

export class ProdOrg extends SalesforceEnvironment {
    constructor(username: string) {
        super(username);
    }

    override getTimeoutMs(): number {
        return 17000000;
    }

    override async preDeploy(): Promise<void> {}

    override async checkDeployStatus(): Promise<string> {
        if (!this.authorized) {
            await this.authorize();
        }
        if (!this.deploymentJobId) {
            throw new Error("Deployment job id is empty");
        }

        try {
            const status = await this.salesforceClient.checkDeployStatus(this.username, this.deploymentJobId);

            logger.infoYellow(
                `[Deploy Checked]                  Sandbox: ${this.username}, status: ${status.result.status}, components: (${status.result.numberComponentsDeployed}+${status.result.numberComponentErrors}/${status.result.numberComponentsTotal}), tests: (${status.result.numberTestsCompleted}+${status.result.numberTestErrors}/${status.result.numberTestsTotal}), details: ${status.result.stateDetail}`
            );
            if (Date.now() - this.deploymentStartTime > this.getTimeoutMs()) {
                this.deployStatus = "Failed";
                this.deployFailedReason = "Timeout";
            }
            if (status.result.numberComponentErrors > 0 || status.result.numberTestErrors > 0) {
                const componentFailures = status.result.details.componentFailures;
                const componentFailureMessages = componentFailures
                    .map((failure: { fullName: any; componentType: any; problem: any; lineNumber: any; columnNumber: any }) => {
                        return `Component Failure in ${this.getSandboxName()}: ${failure.fullName} (${failure.componentType}) - ${failure.problem} at line ${failure.lineNumber}, column ${
                            failure.columnNumber
                        }`;
                    })
                    .join("\n");
                const runTestFailures = status.result.details.runTestResult.failures;
                const runTestFailureMessages = runTestFailures
                    .map((failure: { name: any; methodName: any; message: any; stackTrace: any }) => {
                        return `Test Failure in ${this.getSandboxName()}: ${failure.name}.${failure.methodName} - ${failure.message}\n${failure.stackTrace}`;
                    })
                    .join("\n");
                this.deployStatus = "Failed";
                this.deployFailedReason = componentFailureMessages + "\n" + runTestFailureMessages;
            }
            if (status.result.status === "Failed") {
                this.deployStatus = "Failed";
            }
            if (status.result.status === "Canceled") {
                this.deployStatus = "Failed";
                this.deployFailedReason = "Canceled";
            }
            if (status.result.status === "Succeeded") {
                this.deployStatus = "Deployed";
            }
        } catch (e) {
            logger.errorRed(`Failed to check deploy status: ${e.message}`);
        }
        if (this.deployStatus === "Failed" || this.deployStatus === "Deployed") {
            await this.postDeploy();
        }
        return this.deployStatus;
    }

    async deployValidatedJob(validatedJobId: string) {
        await this.executeDeploy({
            deployAction: () => this.salesforceClient.asyncDeployValidatedJob(this.username, validatedJobId),
            successMessage: `[Deploy Started] Successfully started deployment for ${this.getSandboxName()}`
        });
    }

    async validateDeployWithRunLocalTests(packageFile: string, preDestructiveFile: string, postDestructiveFile: string) {
        await this.executeDeploy({
            deployAction: () => this.salesforceClient.asyncDeployWithRunLocalTests(this.username, packageFile, preDestructiveFile, postDestructiveFile, true),
            successMessage: `[Validation Started] Successfully started validation for ${this.getSandboxName()}`
        });
    }

    async continueValidateDeployProgress(validateJobId: string) {
        await this.executeDeploy({
            deployAction: async () => validateJobId,
            successMessage: `[Validation Continued] Successfully continued validation for ${this.getSandboxName()}`
        });
    }

    async deployWithoutTests(packageFile: string, preDestructiveFile: string, postDestructiveFile: string) {
        const testClasses: string[] = quickFakeDeployConfig.testClassToRun;
        await this.executeDeploy({
            deployAction: () => this.salesforceClient.asyncDeployWithRunSpecificTests(this.username, packageFile, preDestructiveFile, postDestructiveFile, testClasses),
            successMessage: `[Deploy Started] Successfully started deployment for ${this.getSandboxName()}`
        });
    }

    async deployWithRunSpecificTests(packageFile: string, preDestructiveFile: string, postDestructiveFile: string, testClasses: string[]) {
        await this.executeDeploy({
            deployAction: () => this.salesforceClient.asyncDeployWithRunSpecificTests(this.username, packageFile, preDestructiveFile, postDestructiveFile, testClasses),
            successMessage: `[Deploy Started] Successfully started deployment for ${this.getSandboxName()}`
        });
    }

    async deployWithRunLocalTests(packageFile: string, preDestructiveFile: string, postDestructiveFile: string) {
        await this.executeDeploy({
            deployAction: () => this.salesforceClient.asyncDeployWithRunLocalTests(this.username, packageFile, preDestructiveFile, postDestructiveFile),
            successMessage: `[Deploy Started] Successfully started deployment for ${this.getSandboxName()}`
        });
    }

    private async executeDeploy({ deployAction, successMessage }: { deployAction: () => Promise<string>; successMessage: string }) {
        if (!this.authorized) {
            await this.authorize();
        }
        await this.preDeploy();
        try {
            this.initDeploymentInfo();
            this.deploymentJobId = await deployAction();
            logger.infoGreen(`${successMessage}, job id: ${this.deploymentJobId}`);
            this.deployStatus = "Deploying";
        } catch (e) {
            logger.errorRed(`Deployment failed: ${e.message}`);
            throw e;
        } finally {
            await this.postDeploy();
        }
    }
}
