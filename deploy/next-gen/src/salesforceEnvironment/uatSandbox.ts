import { SalesforceEnvironment } from "./salesforceEnvironment";
import { logger, sleep } from "../utils";
import { CircleCIPipeline } from "../pipelineService/circleCIPipeline";

export class UATSandbox extends SalesforceEnvironment {
    private circleCIPipeline: CircleCIPipeline;

    constructor(username: string) {
        super(username);
        this.circleCIPipeline = new CircleCIPipeline();
    }

    override async preDeploy() {
        let uatDeployIsInProgress = await this.salesforceClient.checkDeploymentStatus(`<EMAIL>.sfdcuat2`);
        if (uatDeployIsInProgress) {
            logger.errorBannerRed(`Terminating program due to conflicting deployment job`);
            await this.circleCIPipeline.cancelWorkflow();
            await sleep(60 * 1000);
            throw new Error(`Terminating program due to conflicting deployment job In UAT`);
        }
        logger.infoGreen(`Starting pre-deployment tasks for UAT Sandbox: ${this.getSandboxName()}`);
        logger.infoGreen(`Stopping schedule job in uat`);
        await this.salesforceClient.runApexScript(this.username, `deploy/script/deploy_and_run_test/apex_scripts/abort_apex_jobs.apex`);
    }

    override async postDeploy() {
        logger.infoGreen(`Starting post-deployment tasks for UAT Sandbox: ${this.getSandboxName()}`);
        logger.infoGreen(`Starting schedule job in uat`);
        await this.salesforceClient.runApexScript(this.username, `deploy/script/deploy_and_run_test/apex_scripts/schedule_apex_jobs_part1.apex`);
        await this.salesforceClient.runApexScript(this.username, `deploy/script/deploy_and_run_test/apex_scripts/schedule_apex_jobs_part2.apex`);
    }
}
