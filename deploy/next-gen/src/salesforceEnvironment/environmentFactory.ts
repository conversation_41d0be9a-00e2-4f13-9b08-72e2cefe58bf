import { SalesforceEnvironment } from "./salesforceEnvironment";
import { UATSandbox } from "./uatSandbox";
import { ProdOrg } from "./prodOrg";

export class EnvironmentFactory {
    public static build(username: string, needSetup: boolean = false): SalesforceEnvironment {
        const envType = this.getEnvType(username);
        switch (envType) {
            case "prod":
                return new ProdOrg(username);
            case "uat":
                return new UATSandbox(username);
            // case "dev":
            //     return new Sandbox("")
            default:
                return new SalesforceEnvironment(username, needSetup);
        }
    }

    private static getEnvType(username: string): string {
        // todo: fix it later
        if (username.endsWith(".com")) {
            return "prod";
        }
        if (username.endsWith(".sfdcuat2")) {
            return "uat";
        }
        return "dev";
    }
}
