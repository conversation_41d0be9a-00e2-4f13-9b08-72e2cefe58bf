import axios from "axios";
import * as fs from "fs";
import * as ip from "ip";
import { XMLParser, XMLBuilder } from "fast-xml-parser";
import { findFilePath, getPackagePathsFromSFDXProjectJson, logger } from "./utils";

interface IpRange {
    start: string;
    end: string;
}

const AWS_IP_RANGES_URL = "https://ip-ranges.amazonaws.com/ip-ranges.json";
const AWS_IP_RANGES_FILE = "deploy/next-gen/config/aws_ip_range.json";
const INDENT_BY_4_SPACES = "    ";

export class MetadataCustomProcessor {
    private static async getAwsIpRange(): Promise<IpRange[]> {
        try {
            const response = await axios.get(AWS_IP_RANGES_URL);
            if (response.status === 200) {
                fs.writeFileSync(AWS_IP_RANGES_FILE, JSON.stringify(response.data));
            } else {
                logger.errorRed(`Fail to get AWS IP ranges, reason: ${response.statusText}`);
            }
        } catch (error) {
            logger.errorRed(`Fail to get AWS IP ranges: ${error}`);
        }

        const awsIpRanges = fs.readFileSync(AWS_IP_RANGES_FILE, "utf-8");
        const distrosDict = JSON.parse(awsIpRanges);
        const ipRangeList: IpRange[] = [];

        for (const distro of distrosDict["prefixes"]) {
            ipRangeList.push(this.calcIpRange(distro["ip_prefix"]));
        }

        return ipRangeList;
    }

    private static calcIpRange(ipNetWorkAddr: string): IpRange {
        const ipNetwork = ip.cidrSubnet(ipNetWorkAddr);
        return {
            start: ipNetwork.firstAddress,
            end: ipNetwork.lastAddress
        };
    }

    public static async appendIpRangeToSecuritySetting(): Promise<void> {
        const sourceDirPaths = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
        const SECURITY_SETTINGS_PATH = await findFilePath(sourceDirPaths, "Security.settings-meta.xml");
        const ipRangeList = await this.getAwsIpRange();
        const parser = new XMLParser({ ignoreAttributes: false });
        const securityXmlContent = fs.readFileSync(SECURITY_SETTINGS_PATH, { encoding: "utf8" });
        const securitySettingObj = parser.parse(securityXmlContent);
        const networkAccess = securitySettingObj.SecuritySettings.networkAccess.ipRanges || [];

        for (const ipRange of ipRangeList) {
            networkAccess.push({
                description: `${ipRange.start}-${ipRange.end}`,
                start: ipRange.start,
                end: ipRange.end
            });
        }

        const builder = new XMLBuilder({ ignoreAttributes: false, format: true, indentBy: INDENT_BY_4_SPACES });
        const xml = builder.build(securitySettingObj);
        fs.writeFileSync(SECURITY_SETTINGS_PATH, xml);
    }

    public static async updateBrandLogoConfigInApplication(): Promise<void> {
        const sourceDirPaths = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
        const CRM_APPLICATION_PATH = await findFilePath(sourceDirPaths, "ThoughtWorks_CRM.app-meta.xml");
        const parser = new XMLParser({ ignoreAttributes: false });
        const applicationXmlContent = fs.readFileSync(CRM_APPLICATION_PATH, { encoding: "utf8" });
        const applicationObj = parser.parse(applicationXmlContent);

        const brand = applicationObj.CustomApplication.brand;
        if (brand) {
            brand.logo = "playground_logo";
        }

        const builder = new XMLBuilder({ ignoreAttributes: false, format: true, indentBy: INDENT_BY_4_SPACES });
        const xml = builder.build(applicationObj);
        fs.writeFileSync(CRM_APPLICATION_PATH, xml);
    }
}
