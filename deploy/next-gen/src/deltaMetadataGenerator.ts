import { execPromise, logger } from "./utils";
import { promises as fs } from "fs";
import { Builder, parseStringPromise } from "xml2js";

export class DeltaMetadataGenerator {
    constructor(private projectRoot: string, private resultPath: string) {}

    public async generate(baseCommit: string | null, ignoreMetadataConfigFile: string) {
        if (!baseCommit) {
            throw new Error(`No proper base commit revision found.`);
        }
        await this.generateDelta(baseCommit, "HEAD", this.projectRoot, this.resultPath, ignoreMetadataConfigFile);
    }

    private async generateDelta(fromCommitRevision: string, toCommitRevision: string, projectPath: string, resultPath: string, ignoreFilePath: string) {
        logger.infoGreen(`Generating diff metadata from ${fromCommitRevision} to ${toCommitRevision}`);

        //await createDirectory(resultPath);
        await execPromise(
            `sf sgd:source:delta --from ${fromCommitRevision} --to ${toCommitRevision} --source ${projectPath} --output ${resultPath} --ignore ${ignoreFilePath} --ignore-whitespace`,
            true
        );
        logger.infoGreen(`Generating diff metadata from ${fromCommitRevision} to ${toCommitRevision}`);
        await this.addHackCoverageTestToPackageXml(`package/package.xml`);
    }

    private async addHackCoverageTestToPackageXml(packageXmlPath: string) {
        try {
            const xmlData = await fs.readFile(packageXmlPath, "utf-8");
            const parsedXml = await parseStringPromise(xmlData);

            let apexClassFound = false;

            if (parsedXml.Package.types) {
                for (const type of parsedXml.Package.types) {
                    if (type.name && type.name.includes("ApexClass")) {
                        apexClassFound = true;
                        if (!type.members) {
                            type.members = [];
                        }
                        if (!type.members.includes("HackCoverageTest")) {
                            type.members.unshift("HackCoverageTest"); // Add to the start of the members list
                        }
                    }
                }
            }

            if (apexClassFound) {
                const builder = new Builder();
                const updatedXml = builder.buildObject(parsedXml);
                await fs.writeFile(packageXmlPath, updatedXml, "utf-8");
                logger.infoGreen(`Insert HackCoverageTest to ${packageXmlPath} successfully.`);
            } else {
                logger.infoGreen(`No ApexClass found in ${packageXmlPath}, no changes made.`);
            }
        } catch (error) {
            logger.errorRed(`Error Insert HackCoverageTest to ${packageXmlPath}:`, error);
        }
    }
}
