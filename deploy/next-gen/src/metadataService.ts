import fs from "fs";
import { XMLParser } from "fast-xml-parser";
import { execPromise, findFilePath, getPackagePathsFromSFDXProjectJson, logger, readIgnoreMetadataConfigFile } from "./utils";
import { MetadataPreprocessorFactory } from "./metadataProcessor/metadataPreprocessorFactory";
import { MetadataCustomProcessor } from "./metadataCustomProcessor";
import path from "path";

interface PackageItem {
    members: string;
    name: string;
}

enum MetadataType {
    ApexClass = "ApexClass"
}

const IGNORE_FILE_PATH = "./deploy/next-gen/config/";
const UAT_IGNORE_FILE = "ignore-metadata-uat.txt";
const PROD_IGNORE_FILE = "ignore-metadata-prod.txt";
const CI_IGNORE_FILE = "ignore-metadata-ci.txt";

export class MetadataService {
    private readonly packageXMLPath;
    private readonly destructiveChangesXMLPath;
    private readonly metadataSourcePath;

    constructor() {
        this.packageXMLPath = `package/package.xml`;
        this.destructiveChangesXMLPath = `destructiveChanges/destructiveChanges.xml`;
        this.metadataSourcePath = `force-app`;
    }

    public async getApexTestFiles(): Promise<string[]> {
        const packageXMLContent = fs.readFileSync(this.packageXMLPath);
        const parser = new XMLParser();
        const parserResult = parser.parse(packageXMLContent);
        if (!parserResult.Package.types) {
            return [];
        }

        let apexClassMembers: string[] = [];
        if (Array.isArray(parserResult.Package.types)) {
            const apexClassTypes = parserResult.Package.types.find((item: PackageItem) => item.name === MetadataType.ApexClass);

            if (apexClassTypes) {
                apexClassMembers = Array.isArray(apexClassTypes.members) ? apexClassTypes.members : [apexClassTypes.members];
            }
        } else if (parserResult.Package.types.name === MetadataType.ApexClass) {
            apexClassMembers = Array.isArray(parserResult.Package.types.members) ? parserResult.Package.types.members : [parserResult.Package.types.members];
        }

        const testFiles = await Promise.all(
            apexClassMembers.map(async (memberName) => {
                const isTest = await this.isApexTestFile(memberName);
                return isTest ? memberName : null;
            })
        );

        return testFiles.filter((name): name is string => name !== null);
    }

    public async prepareMetadataToDeploy(deployTo: string[]): Promise<void> {
        logger.infoBannerCyan(`Processing metadata`);
        if (!deployTo.includes("prod")) {
            await this.preProcessMetadata();
            if (!deployTo.includes("sfdcuat2")) {
                await MetadataCustomProcessor.appendIpRangeToSecuritySetting();
            }
        }
        if (deployTo.includes("crmpg")) {
            await MetadataCustomProcessor.updateBrandLogoConfigInApplication();
        }

        const ignoreFiles = await this.getIgnoreMetadataFileList(deployTo);
        await this.removeIgnoredMetadataFile(ignoreFiles);
    }

    public getMetadataChangeStatus() {
        return {
            isUpdate: this.isChange(this.packageXMLPath),
            isDelete: this.isChange(this.destructiveChangesXMLPath),
            testFiles: this.getApexTestFiles()
        };
    }

    private async isApexTestFile(memberName: string): Promise<boolean> {
        try {
            const sourceDirPaths = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
            const filePath = await findFilePath(sourceDirPaths, `${memberName}.cls`);
            try {
                const grepResult = await execPromise(`grep -i "@istest" "${filePath}"`);
                if (grepResult.output.trim()) {
                    return true;
                }
            } catch (error) {
                return false;
            }
        } catch (error) {
            return false;
        }
        return false;
    }

    private isChange(metadataXMLPath: string): boolean {
        const packageXMLContent = fs.existsSync(metadataXMLPath) ? fs.readFileSync(metadataXMLPath, "utf-8") : "<Package></Package>";

        const parser = new XMLParser();
        const parserResult = parser.parse(packageXMLContent);
        logger.infoYellow(`XML file: ${metadataXMLPath}`);
        if (parserResult.Package && parserResult.Package.types) {
            const types = Array.isArray(parserResult.Package.types) ? parserResult.Package.types : [parserResult.Package.types];

            const maxTypeNameLength = Math.max(...types.map((type: { name: any }) => type.name.length));

            types.forEach((type: { name: any; members: any }) => {
                const typeName = type.name;
                const members = type.members;
                const membersList = Array.isArray(members) ? members : [members];

                const maxLineLength = 120;
                let currentLine = `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} `;
                let currentLength = currentLine.length;

                membersList.forEach((member: string) => {
                    if (currentLength + member.length + 2 > maxLineLength) {
                        logger.infoYellow(currentLine);
                        currentLine = `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} ${member}`;
                        currentLength = currentLine.length;
                    } else {
                        if (currentLine !== `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} `) {
                            currentLine += ", ";
                            currentLength += 2;
                        }
                        currentLine += member;
                        currentLength += member.length;
                    }
                });

                if (currentLine.length > `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} `.length) {
                    logger.infoYellow(currentLine);
                }
            });
        } else {
            logger.infoYellow("No members found in the XML");
        }

        return !!parserResult.Package.types;
    }

    private async preProcessMetadata() {
        const processedDirectories = new Set();

        const processDirectory = (dirPath: any) => {
            if (processedDirectories.has(dirPath)) {
                return;
            }

            const entries = fs.readdirSync(dirPath, { withFileTypes: true });
            processedDirectories.add(dirPath);

            let hasProcessed = false;

            entries.forEach((entry) => {
                const fullPath = path.join(dirPath, entry.name);
                if (entry.isDirectory()) {
                    processDirectory(fullPath);
                } else if (!hasProcessed) {
                    const metadataType = path.basename(dirPath);
                    const metadataPreprocessor = MetadataPreprocessorFactory.create(metadataType);
                    metadataPreprocessor.process(dirPath);
                    hasProcessed = true;
                }
            });
        };

        processDirectory(this.metadataSourcePath);
    }

    private async removeIgnoredMetadataFile(unsupportedMetadataFile: string[]) {
        for (const file of unsupportedMetadataFile) {
            try {
                await execPromise(`rm -rf ${file}`);
                logger.infoGreen(`Successfully removed metadata: ${file}`);
            } catch (error) {
                logger.errorRed(`Failed to remove ${file}: ${error}`);
            }
        }
    }

    private async getIgnoreMetadataFileList(deployTo: string[]): Promise<string[]> {
        const fileName = deployTo.includes("sfdcuat2") ? UAT_IGNORE_FILE : deployTo.includes("prod") ? PROD_IGNORE_FILE : CI_IGNORE_FILE;
        const ignoreFilePath = `${IGNORE_FILE_PATH}${fileName}`;

        return await readIgnoreMetadataConfigFile(ignoreFilePath);
    }
}
