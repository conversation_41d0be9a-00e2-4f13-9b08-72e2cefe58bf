import axios from "axios";
import { parseStringPromise } from "xml2js";
import { extractInstanceUrl, isProdEnvironment, logger } from "./utils";

export interface LoginResponse {
    sessionId: string;
    instanceUrl: string;
}

class SoapApiClient {
    private username: string | null = null;
    private password: string | null = null;
    private sessionId: string | null = null;
    private instanceUrl: string | null = null;

    constructor(usernameOrInstanceUrl: string, passwordOrSessionId: string) {
        if (usernameOrInstanceUrl.startsWith("https://")) {
            this.instanceUrl = usernameOrInstanceUrl;
            this.sessionId = passwordOrSessionId;
        } else {
            this.username = usernameOrInstanceUrl;
            this.password = passwordOrSessionId;
        }
    }

    private buildLoginRequestBody(): string {
        return `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:urn="urn:partner.soap.sforce.com">
                <soapenv:Header/>
                <soapenv:Body>
                    <urn:login>
                        <urn:username>${this.username}</urn:username>
                        <urn:password>${this.password}</urn:password>
                    </urn:login>
                </soapenv:Body>
            </soapenv:Envelope>
        `;
    }

    private parseLoginResponse(result: any): LoginResponse {
        const sessionId: string = result["soapenv:Envelope"]["soapenv:Body"][0]["loginResponse"][0]["result"][0]["sessionId"][0];
        const serverUrl: string = result["soapenv:Envelope"]["soapenv:Body"][0]["loginResponse"][0]["result"][0]["serverUrl"][0];
        const instanceUrl = extractInstanceUrl(serverUrl);
        return { sessionId, instanceUrl };
    }

    public async login(): Promise<LoginResponse> {
        if (this.sessionId && this.instanceUrl) {
            return { sessionId: this.sessionId, instanceUrl: this.instanceUrl };
        }

        const requestBody = this.buildLoginRequestBody();
        const headers = {
            "Content-Type": "text/xml",
            SOAPAction: '""'
        };

        const loginUrl = isProdEnvironment(this.username!) ? "https://login.salesforce.com" : "https://test.salesforce.com";
        const serverLoginUrl = this.getServerUrl(loginUrl);

        try {
            const response = await axios.post(serverLoginUrl, requestBody, { headers });
            const result = await parseStringPromise(response.data);
            const { sessionId, instanceUrl } = this.parseLoginResponse(result);
            this.sessionId = sessionId;
            this.instanceUrl = instanceUrl;
            return { sessionId, instanceUrl };
        } catch (error) {
            this.handleError(error);
        }
    }

    private buildToolingDeleteRequestBody(recordIds: string[]): string {
        const idsXml = recordIds.map((id) => `<urn:ids>${id}</urn:ids>`).join("");
        return `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:urn="urn:tooling.soap.sforce.com">
                <soapenv:Header>
                    <urn:SessionHeader>
                        <urn:sessionId>${this.sessionId}</urn:sessionId>
                    </urn:SessionHeader>
                </soapenv:Header>
                <soapenv:Body>
                    <urn:delete>
                        ${idsXml}
                    </urn:delete>
                </soapenv:Body>
            </soapenv:Envelope>
        `;
    }

    public async deleteToolingRecords(recordIds: string[]): Promise<void> {
        if (!this.sessionId || !this.instanceUrl) {
            throw new Error("Client is not logged in");
        }

        const headers = {
            "Content-Type": "text/xml",
            SOAPAction: '""'
        };

        const batches = this.chunkArray(recordIds, 200);
        let index = 0;

        const worker = async () => {
            while (index < batches.length) {
                const currentBatch = batches[index++];
                const requestBody = this.buildToolingDeleteRequestBody(currentBatch);
                try {
                    await axios.post(this.getToolingUrl(this.instanceUrl), requestBody, { headers });
                } catch (error) {
                    this.handleError(error);
                }
            }
        };

        const workers = Array.from({ length: 50 }, () => worker());
        await Promise.all(workers);
    }

    private chunkArray(array: string[], size: number): string[][] {
        const chunks: string[][] = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    private getServerUrl(instanceUrl: string): string {
        return `${instanceUrl}/services/Soap/u/61.0`;
    }

    private getToolingUrl(instanceUrl: string | null): string {
        return `${instanceUrl}/services/Soap/T/61.0`;
    }

    private handleError(error: any): never {
        const errorMessage = error.response ? error.response.data : error.message;
        logger.errorRed(`Error in SOAP request: ${errorMessage}`);
        throw new Error(errorMessage);
    }
}

export default SoapApiClient;
