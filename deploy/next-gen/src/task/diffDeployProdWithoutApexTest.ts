import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { ProdOrg } from "../salesforceEnvironment/prodOrg";
import FakeCoverageService from "../fakeCoverageService";
import { DiffDeployment } from "./diffDeployment";
import { testClassToRun } from "../../config/specified-tests-for-release-without-tests.json";

export class DiffDeployProdWithoutApexTest extends DiffDeployment {
    private fakeCoverageService: FakeCoverageService;

    constructor(sandboxes: SalesforceEnvironment[]) {
        if (sandboxes.length !== 1 || !(sandboxes[0] instanceof ProdOrg)) {
            throw new Error("Only one ProdOrg is supported for this task");
        }
        super(sandboxes, true);
        this.fakeCoverageService = new FakeCoverageService("force-app");
    }

    override async prepareMetadata() {
        await super.prepareMetadata();
        await this.fakeCoverageService.fakeCoverageForQuickDeployOnProd("package/package.xml");
    }

    protected override async deployAsync() {
        if (this.environments[0] instanceof ProdOrg) {
            await this.environments[0].deployWithRunSpecificTests("package/package.xml", "deploy/delete/destructiveChangesPre.xml", "deploy/delete/destructiveChangesPost.xml", testClassToRun);
        }
    }
}
