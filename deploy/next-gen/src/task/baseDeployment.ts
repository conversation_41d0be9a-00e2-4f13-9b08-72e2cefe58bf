import { logger, sleep } from "../utils";
import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import pLimit from "p-limit";

export abstract class BaseDeployment {
    protected environments: SalesforceEnvironment[];

    constructor(
        environments: SalesforceEnvironment[],
        private enableNotification: boolean = false
    ) {
        this.environments = environments;
    }

    public async run(asyncMode: boolean = false) {
        await this.prepareMetadata();
        if (!(await this.isMetadataChanged())) {
            logger.infoGreen(`No metadata changed!`);
            return false;
        }
        await this.deployAsync();
        if (!asyncMode) {
            await this.monitorDeployment();
        }
        return true;
    }
    protected abstract prepareMetadata(): Promise<void>;
    public abstract isMetadataChanged(): Promise<boolean>;
    protected abstract notifyDeploymentStatus(isSuccess: boolean, env: SalesforceEnvironment): Promise<void>;

    protected async deployAsync() {
        const limit = pLimit(5);
        const deployments = this.environments.map((env) => limit(() => env.deploy("package/package.xml", "deploy/delete/destructiveChangesPre.xml", "deploy/delete/destructiveChangesPost.xml")));

        await Promise.all(deployments);
    }

    public async monitorDeployment() {
        const limit = pLimit(5);

        while (true) {
            const pendingEnvironments = this.environments.filter((env) => env.getDeployStatus() !== "Deployed");

            if (pendingEnvironments.length === 0) {
                break;
            }

            const statusCheckTasks = pendingEnvironments.map((env) =>
                limit(async () => {
                    const status = await env.checkDeployStatus();

                    if (status === "Deployed") {
                        if (this.enableNotification) {
                            await this.notifyDeploymentStatus(true, env);
                        }
                        this.logSuccess(env);
                    } else if (status === "Failed") {
                        if (this.enableNotification) {
                            await this.notifyDeploymentStatus(false, env);
                        }
                        this.logFailure(env);
                    }
                })
            );

            await Promise.all(statusCheckTasks);
            await sleep(15000);
        }
    }

    private logSuccess(env: SalesforceEnvironment) {
        const successCount = this.environments.filter((env) => env.getDeployStatus() === "Deployed").length;
        logger.infoGreen(`[Deployment Status]    [${successCount}/${this.environments.length}]    Successfully deployed to ${env.getSandboxName()}`);
    }

    private logFailure(env: SalesforceEnvironment) {
        logger.errorRed(`Deployment failed in environment ${env.getSandboxName()}, failure message: ${env.getDeployFailedReason()}`);
        throw new Error(`Deployment failed in environment ${env.getSandboxName()}, failure message: ${env.getDeployFailedReason()}`);
    }
}
