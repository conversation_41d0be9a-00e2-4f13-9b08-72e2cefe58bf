import { execPromise, getPackagePathsFromSFDXProjectJson, sendWechatNotification } from "../utils";
import { MetadataService } from "../metadataService";
import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { BaseDeployment } from "./baseDeployment";

export class FullDeployment extends BaseDeployment {
    private metadataService: MetadataService;

    constructor(environments: SalesforceEnvironment[], enableNotification: boolean = false) {
        super(environments, enableNotification);
        this.metadataService = new MetadataService();
    }

    public override async isMetadataChanged(): Promise<boolean> {
        return true;
    }

    protected override async prepareMetadata(): Promise<void> {
        const sandboxNames = this.environments.map((env) => env.getSandboxName());
        await this.metadataService.prepareMetadataToDeploy(sandboxNames);
        const sourceDirPaths = await getPackagePathsFromSFDXProjectJson("./sfdx-project.json");
        await execPromise(`sf project generate manifest --source-dir ${sourceDirPaths.join(",")} --output-dir package`, true);
    }

    protected override async notifyDeploymentStatus(isSuccess: boolean, env: SalesforceEnvironment): Promise<void> {
        await sendWechatNotification(isSuccess, env.getSandboxName(), null, true, Date.now() - env.getDeployStartTime());
    }
}
