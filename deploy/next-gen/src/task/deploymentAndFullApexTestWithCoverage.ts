import { getPackagePathsFromSFDXProjectJson, logger } from "../utils";
import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import fs from "fs";
import path from "path";
import { TestCase } from "../testCase";
import { BaseDeployment } from "./baseDeployment";
import { BaseDeploymentAndTest } from "./baseDeploymentAndTest";
import { DeploymentAndFullApexTest } from "./deploymentAndFullApexTest";
import pLimit from "p-limit";

export class DeploymentAndFullApexTestWithCoverage extends DeploymentAndFullApexTest {
    constructor(sandboxes: SalesforceEnvironment[], baseDeployment: BaseDeployment, testRetryTimes: number) {
        super(sandboxes, baseDeployment, 1, testRetryTimes);
    }

    public override async run() {
        await this.clearAllCoverageData();
        await this.deployment.run(true);
        this.testCases = await this.getTestsToRun();
        await this.deployAndTest();
        await this.collectCoverageData();
    }

    private async clearAllCoverageData() {
        logger.infoBannerCyan(`Cleaning Apex code coverage data`);
        const limit = pLimit(5);

        const tasks = this.sandboxes.map((sandbox) => limit(() => sandbox.cleanApexCodeCoverageData()));

        await Promise.all(tasks);
    }

    private async collectCoverageData(): Promise<void> {
        const limit = pLimit(5);
        const coverageSummary: { [testClassName: string]: string[] } = {};
        const aggregateCoverageSummary: { [className: string]: { coverageRate: string; coveredLines: string; uncoveredLines: string } } = {};

        // 使用 p-limit 限制并发获取所有 sandbox 的数据
        const coverageResults = await Promise.allSettled(
            this.sandboxes.map((sandbox) =>
                limit(async () => {
                    try {
                        const result = await sandbox.getCoverageData();
                        logger.infoGreen(`Successfully got coverage data for ${sandbox.getSandboxName()}`);
                        return { sandbox, result };
                    } catch (error) {
                        logger.errorRed(`Failed to get coverage data for ${sandbox.getSandboxName()}, error: ${JSON.stringify(error)}`);
                        throw error;
                    }
                })
            )
        );

        const aggregateResults = await Promise.allSettled(
            this.sandboxes.map((sandbox) =>
                limit(async () => {
                    try {
                        const result = await sandbox.getAggregateCoverageData();
                        logger.infoGreen(`Successfully got aggregate coverage data for ${sandbox.getSandboxName()}`);
                        return { sandbox, result };
                    } catch (error) {
                        logger.errorRed(`Failed to aggregate coverage data for ${sandbox.getSandboxName()}, error: ${JSON.stringify(error)}`);
                        throw error;
                    }
                })
            )
        );

        // 处理 coverageResults 数据
        coverageResults.forEach((result) => {
            if (result.status === "fulfilled") {
                const { result: coverageData } = result.value;
                Object.keys(coverageData).forEach((testClassName) => {
                    coverageSummary[testClassName] = Array.from(new Set([...(coverageSummary[testClassName] || []), ...coverageData[testClassName]]));
                });
            }
        });

        // 处理 aggregateResults 数据
        const coveredLinesMap = new Map();
        const uncoveredLinesMap = new Map();

        aggregateResults.forEach((result) => {
            if (result.status === "fulfilled") {
                const { result: aggregateData } = result.value;

                Object.keys(aggregateData).forEach((className) => {
                    const coverageData = aggregateData[className];

                    // 获取之前的覆盖和未覆盖行
                    const previousCoveredLines = coveredLinesMap.get(className) || new Set();
                    const previousUncoveredLines = uncoveredLinesMap.get(className) || new Set();

                    // 更新覆盖行集合
                    coverageData.coveredLines.forEach((line) => previousCoveredLines.add(line));

                    // 更新未覆盖行集合，排除已覆盖的行
                    coverageData.uncoveredLines.forEach((line) => {
                        previousUncoveredLines.add(line);
                    });

                    // 更新映射表
                    coveredLinesMap.set(className, previousCoveredLines);
                    uncoveredLinesMap.set(className, new Set([...previousUncoveredLines].filter((x) => !previousCoveredLines.has(x))));
                });
            }
        });

        // 遍历完成后，排序和计算覆盖率
        coveredLinesMap.forEach((coveredLines, className) => {
            const uncoveredLines = uncoveredLinesMap.get(className) || new Set();

            // 排序覆盖行和未覆盖行
            const coveredLinesSorted = [...coveredLines].sort((a, b) => a - b);
            const uncoveredLinesSorted = [...uncoveredLines].sort((a, b) => a - b);

            // 计算覆盖率
            const totalLines = coveredLinesSorted.length + uncoveredLinesSorted.length;
            const coverageRate = (coveredLinesSorted.length / totalLines).toFixed(2);

            // 更新 aggregateCoverageSummary
            aggregateCoverageSummary[className] = {
                coverageRate,
                coveredLines: coveredLinesSorted.join(","),
                uncoveredLines: uncoveredLinesSorted.join(",")
            };
        });

        // 对 coverageSummary 和 aggregateCoverageSummary 按 className 排序
        const sortedCoverageSummary = Object.keys(coverageSummary)
            .sort()
            .reduce(
                (obj, key) => {
                    obj[key] = coverageSummary[key];
                    return obj;
                },
                {} as { [testClassName: string]: string[] }
            );

        const sortedAggregateCoverageSummary = Object.keys(aggregateCoverageSummary)
            .sort()
            .reduce(
                (obj, key) => {
                    obj[key] = aggregateCoverageSummary[key];
                    return obj;
                },
                {} as { [className: string]: { coverageRate: string; coveredLines: string; uncoveredLines: string } }
            );

        // 保存排序后的覆盖率数据
        this.saveCoverageData(sortedCoverageSummary, "coverage-data.json");
        this.saveCoverageData(sortedAggregateCoverageSummary, "coverage-data-aggregate.json");
    }

    private saveCoverageData(data: object, fileName: string): void {
        const dirPath = path.join(__dirname, "../..", "coverage-data");
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
        const jsonData = JSON.stringify(data, null, 2);
        fs.writeFileSync(`deploy/next-gen/coverage-data/${fileName}`, jsonData, "utf-8");
        logger.infoGreen(`Successfully saved ${fileName}`);
    }

    protected executeTest(sandbox: SalesforceEnvironment, testNames: string[]): void {
        if (testNames.length > 1) {
            throw new Error("Only one test class can be run when in run apex test with coverage mode");
        }
        sandbox.runApexTestWithCoverage(testNames[0]); // Uses the Apex test method
    }
}
