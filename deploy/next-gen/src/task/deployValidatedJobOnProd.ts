import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { ProdOrg } from "../salesforceEnvironment/prodOrg";
import { validatedJobId, deployMode } from "../../config/deploy-validated-job-config.json";
import { BaseDeployment } from "./baseDeployment";
import { sendWechatNotification } from "../utils";

export class DeployValidatedJobOnProd extends BaseDeployment {
    constructor(sandboxes: SalesforceEnvironment[]) {
        if (sandboxes.length !== 1 || !(sandboxes[0] instanceof ProdOrg)) {
            throw new Error("Only one ProdOrg is supported for this task");
        }
        super(sandboxes, true);
    }

    protected override async deployAsync() {
        if (this.environments[0] instanceof ProdOrg) {
            await this.environments[0].deployValidatedJob(validatedJobId);
        }
    }

    override async isMetadataChanged(): Promise<boolean> {
        return true;
    }

    protected override async notifyDeploymentStatus(isSuccess: boolean, env: SalesforceEnvironment): Promise<void> {
        await sendWechatNotification(isSuccess, env.getSandboxName(), null, deployMode == "FULL", Date.now() - env.getDeployStartTime());
    }

    protected prepareMetadata(): Promise<void> {
        return Promise.resolve(undefined);
    }
}
