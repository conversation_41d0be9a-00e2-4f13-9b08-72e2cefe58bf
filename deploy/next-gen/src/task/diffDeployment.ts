import preConfigBaseCommitRevision from "../../config/base-commit.json";
import { execPromise, logger, sendWechatNotification } from "../utils";
import { CircleCIPipeline } from "../pipelineService/circleCIPipeline";
import { DeltaMetadataGenerator } from "../deltaMetadataGenerator";
import { MetadataService } from "../metadataService";
import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { Builder, parseStringPromise } from "xml2js";
import fs from "fs";
import { BaseDeployment } from "./baseDeployment";
import lastSuccessPointConfig from "../../config/last-success-point-config.json";

const MAX_COMMIT_NUM_TO_COMPARE = 1000;

export class DiffDeployment extends BaseDeployment {
    private circleCIPipeline;
    private deltaMetadataGenerator: DeltaMetadataGenerator;
    private baseCommit: string | null = "";
    private metadataService: MetadataService;

    constructor(environments: SalesforceEnvironment[], enableNotification = false) {
        super(environments, enableNotification);
        this.circleCIPipeline = new CircleCIPipeline();
        this.deltaMetadataGenerator = new DeltaMetadataGenerator("force-app", ".");
        this.metadataService = new MetadataService();
    }

    protected override async prepareMetadata(): Promise<void> {
        await this.getBaseCommit();
        await this.generateDeltaMetadata();
        await this.mergeDestructiveFiles("deploy/delete/destructiveChangesPost.xml", "package/destructiveChanges/destructiveChanges.xml", "deploy/delete/destructiveChangesPost.xml");
    }

    public override async isMetadataChanged(): Promise<boolean> {
        const { isUpdate, isDelete } = this.metadataService.getMetadataChangeStatus();
        return isUpdate || isDelete;
    }

    protected async notifyDeploymentStatus(isSuccess: boolean, env: SalesforceEnvironment): Promise<void> {
        await sendWechatNotification(isSuccess, env.getSandboxName(), this.baseCommit, false, Date.now() - env.getDeployStartTime());
    }

    private async generateDeltaMetadata() {
        const sandboxNames = this.environments.map((env) => env.getSandboxName());
        await this.metadataService.prepareMetadataToDeploy(sandboxNames);
        let ignoreMetadataFile = "";
        if (sandboxNames.includes("sfdcuat2")) {
            ignoreMetadataFile = "./deploy/next-gen/config/ignore-metadata-uat.txt";
        } else if (sandboxNames.includes("prod")) {
            ignoreMetadataFile = "./deploy/next-gen/config/ignore-metadata-prod.txt";
        } else {
            ignoreMetadataFile = "./deploy/next-gen/config/ignore-metadata-ci.txt";
        }
        await this.deltaMetadataGenerator.generate(this.baseCommit, ignoreMetadataFile);
    }

    public async getBaseCommit(): Promise<void> {
        if (this.hasPreConfigBaseCommit()) {
            this.baseCommit = this.getPreConfigBaseCommit();
            return;
        }

        this.baseCommit = await this.getLastSuccessfulDeployedCommit();
        logger.infoGreen(`Base commit revision: ${this.baseCommit}`);
    }

    private hasPreConfigBaseCommit() {
        return !!preConfigBaseCommitRevision.revision;
    }

    private getPreConfigBaseCommit() {
        return preConfigBaseCommitRevision.revision;
    }

    private async getLastSuccessfulDeployedCommit() {
        logger.infoGreen("Finding last successful build commit");

        const workflowName = await this.circleCIPipeline.getWorkflowName();
        const config = (lastSuccessPointConfig as lastSuccessPointConfig)[workflowName];

        if (!config) {
            logger.error(`Unknown workflow name: ${workflowName}`);
            return null;
        }

        // 将 checkBranchFunction 字符串映射到实际方法上
        const deploymentWorkflow = config.deploymentWorkflow;
        const deploymentJob = config.deploymentJob;
        const checkBranchFunction = (this[config.checkBranchFunction as keyof DiffDeployment] as Function).bind(this);

        const commitsFromGit = await this.getRecentCommitsFromGit(MAX_COMMIT_NUM_TO_COMPARE);
        let successBuilds = await this.circleCIPipeline.getRecentCommitsFromPipeline(deploymentWorkflow, deploymentJob);
        const successBuildCommits = successBuilds.items;

        for (const successBuildCommit of successBuildCommits) {
            if (commitsFromGit.includes(successBuildCommit) && (await checkBranchFunction(successBuildCommit))) {
                return successBuildCommit;
            }
        }

        while (successBuilds.hasMore) {
            successBuilds = await this.circleCIPipeline.getRecentCommitsFromPipeline(deploymentWorkflow, deploymentJob);
            const successBuildCommits = successBuilds.items;
            for (const successBuildCommit of successBuildCommits) {
                if (commitsFromGit.includes(successBuildCommit) && (await checkBranchFunction(successBuildCommit))) {
                    return successBuildCommit;
                }
            }
        }

        return null;
    }

    private async getRecentCommitsFromGit(maxCount: number) {
        logger.infoGreen("Get recent commit history from git");
        const { output: gitLog } = await execPromise(`git log --no-merges --max-count ${maxCount}`, true);
        const logLines = gitLog?.split("\n");
        const commits: string[] = [];
        logLines?.forEach((line) => {
            if (line.startsWith("commit")) {
                const commitRevision = line.split(" ")[1];
                commits.push(commitRevision);
            }
        });
        return commits;
    }

    public async isCommitOnMasterBranch(commit: string) {
        const { output } = await execPromise(`git branch -a --contains ${commit}`, true);

        let branches = output?.split("\n");
        branches = branches?.filter((branch) => {
            branch = branch.trim();
            return branch === "remotes/origin/master";
        });

        return branches?.length === 1;
    }

    public async isCommitOnReleaseBranch(commit: string) {
        const { output } = await execPromise(`git branch -a --contains ${commit}`, true);

        let branches = output?.split("\n");
        branches = branches?.filter((branch) => {
            branch = branch.trim();
            return branch === "remotes/origin/release";
        });

        return branches?.length === 1;
    }

    private async mergeDestructiveFiles(preFile: string, postFile: string, outputFile: string) {
        try {
            const preFileContent = fs.existsSync(preFile) ? fs.readFileSync(preFile, "utf-8") : "<Package></Package>";
            const preJson = await parseStringPromise(preFileContent);

            const postFileContent = fs.existsSync(postFile) ? fs.readFileSync(postFile, "utf-8") : "<Package></Package>";
            const postJson = await parseStringPromise(postFileContent);

            const preTypes = preJson.Package.types || [];
            const postTypes = postJson.Package.types || [];

            const mergedTypes = [...preTypes];

            for (const postType of postTypes) {
                const existingType = mergedTypes.find((type: any) => type.name[0] === postType.name[0]);
                if (existingType) {
                    existingType.members = [...new Set([...existingType.members, ...postType.members])];
                } else {
                    mergedTypes.push(postType);
                }
            }

            if (mergedTypes.length > 0) {
                const maxTypeNameLength = Math.max(...mergedTypes.map((type: any) => type.name[0].length));

                mergedTypes.forEach((type: any) => {
                    const typeName = type.name[0];
                    const members = type.members;
                    const membersList = Array.isArray(members) ? members : [members];

                    const maxLineLength = 120;
                    let currentLine = `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} `;
                    let currentLength = currentLine.length;

                    membersList.forEach((member: string) => {
                        if (currentLength + member.length + 2 > maxLineLength) {
                            logger.infoYellow(currentLine);
                            currentLine = `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} ${member}`;
                            currentLength = currentLine.length;
                        } else {
                            if (currentLine !== `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} `) {
                                currentLine += ", ";
                                currentLength += 2;
                            }
                            currentLine += member;
                            currentLength += member.length;
                        }
                    });

                    if (currentLine.length > `${typeName}:${" ".repeat(maxTypeNameLength - typeName.length)} `.length) {
                        logger.infoYellow(currentLine);
                    }
                });
            } else {
                logger.infoYellow("No members found in the XML");
            }

            const builder = new Builder();
            const newPackageJson = {
                Package: {
                    ...preJson.Package,
                    types: mergedTypes
                }
            };

            const newXml = builder.buildObject(newPackageJson);

            fs.writeFileSync(outputFile, newXml, "utf-8");
            logger.infoGreen(`Files ${outputFile} merged successfully!`);
        } catch (error) {
            logger.errorRed(`Error merging files ${outputFile}: ${error}`);
        }
    }
}

interface lastSuccessPointConfig {
    [key: string]: {
        deploymentWorkflow: string;
        deploymentJob: string[];
        checkBranchFunction: string;
    };
}
