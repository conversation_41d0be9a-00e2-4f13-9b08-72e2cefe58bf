import { createDirectory, execPromise, getGitBranchName, getPackagePathsFromSFDXProjectJson, logger } from "../utils";
import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import fs from "fs";
import path from "path";
import { TestCase } from "../testCase";
import { BaseDeployment } from "./baseDeployment";
import { BaseDeploymentAndTest } from "./baseDeploymentAndTest";

export class DeploymentAndUITest extends BaseDeploymentAndTest {
    constructor(sandboxes: SalesforceEnvironment[], baseDeployment: BaseDeployment, testRetryTimes: number) {
        const testParallelLimit = 1;
        super(sandboxes, baseDeployment, testParallelLimit, testRetryTimes);
    }

    public override async run() {
        await this.deployment.run(true);
        this.testCases = await this.getTestsToRun();
        await this.prepareUITestDependencies();
        await this.deployAndTest();
    }

    private async prepareUITestDependencies(): Promise<void> {
        logger.infoBannerCyan("Prepare UI test dependencies");
        await execPromise("export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true && apt update && apt install -y ffmpeg && cd ui-tests && npm install && rm -rf video-record/", true);
        await createDirectory("./ui-tests/logs");
        await createDirectory("./ui-tests/video-record");
    }

    protected executeTest(sandbox: SalesforceEnvironment, testNames: string[]): void {
        if (testNames.length > 1) {
            throw new Error("Only one test class can be run in UI test");
        }
        sandbox.runUITest(testNames[0]); // Uses the Apex test method
    }

    protected async getTestsToRun(): Promise<TestCase[]> {
        const gitBranchName = await getGitBranchName();
        let featureFileDirs: string[];
        switch (true) {
            case gitBranchName.startsWith("sf-"):
                featureFileDirs = ["./ui-tests/features/sf-features/required/"];
                break;
            case gitBranchName.startsWith("crm-"):
                featureFileDirs = ["./ui-tests/features/crm-features/required/"];
                break;
            case gitBranchName.startsWith("psa-"):
                featureFileDirs = ["./ui-tests/features/psa-features/required/"];
                break;
            case gitBranchName.startsWith("master"):
                featureFileDirs = ["./ui-tests/features/psa-features/required/", "./ui-tests/features/crm-features/required/", "./ui-tests/features/sf-features/required/"];
                break;
            case gitBranchName.startsWith("release"):
                featureFileDirs = ["./ui-tests/features/psa-features/required/", "./ui-tests/features/crm-features/required/", "./ui-tests/features/sf-features/required/"];
                break;
            default:
                featureFileDirs = [];
                break;
        }
        return this.getFeatureFilesByDirs(featureFileDirs).map((featureFileName) => new TestCase(featureFileName, this.testRetryTimes));
    }

    private getFeatureFilesByDirs(dirs: string[]): string[] {
        let featureFiles: string[] = [];

        for (let dir of dirs) {
            const files: string[] = fs.readdirSync(dir);

            for (let file of files) {
                const filePath = path.join(dir, file);

                if (fs.lstatSync(filePath).isDirectory()) {
                    featureFiles = featureFiles.concat(this.getFeatureFilesByDirs([filePath]));
                } else if (path.extname(file) === ".feature") {
                    featureFiles.push(filePath.replace("ui-tests/", "./"));
                }
            }
        }

        return featureFiles;
    }
}
