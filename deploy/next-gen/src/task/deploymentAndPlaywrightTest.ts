import { createDirectory, execPromise, getGitBranchName, getPackagePathsFromSFDXProjectJson, logger, sleep } from "../utils";
import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import fs from "fs";
import path from "path";
import { TestCase } from "../testCase";
import { BaseDeployment } from "./baseDeployment";
import { BaseDeploymentAndTest } from "./baseDeploymentAndTest";

export class DeploymentAndPlaywrightTest extends BaseDeploymentAndTest {
    constructor(sandboxes: SalesforceEnvironment[], baseDeployment: BaseDeployment, testRetryTimes: number) {
        const testParallelLimit = 1;
        super(sandboxes, baseDeployment, testParallelLimit, testRetryTimes);
    }

    public override async run() {
        await this.deployment.run(true);
        this.testCases = await this.getTestsToRun();
        await this.prepareUITestDependencies();
        await this.deployAndTest();
    }

    private async prepareUITestDependencies(): Promise<void> {
        logger.infoGreen("Installing ffmpeg");
        await execPromise("cd playwright-tests && npx playwright install ffmpeg", true);
    }

    protected async executeTest(sandbox: SalesforceEnvironment, testNames: string[]): Promise<void> {
        if (testNames.length > 1) {
            throw new Error("Only one test class can be run in UI test");
        }
        await sandbox.runPlaywrightTest(testNames[0]);
    }

    protected async getTestsToRun(): Promise<TestCase[]> {
        const baseDir = path.join(__dirname, "..", "..", "..", "..", "playwright-tests", "ui-tests");
        const teamDirs = ["sales", "crm-tests"];

        let testCases: TestCase[] = [];

        for (const team of teamDirs) {
            const teamDirPath = path.join(baseDir, team);
            if (fs.existsSync(teamDirPath)) {
                const files = fs.readdirSync(teamDirPath);

                for (const file of files) {
                    const filePath = path.join(teamDirPath, file);
                    if (fs.lstatSync(filePath).isFile() && filePath.endsWith("spec.js")) {
                        testCases.push(new TestCase(filePath, 0));
                    }
                }
            }
        }

        return testCases;
    }
}
