import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { TestCase } from "../testCase";
import { BaseDeployment } from "./baseDeployment";
import { BaseDeploymentAndTest } from "./baseDeploymentAndTest";
import { ApexTestService } from "../apexTestService";

export class DeploymentAndAffectedApexTest extends BaseDeploymentAndTest {
    private apexTestService: ApexTestService;
    constructor(sandboxes: SalesforceEnvironment[], baseDeployment: BaseDeployment, testParallelLimit: number = 1, testRetryTimes: number = 1) {
        super(sandboxes, baseDeployment, testParallelLimit, testRetryTimes);
        this.apexTestService = new ApexTestService();
    }

    protected executeTest(sandbox: SalesforceEnvironment, testNames: string[]): void {
        sandbox.runApexTest(testNames); // Uses the Apex test method
    }

    protected async getTestsToRun(): Promise<TestCase[]> {
        const testClassNames = await this.apexTestService.getAffectedApexTestClasses();
        return testClassNames.map((testClassName) => new TestCase(testClassName, this.testRetryTimes));
    }
}
