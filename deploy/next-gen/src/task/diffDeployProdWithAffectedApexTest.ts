import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { ProdOrg } from "../salesforceEnvironment/prodOrg";
import FakeCoverageService from "../fakeCoverageService";
import { DiffDeployment } from "./diffDeployment";
import { ApexTestService } from "../apexTestService";

export class DiffDeployProdWithAffectedApexTest extends DiffDeployment {
    private testsToRun: string[] = [];
    private fakeCoverageService: FakeCoverageService;
    private apexTestService: ApexTestService;

    constructor(sandboxes: SalesforceEnvironment[]) {
        if (sandboxes.length !== 1 || !(sandboxes[0] instanceof ProdOrg)) {
            throw new Error("Only one ProdOrg is supported for this task");
        }
        super(sandboxes, true);
        this.fakeCoverageService = new FakeCoverageService("force-app");
        this.apexTestService = new ApexTestService();
    }

    override async prepareMetadata() {
        await super.prepareMetadata();
        await this.fakeCoverageService.fakeCoverageForDeployOnProd("package/package.xml");
        this.testsToRun = await this.apexTestService.getAffectedApexTestClasses();
    }

    protected override async deployAsync() {
        if (this.environments[0] instanceof ProdOrg) {
            await this.environments[0].deployWithRunSpecificTests("package/package.xml", "deploy/delete/destructiveChangesPre.xml", "deploy/delete/destructiveChangesPost.xml", this.testsToRun);
        }
    }
}
