import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { DeploymentConfig, DeployMode, TestMode } from "../deploymentConfigParser";
import { DiffDeployment } from "./diffDeployment";
import { FullDeployment } from "./fullDeployment";
import { DeploymentAndFullApexTest } from "./deploymentAndFullApexTest";
import { BaseDeployment } from "./baseDeployment";
import { DeploymentAndFullApexTestWithCoverage } from "./deploymentAndFullApexTestWithCoverage";
import { DeploymentAndUITest } from "./deploymentAndUITest";
import { DeploymentAndAffectedApexTest } from "./deploymentAndAffectedApexTest";
import { ProdOrg } from "../salesforceEnvironment/prodOrg";
import { FullValidateProdWithAllApexTest } from "./fullValidateProdWithAllApexTest";
import { DiffDeployProdWithAffectedApexTest } from "./diffDeployProdWithAffectedApexTest";
import { DiffDeployProdWithoutApexTest } from "./diffDeployProdWithoutApexTest";
import { BaseDeploymentAndTest } from "./baseDeploymentAndTest";
import { DeployValidatedJobOnProd } from "./deployValidatedJobOnProd";
import { logger } from "../utils";
import { DeploymentAndPlaywrightTest } from "./deploymentAndPlaywrightTest";

export class TaskFactory {
    private static checkEnvs(sandboxes: SalesforceEnvironment[]) {
        if (this.isProdDeploy(sandboxes) && sandboxes.length !== 1) {
            throw new Error("Only one ProdOrg is supported for this task");
        }
    }
    private static isProdDeploy(sfEnvironments: SalesforceEnvironment[]) {
        return sfEnvironments.some((env) => env instanceof ProdOrg);
    }

    private static createProdTask(config: DeploymentConfig, sfEnvironments: SalesforceEnvironment[]) {
        const prodTaskMap: Record<string, BaseDeployment> = {
            [`${DeployMode.FULL_VALIDATE}_${TestMode.FULL_APEX_TEST}`]: new FullValidateProdWithAllApexTest(sfEnvironments),
            [`${DeployMode.DIFF}_${TestMode.AFFECTED_APEX_TEST}`]: new DiffDeployProdWithAffectedApexTest(sfEnvironments),
            [`${DeployMode.DIFF}_${TestMode.WITHOUT_APEX_TEST}`]: new DiffDeployProdWithoutApexTest(sfEnvironments),
            [`${DeployMode.VALIDATED_DEPLOY}`]: new DeployValidatedJobOnProd(sfEnvironments)
        };
        const key = `${config.deployMode}${config.testMode ? `_${config.testMode}` : ""}`;
        return prodTaskMap[key] || null;
    }

    private static createBaseTask(config: DeploymentConfig, sfEnvironments: SalesforceEnvironment[]) {
        switch (config.deployMode) {
            case DeployMode.DIFF:
                return new DiffDeployment(sfEnvironments);
            case DeployMode.FULL:
                return new FullDeployment(sfEnvironments);
            default:
                return null;
        }
    }

    private static wrapTestModeTask(config: DeploymentConfig, baseTask: BaseDeployment, sfEnvironments: SalesforceEnvironment[]) {
        const testTaskMap: Record<TestMode, BaseDeploymentAndTest | null> = {
            [TestMode.FULL_APEX_TEST]: new DeploymentAndFullApexTest(sfEnvironments, baseTask, 5, 5),
            [TestMode.FULL_APEX_TEST_WITH_COVERAGE]: new DeploymentAndFullApexTestWithCoverage(sfEnvironments, baseTask, 5),
            [TestMode.UI_TEST]: new DeploymentAndUITest(sfEnvironments, baseTask, 0),
            [TestMode.PLAYWRIGHT_TEST]: new DeploymentAndPlaywrightTest(sfEnvironments, baseTask, 0),
            [TestMode.AFFECTED_APEX_TEST]: new DeploymentAndAffectedApexTest(sfEnvironments, baseTask, 5, 5),
            [TestMode.WITHOUT_APEX_TEST]: null // Not implemented now, for check only
        };
        if (config.testMode) {
            return testTaskMap[config.testMode] || baseTask;
        }
        return baseTask;
    }

    static createTask(sfEnvironments: SalesforceEnvironment[], config: DeploymentConfig) {
        this.checkEnvs(sfEnvironments);
        if (this.isProdDeploy(sfEnvironments)) {
            const prodTask = this.createProdTask(config, sfEnvironments);
            if (prodTask) {
                logger.infoBannerCyan(`Task created: ${prodTask?.constructor.name}`);
                return prodTask;
            } else {
                throw new Error("Invalid deployment mode or test mode for Prod deployment");
            }
        }

        const baseTask = this.createBaseTask(config, sfEnvironments);

        const task = baseTask ? this.wrapTestModeTask(config, baseTask, sfEnvironments) : null;
        logger.infoBannerCyan(`Task created: ${task?.constructor.name}`);
        return task;
    }
}
