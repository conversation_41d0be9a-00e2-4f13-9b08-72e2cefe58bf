import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { TestCase } from "../testCase";
import { BaseDeployment } from "./baseDeployment";
import { BaseDeploymentAndTest } from "./baseDeploymentAndTest";
import { ApexTestService } from "../apexTestService";

export class DeploymentAndFullApexTest extends BaseDeploymentAndTest {
    private apexTestService: ApexTestService;

    constructor(sandboxes: SalesforceEnvironment[], baseDeployment: BaseDeployment, testParallelLimit: number, testRetryTimes: number) {
        super(sandboxes, baseDeployment, testParallelLimit, testRetryTimes);
        this.apexTestService = new ApexTestService();
    }

    protected executeTest(sandbox: SalesforceEnvironment, testNames: string[]): void {
        sandbox.runApexTest(testNames); // Uses the Apex test method
    }

    protected async getTestsToRun(): Promise<TestCase[]> {
        const testClasses = await this.apexTestService.getAllApexTestClasses();
        return testClasses.map((testClassName: string) => new TestCase(testClassName, this.testRetryTimes));
    }
}
