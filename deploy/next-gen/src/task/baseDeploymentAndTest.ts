import { logger, sleep } from "../utils";
import { BaseTestResult, SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import pLimit from "p-limit";
import { BaseDeployment } from "./baseDeployment";
import { TestCase } from "../testCase";

export abstract class BaseDeploymentAndTest {
    protected sandboxes: SalesforceEnvironment[];
    protected deployment: BaseDeployment;
    protected testCases: TestCase[] = [];
    private testResultMap: Map<string, BaseTestResult | undefined> = new Map();

    constructor(
        sandboxes: SalesforceEnvironment[],
        deployment: BaseDeployment,
        private testParallelLimit: number = 1,
        protected testRetryTimes: number = 1
    ) {
        this.sandboxes = sandboxes;
        this.deployment = deployment;
        this.testParallelLimit = testParallelLimit;
        this.testRetryTimes = testRetryTimes;
    }

    public async run() {
        await this.deployment.run(true);
        if (!(await this.deployment.isMetadataChanged())) {
            logger.infoGreen(`No metadata changed!`);
            return;
        }
        this.testCases = await this.getTestsToRun();
        await this.deployAndTest();
    }

    protected abstract getTestsToRun(): Promise<TestCase[]>;

    protected abstract executeTest(sandbox: SalesforceEnvironment, testNames: string[]): void;

    allTestFinished() {
        return this.testCases.every((test) => test.status === "finished");
    }

    allDeploymentCompleted() {
        return this.sandboxes.every((sandbox) => sandbox.getDeployStatus() === "Deployed");
    }

    async checkDeployStatus() {
        const limit = pLimit(5);

        const statusCheckTasks = this.sandboxes
            .filter((sandbox) => sandbox.getDeployStatus() !== "Deployed")
            .map((sandbox) =>
                limit(async () => {
                    const status = await sandbox.checkDeployStatus();

                    if (status === "Deployed") {
                        this.logDeploymentSuccess(sandbox);
                    } else if (status === "Failed") {
                        this.logDeploymentFailure(sandbox);
                    }
                })
            );

        await Promise.all(statusCheckTasks);
    }

    checkTestStatus() {
        this.getTestResults();
        this.updateTestCasesStatus();
    }

    async assignTests() {
        let availableTestCases = this.testCases.filter((test) => test.status === "notStart").sort(() => Math.random() - 0.5);
        const totalTestCases = this.testCases.length;

        if (availableTestCases.length < totalTestCases / 4) {
            this.testParallelLimit = 1;
        }

        const availableSandboxes = this.sandboxes.filter((sandbox) => sandbox.getDeployStatus() === "Deployed" && !sandbox.getIsRunningTest());
        while (availableTestCases.length > 0 && availableSandboxes.length > 0) {
            const sandbox = availableSandboxes.shift();
            const testsToRun = availableTestCases.slice(0, this.testParallelLimit);
            testsToRun.forEach((test) => {
                test.status = "running";
            });

            const testNames = testsToRun.map((test) => test.name);
            if (sandbox) {
                this.executeTest(sandbox, testNames);

                const testCasesLength = this.testCases.length;
                const finishedTestCasesLength = this.testCases.filter((test) => test.status === "finished").length;

                logger.infoBlue(`[Test Assigned]    [${finishedTestCasesLength}/${testCasesLength}]         Test ${testNames.join(", ")} assigned to ${sandbox.getSandboxName()}`);

                availableTestCases = availableTestCases.slice(this.testParallelLimit);
            }
        }

        if (availableSandboxes.length > 0) {
            const runningTests = this.testCases.filter((test) => test.status === "notStart").sort(() => Math.random() - 0.5);
            if (runningTests.length > 0) {
                logger.infoBlue(`Still have ${runningTests.length} tests running, assigning them to other sandboxes to speed up testing`);
                availableSandboxes.forEach((sandbox) => {
                    if (runningTests.length > 0) {
                        const testToReassign = runningTests.shift(); // 从运行中的测试中取出一个重新分配
                        if (testToReassign) {
                            this.executeTest(sandbox, [testToReassign.name]);
                            logger.infoBlue(`[Test Reassigned] Test ${testToReassign.name} reassigned to ${sandbox.getSandboxName()}`);
                        }
                    }
                });
            }
        }
    }

    protected async deployAndTest() {
        while (true) {
            if (this.allTestFinished() && this.allDeploymentCompleted()) {
                break;
            }
            await this.checkDeployStatus();
            this.checkTestStatus();
            await this.assignTests();
            await sleep(5000);
        }
        this.logTestResult();
    }

    private logTestResult() {
        const failedTestCases = this.testCases.filter((test) => test.result === "failed");
        if (failedTestCases.length > 0) {
            logger.errorRed(`${failedTestCases.length} tests failed, please check the logs for more details. tests: ${failedTestCases.map((test) => test.name).join(", ")}`);
            failedTestCases.forEach((test, index) => {
                logger.errorRed(`${index + 1}.Test ${test.name} failed, failure messages: \n${test.failureMessages}`);
            });
            throw new Error(`${failedTestCases.length} tests failed, please check the logs for more details`);
        }
    }

    private getTestResults() {
        this.sandboxes
            .filter((sandbox) => sandbox.getDeployStatus() === "Deployed" && !sandbox.getIsRunningTest())
            .forEach((sandbox) => {
                const testResults = sandbox.getTestResultMap();
                testResults.forEach((result: any, testName: string) => {
                    const existingResult = this.testResultMap.get(testName);
                    if (!existingResult) {
                        this.testResultMap.set(testName, result);
                    }
                });
            });
    }

    private updateTestCasesStatus() {
        const testCasesLength = this.testCases.length;
        const unfinishedTestCases = this.testCases.filter((test) => test.status !== "finished");
        let finishedTestLength = testCasesLength - unfinishedTestCases.length;
        unfinishedTestCases.forEach((testCase) => {
            const result = this.testResultMap.get(testCase.name);
            if (result) {
                if (result.isCorrect) {
                    testCase.markAsSucceeded();
                    finishedTestLength++;
                    logger.infoGreen(`[Test Passed]      [${finishedTestLength}/${testCasesLength}]         Test ${testCase.name} passed in ${result.runEnvironment}`);
                } else {
                    testCase.handleFailure(result.failureMessages);
                    if (testCase.status == "finished") {
                        finishedTestLength++;
                    } else {
                        this.testResultMap.set(testCase.name, undefined);
                    }
                    logger.errorRed(
                        `[Test Failed]      [${finishedTestLength}/${testCasesLength}]         Test ${testCase.name} failed in ${result.runEnvironment}, RetryTimes: [${testCase.retryTimes}/${this.testRetryTimes}] \n${result.failureMessages}`
                    );
                }
            }
        });
    }

    private logDeploymentSuccess(sandbox: SalesforceEnvironment) {
        const deployedCount = this.sandboxes.filter((sandbox) => sandbox.getDeployStatus() === "Deployed").length;
        logger.infoGreen(`[Deploy Status]    [${deployedCount}/${this.sandboxes.length}]    Successfully deployed to ${sandbox.getSandboxName()}`);
    }

    private logDeploymentFailure(env: SalesforceEnvironment) {
        logger.errorRed(`Deployment failed in environment ${env.getSandboxName()}, failure message: ${env.getDeployFailedReason()}`);
        throw new Error(`Deployment failed in environment ${env.getSandboxName()}, failure message: ${env.getDeployFailedReason()}`);
    }
}
