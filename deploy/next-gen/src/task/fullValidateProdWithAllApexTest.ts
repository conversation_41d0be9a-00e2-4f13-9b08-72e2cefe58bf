import { SalesforceEnvironment } from "../salesforceEnvironment/salesforceEnvironment";
import { ProdOrg } from "../salesforceEnvironment/prodOrg";
import { FullDeployment } from "./fullDeployment";
import path from "path";
import { promises as fs } from "fs";
import { logger, sendWechatNotification } from "../utils";
import { validatedJobId } from "../../config/deploy-validated-job-config.json";

export class FullValidateProdWithAllApexTest extends FullDeployment {
    constructor(sandboxes: SalesforceEnvironment[]) {
        if (sandboxes.length !== 1 || !(sandboxes[0] instanceof ProdOrg)) {
            throw new Error("Only one ProdOrg is supported for this task");
        }
        super(sandboxes, true);
    }

    public override async run() {
        if (validatedJobId && validatedJobId != "") {
            logger.infoGreen(`Validated job id ${validatedJobId} found, continue to deploy`);
            await this.continueValidateDeployProgress(validatedJobId);
            await this.monitorDeployment();
        } else {
            logger.infoGreen(`No validated job id found, start to validate deploy`);
            await super.run(true);
        }
    }

    private async storeValidatedJobId(): Promise<void> {
        const configPath = path.join(__dirname, "../..", "config", "deploy-validated-job-config.json");
        const data = JSON.stringify(
            {
                validatedJobId: this.environments[0].getDeploymentJobId(),
                deployMode: "FULL"
            },
            null,
            4
        );
        await fs.writeFile(configPath, data, "utf8");
    }

    private async continueValidateDeployProgress(validatedJobId: string) {
        if (this.environments[0] instanceof ProdOrg) {
            await this.environments[0].continueValidateDeployProgress(validatedJobId);
        }
    }

    protected override async deployAsync() {
        if (this.environments[0] instanceof ProdOrg) {
            await this.environments[0].validateDeployWithRunLocalTests("package/package.xml", "deploy/delete/destructiveChangesPre.xml", "deploy/delete/destructiveChangesPost.xml");
            await this.storeValidatedJobId();
        }
    }

    protected override async notifyDeploymentStatus(isSuccess: boolean, env: SalesforceEnvironment): Promise<void> {
        await sendWechatNotification(isSuccess, env.getSandboxName(), null, true, Date.now() - env.getDeployStartTime(), true);
    }
}
