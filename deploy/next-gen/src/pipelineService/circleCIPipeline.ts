import { getGitBranchName, logger } from "../utils";
import { CircleCIApiClient, JobItem, PipelineItem } from "./circleCIApiClient";
import { githubOrg, githubProject } from "../../config/pipeline.json";
import workflowJobMapConfig from "../../config/workflow-job-map-config.json";

// const CHECK_CONCURRENT_BUILD_COUNT = 3;
// const CHECK_CONCURRENT_BUILD_SLEEP_MS = 1000;

type Commit = string;

export interface Commits {
    items: Commit[];
    hasMore: boolean;
}

const MAX_BUILDS_TO_RETRIEVE = 1000;

export class CircleCIPipeline {
    private circleCIApiClient = new CircleCIApiClient(process.env.CIRCLECI_API_KEY || "");
    private nextPageToken: string | undefined = undefined;
    private buildsRetrievedCount: number = 0;

    public async getRecentCommitsFromPipeline(workflow: string, deploymentJobNames: string[]): Promise<Commits> {
        logger.infoGreen(`Get recent successful build commit for job ${deploymentJobNames.join(" | ")} in workflow ${workflow}, builds retrieved count: ${this.buildsRetrievedCount}`);
        if (this.buildsRetrievedCount > MAX_BUILDS_TO_RETRIEVE) {
            return { items: [], hasMore: false };
        }
        const recentBuild = await this.circleCIApiClient.recentBuilds(githubOrg, githubProject, this.nextPageToken);
        this.nextPageToken = recentBuild.nextPageToken;
        this.buildsRetrievedCount += recentBuild.items.length;
        const successDeployments = await this.filterSuccessfulDeploymentJobsInWorkflow(recentBuild.items, workflow, deploymentJobNames);
        return {
            items: successDeployments.map((build) => build.vcs.revision),
            hasMore: !!this.nextPageToken
        };
    }

    public async getWorkflowName() {
        const workflowItem = await this.circleCIApiClient.getWorkflows(process.env.CIRCLE_WORKFLOW_ID || "");
        return workflowItem.name;
    }

    public async getSpecificJobBuildUrl(deployTo: string[]): Promise<string | null> {
        const currentWorkflowId = process.env.CIRCLE_WORKFLOW_ID || "";
        const branchName = await getGitBranchName();
        const builds = await this.circleCIApiClient.runningBuilds(githubOrg, githubProject);

        // 获取 workflowJobMap 配置
        const workflowJobMap = this.getWorkflowJobMap(deployTo, branchName);
        console.log(JSON.stringify(workflowJobMap));
        console.log(JSON.stringify(deployTo));
        if (!workflowJobMap) {
            return null;
        }

        // 查找匹配的构建任务
        for (const [workflowName, jobNames] of Object.entries(workflowJobMap)) {
            const matchingBuild = builds.find(
                (build) =>
                    build.workflows.workflow_name === workflowName && jobNames.includes(build.workflows.job_name) && build.workflows.workflow_id !== currentWorkflowId && build.status === "running"
            );

            if (matchingBuild) {
                return matchingBuild.build_url;
            }
        }

        return null;
    }

    // 辅助函数：根据 deployTo 和 branchName 获取 workflowJobMap 配置
    private getWorkflowJobMap(deployTo: string[], branchName: string): { [key: string]: string[] } | null {
        // 优先匹配 deployTo
        for (const deployKey of deployTo) {
            if ((workflowJobMapConfig as WorkflowJobMapConfig).deployTo[deployKey]) {
                return (workflowJobMapConfig as WorkflowJobMapConfig).deployTo[deployKey];
            }
        }

        // 如果没有匹配 deployTo，则检查 branchPrefix
        for (const [prefix, config] of Object.entries(workflowJobMapConfig.branchPrefix)) {
            if (branchName.startsWith(prefix)) {
                return config;
            }
        }

        return null;
    }

    public async cancelWorkflow(): Promise<void> {
        await this.circleCIApiClient.cancelWorkflow(process.env.CIRCLE_WORKFLOW_ID || "");
    }

    private async filterSuccessfulDeploymentJobsInWorkflow(recentBuilds: PipelineItem[], workflow: string, deploymentJobNames: string[]) {
        const successBuilds: PipelineItem[] = [];
        // const recentBuildsFromCodePush: PipelineItem[] = recentBuilds.filter((item: PipelineItem) => item.trigger.type === "webhook");
        for (const build of recentBuilds) {
            if (await this.isSuccessBuildInWorkflow(build, workflow, deploymentJobNames)) {
                successBuilds.push(build);
            }
        }
        return successBuilds;
    }

    private async isSuccessBuildInWorkflow(build: PipelineItem, workflow: string, deploymentJobNames: string[]) {
        const workflows = await this.circleCIApiClient.workflows(build.id);
        if (workflows.length === 0) {
            return false;
        }
        const firstWorkflowName = workflows[0].name;
        if (firstWorkflowName !== workflow) {
            return false;
        }

        const firstWorkflowId = workflows[0].id;
        return await this.isJobSuccess(firstWorkflowId, deploymentJobNames);
    }

    private async isJobSuccess(workflowId: string, jobNames: string[]) {
        const jobs = await this.circleCIApiClient.jobs(workflowId);

        // Check if any job in the jobNames list is successful
        return jobNames.some((jobName) => {
            const job = jobs.find((item: JobItem) => item.name === jobName);
            return job && job.status === "success";
        });
    }
}
export interface WorkflowJobMapConfig {
    deployTo: {
        [key: string]: {
            [key: string]: string[];
        };
    };
    branchPrefix: {
        [key: string]: {
            [key: string]: string[];
        };
    };
}
