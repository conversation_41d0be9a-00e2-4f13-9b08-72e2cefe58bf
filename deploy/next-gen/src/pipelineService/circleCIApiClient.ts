import axios from "axios";

export interface PipelineItem {
    id: string;
    trigger: {
        type: string;
    };
    vcs: {
        revision: string;
        branch: string;
    };
}

export interface PipelineItems {
    items: PipelineItem[];
    nextPageToken: string | undefined;
}

export interface WorkflowItem {
    id: string;
    name: string;
}

export interface JobItem {
    id: string;
    name: string;
    status: string;
}

export interface Build {
    workflows: {
        workflow_name: string;
        job_name: string;
        workflow_id: string;
    };
    status: string;
    build_url: string;
}

export class CircleCIApiClient {
    private V2_BASE_URL: string = "https://circleci.com/api/v2";
    private V1_BASE_URL: string = "https://circleci.com/api/v1.1";

    constructor(private token: string) {
        this.setupResponseInterceptor();
    }

    private setupResponseInterceptor() {
        axios.interceptors.response.use(undefined, async (error) => {
            if (error.response.status === 429) {
                const retryAfter = error.response.headers["retry-after"];
                if (retryAfter) {
                    console.log("Rate limit exceeded, retrying after " + retryAfter + " seconds");
                    console.log("Headers:", error.response.headers);
                    return new Promise((resolve, reject) => {
                        setTimeout(() => {
                            axios(error.config).then(resolve).catch(reject);
                        }, retryAfter * 1000);
                    });
                }
            }
            return Promise.reject(error);
        });
    }

    public async recentBuilds(org: string, project: string, nextPageToken?: string): Promise<PipelineItems> {
        let path = `/project/gh/${org}/${project}/pipeline`;
        if (nextPageToken) {
            path += `?page-token=${nextPageToken}`;
        }
        const result = await axios.get(`${this.V2_BASE_URL}/${path}`, { headers: { "Circle-Token": this.token } });
        return { items: result.data.items, nextPageToken: result.data.next_page_token };
    }

    public async workflows(pipelineId: string): Promise<WorkflowItem[]> {
        return this.request(`/pipeline/${pipelineId}/workflow`);
    }

    public async getWorkflows(workflowId: string): Promise<WorkflowItem> {
        const result = await axios.get(`${this.V2_BASE_URL}/workflow/${workflowId}?circle-token=${this.token}`);
        return result.data;
    }

    public async jobs(workflowId: string): Promise<JobItem[]> {
        return this.request(`/workflow/${workflowId}/job`);
    }

    public async runningBuilds(org: string, project: string): Promise<Build[]> {
        const result = await axios.get(`${this.V1_BASE_URL}/project/github/${org}/${project}?circle-token=${this.token}&filter=running`);
        return result.data;
    }

    public async cancelWorkflow(workflowId: string): Promise<void> {
        await axios.post(`${this.V2_BASE_URL}/workflow/${workflowId}/cancel?circle-token=${this.token}`);
    }

    private async request(path: string) {
        const result = await axios.get(`${this.V2_BASE_URL}/${path}`, { headers: { "Circle-Token": this.token } });
        return result.data.items;
    }
}
