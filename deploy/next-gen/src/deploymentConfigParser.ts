import { logger } from "./utils";

// 定义 TestMode 枚举
export enum TestMode {
    FULL_APEX_TEST = "FULL_APEX_TEST",
    AFFECTED_APEX_TEST = "AFFECTED_APEX_TEST",
    WITHOUT_APEX_TEST = "WITHOUT_APEX_TEST",
    FULL_APEX_TEST_WITH_COVERAGE = "FULL_APEX_TEST_WITH_COVERAGE",
    UI_TEST = "UI_TEST",
    PLAYWRIGHT_TEST = "PLAYWRIGHT_TEST"
}

// 定义 DeployMode 枚举
export enum DeployMode {
    DIFF = "DIFF",
    FULL = "FULL",
    FULL_VALIDATE = "FULL_VALIDATE",
    VALIDATED_DEPLOY = "VALIDATED_DEPLOY"
}

// 定义 DeploymentConfig 接口
export interface DeploymentConfig {
    testMode?: TestMode;
    deployMode: DeployMode;
    deployTo: string[];
    skipConflictCheck?: boolean;
    needSetup?: boolean;
}

// 辅助函数：从命令行获取参数值
function getArgValue(flag: string): string | undefined {
    const index = process.argv.indexOf(flag);
    return index > -1 && process.argv[index + 1] ? process.argv[index + 1] : undefined;
}

// 日志输出部署配置
function logDeploymentConfig(config: DeploymentConfig) {
    if (config.testMode !== undefined) {
        logger.infoGreen(`Test Mode: ${TestMode[config.testMode]}`);
    }
    logger.infoGreen(`Deploy Mode: ${DeployMode[config.deployMode]}`);
    logger.infoGreen(`Deploy To: ${config.deployTo}`);
    logger.infoGreen(`Skip Conflict Check: ${config.skipConflictCheck}`);
    logger.infoGreen(`Need Setup: ${config.needSetup}`);
}

// 解析命令行参数并返回 DeploymentConfig
export function parseArgs(): DeploymentConfig {
    try {
        const testModeInput = getArgValue("--testMode");
        const deployModeInput = getArgValue("--deployMode");
        const deployToArg = getArgValue("--deployTo");
        const skipConflictCheck = getArgValue("--skipConflictCheck") == "true";
        const needSetup = getArgValue("--needSetup") == "true";

        if (!deployModeInput || !deployToArg) {
            logger.errorRed("Missing required arguments: deployMode and deployTo are mandatory.");
            process.exit(1);
        }

        // 验证并转换为相应的枚举类型
        let testMode: TestMode | undefined;
        if (testModeInput) {
            if (isValidTestMode(testModeInput)) {
                testMode = TestMode[testModeInput as keyof typeof TestMode];
            } else {
                logger.errorRed(`Invalid testMode value: ${testModeInput}`);
                process.exit(1);
            }
        }

        let deployMode: DeployMode;
        if (isValidDeployMode(deployModeInput)) {
            deployMode = DeployMode[deployModeInput as keyof typeof DeployMode];
        } else {
            logger.errorRed(`Invalid deployMode value: ${deployModeInput}`);
            process.exit(1);
        }
        const config: DeploymentConfig = {
            testMode,
            deployMode,
            deployTo: deployToArg.split(","),
            skipConflictCheck,
            needSetup
        };

        validateConfig(config);
        logger.infoGreen("Parsed and validated deployment configuration:");

        config.deployTo = config.deployTo.map((target) => {
            target.trim();
            if (target == "prod") {
                return `<EMAIL>`;
            }
            return `<EMAIL>.${target}`;
        });

        logDeploymentConfig(config);
        return config;
    } catch (error: any) {
        logger.errorRed(`Error: ${error.message}`);
        process.exit(1);
    }
}

// 验证配置的业务规则
function validateConfig(config: DeploymentConfig): void {
    // 检查 deployTo 是否提供
    if (!config.deployTo) {
        throw new Error("deployTo is required.");
    }

    // 定义独立的部署环境
    const exclusiveDeployTargets = ["prod", "crmpg", "sfdcuat2"];

    // 检查是否有任何独立部署环境，并确保它们只能单独作为目标
    for (const target of exclusiveDeployTargets) {
        if (config.deployTo.includes(target)) {
            if (config.deployTo.length > 1) {
                throw new Error(`When deploying to ${target}, no other deploy targets are allowed.`);
            }
        }
    }

    // 如果是 prod，检查 testMode 的合法性
    if (config.deployTo.includes("prod")) {
        if (config.testMode !== undefined && ![TestMode.FULL_APEX_TEST, TestMode.AFFECTED_APEX_TEST, TestMode.WITHOUT_APEX_TEST].includes(config.testMode)) {
            throw new Error("Invalid testMode for prod deployment. Allowed values are: FULL_APEX_TEST, AFFECTED_APEX_TEST, WITHOUT_APEX_TEST.");
        }
    }
}

// 辅助函数：验证 testMode 是否有效
function isValidTestMode(mode: string): boolean {
    return Object.keys(TestMode).includes(mode);
}

// 辅助函数：验证 deployMode 是否有效
function isValidDeployMode(mode: string): boolean {
    return Object.keys(DeployMode).includes(mode);
}
