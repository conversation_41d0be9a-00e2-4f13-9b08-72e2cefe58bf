// import { describe, expect, test } from "@jest/globals";
// import { MetadataService } from "../src/metadataService";
//
// describe("Metadata service get apex test", () => {
//     test("Should empty array given no metadata change", () => {
//         const metadataService = new MetadataService("./__tests__/data/no-metadata-change-package");
//         expect(metadataService.getApexTestFiles()).toEqual([]);
//     });
//
//     test("Should get empty array given only no apex test class change", () => {
//         const metadataService = new MetadataService("./__tests__/data/no-apex-class-change-package");
//         expect(metadataService.getApexTestFiles()).toEqual([]);
//     });
//
//     test("Should get array with one test name given one apex test class and other metadata change", () => {
//         const metadataService = new MetadataService("./__tests__/data/one-apex-class-with-other-metadata-change-package");
//         expect(metadataService.getApexTestFiles()).toEqual(["test"]);
//     });
//
//     test("Should get array with two test names given two apex test class change", () => {
//         const metadataService = new MetadataService("./__tests__/data/two-apex-class-change-package");
//         expect(metadataService.getApexTestFiles()).toEqual(["test", "test1"]);
//     });
//
//     test("Should get array with two test names given two apex test class and other metadata change", () => {
//         const metadataService = new MetadataService("./__tests__/data/two-apex-class-with-other-metadata-change-package");
//         expect(metadataService.getApexTestFiles()).toEqual(["test", "test1"]);
//     });
// });
