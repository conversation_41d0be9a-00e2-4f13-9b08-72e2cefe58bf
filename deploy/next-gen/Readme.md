# Next Generation Deployment Solution for SFDX

This project implements a delta metadata deployment function for Salesforce development, which can significantly speed up deployment times. It integrates with the CircleCI API and Git history to identify the last successful build, then uses the [sfdx-git-delta](https://github.com/scolladon/sfdx-git-delta) plugin to generate the delta metadata changes from the last successful build to the current commit (HEAD). Finally, it uses the Salesforce CLI to deploy these changes to a Salesforce sandbox.

## Prerequisites

Ensure you have Node.js version 20 or higher installed. You can check your Node version by running `node -v` in your terminal. If you need to install or update Node.js, visit [Node.js official website](https://nodejs.org/).

### Installation Steps

1. Install TypeScript, TSX, and Salesforce CLI globally:

    ```bash
    npm install typescript tsx @salesforce/cli -g
    ```

2. Install the sfdx-git-delta plugin:

    ```bash
    echo y | sf plugins install sfdx-git-delta
    ```

## Setup Local Development Environment

1. Clone the repository and navigate to the project directory:

    ```bash
    <NAME_EMAIL>:techops-e2ecs/psa-sfdx.git
    cd deploy/next-gen
    ```

2. Install dependencies:

    ```bash
    npm install
    ```

3. Prepare environment variables by creating a `.env` file in the `next-gen` root directory with the following content:

    ```plaintext
    CIRCLECI_API_KEY=<your-circleci-api-key>
    NEXT_GEN_ACCESS_token_PASSWORD=<your-access-token-password>
    ```

    Replace `<your-circleci-api-key>` and `<your-access-token-password>` with your actual credentials.

4. Execute the main function with the diff-deploy option:

    ```bash
    tsx ./deploy/next-gen/index.ts --run-diff-deploy
    ```

## Features

1. Deploy new/update files from the last successful CircleCI build to the current commit (HEAD).
2. Destructively remove files from the last successful CircleCI build to the current commit (HEAD).
3. Specify a base commit manually using a `base-commit.json` file.
4. Automatically remove attributes in metadata that are not supported in the sandbox.

## Guidance

### Show test coverage in VSCode

1. Install `Coverage Gutters` in VS Code Extensions

2. Config the scan dir of `Coverage Gutters`

```json
// in user setting of vscode
"coverage-gutters.coverageBaseDir": "deploy/next-gen/config/**"
```

3. Build locv file from coverage data

```bash
cd deploy/next-gen && npm run buildTestCoverage
```

The lcov.info file will be generated in deploy/next-gen/config dir, which can be readed by `Coverage Gutters` extension

## TODO List

1. Support delta UI test.
2. Support full Apex/UI test.
3. Split next-gen deployment project into an independent repository.
4. Improve code test coverage.
5. Better visualize deploy failure.
6. Better visualize changed and removed file list.
7. Identify tests to run not only by changed test files, but also analyze the influence from changed implementation.
