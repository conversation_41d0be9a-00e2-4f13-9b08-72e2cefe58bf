import { execPromise, logger } from "../src/utils";
import { SalesforceClient } from "../src/salesforceClient";
import * as dotenv from "dotenv";
import path from "path";

dotenv.config({ path: path.resolve(__dirname, "../.env") });

async function main() {
    let salesforceClient = new SalesforceClient();
    const sandboxesWithDifferentCredential = [
        "<EMAIL>",
        "<EMAIL>.SFDCUAT2",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ];
    const sandboxes = [
        "<EMAIL>.ci6",
        "<EMAIL>.ci7",
        "<EMAIL>.ci8",
        "<EMAIL>.ci9",
        "<EMAIL>.ci10",
        "<EMAIL>.ci11",
        "<EMAIL>.ci12",
        "<EMAIL>.ci13",
        "<EMAIL>.ci14",
        "<EMAIL>.ci15",
        "<EMAIL>.ci16",
        "<EMAIL>.ci17",
        "<EMAIL>.psaci1",
        "<EMAIL>.psaci2",
        "<EMAIL>.psaci3",
        "<EMAIL>.psaci4",
        "<EMAIL>.psaci5",
        "<EMAIL>.psaci6",
        "<EMAIL>.psaci7",
        "<EMAIL>.psaci8",
        "<EMAIL>.crmci01",
        "<EMAIL>.crmci02",
        "<EMAIL>.crmci03",
        "<EMAIL>.crmci04",
        "<EMAIL>.crmci05",
        "<EMAIL>.crmci06",
        "<EMAIL>.crmci07",
        "<EMAIL>.crmci08",
        "<EMAIL>.sfci1",
        "<EMAIL>.sfci2",
        "<EMAIL>.sfci3",
        "<EMAIL>.sfci4",
        "<EMAIL>.sfci5",
        "<EMAIL>.sfci6",
        "<EMAIL>.sfci7",
        "<EMAIL>.sfci8",
        "<EMAIL>.sfci9",
        "<EMAIL>.sfci10",
        // "<EMAIL>.rlsci1",
        // "<EMAIL>.rlsci2",
        // "<EMAIL>.rlsci3",
        // "<EMAIL>.rlsci4",
        // "<EMAIL>.rlsci5",
        // "<EMAIL>.rlsci6",
        // "<EMAIL>.rlsci7",
        // "<EMAIL>.rlsci8",
        // "<EMAIL>.rlsci9",
        // "<EMAIL>.rlsci10",
        // "<EMAIL>.rlsci11",
        // "<EMAIL>.rlsci12",
        // "<EMAIL>.rlsci13",
        // "<EMAIL>.rlsci14",
        // "<EMAIL>.rlsci15",
        // "<EMAIL>.rlsci16",
        // "<EMAIL>.rlsci17",
        // "<EMAIL>.rlsci18",
        // "<EMAIL>.rlsci19",
        // "<EMAIL>.rlsci20",
        // "<EMAIL>.rlsci21",
        // "<EMAIL>.rlsci22",
        // "<EMAIL>.rlsci23",
        // "<EMAIL>",
        // "<EMAIL>",
        // "<EMAIL>",
        // "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>.junfdev2",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        // "<EMAIL>",
        // "<EMAIL>",
        // "<EMAIL>",
        // "<EMAIL>",
        // "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ];

    const BATCH_SIZE = 10;
    const errors: string[] = [];

    for (let i = 0; i < sandboxes.length; i += BATCH_SIZE) {
        const batch = sandboxes.slice(i, i + BATCH_SIZE);

        const tasks = batch.map(async (sandbox) => {
            try {
                logger.infoBlue(`Starting authorization for ${sandbox}...`);
                await salesforceClient.authorizeWithAccessToken(sandbox, "NEXT_GEN_ACCESS_TOKEN_PASSWORD");
                logger.infoGreen(`Successfully authorized ${sandbox}`);

                logger.infoBlue(`Checking installed packages for ${sandbox}...`);
                const installedPackagesResult = await execPromise(`sf package installed list -o ${sandbox} --json`);
                const installedPackages = JSON.parse(installedPackagesResult.output).result;

                const isPackageInstalled = installedPackages.some((pkg: { SubscriberPackageName: string }) => pkg.SubscriberPackageName === "Provus Services Quoting");

                if (isPackageInstalled) {
                    logger.infoGreen(`Package "Provus Services Quoting" is already installed in ${sandbox}`);
                } else {
                    logger.infoBlue(`Installing package in ${sandbox}...`);
                    await execPromise(
                        `sf package install --target-org ${sandbox} --package 04tPI000000J5X3YAK --installation-key $NEXT_GEN_PACKAGE_INSTALLATION_KEY_PROVUS --security-type AdminsOnly --upgrade-type Mixed --no-prompt --wait 60 --json`
                    );
                    logger.infoGreen(`Successfully installed package in ${sandbox}`);
                }
            } catch (error) {
                const errorMessage = `Failed to authorize or run command for ${sandbox}: ${error} | ${JSON.stringify(error)}`;
                logger.errorRed(errorMessage);
                errors.push(errorMessage);
            }
        });

        await Promise.all(tasks);
    }

    if (errors.length > 0) {
        throw new Error(`Encountered errors: \n${errors.join("\n")}`);
    }
}

main()
    .then(() => {
        logger.infoBannerGreen("Program completed successfully");
        process.exit(0);
    })
    .catch((error) => {
        logger.errorBannerRed("Program failed");
        logger.errorRed(`Program failed with error: ${error.message}`);
        logger.errorBannerRed("Program failed");
        process.exit(-1);
    });
