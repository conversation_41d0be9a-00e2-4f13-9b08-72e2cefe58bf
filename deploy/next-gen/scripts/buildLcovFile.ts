import { getGitBranchName, findFilePath } from "../src/utils";

import { readFileSync, writeFileSync } from "fs";
interface fileCoverageAggregate {
    fileName: string;
    coverageRate: string;
    coveredLines: string;
    uncoveredLines: string;
}

async function buildFilePath(fileName: string) {
    if (fileName.endsWith("Trigger")) {
        return findFilePath(["../../force-app/main/default/triggers"], `${fileName}.trigger`);
    }
    return findFilePath(["../../force-app/main/default/classes"], `${fileName}.cls`);
}

async function buildLcovFileFromAggregateCoverageData(aggregateFilePath: string) {
    const rawData = JSON.parse(readFileSync(aggregateFilePath, "utf-8"));
    const fileCoverages: fileCoverageAggregate[] = Object.keys(rawData).map((fileName) => {
        return {
            fileName,
            ...rawData[fileName]
        };
    });
    const allFileBuildPromises = fileCoverages.map(async (fileCoverage) => {
        let filePath;
        try {
            filePath = await buildFilePath(fileCoverage.fileName);
            // code coverage gutter search form the root dir of project, so remove the ../../
            filePath = filePath.replace(/\.\.\/\.\.\//g, "./");
        } catch (e: unknown) {
            console.error(`Build ${fileCoverage.fileName} encounter sucn error: ${e}, will ignore the coverage`);
        }
        const coveredLines = fileCoverage.coveredLines.split(",").map((line) => `DA:${line},1`);
        return `SF:${filePath}\n${coveredLines.join("\n")}\nend_of_record`;
    });
    return await Promise.all(allFileBuildPromises).then((results) => results.join("\n"));
}

(async () => {
    const gitBranchName = await getGitBranchName();
    const fileContent = gitBranchName.startsWith("release")
        ? await buildLcovFileFromAggregateCoverageData("../config/coverage-data-aggregate-release.json")
        : await buildLcovFileFromAggregateCoverageData("./config/coverage-data-aggregate.json");
    writeFileSync("config/lcov.info", fileContent);
})();
