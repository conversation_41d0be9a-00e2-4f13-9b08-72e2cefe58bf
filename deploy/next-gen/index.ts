import { DeploymentService } from "./src/deploymentService";
import { getDevOAuthClientConfig, getOAuthClientConfigByGitBranch, logger, validateArgs } from "./src/utils";
import * as dotenv from "dotenv";
import { parseArgs, DeploymentConfig } from "./src/deploymentConfigParser";

dotenv.config({ path: __dirname + "/.env" });

async function main() {
    logger.infoBannerGreen("Program started");
    const config: DeploymentConfig = parseArgs();

    await new DeploymentService(config).run();
}

main()
    .then(() => {
        logger.infoBannerGreen("Program completed successfully");
        process.exit(0);
    })
    .catch((error) => {
        logger.errorBannerRed("Program failed");
        logger.errorRed(`Program failed with error: ${error.message}`);
        logger.errorRed(`Stack trace: ${error.stack}`);
        logger.errorBannerRed("Program failed");
        process.exit(-1);
    });
