import { execPromise, getSandboxName, logger, readOAuthClientConfigFile } from "./src/utils";
import { SalesforceClient } from "./src/salesforceClient";
import * as dotenv from "dotenv";

dotenv.config({ path: __dirname + "/.env" });

async function authorize(){
    const oauthClientConfig = await readOAuthClientConfigFile();
    const group = "release";
    const usage = "apex";

    const sandboxes = oauthClientConfig
        .filter((client) => (client.group === group && client.usage === usage) || client.usage==='prod');

    const authorizeSandbox = async (sandbox: { username: string; passwordEnv: string }) => {
        try {
            const salesforceClient = new SalesforceClient();
            await salesforceClient.authorizeWithAccessToken(sandbox.username, sandbox.passwordEnv);
            logger.infoGreen(`Successfully authorized ${getSandboxName(sandbox.username)}`);
        } catch (error) {
            logger.errorRed(`Failed to authorize ${sandbox.username}, error: ${JSON.stringify(error)}`);
        }
    };

    for (const sandbox of sandboxes) {
        await authorizeSandbox(sandbox);
    }
    return sandboxes.map((sandbox) => sandbox.username);
}

async function main(){
    const sandboxes = await authorize();
    console.log(sandboxes);
    const excludedRefreshSandboxes = ['<EMAIL>.ci6','<EMAIL>','<EMAIL>.sfdcuat2','<EMAIL>'];
    const shouldRefreshSandboxes = [

        '<EMAIL>.rlci14',
        ];
    const sandboxToRefresh = sandboxes.filter((sandbox) => !excludedRefreshSandboxes.includes(sandbox) && shouldRefreshSandboxes.includes(sandbox));
    console.log(sandboxToRefresh);
    // for (const sandbox of sandboxToRefresh) {
    //     execPromise(`sf org refresh sandbox -o production -n ${sandbox.split('.')[2]} -f config/ci6-sandbox-config.json --async --no-prompt`,false);
    // }

}

main();
