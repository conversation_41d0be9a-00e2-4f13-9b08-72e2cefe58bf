#! /bin/sh

fetch_active_builds() {
    job_status_api_url_template="https://circleci.com/api/v1.1/project/github/techops-e2ecs/psa-sfdx?circle-token=${CIRCLECI_API_KEY}&filter=running"
    curl -f -s ${job_status_api_url_template} > running_jobs.json
    echo "Fetch active builds"
}

cancel_current_build() {
    echo "Cancelleing build ${CIRCLE_BUILD_NUM} start"
    cancel_api_url_template="https://circleci.com/api/v1.1/project/github/techops-e2ecs/psa-sfdx/${CIRCLE_BUILD_NUM}/cancel?circle-token=${CIRCLECI_API_KEY}"
    curl -s -X POST ${cancel_api_url_template} > /dev/null
    echo "Cancelleing build ${CIRCLE_BUILD_NUM} finish"
}

fetch_conflict_running_workflows() {
    current_workflow=`cat running_jobs.json | jq '.[] | select( .build_num == '"${CIRCLE_BUILD_NUM}"' ).workflows.workflow_name'`
    echo "Current job workflow: ${current_workflow}"

    conflict_running_workflows=`cat running_jobs.json | jq '.[] | select( .build_num != '"${CIRCLE_BUILD_NUM}"' ) | .workflows | select ( .workflow_name == '"${current_workflow}"' ) | select (.job_name != "block-workflow") | select (.job_name != "deploy-developer-sandbox") | select (.job_name != "diff-deploy-and-run-full-apex-test-with-coverage") | select (.job_name != "full-deploy-and-run-ui-test") | select (.job_name != "full-deploy-playground-2") | select (.job_name != "full-deploy-playground-1") | select (.job_name != "full-deploy-prod") | select (.job_name != "diff-deploy-prod")'`

    echo "Conflict job workflows: ${conflict_running_workflows}"
}

try_time=0
while [ ${try_time} -lt 3 ]
do
    fetch_active_builds
    fetch_conflict_running_workflows

    if [ "${conflict_running_workflows}" = "" ]; then
        echo "No conflict jobs running on same workflow, ready to run current build!"
        exit 0
    else
        echo "Conflict jobs still running on the same workflow!"
        echo "-------------------------------------------------"
        sleep 3
    fi

    try_time=$(( ${try_time} + 1 ))
done

echo "Conflict jobs still running on the same workflow after check ${try_time} times, will cancel current build!"

cancel_current_build
sleep 10
exit 1
