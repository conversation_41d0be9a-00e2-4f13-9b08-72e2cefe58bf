const { exec } = require("child_process");
const path = require("path");
const fs = require("fs");

// 获取需要处理的文件列表
const getAllFiles = (dir) => {
    let results = [];
    const list = fs.readdirSync(dir);
    list.forEach((file) => {
        file = path.resolve(dir, file);
        const stat = fs.statSync(file);
        if (stat && stat.isDirectory()) {
            results = results.concat(getAllFiles(file));
        } else if (file.endsWith(".js") || file.endsWith(".ts") || file.endsWith(".json") || file.endsWith(".css") || file.endsWith(".cls") || file.endsWith(".trigger")) {
            // 根据需求选择要格式化的文件类型
            results.push(file);
        }
    });
    return results;
};

// 执行 Prettier 格式化命令
const formatFile = (filePath) => {
    return new Promise((resolve, reject) => {
        exec(`prettier --write '${filePath}'`, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error formatting file ${filePath}:`, error);
                reject(error);
            } else {
                console.log(`Formatted: ${filePath}`);
                resolve(stdout);
            }
        });
    });
};

const pLimit = require("p-limit");
const limit = pLimit(10); // 限制并发任务数

const formatFilesInBatches = async (dir) => {
    const files = getAllFiles(dir);
    const promises = files.map((file) => limit(() => formatFile(file)));

    try {
        await Promise.all(promises);
        console.log("All files formatted successfully!");
    } catch (error) {
        console.error("Error during formatting:", error);
    }
};

// 调用格式化
formatFilesInBatches("./force-app");
