const fs = require("fs");
const path = require("path");
const fastGlob = require("fast-glob");
const xml2js = require("xml2js");
const util = require("util");
const exec = util.promisify(require("child_process").exec);

async function main() {
    // 指定要搜索的目录，默认当前目录
    const targetDir = process.argv[2] || ".";

    // 查找所有 .profile-meta.xml 和 .permissionset-meta.xml 文件
    const xmlFiles = await fastGlob([path.join(targetDir, "**/*.profile-meta.xml"), path.join(targetDir, "**/*.permissionset-meta.xml")]);

    // 查找所有 Apex 类文件
    const apexClassFiles = await fastGlob([path.join(targetDir, "**/*.cls")]);

    // 构建类名到文件路径的映射
    const apexClassMap = {};
    for (const classFile of apexClassFiles) {
        const className = path.basename(classFile, ".cls");
        apexClassMap[className] = classFile;
    }

    // 处理每个 XML 文件
    for (const xmlFile of xmlFiles) {
        const xmlContent = fs.readFileSync(xmlFile, "utf8");

        // 解析 XML
        const parser = new xml2js.Parser({
            preserveChildrenOrder: true,
            explicitArray: false
            // 去掉 mergeAttrs 选项
            // mergeAttrs: true,
        });
        const builder = new xml2js.Builder({
            preserveChildrenOrder: true,
            xmldec: { version: "1.0", encoding: "UTF-8" }
            // 去掉 mergeAttrs 选项
            // mergeAttrs: true,
        });
        const xmlObj = await parser.parseStringPromise(xmlContent);

        // 确定根元素是 Profile 还是 PermissionSet
        const rootKey = xmlObj.Profile ? "Profile" : "PermissionSet";
        const root = xmlObj[rootKey];

        if (root && root.classAccesses) {
            let classAccesses = root.classAccesses;
            if (!Array.isArray(classAccesses)) {
                classAccesses = [classAccesses];
            }

            // 过滤 classAccesses
            const newClassAccesses = [];
            for (const classAccess of classAccesses) {
                const apexClassValue = classAccess.apexClass;
                const apexClassName = Array.isArray(apexClassValue) ? apexClassValue[0] : apexClassValue;

                const apexClassFile = apexClassMap[apexClassName];

                let shouldKeep = true;

                if (apexClassFile) {
                    const classContent = fs.readFileSync(apexClassFile, "utf8");
                    const filename = path.basename(apexClassFile);
                    if (classContent.toLowerCase().includes("@istest") || (!classContent.toLowerCase().includes("@auraenabled") && !filename.toLowerCase().includes("controller"))) {
                        // 删除 classAccesses 元素
                        shouldKeep = false;
                    }
                } else {
                    // 如果找不到对应的类文件，可根据需求决定是否保留
                    shouldKeep = true;
                }

                if (shouldKeep) {
                    newClassAccesses.push(classAccess);
                }
            }

            // 更新 classAccesses
            if (newClassAccesses.length === 0) {
                delete root.classAccesses;
            } else {
                root.classAccesses = newClassAccesses;
            }
        }

        // 重新构建 XML
        const newXmlContent = builder.buildObject(xmlObj);

        // 保存文件
        fs.writeFileSync(xmlFile, newXmlContent, "utf8");

        // 使用 Prettier 格式化
        try {
            const prettierConfigPath = path.resolve(".prettierrc"); // 请将此路径替换为您实际的 .prettierrc 文件路径
            await exec(`npx prettier --config "${prettierConfigPath}" --write "${xmlFile}"`);
            console.log(`已格式化文件 ${xmlFile}`);
        } catch (error) {
            console.error(`格式化文件 ${xmlFile} 时出错:`, error.stderr);
        }
    }
}

main();
