#!/bin/bash

gitkeep_path="force-app/sf-temp/main/default/.gitkeep"
base_dir="force-app/sf-temp"

# Check if the .gitkeep file exists
if [[ ! -f "$gitkeep_path" ]]; then
    echo -e "\033[31mError: $gitkeep_path file not found. This directory ($base_dir) is supposed to store temporary Salesforce CLI metadata. Need to use .gitkeep to ensure the directory structure."
    exit 1
fi

# Check if there are any files other than .gitkeep in the base directory and its subdirectories
other_files=$(find "$base_dir" -type f ! -path "$gitkeep_path")

if [[ -n "$other_files" ]]; then
  formatted_other_files=$(echo "$other_files" | sed 's/^/\t  /')
    echo -e "\033[31mError: [sf-temp is the default directory for fetching Salesforce CLI metadata. Please move the metadata to the correct directory and avoid deleting this folder directly.]\n    Metadata found in $base_dir:\n$formatted_other_files"
    exit 1
fi

echo -e "\033[32mCheck passed: Only .gitkeep file exists in $base_dir, as expected for Salesforce CLI temp storage."
