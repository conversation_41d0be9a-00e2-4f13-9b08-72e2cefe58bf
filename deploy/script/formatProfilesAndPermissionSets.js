// const fs = require("fs").promises;
// const path = require("path");
// const { parseStringPromise, Builder } = require("xml2js");
// const prettier = require("prettier");
// const chalk = require("chalk");
// const { exec } = require("child_process");
// const diff = require("diff");
//
// // Recursively find all files ending with .profile-meta.xml and .permissionset-meta.xml
// async function findXmlFiles(dir) {
//     const results = [];
//     const list = await fs.readdir(dir, { withFileTypes: true });
//
//     await Promise.all(
//         list.map(async (file) => {
//             const filePath = path.join(dir, file.name);
//             if (file.isDirectory()) {
//                 const subDirFiles = await findXmlFiles(filePath);
//                 results.push(...subDirFiles);
//             } else if (file.name.endsWith(".profile-meta.xml") || file.name.endsWith(".permissionset-meta.xml")) {
//                 results.push(filePath);
//             }
//         })
//     );
//
//     return results;
// }
//
// // Get the list of files staged in Git
// async function getStagedFiles() {
//     return new Promise((resolve, reject) => {
//         exec("git diff --cached --name-only --diff-filter=d", (error, stdout) => {
//             if (error) {
//                 return reject(error);
//             }
//
//             const files = stdout.split("\n").filter((file) => file.endsWith(".profile-meta.xml") || file.endsWith(".permissionset-meta.xml"));
//             resolve(files);
//         });
//     });
// }
//
// // Utility function: Sort by field name and keep the last duplicate
// const sortByField = (arr, fieldName, secondaryFieldName = null, removeDuplicate) => {
//     // Sort the array by fieldName (and optionally secondaryFieldName) alphabetically
//     arr.sort((a, b) => {
//         const aPrimary = a[fieldName][0].toLowerCase();
//         const bPrimary = b[fieldName][0].toLowerCase();
//
//         if (secondaryFieldName) {
//             const aSecondary = a[secondaryFieldName] && a[secondaryFieldName][0] ? a[secondaryFieldName][0].toLowerCase() : "";
//             const bSecondary = b[secondaryFieldName] && b[secondaryFieldName][0] ? b[secondaryFieldName][0].toLowerCase() : "";
//
//             // Compare primary field first, then secondary field if primary fields are equal
//             if (aPrimary < bPrimary) return -1;
//             if (aPrimary > bPrimary) return 1;
//             if (aSecondary < bSecondary) return -1;
//             if (aSecondary > bSecondary) return 1;
//         } else {
//             if (aPrimary < bPrimary) return -1;
//             if (aPrimary > bPrimary) return 1;
//         }
//         return 0; // Equal
//     });
//
//     // Track duplicates
//     const seen = new Map();
//     const duplicates = [];
//
//     // Keep only the last occurrence of each unique key
//     arr.forEach((item, index) => {
//         let key;
//         if (secondaryFieldName) {
//             const secondaryValue = item[secondaryFieldName] && item[secondaryFieldName][0] ? item[secondaryFieldName][0].toLowerCase() : "";
//             key = `${item[fieldName][0].toLowerCase()}${secondaryValue}`;
//         } else {
//             key = item[fieldName][0].toLowerCase();
//         }
//
//         if (seen.has(key)) {
//             duplicates.push(key); // Record duplicate
//         }
//         seen.set(key, index); // Overwrite previous with the last occurrence
//     });
//
//     // Build the unique array based on the 'seen' indices if removeDuplicate is true
//     const uniqueArr = removeDuplicate ? Array.from(seen.values()).map((index) => arr[index]) : arr;
//
//     return { sortedArray: uniqueArr, duplicates };
// };
//
// async function sortProfileSections(profile, duplicateReport, options) {
//     const sortables = [
//         { key: "fieldPermissions", field: "field" },
//         { key: "applicationVisibilities", field: "application" },
//         { key: "classAccesses", field: "apexClass" },
//         { key: "layoutAssignments", field: "layout", secondaryField: "recordType" },
//         { key: "objectPermissions", field: "object" },
//         { key: "pageAccesses", field: "apexPage" },
//         { key: "recordTypeVisibilities", field: "recordType" },
//         { key: "tabVisibilities", field: "tab" },
//         { key: "userPermissions", field: "name" }
//     ];
//
//     sortables.forEach(({ key, field, secondaryField }) => {
//         if (profile[key]) {
//             const { sortedArray, duplicates } = sortByField(profile[key], field, secondaryField, options.removeDuplicate);
//             profile[key] = sortedArray; // Update to include only the last occurrence
//             if (duplicates.length > 0) {
//                 duplicateReport[key] = (duplicateReport[key] || []).concat(duplicates);
//             }
//         }
//     });
// }
//
// // Save a backup of the file
// async function saveBackup(filePath) {
//     const content = await fs.readFile(filePath, "utf-8");
//     const backupFilePath = `${filePath}.backup.txt`;
//     await fs.writeFile(backupFilePath, content, "utf-8");
//     return backupFilePath;
// }
//
// // Delete the backup
// async function deleteBackup(backupFilePath) {
//     await fs.unlink(backupFilePath);
// }
//
// // Compare the original file with the formatted file for differences
// async function compareWithBackup(filePath, backupFilePath) {
//     const originalContent = await fs.readFile(backupFilePath, "utf-8");
//     const formattedContent = await fs.readFile(filePath, "utf-8");
//
//     const hasDifferences = originalContent !== formattedContent;
//
//     let differences = null;
//     if (hasDifferences) {
//         differences = diff.createTwoFilesPatch(filePath + " (original)", filePath + " (formatted)", originalContent, formattedContent);
//     }
//
//     return { hasDifferences, differences };
// }
//
// // Add file to Git staging area
// async function gitAdd(filePath) {
//     return new Promise((resolve) => {
//         exec(`git add '${filePath}'`, (error) => {
//             if (error) {
//                 console.log(error);
//             }
//             resolve();
//         });
//     });
// }
//
// async function sortProfile(filePath, options, allDuplicateReports, checkAndThrowResults) {
//     try {
//         const xmlData = await fs.readFile(filePath, "utf-8");
//
//         const originalObj = await parseStringPromise(xmlData);
//         const duplicateReport = {};
//
//         if (filePath.endsWith(".permissionset-meta.xml")) {
//             await sortProfileSections(originalObj.PermissionSet, duplicateReport, options);
//         } else {
//             await sortProfileSections(originalObj.Profile, duplicateReport, options);
//         }
//
//         const builder = new Builder({
//             xmldec: { version: "1.0", encoding: "UTF-8" }
//         });
//         const sortedXml = builder.buildObject(originalObj);
//
//         const formattedXml = prettier.format(sortedXml, {
//             parser: "xml",
//             plugins: [require.resolve("prettier-plugin-xml")],
//             tabWidth: 4,
//             printWidth: 200,
//             xmlWhitespaceSensitivity: "ignore"
//         });
//
//         const backupFilePath = await saveBackup(filePath); // Save original file backup
//         await fs.writeFile(filePath, formattedXml, "utf-8");
//
//         const { hasDifferences, differences } = await compareWithBackup(filePath, backupFilePath); // Compare files
//         if (hasDifferences) {
//             await gitAdd(filePath);
//         }
//
//         if (hasDifferences && options.checkAndThrow) {
//             checkAndThrowResults.push({ filePath, differences }); // Add to check list if differences exist
//         }
//
//         await deleteBackup(backupFilePath); // Delete backup
//
//         if (Object.keys(duplicateReport).length > 0) {
//             allDuplicateReports[filePath] = duplicateReport; // Collect duplicate reports
//         } else {
//             console.log(chalk.green(`File ${filePath} sorted and updated successfully.`));
//         }
//     } catch (error) {
//         console.error(chalk.red(`Error processing file ${filePath}:`), error);
//         process.exit(1);
//     }
// }
//
// // Main function: Handle based on whether it's in stage mode
// async function formatProfilesAndPermissionSets(directory, options) {
//     const allDuplicateReports = {};
//     const checkAndThrowResults = []; // Used to collect files with differences
//
//     try {
//         let xmlFiles = [];
//
//         if (options.stage) {
//             xmlFiles = await getStagedFiles(); // Get staged files
//         } else {
//             xmlFiles = await findXmlFiles(directory); // Get files from directory
//         }
//
//         console.log(chalk.blue(`Found ${xmlFiles.length} files to format.`));
//
//         await Promise.all(
//             xmlFiles.map(async (file) => {
//                 await sortProfile(file, options, allDuplicateReports, checkAndThrowResults);
//             })
//         );
//
//         console.log(chalk.green("All files sorted and formatted successfully."));
//
//         if (Object.keys(allDuplicateReports).length > 0) {
//             console.error(chalk.red("Duplicates found in the following files:"));
//             Object.entries(allDuplicateReports).forEach(([filePath, report]) => {
//                 console.error(chalk.red(`File: ${filePath}`));
//                 console.error(chalk.red(`Duplicates: ${JSON.stringify(report, null, 2)}`));
//             });
//         }
//
//         // Unified check for differences and report errors
//         if (options.checkAndThrow && checkAndThrowResults.length > 0) {
//             console.error(chalk.red("The following files have changed after formatting:"));
//             console.error(chalk.red("Please format your code to maintain consistency."));
//             checkAndThrowResults.forEach(({ filePath, differences }) => {
//                 console.error(chalk.red(`File: ${filePath}`));
//                 console.error(chalk.yellow(differences)); // Output differences
//             });
//             process.exit(1); // Exit if there are files with differences
//         }
//
//         // Exit program if duplicates exist
//         if (Object.keys(allDuplicateReports).length > 0) {
//             process.exit(1);
//         }
//     } catch (error) {
//         console.error(chalk.red("Error formatting files:"), error);
//         process.exit(1);
//     }
// }
//
// // Command line argument parsing
// const args = process.argv.slice(2);
// const targetDirectory = args[0] || "./force-app";
// const options = {
//     checkAndThrow: args.includes("--checkAndThrow"),
//     removeDuplicate: args.includes("--removeDuplicate"),
//     stage: args.includes("--stage")
// };
//
// // Call the main function
// formatProfilesAndPermissionSets(targetDirectory, options);
