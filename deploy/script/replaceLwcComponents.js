const fs = require('fs');
const path = require('path');

// 递归删除目录下所有内容，但保留目录本身
function emptyDir(dir) {
  if (!fs.existsSync(dir)) return;
  for (const file of fs.readdirSync(dir)) {
    const curPath = path.join(dir, file);
    if (fs.lstatSync(curPath).isDirectory()) {
      fs.rmSync(curPath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(curPath);
    }
  }
}

// 递归复制目录
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) fs.mkdirSync(dest, { recursive: true });
  for (const file of fs.readdirSync(src)) {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);
    if (fs.lstatSync(srcPath).isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

const componentTargetDir = path.resolve(__dirname, '../../force-app/base/commonComponents/lwc');
const componentSourceDir = path.resolve(__dirname, '../../node_modules/@techops-e2ecs/storybook-lwc/src/modules/c');

emptyDir(componentTargetDir);
copyDir(componentSourceDir, componentTargetDir);

console.log('组件已替换完成');
