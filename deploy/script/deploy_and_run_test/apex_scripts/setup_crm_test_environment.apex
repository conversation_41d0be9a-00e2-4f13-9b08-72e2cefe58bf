System.debug('Start to setup crm ui test environment ');

User uicrmUser = [SELECT Id FROM User WHERE Name = 'uicrm' LIMIT 1];

Id uicrmUserId = uicrmUser.Id;
List<Id> recordNeedToDelete = new List<Id>();

// delete Opportunity
List<Opportunity> oppos = [SELECT Id FROM Opportunity WHERE Name IN ('testOpportunityName')];
delete oppos;

// delete specified Account
List<Account> accs = [SELECT Id FROM Account WHERE Name IN ('AsignToSalesNewForLead', 'newAddToAccountForLead', 'ExistingAccountForLead', 'newAsignToSalesAccountForContact', 'newAddtoMapforContact')];
for (Account acc : accs) {
    recordNeedToDelete.add(acc.Id);
}
delete accs;

// delete all leads
List<Lead> leads = [SELECT Id FROM Lead];
for (Lead lead : leads) {
    recordNeedToDelete.add(lead.Id);
}
delete leads;

//delete all tasks
List<Task> tasks = [SELECT Id FROM Task];
for (Task task : tasks) {
    recordNeedToDelete.add(task.Id);
}
delete tasks;

if (!recordNeedToDelete.isEmpty()) {
    Database.emptyRecycleBin(recordNeedToDelete);
}

// create lead for duplicate
List<Lead> leadsForTest = new List<Lead>();
// insert Accounts for existing Account
List<Account> accounts = new List<Account>();

Account existingAccount = new Account(Industry = 'Automotive', Name = 'ExistingAccountForLead', CurrencyIsoCode = 'USD', BillingCountry = 'China', OwnerId = uicrmUserId);
Account newAddtoMapforContact = new Account(Industry = 'Automotive', Name = 'newAddtoMapforContact', CurrencyIsoCode = 'USD', BillingCountry = 'China', OwnerId = uicrmUserId);

accounts.add(existingAccount);
accounts.add(newAddtoMapforContact);

insert accounts;

Opportunity oppo = new Opportunity(
    Name = 'testOpportunityName',
    CurrencyIsoCode = 'USD',
    Amount = ********.0,
    Contract_Type__c = 'Fixed Bid',
    CloseDate = Date.today(),
    StageName = 'Qualification',
    Delivery__c = 'Onshore',
    Opportunity_Region__c = 'US Central',
    Start_Date__c = Date.newInstance(2012, 7, 14),
    End_Date__c = Date.newInstance(2020, 7, 14),
    Approval_Criteria_Fulfilled__c = true,
    Timesheets_are_auto_approved_FF__c = true,
    Primary_Service__c = 'Consulting - IT Technology & Architecture',
    Region__c = 'Germany',
    Opportunity_Contract_Value__c = ********.0,
    TW_Contracting_Location_Picklist__c = 'Beijing',
    AccountId = accounts[0].id,
    OwnerId = uicrmUserId
);

insert oppo;

List<Contact> contacts = new List<Contact>();
List<Contact> contactsInsertLater = new List<Contact>();

Contact contactForWillNeverBuy = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = existingAccount.Id,
    Firstname = 'CRMTestData',
    Lastname = 'contactToTestWillNeverBuy',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId
);

Contact contactForNotSalesReady = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = existingAccount.Id,
    Firstname = 'CRMTestData',
    Lastname = 'contactToTestNotSalesReady',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId
);

Contact InterContactForRelationshipMapforAdd1 = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = newAddtoMapforContact.Id,
    Firstname = 'CRMTestData',
    Lastname = 'InterContactForRelationshipMapforAdd1',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId
);

Contact InterContactForRelationshipMapforAdd2 = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = newAddtoMapforContact.Id,
    Firstname = 'CRMTestData',
    Lastname = 'InterContactForRelationshipMapforAdd2',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId
);

Contact InterContactForRelationshipMapforEdit2 = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = newAddtoMapforContact.Id,
    Firstname = 'CRMTestData',
    Lastname = 'InterContactForRelationshipMapforEdit2',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId
);

Contact InterContactForRelationshipMapforRemove2 = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = newAddtoMapforContact.Id,
    Firstname = 'CRMTestData',
    Lastname = 'InterContactForRelationshipMapforRemove2',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId
);

contacts.add(contactForWillNeverBuy);
contacts.add(contactForNotSalesReady);
contacts.add(InterContactForRelationshipMapforAdd1);
contacts.add(InterContactForRelationshipMapforAdd2);
contacts.add(InterContactForRelationshipMapforEdit2);
contacts.add(InterContactForRelationshipMapforRemove2);

insert contacts;

Contact InterContactForRelationshipMapforEdit1 = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = newAddtoMapforContact.Id,
    Firstname = 'CRMTestData',
    Lastname = 'InterContactForRelationshipMapforEdit1',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId,
    ReportsToId = InterContactForRelationshipMapforEdit2.Id
);

Contact InterContactForRelationshipMapforRemove1 = new Contact(
    RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId(),
    AccountId = newAddtoMapforContact.Id,
    Firstname = 'CRMTestData',
    Lastname = 'InterContactForRelationshipMapforRemove1',
    MailingCountry = 'India',
    Status__c = 'Sales Accepted',
    LeadSource = 'Added by Sales – Salesforce or Cirrus Insight',
    Lead_Source_Details__c = 'Added by Sales – Salesforce or Cirrus Insight',
    OwnerId = uicrmUserId,
    ReportsToId = InterContactForRelationshipMapforRemove2.Id
);

contactsInsertLater.add(InterContactForRelationshipMapforEdit1);
contactsInsertLater.add(InterContactForRelationshipMapforRemove1);

insert contactsInsertLater;

Lead leadToTestAddToNewAccount = new Lead(
    RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
    FirstName = 'CRMTestData',
    LastName = 'leadToTestAddToNewAccount',
    Company = 'BigCampany',
    Country = 'India',
    LeadSource = 'Customer Referral',
    OwnerId = uicrmUserId
);

Lead leadToTestAddToExistingAccount = new Lead(
    RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
    FirstName = 'CRMTestData',
    LastName = 'leadToTestAddToExistingAccount',
    Company = 'BigCampany',
    Country = 'India',
    LeadSource = 'Customer Referral',
    OwnerId = uicrmUserId
);

Lead leadToTestAssignToNewAccount = new Lead(
    RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
    FirstName = 'CRMTestData',
    LastName = 'leadToTestAssignToNewAccount',
    Company = 'BigCampany',
    Country = 'India',
    LeadSource = 'Customer Referral',
    OwnerId = uicrmUserId
);

Lead leadToTestAssignToExistingAccount = new Lead(
    RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
    FirstName = 'CRMTestData',
    LastName = 'leadToTestAssignToExistingAccount',
    Company = 'BigCampany',
    Country = 'India',
    LeadSource = 'Customer Referral',
    OwnerId = uicrmUserId
);

Lead leadToTestWillNeverBuy = new Lead(
    RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
    FirstName = 'CRMTestData',
    LastName = 'leadToTestWillNeverBuy',
    Company = 'BigCampany',
    Country = 'India',
    LeadSource = 'Customer Referral',
    Status = 'Sales Accepted',
    OwnerId = uicrmUserId
);

Lead leadToTestNotSalesReady = new Lead(
    RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
    FirstName = 'CRMTestData',
    LastName = 'leadToTestNotSalesReady',
    Company = 'BigCampany',
    Country = 'India',
    LeadSource = 'Customer Referral',
    Status = 'Sales Accepted',
    OwnerId = uicrmUserId
);

leadsForTest.add(leadToTestAddToNewAccount);
leadsForTest.add(leadToTestAddToExistingAccount);
leadsForTest.add(leadToTestAssignToNewAccount);
leadsForTest.add(leadToTestAssignToExistingAccount);
leadsForTest.add(leadToTestWillNeverBuy);
leadsForTest.add(leadToTestNotSalesReady);

insert leadsForTest;
