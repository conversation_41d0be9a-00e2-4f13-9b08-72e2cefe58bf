try {
    ShareRecordRevokeProcessSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ShareRecordRevokeProcessSchedulable failed', ex);
}

try {
    HourlySyncExpensifyPoliciesSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job HourlySyncExpensifyPoliciesSchedulable failed', ex);
}

try {
    NextInvoiceDueDateScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job NextInvoiceDueDateScheduleJob failed', ex);
}

try {
    NextInvoiceDueDateFixedBidScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job NextInvoiceDueDateFixedBidScheduleJob failed', ex);
}

try {
    FixedBidCalculationScheduleJob.scheduleJobForDraft();
} catch (Exception ex) {
    printException('Start Job FixedBidCalculationBeforeDraftScheduleJob failed', ex);
}

try {
    FixedBidCalculationScheduleJob.scheduleJobForFinal();
} catch (Exception ex) {
    printException('Start Job FixedBidCalculationFinalScheduleJob failed', ex);
}

try {
    StandarCostRateSchedualeJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job StandardCostRate failed', ex);
}

try {
    ApexLogsCleanScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ApexLogsClean failed', ex);
}

try {
    MonthEndTimecardCreationSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Month End Timecard Creation failed', ex);
}

try {
    ReimbursementStatusUpdateSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Reimbursement Status Update Schedule Job failed', ex);
}

try {
    ExpReptStatusSyncToExpensifySchedule.scheduleJob();
} catch (Exception ex) {
    printException('Start Expense Report Status Sync to Expensify Job failed', ex);
}

try {
    AssignProfileFailureRetryScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Assign Profile Failure Retry Schedule Job failed', ex);
}

try {
    AssignPermissionSetRetryScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Assign Permission Set Failure Retry Schedule Job failed', ex);
}

try {
    FixedBidFinalReportReminderScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start FixedBid Final Report Reminder Schedule Job failed', ex);
}

try {
    SalesAppScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start SalesApp Schedule Job failed', ex);
}

try {
    AsyncApexJobFailedBackupScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AsyncApexJobFailedBackupScheduleJob failed', ex);
}

try {
    CampaignStatusUpdateScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job CampaignStatusUpdateScheduleJob failed', ex);
}

try {
    MarketoActivitySyncScheduledJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job MarketoActivitySyncScheduledJob failed', ex);
}

try {
    UpdateClientStatusScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job UpdateClientStatusScheduleJob failed', ex);
}

try {
    DepartmentScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job DepartmentScheduleJob failed', ex);
}

try {
    AutoInvoiceBEIScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AutoInvoiceBEIScheduleJob failed', ex);
}

try {
    DealApprovalScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job DealApprovalScheduleJob at 0min failed', ex);
}

try {
    DealApprovalScheduleJob.scheduleJob2();
} catch (Exception ex) {
    printException('Start Job DealApprovalScheduleJob at 15min failed', ex);
}

try {
    DealApprovalScheduleJob.scheduleJob3();
} catch (Exception ex) {
    printException('Start Job DealApprovalScheduleJob at 30min failed', ex);
}

try {
    DealApprovalScheduleJob.scheduleJob4();
} catch (Exception ex) {
    printException('Start Job DealApprovalScheduleJob at 45min failed', ex);
}

try {
    EventTrackingRetryScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job EventTrackingRetryScheduleJob failed', ex);
}

try {
    AutoAssignSalesFunnelAccessScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AutoAssignSalesFunnelAccessScheduleJob at 0min failed', ex);
}

try {
    AutoAssignSalesFunnelAccessScheduleJob.scheduleJob2();
} catch (Exception ex) {
    printException('Start Job AutoAssignSalesFunnelAccessScheduleJob at 15min failed', ex);
}

try {
    AutoAssignSalesFunnelAccessScheduleJob.scheduleJob3();
} catch (Exception ex) {
    printException('Start Job AutoAssignSalesFunnelAccessScheduleJob at 30min failed', ex);
}

try {
    AutoAssignSalesFunnelAccessScheduleJob.scheduleJob4();
} catch (Exception ex) {
    printException('Start Job AutoAssignSalesFunnelAccessScheduleJob at 45min failed', ex);
}

try {
    DraftContractNotificationJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job DraftContractNotificationJob failed', ex);
}

try {
    SyncMarketoIdScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job SyncMarketoIdScheduleJob failed', ex);
}

try {
    CountNumOfEngagementThisYearScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job CountNumOfEngagementThisYearScheduleJob failed', ex);
}

try {
    GenerateEVAByAssignmentScheduleJob.scheduleJobs();
} catch (Exception ex) {
    printException('Start Job GenerateEVAByAssignmentScheduleJob failed', ex);
}

try {
    AutoRefreshAbsenceRequestApprover.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AutoRefreshAbsenceRequestApprover failed', ex);
}

try {
    ContactMAExpiredDateScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ContactMAExpiredDateScheduleJob failed', ex);
}

private void printException(String message, Exception ex) {
    System.debug(message);
    System.debug(ex.getMessage());
    System.debug(ex.getStackTraceString());
}
