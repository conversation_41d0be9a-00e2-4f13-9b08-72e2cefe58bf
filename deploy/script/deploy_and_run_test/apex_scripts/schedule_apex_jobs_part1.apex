//Must run this script as "PSA Automation", all the job time are based on "PSA Automation" timezone.
try {
    SyncOppoPermAuditLogMonthlyScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job SyncOppoPermAuditLogMonthlyScheduleJob failed', ex);
}

try {
    AdoptionTrackingWeeklyScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AdoptionTrackingWeeklyScheduleJob failed', ex);
}

try {
    AdoptionTrackingMonthlyScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AdoptionTrackingMonthlyScheduleJob failed', ex);
}

try {
    AppLogsMonitor.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AppLogsMonitor failed', ex);
}

try {
    AutoDeleteTimecardCustomSettingSchedule.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AutoDeleteTimecardCustomSettingSchedule failed', ex);
}

try {
    MonitorAsyncApexJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job MonitorAsyncApexJob failed', ex);
}

try {
    ReassignObjectsSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ReassignObjectsSchedulable failed', ex);
}

try {
    RemovePSELicense.scheduleJob();
} catch (Exception ex) {
    printException('Start Job RemovePSELicense failed', ex);
}

try {
    ExpenseReportClearScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ExpenseReportClearScheduleJob failed', ex);
}

try {
    AutoDeactivateProjectSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AutoDeactivateProjectSchedulable failed', ex);
}

try {
    // Schedule PSA Invoice Draft Submitter and Prepocessor jobs
    NotificationWhenPaymentOverdue.scheduleJob();
} catch (Exception ex) {
    printException('Start Job NotificationWhenPaymentOverdue failed', ex);
}

try {
    // Schedule CPNotificationScheduleJob job
    CPNotificationScheduleJob p = new CPNotificationScheduleJob();
    String sch = '0 1 0 * * ?';
    system.schedule('CPNotification', sch, p);
} catch (Exception ex) {
    printException('Start Job CPNotificationScheduleJob failed', ex);
}

try {
    FF_Expense_Report_Update_Schedule.scheduleJob();
} catch (Exception ex) {
    printException('Start Job FF_Expense_Report_Update_Schedule failed', ex);
}

try {
    ExpenseRawDataProcessSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ExpenseRawDataProcessSchedulable failed', ex);
}

try {
    ExpensesUnapprovedCountingScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ExpensesUnapprovedCountingScheduleJob failed', ex);
}

try {
    SIPendingApprovalScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job SIPendingApprovalScheduleJob failed', ex);
}

try {
    AutoCloseForTimeExpenseEntry.scheduleJob();
} catch (Exception ex) {
    printException('Start Job AutoCloseForTimeExpenseEntry failed', ex);
}

try {
    GuestStayNotificationJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job GuestStayNotificationJob failed', ex);
}

try {
    GuestStayValidationJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job GuestStayValidationJob failed', ex);
}

try {
    ClearCorpApartAutoCodingInfoScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job GuestStayNotificationJob failed', ex);
}

try {
    FillDateTimecardEnteredScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job FillDateTimecardEnteredScheduleJob failed', ex);
}

try {
    UserExitScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job UserExitScheduleJob failed', ex);
}

try {
    MonthlyClearDataJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job MonthlyClearDataJob failed', ex);
}

try {
    UserFieldsResetScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job UserFieldsResetScheduleJob failed', ex);
}

try {
    InviteUserToExpensifyScheduledJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job InviteUserToExpensifyScheduledJob failed', ex);
}

try {
    CurrencyUpdateSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job CurrencyUpdateSchedulable failed', ex);
}

try {
    SalesInvoiceReassignSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job SalesInvoiceReassignSchedulable failed', ex);
}

try {
    ShareRecordProcessSchedulable.scheduleJob();
} catch (Exception ex) {
    printException('Start Job ShareRecordProcessSchedulable failed', ex);
}

try {
    LeadContactAutoRecycledScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job LeadContactAutoRecycledScheduleJob failed', ex);
}


try {
    CRMLicenseAssignmentScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job CRMLicenseAssignmentScheduleJob failed', ex);
}

try {
    SendEmailToTravelTeamScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job CRMLicenseAssignmentScheduleJob failed', ex);
}

try {
    InfluenceAttributionValueScheduleJob.scheduleJob();
} catch (Exception ex) {
    printException('Start Job InfluenceAttributionValueScheduleJob failed', ex);
}

private void printException(String message, Exception ex) {
    System.debug(message);
    System.debug(ex.getMessage());
    System.debug(ex.getStackTraceString());
}
