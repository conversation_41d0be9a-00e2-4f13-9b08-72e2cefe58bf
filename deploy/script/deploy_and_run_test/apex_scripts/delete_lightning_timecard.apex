Set<Id> testUserIds = new Map<Id, User>(
        [SELECT id FROM User WHERE Username LIKE '<EMAIL>%' OR Username LIKE '<EMAIL>%' OR Username LIKE '<EMAIL>%']
    )
    .keySet();

Database.delete([SELECT id FROM pse__Timecard_Header__c WHERE pse__Resource__r.pse__Salesforce_User__c IN :testUserIds], true);

// Clear billing data
Set<Id> bebIds = new Map<Id, pse__Billing_Event_Batch__c>([SELECT id FROM pse__Billing_Event_Batch__c]).keySet();

List<pse__Billing_Event__c> beList = new BillingEventSelector().getBEsByBEBId(bebIds);
List<pse__Billing_Event_Batch__c> bebList = new BillingEventBatchSelector().getBillingEventBatchById(bebIds).values();

Set<pse__Billing_Event__c> releasedBEs = new Set<pse__Billing_Event__c>();
Set<pse__Billing_Event_Batch__c> releasedBEBs = new Set<pse__Billing_Event_Batch__c>();
for (pse__Billing_Event_Batch__c beb : bebList) {
    beb.pse__Is_Released__c = false;
    releasedBEBs.add(beb);
}
for (pse__Billing_Event__c be : beList) {
    be.pse__Is_Released__c = false;
    releasedBEs.add(be);
}
update new List<pse__Billing_Event_Batch__c>(releasedBEBs);
update new List<pse__Billing_Event__c>(releasedBEs);

List<pse__Billing_Event_Item__c> beiList = new BillingEventItemSelector().getBillingEventItemByBillingEventBatch(bebList);

Set<Id> beiIds = new Map<Id, pse__Billing_Event_Item__c>(beiList).keySet();
Set<Id> beIds = new Map<Id, pse__Billing_Event__c>(beList).keySet();
Integer batchSize = 200;

// Release timecard split
List<pse__Timecard__c> timecards = [SELECT Id FROM pse__Timecard__c];
for (pse__Timecard__c timecard : timecards) {
    timecard.pse__Billed__c = false;
    timecard.pse__Bill_Date__c = null;
    timecard.pse__Invoiced__c = false;
    timecard.pse__Invoice_Number__c = '';
    timecard.pse__Invoice_Date__c = null;
}
update timecards;

// Release milestone
List<pse__Milestone__c> milestones = [SELECT Id FROM pse__Milestone__c WHERE pse__Billing_Event_Item__c IN :beiIds OR pse__Billed__c = TRUE OR pse__Invoiced__c = TRUE];
for (pse__Milestone__c milestone : milestones) {
    milestone.pse__Billed__c = false;
    milestone.pse__Bill_Date__c = null;
    milestone.pse__Invoiced__c = false;
    milestone.pse__Invoice_Date__c = null;
    milestone.pse__Invoice_Number__c = '';
}
update milestones;

// Release misc
List<pse__Miscellaneous_Adjustment__c> maList = [SELECT Id FROM pse__Miscellaneous_Adjustment__c WHERE pse__Billing_Event_Item__c IN :beiIds OR pse__Billed__c = TRUE OR pse__Invoiced__c = TRUE];
for (pse__Miscellaneous_Adjustment__c ma : maList) {
    ma.pse__Billed__c = false;
    ma.pse__Bill_Date__c = null;
    ma.pse__Invoiced__c = false;
    ma.pse__Invoice_Date__c = null;
    ma.pse__Invoice_Number__c = '';
}
update maList;

// Delete billing event items
List<pse__Billing_Event_Item__c> existedBEIs = [SELECT Id FROM pse__Billing_Event_Item__c WHERE Id IN :beiIds];
for (pse__Billing_Event_Item__c bei : existedBEIs) {
    bei.pse__Billing_Event__c = null;
}
update existedBEIs;
delete existedBEIs;

// Delete billing event
List<pse__Billing_Event__c> beLists = [SELECT Id, pse__Invoiced__c FROM pse__Billing_Event__c WHERE Id IN :beList];
for (pse__Billing_Event__c be : beLists) {
    if (be.pse__Invoiced__c) {
        pse.APIBillingService.BillingContextClear bcc = new pse.APIBillingService.BillingContextClear();
        bcc.id = be.Id;
        bcc.objectType = pse.APIBillingService.ObjectTypesClear.event;
        pse.APICommonsService.BatchStatus bs = pse.APIBillingService.clear(bcc);
        if ('Error' == bs.status) {
            PSAEmail.sendEmailToPsaTeam('Clear BE error: ' + be.Id, bs.errorMessage);
        }
    } else {
        delete be;
    }
}

// Delete billing event batches
List<pse__Billing_Event_Batch__c> bebLists = [SELECT Id, Sales_Invoice__c FROM pse__Billing_Event_Batch__c WHERE Id IN :bebList];
Set<Id> remainingIds = new Set<Id>();
for (pse__Billing_Event_Batch__c beb : bebLists) {
    beb.Sales_Invoice__c = null;
    remainingIds.add(beb.Id);
}
update bebLists;

Set<Id> successSet = new Set<Id>();
for (Integer countNum = 1; countNum < 5 && remainingIds.size() > 0; countNum = countNum + 1) {
    Database.DeleteResult[] drList = Database.delete(bebLists, false);
    for (Database.DeleteResult dr : drList) {
        if (dr.isSuccess()) {
            successSet.add(dr.getId());
        }
    }
    remainingIds.removeAll(successSet);
    successSet = new Set<Id>();
}

//clear data
Database.delete([SELECT id FROM pse__Transaction__c LIMIT 200], false);
Database.delete([SELECT id FROM pse__Est_Vs_Actuals__c LIMIT 200], false);
Database.delete([SELECT id FROM pse__Transaction_Delta__c LIMIT 200], false);
Database.delete([SELECT id FROM Finance_Invoicing_Event__c LIMIT 200], false);
Database.delete([SELECT id FROM Timecard_Log__c LIMIT 200], false);
Database.delete([SELECT id FROM Apex_Log__c LIMIT 200], false);
