import argparse
import os
import sys

from modules.deploy_test_manager import Deploy<PERSON>est<PERSON>anager
from modules.thread_pool import Thread<PERSON><PERSON>

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-u", '--usernames', type=str, dest="usernames", help="Usermanes of sandboxes")
    parser.add_argument("-p", "--password", type=str, dest="password",  help="Password of sandbox")
    parser.add_argument('-t', "--token", type=str, dest="token", help='Password of sandbox')
    parser.add_argument("-r", "--run_test_type", dest="run_test_type", help="Which types of test should be run", default="")
    parser.add_argument("-s", "--run_test_scope", dest="run_test_scope", help="Which scope of test should be run, required, optional or all", default="required")
    parser.add_argument("-d", "--deploy_to", type=str, dest="deploy_to", help="Deploy to Target", default="Sandbox")

    args = parser.parse_args()

    assert "usernames" in args, "Should specify usernames"
    assert "password" in args, "Should specify password"
    assert "token" in args, "Should specify token"

    os.environ["MAIN_FILE_PATH"] = os.path.dirname(os.path.abspath(sys.argv[0]))
    kwargs = {
                "usernames": args.usernames,
                "password": args.password,
                "token": args.token,
                "run_test_type": args.run_test_type,
                "run_test_scope": args.run_test_scope,
                "deploy_to": args.deploy_to,
                "common_thread_pool": ThreadPool(processes=30)
              }
    os.chdir(f"{os.environ['MAIN_FILE_PATH']}/../../../")
    DeployTestManager(**kwargs).start_deploy_and_run_test()



