ACTION_LINK_GROUP_TEMPLATE = 'ActionLinkGroupTemplate'
ANALYTIC_SNAPSHOT = 'AnalyticSnapshot'
ARTICLE_TYPE = 'ArticleType'
APEX_CLASS = 'ApexClass'
APEX_COMPONENT = 'ApexComponent'
APEX_PAGE = 'ApexPage'
APEX_TEST_SUITE = 'ApexTestSuite'
APEX_TRIGGER = 'ApexTrigger'
APP_MENU = 'AppMenu'
APPROVAL_PROCESS = 'ApprovalProcess'
ASSIGNMENT_RULES = 'AssignmentRules'
AURA_DEFINITION_BUNDLE = 'AuraDefinitionBundle'
AUTH_PROVIDER = 'AuthProvider'
AUTO_RESPONSE_RULES = 'AutoResponseRules'
BOT_VERSION = 'BotVersion'
BRANDING_SET = 'BrandingSet'
CALL_CENTER = 'CallCenter'
CAMPAIGN_INFLUENCE_MODEL = 'CampaignInfluenceModel'
CASE_SUBJECT_PARTICLE = 'CaseSubjectParticle'
CERTIFICATE = 'Certificate'
CHATTER_EXTENSION = 'ChatterExtension'
CLEAN_DATA_SERVICE = 'CleanDataService'
C_M_S_CONNECT_SOURCE = 'CMSConnectSource'
COMMUNITY = 'Community'
COMMUNITY_TEMPLATE_DEFINITION = 'CommunityTemplateDefinition'
COMMUNITY_THEME_DEFINITION = 'CommunityThemeDefinition'
CONNECTED_APP = 'ConnectedApp'
CONTENT_ASSET = 'ContentAsset'
CORS_WHITELIST_ORIGIN = 'CorsWhitelistOrigin'
CSP_TRUSTED_SITE = 'CspTrustedSite'
CUSTOM_APPLICATION = 'CustomApplication'
CUSTOM_APPLICATION_COMPONENT = 'CustomApplicationComponent'
CUSTOM_FEED_FILTER = 'CustomFeedFilter'
CUSTOM_LABELS = 'CustomLabels'
CUSTOM_OBJECT = 'CustomObject'
CUSTOM_OBJECT_TRANSLATION = 'CustomObjectTranslation'
CUSTOM_PAGE_WEB_LINK = 'CustomPageWebLink'
CUSTOM_PERMISSION = 'CustomPermission'
CUSTOM_SITE = 'CustomSite'
CUSTOM_TAB = 'CustomTab'
CUSTOM_VALUE = 'CustomValue'
DASHBOARD = 'Dashboard'
DATA_CATEGORY_GROUP = 'DataCategoryGroup'
DELEGATE_GROUP = 'DelegateGroup'
DOCUMENT = 'Document'
DUPLICATE_RULE = 'DuplicateRule'
ECLAIR_GEO_DATA = 'EclairGeoData'
EMAIL_SERVICES_FUNCTION = 'EmailServicesFunction'
EMAIL_TEMPLATE = 'EmailTemplate'
EMBEDDED_SERVICE_BRANDING = 'EmbeddedServiceBranding'
EMBEDDED_SERVICE_CONFIG = 'EmbeddedServiceConfig'
EMBEDDED_SERVICE_FIELD_SERVICE = 'EmbeddedServiceFieldService'
EMBEDDED_SERVICE_LIVE_AGENT = 'EmbeddedServiceLiveAgent'
ENTITLEMENT_PROCESS = 'EntitlementProcess'
ENTITLEMENT_TEMPLATE = 'EntitlementTemplate'
ESCALATION_RULES = 'EscalationRules'
EVENT_DELIVERY = 'EventDelivery'
EVENT_SUBSCRIPTION = 'EventSubscription'
EXTERNAL_DATA_SOURCE = 'ExternalDataSource'
EXTERNAL_SERVICE_REGISTRATION = 'ExternalServiceRegistration'
FEATURE_PARAMETER_BOOLEAN = 'FeatureParameterBoolean'
FEATURE_PARAMETER_DATE = 'FeatureParameterDate'
FEATURE_PARAMETER_INTEGER = 'FeatureParameterInteger'
FLEXI_PAGE = 'FlexiPage'
FLOW = 'Flow'
FLOW_CATEGORY = 'FlowCategory'
FLOW_DEFINITION = 'FlowDefinition'
FOLDER = 'Folder'
GLOBAL_PICKLIST = 'GlobalPicklist'
GLOBAL_PICKLIST_VALUE = 'GlobalPicklistValue'
GLOBAL_VALUE_SET = 'GlobalValueSet'
GLOBAL_VALUE_SET_TRANSLATION = 'GlobalValueSetTranslation'
GROUP = 'Group'
HOME_PAGE_COMPONENT = 'HomePageComponent'
HOME_PAGE_LAYOUT = 'HomePageLayout'
INSTALLED_PACKAGE = 'InstalledPackage'
KEYWORD_LIST = 'KeywordList'
LAYOUT = 'Layout'
LETTERHEAD = 'Letterhead'
LIGHTNING_BOLT = 'LightningBolt'
LIGHTNING_COMPONENT_BUNDLE = 'LightningComponentBundle'
LIGHTNING_EXPERIENCE_THEME = 'LightningExperienceTheme'
LIVE_CHAT_AGENT_CONFIG = 'LiveChatAgentConfig'
LIVE_CHAT_BUTTON = 'LiveChatButton'
LIVE_CHAT_DEPLOYMENT = 'LiveChatDeployment'
LIVE_CHAT_SENSITIVE_DATA_RULE = 'LiveChatSensitiveDataRule'
MANAGED_TOPICS = 'ManagedTopics'
MATCHING_RULE = 'MatchingRule'
METADATA = 'Metadata'
METADATA_WITH_CONTENT = 'MetadataWithContent'
MILESTONE_TYPE = 'MilestoneType'
ML_DOMAIN = 'MlDomain'
MODERATION_RULE = 'ModerationRule'
NAMED_CREDENTIAL = 'NamedCredential'
NETWORK = 'Network'
NETWORK_BRANDING = 'NetworkBranding'
PACKAGE = 'Package'
PATH_ASSISTANT = 'PathAssistant'
PERMISSION_SET = 'PermissionSet'
PLATFORM_CACHE_PARTITION = 'PlatformCachePartition'
PORTAL = 'Portal'
POST_TEMPLATE = 'PostTemplate'
PRESENCE_DECLINE_REASON = 'PresenceDeclineReason'
PRESENCE_USER_CONFIG = 'PresenceUserConfig'
PROFILE = 'Profile'
PROFILE_ACTION_OVERRIDE = 'ProfileActionOverride'
PROFILE_PASSWORD_POLICY = 'ProfilePasswordPolicy'
QUEUE = 'Queue'
QUEUE_ROUTING_CONFIG = 'QueueRoutingConfig'
QUICK_ACTION = 'QuickAction'
REMOTE_SITE_SETTING = 'RemoteSiteSetting'
REPORT = 'Report'
REPORT_TYPE = 'ReportType'
ROLE = 'Role'
ROLE_OR_TERRITORY = 'RoleOrTerritory'
SAML_SSO_CONFIG = 'SamlSsoConfig'
SCONTROL = 'Scontrol'
SETTING = 'Setting'
SERVICE_CHANNEL = 'ServiceChannel'
SERVICE_PRESENCE_STATUS = 'ServicePresenceStatus'
SHARED_TO = 'SharedTo'
SHARING_BASE_RULE = 'SharingBaseRule'
SHARING_RULES = 'SharingRules'
SHARING_SET = 'SharingSet'
SITE_DOT_COM = 'SiteDotCom'
SKILL = 'Skill'
STANDARD_VALUE_SET = 'StandardValueSet'
STANDARD_VALUE_SET_TRANSLATION = 'StandardValueSetTranslation'
STATIC_RESOURCE = 'StaticResource'
SYNONYM_DICTIONARY = 'SynonymDictionary'
TERRITORY = 'Territory'
TERRITORY2 = 'Territory2'
TERRITORY2_MODEL = 'Territory2Model'
TERRITORY2_RULE = 'Territory2Rule'
TERRITORY2_TYPE = 'Territory2Type'
TOPICS_FOR_OBJECTS = 'TopicsForObjects'
TRANSACTION_SECURITY_POLICY = 'TransactionSecurityPolicy'
TRANSLATIONS = 'Translations'
USER_CRITERIA = 'UserCriteria'
WAVE_APPLICATION = 'WaveApplication'
WAVE_DATAFLOW = 'WaveDataflow'
WAVE_DASHBOARD = 'WaveDashboard'
WAVE_DATASET = 'WaveDataset'
WAVE_LENS = 'WaveLens'
WAVE_TEMPLATE_BUNDLE = 'WaveTemplateBundle'
WAVE_XMD = 'WaveXmd'
WORKFLOW = 'Workflow'

EXTENSION_TO_TYPE = {
    'app': CUSTOM_APPLICATION,
    'appMenu': APP_MENU,
    'approvalProcess': APPROVAL_PROCESS,
    'assignmentRules': ASSIGNMENT_RULES,
    'autoResponseRules': AUTO_RESPONSE_RULES,
    'cls': APEX_CLASS,
    'community': COMMUNITY,
    'component': APEX_COMPONENT,
    'connectedApp': CONNECTED_APP,
    'crt': CERTIFICATE,
    'customPermission': CUSTOM_PERMISSION,
    'dashboard': DASHBOARD,
    'duplicateRule': DUPLICATE_RULE,
    'dataSource': EXTERNAL_DATA_SOURCE,
    'email': EMAIL_TEMPLATE,
    'escalationRules': ESCALATION_RULES,
    'flexipage': FLEXI_PAGE,
    'globalValueSet': GLOBAL_VALUE_SET,
    'group': GROUP,
    'homePageLayout': HOME_PAGE_LAYOUT,
    'labels': CUSTOM_LABELS,
    'layout': LAYOUT,
    'letter': LETTERHEAD,
    'managedTopics': MANAGED_TOPICS,
    'matchingRule': MATCHING_RULE,
    'network': NETWORK,
    'object': CUSTOM_OBJECT,
    'objectTranslation': CUSTOM_OBJECT_TRANSLATION,
    'page': APEX_PAGE,
    'permissionset': PERMISSION_SET,
    'profile': PROFILE,
    'queue': QUEUE,
    'quickAction': QUICK_ACTION,
    'remoteSite': REMOTE_SITE_SETTING,
    'reportType': REPORT_TYPE,
    'report': REPORT,
    'resource': STATIC_RESOURCE,
    'role': ROLE,
    'settings': SETTING,
    'sharingRules': SHARING_RULES,
    'standardValueSet': STANDARD_VALUE_SET,
    'site': CUSTOM_SITE,
    'tab': CUSTOM_TAB,
    'translation': TRANSLATIONS,
    'territory2Type': TERRITORY2_TYPE,
    'trigger': APEX_TRIGGER,
    'workflow': WORKFLOW
}


FOLDER_TO_TYPE = {
    'actionLinkGroupTemplates' : ACTION_LINK_GROUP_TEMPLATE,
    'analyticSnapshots' : ANALYTIC_SNAPSHOT,
    'articleTypes' : ARTICLE_TYPE,
    'classes' : APEX_CLASS,
    'components' : APEX_COMPONENT,
    'pages' : APEX_PAGE,
    'apexTestSuite' : APEX_TEST_SUITE,
    'triggers' : APEX_TRIGGER,
    'appMenus' : APP_MENU,
    'approvalProcesses' : APPROVAL_PROCESS,
    'assignmentRules' : ASSIGNMENT_RULES,
    'aura' : AURA_DEFINITION_BUNDLE,
    'authProviders' : AUTH_PROVIDER,
    'autoResponseRules' : AUTO_RESPONSE_RULES,
    'bots' : BOT_VERSION,
    'brandingSets' : BRANDING_SET,
    'callCenters' : CALL_CENTER,
    'campaignInfluenceModels' : CAMPAIGN_INFLUENCE_MODEL,
    'caseSubjectParticles' : CASE_SUBJECT_PARTICLE,
    'certs' : CERTIFICATE,
    'chatterExtensions' : CHATTER_EXTENSION,
    'cleanDataServices' : CLEAN_DATA_SERVICE,
    'cMSConnectSources' : C_M_S_CONNECT_SOURCE,
    'communities' : COMMUNITY,
    'communityTemplateDefinitions' : COMMUNITY_TEMPLATE_DEFINITION,
    'communityThemeDefinitions' : COMMUNITY_THEME_DEFINITION,
    'connectedApps' : CONNECTED_APP,
    'contentAssets' : CONTENT_ASSET,
    'corsWhitelistOrigins' : CORS_WHITELIST_ORIGIN,
    'cspTrustedSite' : CSP_TRUSTED_SITE,
    'applications' : CUSTOM_APPLICATION,
    'applicationComponents' : CUSTOM_APPLICATION_COMPONENT,
    'feedFilters' : CUSTOM_FEED_FILTER,
    'labels' : CUSTOM_LABELS,
    'objects' : CUSTOM_OBJECT,
    'objectTranslations' : CUSTOM_OBJECT_TRANSLATION,
    'customPageWebLink' : CUSTOM_PAGE_WEB_LINK,
    'permissions' : CUSTOM_PERMISSION,
    'sites' : CUSTOM_SITE,
    'tabs' : CUSTOM_TAB,
    'values' : CUSTOM_VALUE,
    'customValues' : CUSTOM_VALUE,
    'dashboards' : DASHBOARD,
    'dataCategoryGroups' : DATA_CATEGORY_GROUP,
    'delegateGroups' : DELEGATE_GROUP,
    'documents' : DOCUMENT,
    'duplicateRules' : DUPLICATE_RULE,
    'eclairGeoDatas' : ECLAIR_GEO_DATA,
    'emailServices' : EMAIL_SERVICES_FUNCTION,
    'email' : EMAIL_TEMPLATE,
    'embeddedServiceBrandings' : EMBEDDED_SERVICE_BRANDING,
    'embeddedServiceConfigs' : EMBEDDED_SERVICE_CONFIG,
    'embeddedServiceFieldServices' : EMBEDDED_SERVICE_FIELD_SERVICE,
    'embeddedServiceLiveAgents' : EMBEDDED_SERVICE_LIVE_AGENT,
    'entitlementProcesses' : ENTITLEMENT_PROCESS,
    'entitlementTemplates' : ENTITLEMENT_TEMPLATE,
    'escalationRules' : ESCALATION_RULES,
    'eventDeliveries' : EVENT_DELIVERY,
    'eventSubscriptions' : EVENT_SUBSCRIPTION,
    'externalDataSources' : EXTERNAL_DATA_SOURCE,
    'externalServiceRegistrations' : EXTERNAL_SERVICE_REGISTRATION,
    'featureParameterBooleans' : FEATURE_PARAMETER_BOOLEAN,
    'featureParameterDates' : FEATURE_PARAMETER_DATE,
    'featureParameterIntegers' : FEATURE_PARAMETER_INTEGER,
    'flexiPages' : FLEXI_PAGE,
    'flows' : FLOW,
    'flowCategories' : FLOW_CATEGORY,
    'flowDefinitions' : FLOW_DEFINITION,
    'folders' : FOLDER,
    'globalPicklists' : GLOBAL_PICKLIST,
    'globalPicklistValues' : GLOBAL_PICKLIST_VALUE,
    'globalValueSets' : GLOBAL_VALUE_SET,
    'globalValueSetTranslations' : GLOBAL_VALUE_SET_TRANSLATION,
    'groups' : GROUP,
    'homePageComponents' : HOME_PAGE_COMPONENT,
    'homePageLayouts' : HOME_PAGE_LAYOUT,
    'installedPackages' : INSTALLED_PACKAGE,
    'keywordLists' : KEYWORD_LIST,
    'layouts' : LAYOUT,
    'letterheads' : LETTERHEAD,
    'lightningBolts' : LIGHTNING_BOLT,
    'lwc' : LIGHTNING_COMPONENT_BUNDLE,
    'lightningExperienceThemes' : LIGHTNING_EXPERIENCE_THEME,
    'liveChatAgentConfigs' : LIVE_CHAT_AGENT_CONFIG,
    'liveChatButtons' : LIVE_CHAT_BUTTON,
    'liveChatDeployments' : LIVE_CHAT_DEPLOYMENT,
    'liveChatSensitiveDataRules' : LIVE_CHAT_SENSITIVE_DATA_RULE,
    'managedTopics' : MANAGED_TOPICS,
    'matchingRules' : MATCHING_RULE,
    'metadata' : METADATA,
    'metadataWithContents' : METADATA_WITH_CONTENT,
    'milestoneTypes' : MILESTONE_TYPE,
    'mlDomains' : ML_DOMAIN,
    'moderationRules' : MODERATION_RULE,
    'namedCredentials' : NAMED_CREDENTIAL,
    'networks' : NETWORK,
    'networkBrandings' : NETWORK_BRANDING,
    'packages' : PACKAGE,
    'pathAssistants' : PATH_ASSISTANT,
    'permissionSets' : PERMISSION_SET,
    'platformCachePartitions' : PLATFORM_CACHE_PARTITION,
    'portals' : PORTAL,
    'postTemplates' : POST_TEMPLATE,
    'presenceDeclineReasons' : PRESENCE_DECLINE_REASON,
    'presenceUserConfigs' : PRESENCE_USER_CONFIG,
    'profiles' : PROFILE,
    'profileActionOverrides' : PROFILE_ACTION_OVERRIDE,
    'profilePasswordPolicies' : PROFILE_PASSWORD_POLICY,
    'queues' : QUEUE,
    'queueRoutingConfigs' : QUEUE_ROUTING_CONFIG,
    'quickActions' : QUICK_ACTION,
    'remoteSiteSettings' : REMOTE_SITE_SETTING,
    'reports' : REPORT,
    'reportTypes' : REPORT_TYPE,
    'roles' : ROLE,
    'roleOrTerritorys' : ROLE_OR_TERRITORY,
    'samlSsoConfigs' : SAML_SSO_CONFIG,
    'scontrols' : SCONTROL,
    'serviceChannels' : SERVICE_CHANNEL,
    'servicePresenceStatus' : SERVICE_PRESENCE_STATUS,
    'sharedTos' : SHARED_TO,
    'sharingBaseRules' : SHARING_BASE_RULE,
    'sharingRules' : SHARING_RULES,
    'sharingSets' : SHARING_SET,
    'siteDotComs' : SITE_DOT_COM,
    'skills' : SKILL,
    'standardValueSets' : STANDARD_VALUE_SET,
    'standardValueSetTranslations' : STANDARD_VALUE_SET_TRANSLATION,
    'staticResources' : STATIC_RESOURCE,
    'synonymDictionaries' : SYNONYM_DICTIONARY,
    'territories' : TERRITORY,
    'territory2s' : TERRITORY2,
    'territory2Models' : TERRITORY2_MODEL,
    'territory2Rules' : TERRITORY2_RULE,
    'territory2Types' : TERRITORY2_TYPE,
    'topicsForObjects' : TOPICS_FOR_OBJECTS,
    'transactionSecurityPolicies' : TRANSACTION_SECURITY_POLICY,
    'translations' : TRANSLATIONS,
    'userCriterias' : USER_CRITERIA,
    'waveApplications' : WAVE_APPLICATION,
    'waveDataflows' : WAVE_DATAFLOW,
    'waveDashboards' : WAVE_DASHBOARD,
    'waveDatasets' : WAVE_DATASET,
    'waveLens' : WAVE_LENS,
    'waveTemplateBundles' : WAVE_TEMPLATE_BUNDLE,
    'waveXmds' : WAVE_XMD,
    'workflows' : WORKFLOW
}
