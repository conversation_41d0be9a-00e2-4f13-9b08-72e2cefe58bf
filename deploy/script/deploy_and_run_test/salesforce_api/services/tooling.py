from .. import core, config
from ..models import tooling as tooling_models
from . import base
from modules.loggger import CILogger
import time, requests
import math
from modules.thread_pool import ThreadPool

# https://developer.salesforce.com/docs/atlas.en-us.api_tooling.meta/api_tooling/intro_rest_resources.htm
class Tooling(base.RestService):
    LIMIT_OF_SUB_REQUEST_IN_ONE_COMPOSITE_REQUEST = 25

    def __init__(self, connection: core.Connection):
        super().__init__(connection, 'tooling')

    def completions(self, sf_type: str):
        return self._get('completions', {'type': sf_type})

    def execute_apex(self, body: str):
        return tooling_models.ApexExecutionResult.create(
            self._get('executeAnonymous', {'anonymousBody': body})
        )

    def execute_apex_from_file(self, file_path: str):
        return self.execute_apex(
            open(file_path, 'r').read()
        )

    def format_composite_sub_request_url(self, url: str = None) -> str:
        parts = ['/', self.base_uri]
        if url is not None:
            parts.append(url)
        return str('/'.join([
            x.strip('/')
            for x in parts
            if x is not None and x != ''
        ])).format(
            version=self.connection.version
        )

    def run_composite_request(self, json: dict = None):
        return self._post('composite', json)

    def composite_delete_tooling_object(self, object_name: str, id_list: list = None):
        composite_sub_request_list = []
        base_url = self.format_composite_sub_request_url(
            'sobjects')+'/{objectName}/{Id}'
        for index, id in enumerate(id_list):
            sub_request = {
                "method": "DELETE",
                "url": base_url.format(objectName=object_name, Id=id),
                "referenceId": str(index)
            }
            composite_sub_request_list.append(sub_request)
        json = {
            "allOrNone": False,
            "compositeRequest": composite_sub_request_list
        }
        self.run_composite_request(json)

    def batch_delete_tooling_object(self, object_name: str):
        pool = ThreadPool(processes=100)
        delete_pool = pool.new_pool_status(exit_for_any_exception=False)

        objects = self.query_all_when_meet_query_return_limit('SELECT Id FROM '+ object_name)
        object_list_need_to_delete = [object.get("Id") for object in objects]
        spited_object_list_list = self.split_list_by_composite_sub_request_limit(
            object_list_need_to_delete)
        for list in spited_object_list_list:
            delete_pool.apply_async(
                self.composite_delete_tooling_object, (object_name, list))
        delete_pool.get_threads_result(raise_exception=True)

    def split_list_by_composite_sub_request_limit(self, list_need_to_split: list):
        return [list_need_to_split[i:i+self.LIMIT_OF_SUB_REQUEST_IN_ONE_COMPOSITE_REQUEST] for i in range(0, len(list_need_to_split), self.LIMIT_OF_SUB_REQUEST_IN_ONE_COMPOSITE_REQUEST)]

    def query_more(self, next_url: str):
        return self._get_url(self.connection.instance_url + next_url)
    
    def query_all_when_meet_query_return_limit(self, query_statement:str):
        query_response = self.query(query_statement)
        out_put = query_response['records']
        while not query_response['done']:
            query_response = self.query_more(query_response['nextRecordsUrl'])
            out_put += query_response['records']
        return out_put

    def query_tooling_object_count(self, object_name: str):
        return self.query("SELECT Count(Id) FROM " + object_name)["records"][0]["expr0"]

    def query(self, query: str):
        return self._get('query', {'q': query})

    def run_tests_asynchronous(self):
        raise NotImplementedError

    def run_tests_synchronous(self, classnames: str):
        return self._post('runTestsSynchronous', {'tests': [{'className': classnames}]})

    def search(self, query: str):
        raise NotImplementedError

    def sobjects(self):
        return self._get('sobjects')

    def __getattr__(self, name: str):
        return ToolingObject(name, self.connection)


class ToolingObject(base.RestService):
    def __init__(self, object_name: str, connection: core.Connection):
        super().__init__(connection, 'tooling/sobjects/' + object_name)

    def describe(self):
        return self._get('describe')

    def create(self, json: dict = None):
        return self._post('', json=json)

    def get(self, record_id: str):
        return self._get(record_id)

    def update(self, record_id: str, json: dict = None):
        return self._patch(record_id, json)

    def delete(self, record_id: str):
        return self._delete(record_id)
