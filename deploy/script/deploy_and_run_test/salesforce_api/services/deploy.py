import time, requests
import os
from base64 import b64encode
from .. import exceptions, const, config
from ..utils import misc
from ..models import deploy as models
from . import base
from modules.loggger import CILogger


class Deploy(base.SoapService):
    def _get_zip_content(self, input_file, is_path=True) -> str:
        if is_path:
            with open(input_file, 'rb') as f:
                input_file = f.read()
        return b64encode(input_file).decode('utf-8')

    def deploy(self, input_zip, is_path=True, options: models.Options = models.Options()) -> 'Deployment':
        result = self._post(action='deploy', message_path='deploy/deploy.msg', message_attributes={
            'zip_file': self._get_zip_content(input_zip, is_path),
            'options': options.as_xml()
        })
        if result.has('soapenv:Envelope/soapenv:Body/soapenv:Fault/faultcode'):
            raise exceptions.DeployCreateError(result.get_value('soapenv:Envelope/soapenv:Body/soapenv:Fault/faultstring'))
        return Deployment(self, result.get_value('soapenv:Envelope/soapenv:Body/deployResponse/result/id'))

    def check_deploy_status(self, async_process_id: str) -> models.Status:
        result = None
        for retry_count in range(config.RETRY_TIMES):
            try:
                result = self._post(action='checkDeployStatus', message_path='deploy/status.msg', message_attributes={
                    'async_process_id': async_process_id})
            except Exception as e:
                CILogger.error(f"Get deploy status error because exception happen, retryCount: {retry_count}, exception: {e}")
            else:
                result = result.get('soapenv:Envelope/soapenv:Body/checkDeployStatusResponse/result')
                if result is not None:
                    break
                CILogger.error(f"Get deploy status error because return empty result, retryCount: {retry_count}")

            if retry_count == config.RETRY_TIMES-1:
                raise Exception(f"Deploy code not successfully finished after retry {config.RETRY_TIMES} times")
            time.sleep(2 ** (retry_count+1) * config.RETRY_SLEEP_TIME_SECOND_UNIT)

        status = models.Status(result.get_value('status'), result.get_value('stateDetail', None), models.DeployDetails(
            int(result.get_value('numberComponentsTotal')),
            int(result.get_value('numberComponentErrors')),
            int(result.get_value('numberComponentsDeployed'))
        ), models.DeployDetails(
            int(result.get_value('numberTestsTotal')),
            int(result.get_value('numberTestErrors')),
            int(result.get_value('numberTestsCompleted'))
        ))

        if status.status.lower().strip() == 'failed':
            for failure in result.get_list('details/componentFailures'):
                status.components.append_failure(models.ComponentFailure(
                    failure.get('componentType'),
                    failure.get('fileName'),
                    failure.get('problemType'),
                    failure.get('problem')
                ))

            for failure in result.get_list('details/runTestResult/failures'):
                status.tests.append_failure(models.UnitTestFailure(
                    failure.get('name'),
                    failure.get('methodName'),
                    failure.get('message'),
                    failure.get('stackTrace')
                ))

        return status

    def cancel(self, async_process_id: str) -> bool:
        result = self._post(action='cancelDeploy', message_path='deploy/cancel.msg', message_attributes={
            'async_process_id': async_process_id
        })
        resultValue = result.get_value('soapenv:Envelope/soapenv:Body/cancelDeployResponse/result/done')
        if(type(resultValue) is bool):
            return resultValue
        return misc.parse_bool(resultValue)


class Deployment:
    def __init__(self, deploy_service: Deploy, async_process_id: str):
        self.deploy_service = deploy_service
        self.async_process_id = async_process_id
        self.start_time = time.time()
        self.notification_sent = False

    def get_elapsed_seconds(self):
        return time.time() - self.start_time

    def get_elapsed_time(self):
        return time.strftime("%H:%M:%S", time.gmtime(self.get_elapsed_seconds()))

    def cancel(self) -> bool:
        return self.deploy_service.cancel(self.async_process_id)

    def get_status(self) -> models.Status:
        return self.deploy_service.check_deploy_status(self.async_process_id)

    def is_done(self):
        return self.get_status().status in const.STATUSES_DONE

    def has_failed(self):
        return self.get_status().status == const.STATUS_FAILED

    def has_succeeded(self):
        return self.get_status().status == const.STATUS_SUCCEEDED

    def wait(self, tick: callable = None, timeout=36000, sandbox_name: str = ""):
        for _ in range(int(timeout / config.DEPLOY_SLEEP_SECONDS)):
            status = self.get_status()
            formatted_status = self.format_status(status, sandbox_name)
            CILogger.info(formatted_status)
            if tick is not None and callable(tick):
                tick(status)
            if (status.components.failed_count > 0 or status.tests.failed_count > 0) and not self.notification_sent:
                self.send_wechat_notification("failed", sandbox_name)
                self.notification_sent = True
            if status.status == "Succeeded":
                self.send_wechat_notification("succeeded", sandbox_name)
                return status
            elif status.status == "Failed":
                if not self.notification_sent:
                    self.send_wechat_notification("failed", sandbox_name)
                    self.notification_sent = True
                return status
            elif status.status == "Canceled":
                return status
            time.sleep(config.DEPLOY_SLEEP_SECONDS)

    def wait_for_deploy(self, tick: callable = None, timeout=4000):
        for _ in range(int(timeout / config.DEPLOY_SLEEP_SECONDS)):
            status = self.get_status()
            if tick is not None and callable(tick):
                tick(status)
            if self.get_status().components.total_count == (
                    self.get_status().components.failed_count + self.get_status().components.completed_count
            ) and self.get_status().components.total_count != 0:
                return status
            time.sleep(config.DEPLOY_SLEEP_SECONDS)

    def format_status(self, status, sandbox_name):
        components_progress = (f"({status.components.completed_count} + "
                               f"{status.components.failed_count} / "
                               f"{status.components.total_count})")
        tests_progress = (f"({status.tests.completed_count} + "
                          f"{status.tests.failed_count} / "
                          f"{status.tests.total_count})")
        return (f"Sandbox: {sandbox_name}, Status: {status.status}, "
                f"Components: {components_progress}, "
                f"Tests: {tests_progress}, "
                f"Details: {status.details}")

    def send_wechat_notification(self, status, sandbox_name):
        if sandbox_name != "Production":
            return

        url = os.getenv('CIRCLE_BUILD_URL')
        webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=15bcd791-01bd-4010-a2f6-ebf32773813b'
        headers = {'Content-Type': 'application/json'}

        if status == "succeeded":
            message_content = (
                f"🎉 部署状态: **成功**\n\n"
                f"📦 环境: {sandbox_name}\n\n"
                f"🔗 任务链接: {url}"
            )
        else:
            message_content = (
                f"❌ 部署状态: **失败**\n\n"
                f"📦 环境: {sandbox_name}\n\n"
                f"🔗 任务链接: {url}"
            )

        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": message_content
            }
        }

        response = requests.post(webhook_url, json=message, headers=headers)
        if response.status_code != 200:
            CILogger.error(f"Failed to send notification to WeChat: {response.text}")
