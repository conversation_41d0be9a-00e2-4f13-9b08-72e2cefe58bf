import logging
import sys


class CILogger(object):

    stdout_logger = logging.getLogger("stdout_logger")
    format_str = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    stdout_logger.setLevel(logging.INFO)
    sh = logging.StreamHandler(sys.stdout)
    sh.setFormatter(format_str)
    stdout_logger.addHandler(sh)

    stderr_logger = logging.getLogger("stderr_logger")
    format_str = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    stderr_logger.setLevel(logging.ERROR)
    sh = logging.StreamHandler(sys.stderr)
    sh.setFormatter(format_str)
    stderr_logger.addHandler(sh)

    @staticmethod
    def info(msg):
        CILogger.stdout_logger.info(msg)

    @staticmethod
    def error(msg):
        CILogger.stderr_logger.error(msg)
