# import json
# import logging
# import traceback
#
# import requests
#
#
# class GocdAgent:
#     def __init__(self, **kwargs):
#         self.username = kwargs["username"]
#         self.passwd = kwargs["passwd"]
#         self.server_url = kwargs["server_url"]
#         self.pipeline_name = kwargs["pipeline_name"]
#         self.current_pipeline_counter = int(kwargs["current_pipeline_counter"])
#         self.stage_name = kwargs["stage_name"]
#         self.job_name = kwargs["job_name"]
#         self.current_revision = kwargs["revision"]
#
#     def _get_history(self, url, type):
#         try:
#             session = requests.Session()
#             session.keep_alive = False
#             session.trust_env = False
#             response = session.get(url, auth=(self.username, self.passwd), verify=False)
#             return json.loads(response.content)[type]
#         except Exception:
#             CILogger.info(traceback.format_exc())
#             CILogger.info('Password for pipeline user not correct, please change the environment variables for pipeline user')
#             exit(255)
#
#     def get_pipeline_history(self, offset):
#         pipeline_history_url = self.server_url + '/api/pipelines/' + self.pipeline_name + '/history/' + str(offset)
#         return self._get_history(pipeline_history_url, 'pipelines')
#
#     def get_last_pipleline_revision(self, latest_pipeline_counter, last_success_pipeline_counter):
#         pipeline_history = self.get_pipeline_history(latest_pipeline_counter - last_success_pipeline_counter)
#         for pipeline in pipeline_history:
#             if pipeline['counter'] == last_success_pipeline_counter:
#                 return pipeline['build_cause']['material_revisions'][0]['modifications'][0]['revision']
#
#     def get_pipeline_counter_of_last_job_success(self, offset):
#         pipeline_history = self.get_pipeline_history(offset)
#         for history in pipeline_history:
#             for stage in history['stages']:
#                 if stage['name'] == self.stage_name:
#                     for job in stage['jobs']:
#                         if job['name'] == self.job_name:
#                             if job['result'] == 'Passed':
#                                 return history['counter']
#                             break
#                     break
#             offset += 1
#         return self.get_pipeline_counter_of_last_job_success(offset)
#
#     def _get_job_history(self, offset):
#         job_history_url = f"{self.server_url}/api/jobs/{self.pipeline_name}/{self.stage_name}/{self.job_name}/history/{offset}"
#         return self._get_history(job_history_url, 'jobs')
#
#     def _get_job_result(self, offset):
#         pipeline_history = self.get_pipeline_history(offset)
#         for stage in pipeline_history[0]['stages']:
#             if stage['name'] == self.stage_name:
#                 for job in stage['jobs']:
#                     if job['name'] == self.job_name:
#                         return job
#         return None
#
#     def _is_duplicate_run(self, latest_pipeline_counter):
#         job_result = self._get_job_result(latest_pipeline_counter - self.current_pipeline_counter)
#         if job_result is not None:
#             job_counter_offset = 0
#             while True:
#                 job_history = self._get_job_history(job_counter_offset)
#                 for history in job_history:
#                     if history['pipeline_counter'] == self.current_pipeline_counter:
#                         if history['result'] == 'Passed':
#                             return True
#                         if int(history['stage_counter']) == 1:
#                             return False
#                     else:
#                         job_counter_offset += 1
#         else:
#             return False
#
#     def analyze_job_history(self):
#         CILogger.info(f'parameters: {self.server_url}, {self.pipeline_name}, {self.current_pipeline_counter}, {self.stage_name}, {self.job_name},{ self.current_revision}')
#
#         latest_pipeline_counter = self.get_pipeline_history(0)[0]['counter']
#         CILogger.info(f'latest_pipeline_counter: {latest_pipeline_counter}')
#
#         last_success_pipeline_counter = self.get_pipeline_counter_of_last_job_success(latest_pipeline_counter - self.current_pipeline_counter + 1)
#         CILogger.info(f'last success pipeline counter: {last_success_pipeline_counter}')
#
#         last_pipleline_revision = self.get_last_pipleline_revision(latest_pipeline_counter, last_success_pipeline_counter)
#         CILogger.info(f'last pipeline revision: {last_pipleline_revision}')
#
#         is_duplicate_run = self._is_duplicate_run(latest_pipeline_counter)
#
#         return self.current_revision, last_pipleline_revision, is_duplicate_run
