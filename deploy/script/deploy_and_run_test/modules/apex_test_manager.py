import json

from modules.loggger import <PERSON><PERSON><PERSON>ger
from modules.source_code_manager import <PERSON><PERSON>ode<PERSON>anager
from modules.test_job import <PERSON><PERSON><PERSON>
from salesforce_api import config

class ApexTestManager(TestJob):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.source_code_manager = SourceCodeManager(**self.kwargs)
        self.pure_test_class_names = []
        self.filename_to_coverage_map = dict()
        self.trigger_list = []
        self.apex_file_set_manage_by_code = set()
        self.code_coverage_standard = self._read_code_coverage_standard()
        self.test_method_number = 1
        self.failed_test_case_index = 1
        self.test_failure_string = ""

    def _get_test_case(self):
        CILogger.info("Start running apex test...")
        test_class_names, self.pure_test_class_names = self.source_code_manager.get_apex_test_file_list()
        self._get_class_and_trigger_file_list()
        return test_class_names

    def _run_one_test(self, sandbox, test_case):
        return sandbox.run_apex_test(test_case)

    def _get_class_and_trigger_file_list(self):
        apex_class_set_need_to_calculate_coverage = set(self.source_code_manager.get_apex_class_file_with_out_interface_list()) - set(self.pure_test_class_names)
        self.trigger_list = self.source_code_manager.get_trigger_file_list()
        self.apex_file_set_manage_by_code = set(self.trigger_list).union(apex_class_set_need_to_calculate_coverage)

    def _analyse_test_result(self, test_result):
        if not test_result["finished"]:
            return
        self._print_success(test_result["successes"], test_result['sandbox_url'])
        self._print_error(test_result["failures"], test_result['sandbox_url'])

    def _get_overall_job_result(self):
        return self._calculate_and_print_coverage_and_print_test_need_to_manually_test()

    def _read_code_coverage_standard(self):
        with open("deploy/script/deploy_and_run_test/config/deploy_test.json") as f:
            json_string = f.read()
        return json.loads(json_string)["code_coverage_standard"]

    def _print_success(self, successes, sandbox_url):
        for success in successes:
            CILogger.info(f"{self.test_method_number}.  {success['name']}.{success['methodName']} pass in {sandbox_url}. duration is {success['time']} ms.")
            self.test_method_number += 1

    def _print_error(self, failures, sandbox_url):
        for failure in failures:
            error_msg = f".  {failure['name']}.{failure['methodName']} failed in {sandbox_url}" \
                        f"\n\n\t{failure['message']}" \
                        f"\n\n\t{failure['stackTrace']}"
            CILogger.error(f"{self.test_method_number}{error_msg}")
            self.test_failure_string += f"\n\n{self.failed_test_case_index}{error_msg}"
            self.test_method_number += 1
            self.failed_test_case_index += 1

    def get_and_merge_filename_to_coverage_map(self,sandbox, count):
        CILogger.info(f'Merging code coverage of  {sandbox.get_sandbox_name()}')
        coverage_map = sandbox.get_filename_to_coverage_map(self.pure_test_class_names,self.apex_file_set_manage_by_code)
        for file_name,code_coverage in coverage_map.items():
            if file_name not in self.filename_to_coverage_map:
                self.filename_to_coverage_map[file_name] = code_coverage
            else:
                self.filename_to_coverage_map[file_name]["covered_line_set"] |= code_coverage["covered_line_set"]
        CILogger.info(f'Successfully merge code coverage of {count} sandbox')
        return sandbox
    
    def generate_filename_to_coverage_map(self):
        pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=True)
        for index in range(len(self.kwargs["usernames"].split(","))):
            sandbox = self.available_sandbox.get(timeout=2400)
            pool.apply_async(self.get_and_merge_filename_to_coverage_map(sandbox,index + 1))
        sandboxes = pool.get_threads_result(raise_exception=True, with_status=False)
        [self.available_sandbox.put(sandbox) for sandbox in sandboxes]

    def _calculate_and_print_coverage_and_print_test_need_to_manually_test(self):
        self.generate_filename_to_coverage_map()
        apex_not_get_coverage_set = set(self.apex_file_set_manage_by_code) - set(self.filename_to_coverage_map.keys());
        for file_name in apex_not_get_coverage_set:
            # self.test_failure_string += f"\nCode coverage of {file_name} in codebase not get the coverage after test"
            self.raise_failure_when_file_is_trigger_and_no_line_test_covered(file_name, True)

        total_lines = 0
        total_covered_lines = 0
        coverage_detail_list = []

        for file_name, coverage in self.filename_to_coverage_map.items():
            line_number = coverage["NumLines"]
            if line_number > 0:
                covered_line_number = len(coverage["covered_line_set"])
                coverage_percent = covered_line_number / line_number
                coverage_detail_list.append((file_name, covered_line_number, line_number, coverage_percent))
                total_covered_lines += covered_line_number
                total_lines += line_number
            self.raise_failure_when_file_is_trigger_and_no_line_test_covered(file_name, line_number == 0)

        for file_name, covered_lines, lines, coverage_value in sorted(coverage_detail_list, key=lambda d: d[-1], reverse=True):
            CILogger.info(f"Code coverage of {file_name} is {covered_lines} / {lines} = {coverage_value}")
            self.raise_failure_when_file_is_trigger_and_no_line_test_covered(file_name, covered_lines == 0)

        code_coverage = total_covered_lines / total_lines
        if code_coverage >= self.code_coverage_standard:
            CILogger.info(f"CodeCoverage is {code_coverage}, reach standard {self.code_coverage_standard}")
        else:
            self.test_failure_string += f"\n\nCodeCoverage is {code_coverage}, less than standard {self.code_coverage_standard} "
        self.logger_test_need_to_manually_operate_if_need()
        if len(self.test_failure_string) > 0:
            CILogger.error(f"\nAll Test Failures: {self.test_failure_string}\n")
            raise RuntimeError("Apex test failed !!!")

    def raise_failure_when_file_is_trigger_and_no_line_test_covered(self, file_name, no_line_covered=False):
        if no_line_covered and file_name in self.trigger_list:
            self.test_failure_string += f'\n\nAll triggers must have some code coverage,{file_name} didn\'t have any line covered in test'

    def logger_test_need_to_manually_operate_if_need(self):
        if self.result_none_test_queue.qsize() != 0:
            CILogger.error(f"After retry {config.RESULT_NONE_TEST_RETRY_MAX_TIMES} times, {self.result_none_test_queue.qsize()} tests still get empty result and need to manually test!")
        index = 0
        for test_case in list(self.result_none_test_queue.queue):
            index = index + 1
            CILogger.error(f"{index}.\t {test_case} need to manually test!")
    
    def retry_result_none_test(self,retry_times):
        for retry_count in range(1, retry_times + 1):
            if self.result_none_test_queue.empty():
                return
            pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=True)
            CILogger.info(f"Retry run test which result is none, retryCount: {retry_count}")
            CILogger.info(f"There are still remain {self.result_none_test_queue.qsize()} tests which result is none")
            for _ in range(self.result_none_test_queue.qsize()):
                sandbox = self.available_sandbox.get(timeout=2400)
                pool.apply_async(self._run_one_test_and_analyse_result, (sandbox, self.result_none_test_queue.get()))
            pool.get_threads_result(raise_exception=True)

    def _add_none_result_test_to_queue(self,test_result,test_case):
        if test_result["finished"] == False:
            self.result_none_test_queue.put(test_case)