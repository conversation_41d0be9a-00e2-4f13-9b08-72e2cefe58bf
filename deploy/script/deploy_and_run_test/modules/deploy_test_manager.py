from queue import Queue
import os
import signal
import sys

from .loggger import CILogger
from .apex_test_manager import ApexTestManager
from .timeworks_unit_test_manager import TimeworksUnitTestManager
from .ui_test_manager import UITestManager
from .sandbox import Sandbox
from .source_code_manager import SourceCodeManager


class DeployTestManager:
    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.common_thread_pool = kwargs["common_thread_pool"]
        self.source_code_manager = SourceCodeManager(**self.kwargs)
        self.sandboxes = []

    def _get_sandboxes_from_env_variables(self):
        pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=True)
        for username in self.kwargs["usernames"].split(","):
            pool.apply_async(Sandbox, kwds={**self.kwargs, **{"username": username, "api_version": "52.0"}})
        return pool.get_threads_result(raise_exception=True, with_status=False)

    def start_deploy_and_run_test(self):

        # should_run_deploy, should_run_apex_test = source_code_manager.should_run_job_after_diff_commit()
        should_run_deploy, should_run_apex_test, should_run_timeworks_unit_test, should_run_ui_test = True, True, True, True
        CILogger.info(
            f"should_run_deploy: {should_run_deploy} \n should_run_apex_test: {should_run_apex_test} \n should_run_timeworks_unit_test: {should_run_timeworks_unit_test} \n should_run_ui_test: {should_run_ui_test}")
        # TODO: add more job check result about should run or not
        pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=True)
        available_sandbox_queue = Queue()
        self.kwargs["available_sandbox"] = available_sandbox_queue
        if should_run_deploy:
            signal.signal(signal.SIGTERM, self.cancel_all_deploy)
            pool.apply_async(self._deploy_code_to_sandboxes, (available_sandbox_queue,))
            # should run test
            if self.kwargs["run_test_type"] != "" :
                test_run_pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=True)
                if should_run_apex_test and self.kwargs["run_test_type"] == "apex_test":
                    test_run_pool.apply_async(ApexTestManager(**self.kwargs).run)
                if should_run_timeworks_unit_test and self.kwargs["run_test_type"] == "timeworks_unit_test":
                    test_run_pool.apply_async(TimeworksUnitTestManager(**self.kwargs).run)
                if should_run_ui_test and self.kwargs["run_test_type"] == "ui_test":
                    test_run_pool.apply_async(UITestManager(**self.kwargs).run)
                test_run_pool.get_threads_result(raise_exception=True)
                CILogger.info(f"{self.kwargs['run_test_type']} completed, program will exit!")
                sys.exit()

            pool.get_threads_result(raise_exception=True)

        else:
            print('\nNo code changes related to this job since last job success, should skip this job')

    def _deploy_code_to_sandboxes(self, available_sandbox_queue):
        zipfile_content = self.source_code_manager.generate_zip_file_for_deploy()
        pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=False)
        self.sandboxes = self._get_sandboxes_from_env_variables()
        for sandbox in self.sandboxes:
            pool.apply_async(self._deploy_to_one_sandbox,
                             (zipfile_content, sandbox, available_sandbox_queue, self.kwargs.get('deploy_to', 'Sandbox')))
        try:
            pool.get_threads_result(raise_exception=True)
        except Exception as e:
            CILogger.error(f"Deploy code to sandbox failed!!!, err: {e}")
            self.cancel_all_deploy(None, None)
            os._exit(1)

    def cancel_all_deploy(self, sig, frame):
        CILogger.info("Start cancel all deploy...")
        pool = self.common_thread_pool.new_pool_status(exit_for_any_exception=False)
        for sandbox in self.sandboxes:
            pool.apply_async(sandbox.cancel_deploy)
        pool.get_threads_result(raise_exception=False)
        CILogger.info("All deployments are cancelled")

    def _deploy_to_one_sandbox(self, zipfile_content, sandbox, available_sandbox_queue, deploy_to: str):
        try:
            if deploy_to.lower() == 'prod':
                CILogger.info(f"Start deploy prod code to {sandbox.get_sandbox_name()}")
                sandbox.prod_deploy_zip(zipfile_content)
                CILogger.info(f"Successfully deploy prod code to {sandbox.get_sandbox_name()}")
            else:
                CILogger.info(f"Start stop schedule jobs in {sandbox.get_sandbox_name()}")
                sandbox.stop_schedule_jobs()
                CILogger.info(f"Successfully stop schedule jobs in {sandbox.get_sandbox_name()}")

                CILogger.info(f"Start pre deploy in {sandbox.get_sandbox_name()}")
                sandbox.pre_deploy(self.kwargs["run_test_type"])
                CILogger.info(f"Successfully pre deploy in {sandbox.get_sandbox_name()}")
                CILogger.info(f"Start deploy code to {sandbox.get_sandbox_name()}")
                sandbox.deploy_zip(zipfile_content)
                CILogger.info(f"Successfully deploy code to {sandbox.get_sandbox_name()}")
                available_sandbox_queue.put(sandbox)
                CILogger.info(f"Sandbox {sandbox.get_sandbox_name()} available now")

                if deploy_to.lower() == 'uat':
                    CILogger.info(f"Start start schedule jobs in {sandbox.get_sandbox_name()}")
                    sandbox.start_schedule_jobs()
                    CILogger.info(f"Successfully start schedule jobs in {sandbox.get_sandbox_name()}")
        except Exception as err:
            CILogger.error(f"Sandbox {sandbox.get_sandbox_name()} deploy failed, error:{err}")
            raise err
