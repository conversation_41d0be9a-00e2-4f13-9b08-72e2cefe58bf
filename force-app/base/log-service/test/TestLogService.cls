@IsTest
public with sharing class TestLogService {
    @IsTest
    static void logWritesCorrectValues() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        LogService.log('My message', 'foo/method', 'Team A', LoggingLevel.WARN);
        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assertEquals('My message', apexLog.Message__c);
        System.assertEquals('WARN', apexLog.LogLevel__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void debugWritesCorrectValues() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        LogService.log('My message', 'foo/method', 'Team A', LoggingLevel.DEBUG);
        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assertEquals('My message', apexLog.Message__c);
        System.assertEquals('DEBUG', apexLog.LogLevel__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void warnWritesCorrectValues() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        LogService.log('My message', 'foo/method', 'Team A', LoggingLevel.WARN);
        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assertEquals('My message', apexLog.Message__c);
        System.assertEquals('WARN', apexLog.LogLevel__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void errorWritesCorrectValues() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        LogService.log('My message', 'foo/method', 'Team A', LoggingLevel.ERROR);
        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assertEquals('My message', apexLog.Message__c);
        System.assertEquals('ERROR', apexLog.LogLevel__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void errorWritesCorrectValuesForException() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        try {
            //noinspection ApexUnusedDeclaration
            Double badNumber = 1 / 0; // force MathException
        } catch (Exception ex) {
            LogService.log(LogService.getExceptionText(ex), 'foo/method', 'Team A', LoggingLevel.ERROR);
        }

        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assert(apexLog.Message__c.startsWith('Exception - Type: System.MathException;'));
        System.assertEquals('ERROR', apexLog.LogLevel__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void errorWithIdWritesCorrectValuesForException() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        try {
            //noinspection ApexUnusedDeclaration
            Double badNumber = 1 / 0; // force MathException
        } catch (Exception ex) {
            LogService.log(LogService.getExceptionText(ex), 'foo/method', 'my id', 'Team A', LoggingLevel.ERROR);
        }

        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Affected_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assert(apexLog.Message__c.startsWith('Exception - Type: System.MathException;'));
        System.assertEquals('ERROR', apexLog.LogLevel__c);
        System.assertEquals('my id', apexLog.Affected_Id__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void logWritesCorrectValuesForException() {
        LogService.ignoreTestMode = true;
        System.Test.startTest();
        try {
            //noinspection ApexUnusedDeclaration
            Double badNumber = 1 / 0; // force MathException
        } catch (Exception ex) {
            LogService.log(LogService.getExceptionText(ex), 'foo/method', 'Team A', LoggingLevel.DEBUG);
        }

        System.Test.stopTest();

        Apex_Log__c apexLog = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assert(apexLog.Message__c.startsWith('Exception - Type: System.MathException;'));
        System.assertEquals('DEBUG', apexLog.LogLevel__c);
        System.assertEquals(UserInfo.getUserId(), apexLog.User_Id__c);
        System.assertEquals('Team A', apexLog.Team__c);
    }

    @IsTest
    static void errorDoesNotThrow() {
        String tooLong =
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '1234567';
        System.Test.startTest();
        LogService.log('errorDoesNotThrow_message', tooLong, 'Team A', LoggingLevel.WARN);
        System.Test.stopTest();

        List<Apex_Log__c> apexLogs = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE LogLevel__c = 'WARN' LIMIT 1];
        System.assertEquals(0, apexLogs.size());
    }

    @IsTest
    static void logFailsSansIgnoreTestMode() {
        System.Test.startTest();
        LogService.log('My message', 'foo/method', 'Team A', LoggingLevel.DEBUG);
        System.Test.stopTest();

        List<Apex_Log__c> apexLogs = [SELECT Message__c, LogLevel__c, User_Id__c, Team__c FROM Apex_Log__c WHERE Class_Method__c = 'foo/method' LIMIT 1];

        System.assertEquals(0, apexLogs.size());
    }

    @IsTest
    static void badEventGeneratesError() {
        LogService.ignoreTestMode = true;
        String tooLong =
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '12345678901234567890123456789012345678901234567890' +
            '1234567';
        Apex_Log_Event__e event = new Apex_Log_Event__e();
        event.Class_Method__c = tooLong;
        List<List<Database.Error>> errors = LogService.publishEvents(new List<Apex_Log_Event__e>{ event });
        System.assertEquals(1, errors.size());
    }

    @IsTest
    static void cleanApexLogsFromOneYearAgo() {
        Apex_Log__c logFromOneYearAgo = new Apex_Log__c(
            Name = 'ApexLogOld',
            Class_Method__c = 'test clean',
            Publish_Time__c = DateTime.now().addYears(-1).formatGMT('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''),
            Team__c = 'Team A'
        );
        insert logFromOneYearAgo;

        Apex_Log__c logNow = new Apex_Log__c(Name = 'ApexLogNow', Class_Method__c = 'test clean', Publish_Time__c = DateTime.now().formatGMT('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''), Team__c = 'Team A');
        insert logNow;

        System.Test.startTest();
        LogService.cleanApexLogsFromOneYearAgo();
        System.Test.stopTest();

        List<Apex_Log__c> apexLogs = [SELECT Name FROM Apex_Log__c WHERE Class_Method__c = 'test clean'];
        System.assertEquals(1, apexLogs.size());
        System.assertEquals('ApexLogNow', apexLogs.get(0).Name);
    }
}
