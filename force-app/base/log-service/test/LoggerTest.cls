@IsTest(IsParallel=true)
private class LoggerTest {
    @IsTest
    static void testConstructorWithClassName() {
        Logger logger = new Logger('TestClassName');
        System.assertEquals('TestClassName', logger.className);
        System.assertEquals('None', logger.teamName);
    }

    @IsTest
    static void testConstructorWithClassNameAndPSA() {
        Logger logger = new Logger('TestClassName', TeamName.PSA);
        System.assertEquals('TestClassName', logger.className);
        System.assertEquals('PSA', logger.teamName);
    }

    @IsTest
    static void testConstructorWithClassNameAndCRM() {
        Logger logger = new Logger('TestClassName', TeamName.CRM);
        System.assertEquals('TestClassName', logger.className);
        System.assertEquals('CRM', logger.teamName);
    }

    @IsTest
    static void testConstructorWithClassNameAndSales() {
        Logger logger = new Logger('TestClassName', TeamName.Sales);
        System.assertEquals('TestClassName', logger.className);
        System.assertEquals('Sales', logger.teamName);
    }

    @IsTest
    static void testConstructorWithClassNameAndTeamNameString() {
        Logger logger = new Logger('TestClassName', 'QA');
        System.assertEquals('TestClassName', logger.className);
        System.assertEquals('QA', logger.teamName);
    }
}
