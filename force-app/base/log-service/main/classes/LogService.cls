//For writing persistent logs using platform events
global without sharing class LogService {
    //When test is running, event will not be published unless ignoreTestMode is set to true
    @TestVisible
    private static Boolean ignoreTestMode { get; set; }
    static {
        ignoreTestMode = false;
    }

    public static void log(String message, String classMethod, String teamName, System.LoggingLevel logLevel) {
        System.debug(logLevel, message);
        List<Apex_Log_Event__e> logEvents = initApexLogEvent(new List<String>{ message }, classMethod, teamName, logLevel, null);
        publishEvents(logEvents);
    }

    @TestVisible
    public static void log(List<String> messages, String classMethod, String teamName, System.LoggingLevel logLevel) {
        System.debug(logLevel, JSON.serialize(messages));
        List<Apex_Log_Event__e> logEvents = initApexLogEvent(messages, classMethod, teamName, logLevel, null);
        publishEvents(logEvents);
    }

    @TestVisible
    public static void log(String message, String classMethod, String affectedId, String teamName, System.LoggingLevel logLevel) {
        System.debug(logLevel, message);
        List<Apex_Log_Event__e> logEvents = initApexLogEvent(new List<String>{ message }, classMethod, teamName, logLevel, affectedId);
        publishEvents(logEvents);
    }

    // Called from trigger to write to log
    global static void insertApexLogs(List<Apex_Log_Event__e> events) {
        List<Apex_Log__c> logEntries = new List<Apex_Log__c>();
        for (Apex_Log_Event__e event : events) {
            Apex_Log__c logEntry = new Apex_Log__c();
            logEntry.Name = 'ApexLog';
            logEntry.Message__c = event.Message__c;
            logEntry.LogLevel__c = event.LogLevel__c;
            logEntry.Class_Method__c = event.Class_Method__c;
            logEntry.Publish_Time__c = event.Publish_Time__c;
            logEntry.User_Id__c = event.User_Id__c;
            logEntry.Affected_Id__c = event.Affected_Id__c;
            logEntry.Team__c = event.Team__c;
            logEntries.add(logEntry);
        }
        DMLUtils.upsertAndSendEmailWhenErrorHappen(logEntries, 'insert');
    }

    /**
     * Returns consistent string for exception message
     * @param ex the exception to retrieve text for
     * @return desired text from exception
     */
    @TestVisible
    public static String getExceptionText(Exception ex) {
        return 'Exception - Type: ' + ex.getTypeName() + '; Line: ' + ex.getLineNumber() + '; Message: ' + ex.getMessage() + '; Stacktrace: ' + ex.getStackTraceString();
    }

    public static void cleanApexLogsFromOneYearAgo() {
        String timestampOneYearAgoFromNow = DateTime.now().addYears(-1).formatGMT('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'');
        List<Apex_Log__c> apexLogs = [SELECT Id FROM Apex_Log__c WHERE Publish_Time__c <= :timestampOneYearAgoFromNow LIMIT 10000];
        if (apexLogs.size() > 0) {
            Database.delete(apexLogs, false);
        }
    }

    // publishes event... optionally suppresses when in test... deals with errors
    @TestVisible
    private static List<List<Database.Error>> publishEvents(List<Apex_Log_Event__e> events) {
        // don't publish event during unit tests unless ignoreTestMode is true
        if (!ignoreTestMode && System.Test.isRunningTest()) {
            return null;
        }
        List<Database.SaveResult> dbResults = EventBus.publish(events);

        List<List<Database.Error>> errorResults = new List<List<Database.Error>>();
        for (Database.SaveResult dbResult : dbResults) {
            if (!dbResult.isSuccess()) {
                errorResults.add(dbResult.getErrors());
            }
        }
        if (!errorResults.isEmpty()) {
            PSAEmail.sendEmailToPsaTeam(OrganizationInfo.sandBoxName() + 'Platform events(Apex logs) failed to publish', JSON.serialize(events));
        }

        return errorResults;
    }

    private static List<Apex_Log_Event__e> initApexLogEvent(List<String> messages, String classMethod, String teamName, System.LoggingLevel logLevel, String affectedId) {
        List<Apex_Log_Event__e> logEvents = new List<Apex_Log_Event__e>();
        for (String message : messages) {
            Apex_Log_Event__e event = new Apex_Log_Event__e();
            event.Message__c = message;
            event.LogLevel__c = logLevel.name();
            event.Class_Method__c = classMethod;
            event.Team__c = teamName;
            event.Publish_Time__c = DateTime.now().formatGMT('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'');
            event.Affected_Id__c = affectedId;
            String userId = UserInfo.getUserId();
            if (userId != null) {
                event.User_Id__c = userId;
            }
            logEvents.add(event);
        }

        return logEvents;
    }
}
