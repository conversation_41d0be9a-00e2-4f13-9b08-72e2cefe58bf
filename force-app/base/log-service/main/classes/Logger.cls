public without sharing class Logger {
    @TestVisible
    private String className;
    @TestVisible
    private String teamName;

    public Logger(String className) {
        this.className = className;
        this.teamName = 'None';
    }

    public Logger(String className, String teamName) {
        this.className = className;
        this.teamName = teamName;
    }

    public void info(String message) {
        LogService.log(message, className, teamName, LoggingLevel.INFO);
    }

    public void info(List<String> messages) {
        LogService.log(messages, className, teamName, LoggingLevel.INFO);
    }

    public void debug(String message) {
        LogService.log(message, className, teamName, LoggingLevel.DEBUG);
    }

    public void warn(String message) {
        LogService.log(message, className, teamName, LoggingLevel.WARN);
    }

    public void warn(Exception ex) {
        String message = LogService.getExceptionText(ex);
        LogService.log(message, className, teamName, LoggingLevel.WARN);
    }

    public void error(Exception ex) {
        String message = LogService.getExceptionText(ex);
        LogService.log(message, className, teamName, LoggingLevel.ERROR);
    }

    public void error(String message) {
        LogService.log(message, className, teamName, LoggingLevel.ERROR);
    }

    public void error(String message, Exception ex) {
        String exceptionMessage = LogService.getExceptionText(ex);
        LogService.log(message + exceptionMessage, className, teamName, LoggingLevel.ERROR);
    }

    public void error(Exception ex, String classMethod, String affectedId) {
        String message = LogService.getExceptionText(ex);
        LogService.log(message, classMethod, affectedId, teamName, LoggingLevel.ERROR);
    }
}
