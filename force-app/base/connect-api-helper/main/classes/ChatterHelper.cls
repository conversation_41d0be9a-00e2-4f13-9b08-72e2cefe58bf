public class ChatterHelper {
    public static List<ConnectApi.MessageSegmentInput> addText(List<ConnectApi.MessageSegmentInput> messageSegments, String text) {
        ConnectApi.MarkupBeginSegmentInput start;
        ConnectApi.MarkupEndSegmentInput ending;
        ConnectApi.TextSegmentInput textInput;
        start = new ConnectApi.MarkupBeginSegmentInput();
        start.markupType = ConnectApi.MarkupType.Paragraph;
        messageSegments.add(start);

        textInput = new ConnectApi.TextSegmentInput();
        textInput.text = text;
        messageSegments.add(textInput);

        ending = new ConnectApi.MarkupEndSegmentInput();
        ending.markupType = ConnectApi.MarkupType.Paragraph;
        messageSegments.add(ending);
        return messageSegments;
    }

    public static ConnectApi.TextSegmentInput createTextSegment(String text) {
        ConnectApi.TextSegmentInput textSegment = new ConnectApi.TextSegmentInput();
        textSegment.text = text;
        return textSegment;
    }

    public static ConnectApi.MentionSegmentInput createMentionSegment(Id userId) {
        ConnectApi.MentionSegmentInput mentionSegment = new ConnectApi.MentionSegmentInput();
        mentionSegment.id = userId;
        return mentionSegment;
    }

    public static ConnectApi.MarkupBeginSegmentInput createMarkupBeginSegmentInput(ConnectApi.MarkupType markupType) {
        ConnectApi.MarkupBeginSegmentInput markupBeginSegmentInput = new ConnectApi.MarkupBeginSegmentInput();
        markupBeginSegmentInput.markupType = markupType;
        return markupBeginSegmentInput;
    }

    public static ConnectApi.MarkupEndSegmentInput createMarkupEndSegmentInput(ConnectApi.MarkupType markupType) {
        ConnectApi.MarkupEndSegmentInput markupEndSegmentInput = new ConnectApi.MarkupEndSegmentInput();
        markupEndSegmentInput.markupType = markupType;
        return markupEndSegmentInput;
    }
}
