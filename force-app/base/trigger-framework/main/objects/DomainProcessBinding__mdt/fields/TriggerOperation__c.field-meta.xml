<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>TriggerOperation__c</fullName>
    <description>For a process context of &quot;trigger execution&quot;, you need to specify which trigger operation
        this trigger handler would respond to.
    </description>
    <externalId>false</externalId>
    <fieldManageability>SubscriberControlled</fieldManageability>
    <inlineHelpText>For a process context of &quot;trigger execution&quot;, you need to specify which trigger operation
        this trigger handler would respond to.
    </inlineHelpText>
    <label>Trigger Operation</label>
    <required>false</required>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Before_Insert</fullName>
                <isActive>false</isActive>
                <label>BeforeInsert</label>
            </value>
            <value>
                <fullName>After_Insert</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>AfterInsert</label>
            </value>
            <value>
                <fullName>Before_Update</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>BeforeUpdate</label>
            </value>
            <value>
                <fullName>After_Update</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>AfterUpdate</label>
            </value>
            <value>
                <fullName>Before_Delete</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>BeforeDelete</label>
            </value>
            <value>
                <fullName>After_Delete</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>AfterDelete</label>
            </value>
            <value>
                <fullName>After_Undelete</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>AfterUndelete</label>
            </value>
            <value>
                <fullName>beforeInsert</fullName>
                <default>true</default>
                <label>Before Insert</label>
            </value>
            <value>
                <fullName>afterInsert</fullName>
                <default>false</default>
                <label>After Insert</label>
            </value>
            <value>
                <fullName>beforeUpdate</fullName>
                <default>false</default>
                <label>Before Update</label>
            </value>
            <value>
                <fullName>afterUpdate</fullName>
                <default>false</default>
                <label>After Update</label>
            </value>
            <value>
                <fullName>beforeDelete</fullName>
                <default>false</default>
                <label>Before Delete</label>
            </value>
            <value>
                <fullName>afterDelete</fullName>
                <default>false</default>
                <label>After Delete</label>
            </value>
            <value>
                <fullName>afterUndelete</fullName>
                <default>false</default>
                <label>After Undelete</label>
            </value>

        </valueSetDefinition>
    </valueSet>
</CustomField>
