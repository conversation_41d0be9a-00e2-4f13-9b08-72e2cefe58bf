<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ClassToInject__c</fullName>
    <description>This is the Apex class that will be injected into the process execution. The class has to implement
        TriggerHandlerBase.
    </description>
    <externalId>false</externalId>
    <fieldManageability>SubscriberControlled</fieldManageability>
    <inlineHelpText>This is the Apex class that will be injected into the process execution. The class has to implement
        TriggerHandlerBase.
    </inlineHelpText>
    <label>Class To Inject</label>
    <length>255</length>
    <required>true</required>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
