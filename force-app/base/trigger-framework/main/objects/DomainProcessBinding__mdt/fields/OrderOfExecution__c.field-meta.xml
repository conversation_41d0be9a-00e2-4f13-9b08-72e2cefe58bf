<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>OrderOfExecution__c</fullName>
    <description>In a process context, you can define one or more trigger handlers, allowing you to control the
        execution order. If multiple sequences are used, designate the order with decimal numbers (e.g., 1.1, 1.21,
        1.32, 2.13, 2.23).
    </description>
    <externalId>false</externalId>
    <fieldManageability>SubscriberControlled</fieldManageability>
    <inlineHelpText>Within a process context, you will have one or more trigger handler. This allows you to specify what
        the execution order is.
    </inlineHelpText>
    <label>Order Of Execution</label>
    <precision>4</precision>
    <required>true</required>
    <scale>2</scale>
    <type>Number</type>
    <unique>false</unique>
</CustomField>
