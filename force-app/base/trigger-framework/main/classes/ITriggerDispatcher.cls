/**
 * <AUTHOR>
 * @date 07/16/2013
 * @description Defines the interface for the trigger dispatching architecture.
 */
public interface ITriggerDispatcher {
    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions before the records are inserted.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that is getting inserted.
     */
    void beforeInsert(TriggerParameters tp);

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions before the records are updated.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that is getting updated.
     */
    void beforeUpdate(TriggerParameters tp);

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions before the records are deleted.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that is getting deleted.
     */
    void beforeDelete(TriggerParameters tp);

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions after the records are inserted.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that got inserted.
     */
    void afterInsert(TriggerParameters tp);

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions before the records are updated.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that got updated.
     */
    void afterUpdate(TriggerParameters tp);

    /**
     * <AUTHOR> Krishnan
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions after the records got deleted.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that got deleted.
     */
    void afterDelete(TriggerParameters tp);

    /**
     * <AUTHOR> Krishnan
     * @date 07/16/2013
     * @description Called by the trigger framework to carry out the actions after the records are undeleted.
     * @param tp TriggerParameters Contains the trigger parameters which includes the records that got undeleted.
     */
    void afterUnDelete(TriggerParameters tp);
}
