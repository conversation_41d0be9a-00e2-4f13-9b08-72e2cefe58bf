public with sharing class TriggerFactory {
    private static final String CUSTOM_OBJECT_SUFFIX = '__c';
    private static final String TRIGGER_DISPATCHER = 'TriggerDispatcher';

    public static void createTriggerDispatcher(Schema.SObjectType soType) {
        createTriggerDispatcher(soType.getDescribe().getName());
    }

    public static void createTriggerDispatcher(String soTypeStr) {
        if (!TriggerToggle.shouldTriggerExecuted())
            return;
        ITriggerDispatcher dispatcher = getTriggerDispatcher(soTypeStr);
        TriggerParameters tp = new TriggerParameters(
            Trigger.old,
            Trigger.new,
            Trigger.oldMap,
            Trigger.newMap,
            Trigger.isBefore,
            Trigger.isAfter,
            Trigger.isDelete,
            Trigger.isInsert,
            Trigger.isUpdate,
            Trigger.isUndelete,
            Trigger.isExecuting
        );
        if (dispatcher == null) {
            throw new TriggerException('No Trigger dispatcher registered for Object Type: ' + soTypeStr);
        }
        execute(dispatcher, tp);
        GovernLimitsMonitor.checkLimitsAndSendWarning(soTypeStr, tp.tEvent);
    }

    public static void execute(ITriggerDispatcher dispatcher, TriggerParameters tp) {
        // Handle before trigger events
        if (Trigger.isBefore) {
            if (Trigger.isDelete) {
                dispatcher.beforeDelete(tp);
            } else if (Trigger.isInsert) {
                dispatcher.beforeInsert(tp);
            } else if (Trigger.isUpdate) {
                dispatcher.beforeUpdate(tp);
            }
        } else {
            if (Trigger.isDelete) {
                dispatcher.afterDelete(tp);
            } else if (Trigger.isInsert) {
                dispatcher.afterInsert(tp);
            } else if (Trigger.isUpdate) {
                dispatcher.afterUpdate(tp);
            } else if (Trigger.isUndelete) {
                dispatcher.afterUnDelete(tp);
            }
        }
    }

    private static ITriggerDispatcher getTriggerDispatcher(String soTypeStr) {
        String dispatcherTypeName = deriveDispatcherTypeName(soTypeStr);

        Type obType = Type.forName(dispatcherTypeName);
        ITriggerDispatcher dispatcher = (obType == null) ? null : (ITriggerDispatcher) obType.newInstance();
        return dispatcher;
    }

    private static String deriveDispatcherTypeName(String originalTypeName) {
        if (originalTypeName.toLowerCase().endsWith(CUSTOM_OBJECT_SUFFIX)) {
            Integer index = originalTypeName.toLowerCase().indexOf(CUSTOM_OBJECT_SUFFIX);
            Integer namespaceIndex = originalTypeName.startsWith('pse__') ? 5 : (originalTypeName.startsWith('am_') ? 3 : 0);
            return originalTypeName.substring(namespaceIndex, index) + TRIGGER_DISPATCHER;
        } else {
            return originalTypeName + TRIGGER_DISPATCHER;
        }
    }
}
