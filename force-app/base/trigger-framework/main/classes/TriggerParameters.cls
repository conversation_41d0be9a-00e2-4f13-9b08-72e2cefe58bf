/**
 * <AUTHOR>
 * @date 07/16/2013
 * @description This class holds the trigger parameters.
 */
public class TriggerParameters {
    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @description A enum that represents the trigger event.
     */
    public enum TriggerEvent {
        beforeInsert,
        beforeUpdate,
        beforeDelete,
        afterInsert,
        afterUpdate,
        afterDelete,
        afterUndelete
    }
    public TriggerEvent tEvent;
    public String operationType { get; private set; }

    public List<SObject> oldList { get; private set; }
    public List<SObject> newList { get; private set; }
    public Map<Id, SObject> oldMap { get; private set; }
    public Map<Id, SObject> newMap { get; private set; }
    public String triggerObject { get; private set; }
    public Boolean isExecuting { get; private set; }

    private static final Map<TriggerEvent, String> triggerEventOperationMap = new Map<TriggerEvent, String>{
        TriggerEvent.beforeInsert => 'Insert',
        TriggerEvent.afterInsert => 'Insert',
        TriggerEvent.beforeUpdate => 'Update',
        TriggerEvent.afterUpdate => 'Update',
        TriggerEvent.beforeDelete => 'Delete',
        TriggerEvent.afterDelete => 'Delete',
        TriggerEvent.afterUndelete => 'Undelete'
    };

    /**
     * <AUTHOR> Krishnan
     * @date 07/16/2013
     * @description Constructs the TriggerParameter object.
     * @param olist List<SObject> A list of records with the state of 'before' event.
     * @param nlist List<SObject> A list of records with the state of 'after' event.
     * @param omap Map<Id, SObject> A map of records with the state of 'before' event.
     * @param nmap Map<Id, SObject> A map of records with the state of 'after' event.
     * @param ib Boolean A flag to indicate 'isBefore' event.
     * @param ia Boolean A flag to indicate 'isAfter' event.
     * @param id Boolean A flag to indicate 'isDelete' event.
     * @param ii Boolean A flag to indicate 'isInsert' event.
     * @param iu Boolean A flag to indicate 'isUpdate' event.
     * @param iud Boolean A flag to indicate 'isUnDelete' event.
     * @param ie Boolean A flag to indicate 'isExecuting'.
     */
    public TriggerParameters(
        List<SObject> olist,
        List<SObject> nlist,
        Map<Id, SObject> omap,
        Map<Id, SObject> nmap,
        Boolean ib,
        Boolean ia,
        Boolean id,
        Boolean ii,
        Boolean iu,
        Boolean iud,
        Boolean ie
    ) {
        this.oldList = olist;
        this.newList = nlist;
        this.oldMap = omap;
        this.newMap = nmap;
        this.triggerObject = UtilityClass.getSObjectTypeName((this.oldList != null && this.oldList.size() > 0) ? this.oldList[0] : this.newList[0]);
        if (ib & ii) {
            tEvent = TriggerEvent.beforeInsert;
        } else if (ib && iu) {
            tEvent = TriggerEvent.beforeUpdate;
        } else if (ib && id) {
            tEvent = TriggerEvent.beforeDelete;
        } else if (ia && ii) {
            tEvent = TriggerEvent.afterInsert;
        } else if (ia && iu) {
            tEvent = TriggerEvent.afterUpdate;
        } else if (ia && id) {
            tEvent = TriggerEvent.afterDelete;
        } else if (ia && iud) {
            tEvent = TriggerEvent.afterUndelete;
        }
        operationType = triggerEventOperationMap.get(tEvent);
        isExecuting = ie;
    }
}
