public class DomainProcessCoordinator implements IDomainProcessCoordinator {
    private String sObjAPIName;

    private static Map<String, Map<String, Boolean>> objectOperationProcessingMap = new Map<String, Map<String, Boolean>>();

    private Map<String, Map<Decimal, DomainProcessBinding__mdt>> domainProcessMap = new Map<String, Map<Decimal, DomainProcessBinding__mdt>>();

    private static Map<String, List<DomainProcessBinding__mdt>> mockDomainProcessBindingsBySObjectMap;

    private static TriggerParameters mockTriggerParameters;

    @TestVisible
    private static void setMockTriggerParameters(TriggerParameters tp) {
        mockTriggerParameters = tp;
    }

    @TestVisible
    private static void setMockDomainProcessBindings(List<DomainProcessBinding__mdt> mockDomainProcessBindings) {
        mockDomainProcessBindingsBySObjectMap = new Map<String, List<DomainProcessBinding__mdt>>();
        System.debug(mockDomainProcessBindings.size() + ' mock records supplied by test class');

        if (mockDomainProcessBindings != null) {
            String sobjAPIName = null;

            for (DomainProcessBinding__mdt mockDomainProcessBinding : mockDomainProcessBindings) {
                if (String.isNotBlank(mockDomainProcessBinding.RelatedDomainBindingSObject__c)) {
                    sobjAPIName = mockDomainProcessBinding.RelatedDomainBindingSObject__c;
                } else if (String.isNotBlank(mockDomainProcessBinding.RelatedDomainBindingSObjectAlternate__c)) {
                    sobjAPIName = mockDomainProcessBinding.RelatedDomainBindingSObjectAlternate__c;
                } else {
                    System.debug(LoggingLevel.WARN, 'SObject API name could not be descerned from mockDomainProcessBinding == ' + mockDomainProcessBinding);
                    continue;
                }

                if (!mockDomainProcessBindingsBySObjectMap.containsKey(sobjAPIName)) {
                    mockDomainProcessBindingsBySObjectMap.put(sobjAPIName, new List<DomainProcessBinding__mdt>());
                }

                mockDomainProcessBindingsBySObjectMap.get(sobjAPIName).add(mockDomainProcessBinding);
            }
        }
    }

    private void initOperationProcessingMapForCurrentSObject() {
        if (!objectOperationProcessingMap.containsKey(sObjAPIName)) {
            objectOperationProcessingMap.put(
                sObjAPIName,
                new Map<String, Boolean>{
                    TriggerParameters.TriggerEvent.beforeInsert.name() => false,
                    TriggerParameters.TriggerEvent.beforeUpdate.name() => false,
                    TriggerParameters.TriggerEvent.beforeDelete.name() => false,
                    TriggerParameters.TriggerEvent.afterInsert.name() => false,
                    TriggerParameters.TriggerEvent.afterUpdate.name() => false,
                    TriggerParameters.TriggerEvent.afterDelete.name() => false,
                    TriggerParameters.TriggerEvent.afterUndelete.name() => false
                }
            );
        }
    }

    public DomainProcessCoordinator(String sObjAPIName) {
        this.sObjAPIName = sObjAPIName;
    }

    public DomainProcessCoordinator(Schema.SObjectType sObjectType) {
        this.sObjAPIName = sObjectType.getDescribe().getName();
    }

    private Map<String, Map<Decimal, DomainProcessBinding__mdt>> getDomainProcessMap() {
        if (domainProcessMap.isEmpty()) {
            primeDomainLogicInjections();
        }
        return domainProcessMap;
    }

    private void primeDomainLogicInjections() {
        List<DomainProcessBinding__mdt> allProcesses = new List<DomainProcessBinding__mdt>();
        if (Test.isRunningTest() && mockDomainProcessBindingsBySObjectMap != null) {
            allProcesses = mockDomainProcessBindingsBySObjectMap.get(sObjAPIName);
        } else {
            allProcesses = getAllProcesses(sObjAPIName);
        }

        if (allProcesses == null) {
            return;
        }

        for (DomainProcessBinding__mdt process : allProcesses) {
            if (!domainProcessMap.containsKey(process.TriggerOperation__c)) {
                domainProcessMap.put(process.TriggerOperation__c, new Map<Decimal, DomainProcessBinding__mdt>());
            }
            if (!domainProcessMap.get(process.TriggerOperation__c).containsKey(process.OrderOfExecution__c)) {
                domainProcessMap.get(process.TriggerOperation__c).put(process.OrderOfExecution__c, process);
            }
        }
    }

    private List<DomainProcessBinding__mdt> getAllProcesses(String sObjAPIName) {
        List<DomainProcessBinding__mdt> allProcesses = new List<DomainProcessBinding__mdt>();
        // find all process DomainProcessBinding__mdt records that are related
        // Unfortunately, you cannot query MDTs with a where clause of "this or that".  If you do, it throws a "Disjunctions not supported" exception
        // So, you have to query the records with two queries and add them together.
        // first query the RelatedDomainBindingSObject__r.QualifiedApiName = :sObjAPIName
        allProcesses.addAll(
            [
                SELECT
                    Id,
                    ClassToInject__c,
                    OrderOfExecution__c,
                    RelatedDomainBindingSObject__c,
                    RelatedDomainBindingSObject__r.QualifiedApiName,
                    RelatedDomainBindingSObjectAlternate__c,
                    TriggerOperation__c
                FROM DomainProcessBinding__mdt
                WHERE RelatedDomainBindingSObject__r.QualifiedApiName = :sObjAPIName AND IsActive__c = TRUE
                ORDER BY TriggerOperation__c, OrderOfExecution__c
            ]
        );
        // second, query RelatedDomainBindingSObjectAlternate__c = :sObjAPIName
        allProcesses.addAll(
            [
                SELECT
                    Id,
                    ClassToInject__c,
                    OrderOfExecution__c,
                    RelatedDomainBindingSObject__c,
                    RelatedDomainBindingSObject__r.QualifiedApiName,
                    RelatedDomainBindingSObjectAlternate__c,
                    TriggerOperation__c
                FROM DomainProcessBinding__mdt
                WHERE RelatedDomainBindingSObjectAlternate__c = :sObjAPIName AND IsActive__c = TRUE
                ORDER BY TriggerOperation__c, OrderOfExecution__c
            ]
        );
        return allProcesses;
    }

    public void processDomainLogicInjections() {
        if (!TriggerToggle.shouldTriggerExecuted()) {
            return;
        }
        if (getDomainProcessMap().isEmpty()) {
            System.debug(LoggingLevel.ERROR, 'No Trigger handler registered for Object Type: ' + sObjAPIName);
            return;
        }
        TriggerParameters tp = getTriggerParameters();
        initOperationProcessingMapForCurrentSObject();
        processDomainLogicInjections(tp);
        GovernLimitsMonitor.checkLimitsAndSendWarning(sObjAPIName, tp.tEvent);
    }

    private TriggerParameters getTriggerParameters() {
        if (Test.isRunningTest() && mockTriggerParameters != null) {
            return mockTriggerParameters;
        }
        return new TriggerParameters(
            Trigger.old,
            Trigger.new,
            Trigger.oldMap,
            Trigger.newMap,
            Trigger.isBefore,
            Trigger.isAfter,
            Trigger.isDelete,
            Trigger.isInsert,
            Trigger.isUpdate,
            Trigger.isUndelete,
            Trigger.isExecuting
        );
    }

    private void processDomainLogicInjections(TriggerParameters tp) {
        String triggerOperationType = tp.tEvent.name();
        Map<Decimal, DomainProcessBinding__mdt> domainProcessesToExecuteMap = getDomainProcessMap().get(triggerOperationType);
        if (domainProcessesToExecuteMap == null) {
            return;
        }

        List<Decimal> executionOrderKeys = new List<Decimal>(domainProcessesToExecuteMap.keySet());
        executionOrderKeys.sort();

        Map<String, Boolean> operationProcessingMap = objectOperationProcessingMap.get(sObjAPIName);
        Boolean isOperationProcessing = operationProcessingMap.get(triggerOperationType);
        operationProcessingMap.put(triggerOperationType, true);
        for (Decimal orderOfExecutionKey : executionOrderKeys) {
            DomainProcessBinding__mdt process = domainProcessesToExecuteMap.get(orderOfExecutionKey);
            Type classToInject = Type.forName(process.ClassToInject__c);
            if (classToInject == null) {
                throw new ProcessInjectionException('Class not found: ' + process.ClassToInject__c);
            }

            executeProcess(classToInject, tp, isOperationProcessing);
        }
        operationProcessingMap.put(triggerOperationType, false);
    }

    private void executeProcess(Type classToInject, TriggerParameters tp, Boolean isOperationProcessing) {
        try {
            TriggerHandlerBase handler = (TriggerHandlerBase) classToInject.newInstance();
            if (isOperationProcessing) {
                System.debug('Processing in progress: ' + tp.tEvent.name());
                handler.inProgressEntry(tp);
            } else {
                System.debug('Processing start : ' + tp.tEvent.name());
                handler.mainEntry(tp);
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Original Exception: ' + e);
            throw e;
        }
    }

    public class ProcessInjectionException extends Exception {
    }
}
