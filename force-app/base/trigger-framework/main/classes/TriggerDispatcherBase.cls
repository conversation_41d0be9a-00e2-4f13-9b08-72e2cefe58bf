/**
 * <AUTHOR>
 * @date 07/16/2013
 * @description This class implements the ITriggerDispatcher and acts as an adapter to avoid implementing all the
 *				ITriggerDispatcher methods.
 */
public virtual class TriggerDispatcherBase implements ITriggerDispatcher {
    private static ITriggerHandler beforeInsertHandler;
    private static ITriggerHandler beforeUpdateHandler;
    private static ITriggerHandler beforeDeleteHandler;
    private static ITriggerHandler afterInsertHandler;
    private static ITriggerHandler afterUpdateHandler;
    private static ITriggerHandler afterDeleteHandler;
    private static ITriggerHandler afterUndeleteHandler;

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @param tp TriggerParameters
     * @description This method is called for records to be inserted during a BEFORE trigger.
     */
    public virtual void beforeInsert(TriggerParameters tp) {
    }

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @param tp The trigger parameters passed by the framework.
     * @description This method is called for records to be updated during a BEFORE trigger.
     */
    public virtual void beforeUpdate(TriggerParameters tp) {
    }

    /**
     * <AUTHOR>
     * @date 07/16/2013
     * @param tp The trigger parameters passed by the framework.
     * @description This method is called for records to be deleted during a BEFORE trigger.
     */
    public virtual void beforeDelete(TriggerParameters tp) {
    }

    /**
     * <AUTHOR> <PERSON>n
     * @date 07/16/2013
     * @param tp The trigger parameters passed by the framework.
     * @description This method is called for records inserted during an AFTER trigger. Always put field validation
     *				in the 'After' methods in case another trigger has modified any values. The record is 'read only'
     *				at this point.
     */
    public virtual void afterInsert(TriggerParameters tp) {
    }

    /**
     * <AUTHOR> Krishnan
     * @date 07/16/2013
     * @param tp The trigger parameters passed by the framework.
     * @description This method is called iteratively for each record updated during an AFTER trigger.
     */
    public virtual void afterUpdate(TriggerParameters tp) {
    }

    /**
     * <AUTHOR> Krishnan
     * @date 07/16/2013
     * @param tp The trigger parameters passed by the framework.
     * @description This method is called iteratively for each record deleted during an AFTER trigger.
     */
    public virtual void afterDelete(TriggerParameters tp) {
    }

    /**
     * <AUTHOR> Krishnan
     * @date 07/16/2013
     * @param tp The trigger parameters passed by the framework.
     * @description This method is called prior to execution of a AFTER UNDELETE trigger.
     */
    public virtual void afterUnDelete(TriggerParameters tp) {
    }

    /**
     * <AUTHOR> Krishnan
     * @date 06/20/2013
     * @description Called by the event handlers. If this is the first call in the context, then this method will create a new
     *				instance of the appropriate handler and execute the mainEntry method. If there is an existing call runing
     *				on the same context, then this method will use the existing handler instance created by the original call
     *				and execute the inProgressEntry method.
     * @param handlerInstance ITriggerHandler The trigger handler instance. The dispatcher need to pass an instance of the trigger handler, such
     *							as AccountAfterInsertTriggerHandler if this is the first call in a given context. If it is retry,
     *							then the dispatcher will need to pass null.
     * @param tp TriggerParameters The trigger parameters passed by the framework.
     * @param tEvent TriggerParameters.TriggerEvent The trigger event.
     */
    protected void execute(ITriggerHandler handlerInstance, TriggerParameters tp, TriggerParameters.TriggerEvent tEvent) {
        //disable trigger while prepare test data for solving "Too many SOQL queries: 101"
        if (!TriggerToggle.shouldTriggerExecuted()) {
            return;
        }

        if (handlerInstance != null) {
            if (tEvent == TriggerParameters.TriggerEvent.beforeInsert) {
                beforeInsertHandler = handlerInstance;
            }
            if (tEvent == TriggerParameters.TriggerEvent.beforeUpdate) {
                beforeUpdateHandler = handlerInstance;
            }
            if (tEvent == TriggerParameters.TriggerEvent.beforeDelete) {
                beforeDeleteHandler = handlerInstance;
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterInsert) {
                afterInsertHandler = handlerInstance;
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterUpdate) {
                afterUpdateHandler = handlerInstance;
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterDelete) {
                afterDeleteHandler = handlerInstance;
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterUndelete) {
                afterUndeleteHandler = handlerInstance;
            }
            handlerInstance.mainEntry(tp);
            handlerInstance.updateObjects();
        } else {
            if (tEvent == TriggerParameters.TriggerEvent.beforeInsert) {
                beforeInsertHandler.inProgressEntry(tp);
            }
            if (tEvent == TriggerParameters.TriggerEvent.beforeUpdate) {
                beforeUpdateHandler.inProgressEntry(tp);
            }
            if (tEvent == TriggerParameters.TriggerEvent.beforeDelete) {
                beforeDeleteHandler.inProgressEntry(tp);
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterInsert) {
                afterInsertHandler.inProgressEntry(tp);
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterUpdate) {
                afterUpdateHandler.inProgressEntry(tp);
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterDelete) {
                afterDeleteHandler.inProgressEntry(tp);
            }
            if (tEvent == TriggerParameters.TriggerEvent.afterUndelete) {
                afterUndeleteHandler.inProgressEntry(tp);
            }
        }
    }
}
