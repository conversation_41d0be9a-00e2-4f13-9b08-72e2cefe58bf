@IsTest(IsParallel=true)
public class DomainProcessCoordinatorTest {
    @IsTest
    private static void userBeforeInsertThreeHandlersInRightOrder() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>{
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.SyncCurrencyWithCountry',
                IsActive__c = true,
                OrderOfExecution__c = 2,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'RichmondAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCountryToUSAWhenNameContainMAGA',
                IsActive__c = true,
                OrderOfExecution__c = 1,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountAction',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCityAsCapitalOfCountry',
                IsActive__c = true,
                OrderOfExecution__c = 3,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            )
        };

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(null, newList, null, newMap, true, false, false, true, false, false, true));

        new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();

        List<User> expectedResult = new List<User>{
            new User(Id = newList[0].Id, LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'CNY', City = 'Beijing'),
            new User(Id = newList[1].Id, LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'CNY', City = 'Beijing'),
            new User(Id = newList[2].Id, LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'USD', City = 'Washington, D.C.'),
            new User(Id = newList[3].Id, LastName = 'MAGA111', Country = 'America', CurrencyIsoCode = 'USD', City = 'Washington, D.C.')
        };
        System.assertEquals(expectedResult, newList);
    }

    @IsTest
    private static void userBeforeInsertThreeHandlersInWrongOrder() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>{
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.SyncCurrencyWithCountry',
                IsActive__c = true,
                OrderOfExecution__c = 1,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'RichmondAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCountryToUSAWhenNameContainMAGA',
                IsActive__c = true,
                OrderOfExecution__c = 3,
                RelatedDomainBindingSObjectAlternate__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountAction',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCityAsCapitalOfCountry',
                IsActive__c = true,
                OrderOfExecution__c = 2,
                RelatedDomainBindingSObjectAlternate__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            )
        };

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(null, newList, null, newMap, true, false, false, true, false, false, true));

        new DomainProcessCoordinator('User').processDomainLogicInjections();

        List<User> expectedResult = new List<User>{
            new User(Id = newList[0].Id, LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'CNY', City = 'Beijing'),
            new User(Id = newList[1].Id, LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'CNY', City = 'Beijing'),
            new User(Id = newList[2].Id, LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'USD', City = 'Washington, D.C.'),
            new User(Id = newList[3].Id, LastName = 'MAGA111', Country = 'America', CurrencyIsoCode = 'CNY', City = 'Beijing')
        };
        System.assertEquals(expectedResult, newList);
    }

    @IsTest
    private static void shouldNotRunHandlerWhenTriggerToggleOff() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>{
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.SyncCurrencyWithCountry',
                IsActive__c = true,
                OrderOfExecution__c = 2,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'RichmondAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCountryToUSAWhenNameContainMAGA',
                IsActive__c = true,
                OrderOfExecution__c = 1,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountAction',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCityAsCapitalOfCountry',
                IsActive__c = true,
                OrderOfExecution__c = 3,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            )
        };

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(null, newList, null, newMap, true, false, false, true, false, false, true));

        TriggerToggle.turnOff();
        new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();

        List<User> expectedResult = new List<User>{
            new User(Id = newList[0].Id, LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = newList[1].Id, LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = newList[2].Id, LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = newList[3].Id, LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        System.assertEquals(expectedResult, newList);
    }

    @IsTest
    private static void shouldThrowExceptionWhenNoTypeForHandler() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>{
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountAction',
                ClassToInject__c = 'DomainProcessCoordinatorTest.notFoundHandler',
                IsActive__c = true,
                OrderOfExecution__c = 3,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            )
        };

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(null, newList, null, newMap, true, false, false, true, false, false, true));

        try {
            new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();
            System.assert(false, 'should throw exception');
        } catch (DomainProcessCoordinator.ProcessInjectionException ex) {
            System.debug(ex);
            System.assert(true);
        }
    }

    @IsTest
    private static void shouldThrowExceptionWhenHandlerNotExtendsFromTriggerHandlerBase() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>{
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountAction',
                ClassToInject__c = 'DomainProcessCoordinatorTest.HandlerNotExtendTriggerHandlerBase',
                IsActive__c = true,
                OrderOfExecution__c = 3,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeInsert'
            ),
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.SyncCurrencyWithCountry',
                IsActive__c = true,
                OrderOfExecution__c = 2,
                TriggerOperation__c = 'beforeInsert'
            )
        };

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(null, newList, null, newMap, true, false, false, true, false, false, true));

        try {
            new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();
            System.assert(false, 'should throw exception');
        } catch (Exception ex) {
            System.debug(ex);
            System.assert(true);
        }
    }

    @IsTest
    private static void shouldNotRunHandlerWhenNoHandlerFound() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>();

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(null, newList, null, newMap, true, false, false, true, false, false, true));

        new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();

        List<User> expectedResult = new List<User>{
            new User(Id = newList[0].Id, LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = newList[1].Id, LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = newList[2].Id, LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = newList[3].Id, LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        System.assertEquals(expectedResult, newList);
    }

    @IsTest
    private static void shouldRunInProgressEntryWhenTriggerCallTrigger() {
        List<DomainProcessBinding__mdt> config = new List<DomainProcessBinding__mdt>{
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.SyncCurrencyWithCountry',
                IsActive__c = true,
                OrderOfExecution__c = 2,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeUpdate'
            ),
            new DomainProcessBinding__mdt(
                Label = 'RichmondAccountCriteria',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCountryToUSAWhenNameContainMAGA',
                IsActive__c = true,
                OrderOfExecution__c = 1,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeUpdate'
            ),
            new DomainProcessBinding__mdt(
                Label = 'DallasAccountAction',
                ClassToInject__c = 'DomainProcessCoordinatorTest.setCityAsCapitalOfCountry',
                IsActive__c = true,
                OrderOfExecution__c = 3,
                RelatedDomainBindingSObject__c = 'User',
                TriggerOperation__c = 'beforeUpdate'
            )
        };

        DomainProcessCoordinator.setMockDomainProcessBindings(config);

        List<User> newList = new List<User>{
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'AUD'),
            new User(Id = fflib_IDGenerator.generate(User.SObjectType), LastName = 'MAGA111', Country = 'China', CurrencyIsoCode = 'AUD')
        };
        List<User> oldList = new List<User>{
            new User(Id = newList[0].Id, LastName = 'Test1'),
            new User(Id = newList[1].Id, LastName = 'Test2'),
            new User(Id = newList[2].Id, LastName = 'Test3'),
            new User(Id = newList[3].Id, LastName = 'MAGA111')
        };
        Map<Id, User> newMap = new Map<Id, User>(newList);
        Map<Id, User> oldMap = new Map<Id, User>(oldList);

        DomainProcessCoordinator.setMockTriggerParameters(new TriggerParameters(oldList, newList, oldMap, newMap, true, false, false, false, true, false, true));

        new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();

        List<User> expectedResult = new List<User>{
            new User(Id = newList[0].Id, LastName = 'Test1', Country = 'China', CurrencyIsoCode = 'CNY', City = 'Beijing', Street = 'Tian an men Square'),
            new User(Id = newList[1].Id, LastName = 'Test2', Country = 'China', CurrencyIsoCode = 'CNY', City = 'Beijing', Street = 'Tian an men Square'),
            new User(Id = newList[2].Id, LastName = 'Test3', Country = 'America', CurrencyIsoCode = 'USD', City = 'Washington, D.C.'),
            new User(Id = newList[3].Id, LastName = 'MAGA111', Country = 'America', CurrencyIsoCode = 'USD', City = 'Washington, D.C.')
        };
        System.assertEquals(expectedResult, newList);
    }

    public class SyncCurrencyWithCountry extends TriggerHandlerBase {
        public override void mainEntry(TriggerParameters tp) {
            List<User> newList = (List<User>) tp.newList;
            for (User user : newList) {
                if (user.Country == 'China') {
                    user.CurrencyIsoCode = 'CNY';
                }
                if (user.Country == 'America') {
                    user.CurrencyIsoCode = 'USD';
                }
            }
        }
    }

    public class setCountryToUSAWhenNameContainMAGA extends TriggerHandlerBase {
        public override void mainEntry(TriggerParameters tp) {
            List<User> newList = (List<User>) tp.newList;
            for (User user : newList) {
                if (user.LastName.contains('MAGA')) {
                    user.Country = 'America';
                }
            }
        }
    }

    public class setCityAsCapitalOfCountry extends TriggerHandlerBase {
        public override void mainEntry(TriggerParameters tp) {
            List<User> newList = (List<User>) tp.newList;
            List<User> userListToUpdate = new List<User>();
            for (User user : newList) {
                if (user.Country == 'China') {
                    user.City = 'Beijing';
                    userListToUpdate.add(user);
                }
                if (user.Country == 'America') {
                    user.City = 'Washington, D.C.';
                }
            }
            if (tp.operationType == 'Update') {
                DomainProcessCoordinator.setMockTriggerParameters(
                    new TriggerParameters(
                        userListToUpdate,
                        userListToUpdate,
                        new Map<Id, SObject>(userListToUpdate),
                        new Map<Id, SObject>(userListToUpdate),
                        true,
                        false,
                        false,
                        false,
                        true,
                        false,
                        true
                    )
                );
                new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();
            }
        }

        public override void inProgressEntry(TriggerParameters tp) {
            List<User> newList = (List<User>) tp.newList;
            for (User user : newList) {
                if (user.Country == 'China') {
                    user.Street = 'Tian an men Square';
                }
            }
        }
    }

    public class HandlerNotExtendTriggerHandlerBase {
        public void mainEntry(TriggerParameters tp) {
            List<User> newList = (List<User>) tp.newList;
            for (User user : newList) {
                if (user.Country == 'China') {
                    user.City = 'Beijing';
                }
                if (user.Country == 'America') {
                    user.City = 'Washington, D.C.';
                }
            }
        }
    }
}
