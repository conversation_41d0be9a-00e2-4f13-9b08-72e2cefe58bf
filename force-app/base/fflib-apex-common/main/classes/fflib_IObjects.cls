/**
 * Copyright (c), FinancialForce.com, inc
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 *   are permitted provided that the following conditions are met:
 *
 * - Redistributions of source code must retain the above copyright notice,
 *      this list of conditions and the following disclaimer.
 * - Redistributions in binary form must reproduce the above copyright notice,
 *      this list of conditions and the following disclaimer in the documentation
 *      and/or other materials provided with the distribution.
 * - Neither the name of the FinancialForce.com, inc nor the names of its contributors
 *      may be used to endorse or promote products derived from this software without
 *      specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 *  OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL
 *  THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 *  EXEMPLARY, OR CO<PERSON><PERSON>QUE<PERSON><PERSON><PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 *  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 **/
public interface fflib_IObjects extends fflib_IDomain {
    /**
     * @param value Values to check if they are part of the domain
     *
     * @return True if the provided value is part of the domain
     */
    Boolean contains(Object value);

    /**
     * @param values Values to check if they are part of the domain
     *
     * @return True if all the provided values are part of the domain
     */
    Boolean containsAll(List<Object> values);

    /**
     * @param values Values to check if they are part of the domain
     *
     * @return True if all the provided values are part of the domain
     */
    Boolean containsAll(Set<Object> values);

    /**
     * @param value Value to check if it is part of the domain
     *
     * @return True if the provided value is not part of the domain
     */
    Boolean containsNot(Object value);

    /**
     * @param values Values to check if they are part of the domain
     *
     * @return True if all the provided values are not part of the domain
     */
    Boolean containsNot(List<Object> values);

    /**
     * @param values Values to check if they are part of the domain
     *
     * @return True if all the provided values are not part of the domain
     */
    Boolean containsNot(Set<Object> values);

    /**
     * @return Returns True is the domain is empty
     */
    Boolean isEmpty();

    /**
     * @return Returns True is the domain has objects
     */
    Boolean isNotEmpty();

    /**
     * @return Returns the amount of records contained in the domain
     */
    Integer size();
}
