@isTest(IsParallel=true)
private class DateUitlsTest {
    @isTest
    static void toTheeMonthFormat() {
        Date d = Date.newInstance(2017, 3, 1);
        System.assertEquals('01-Mar-2017', DateUtils.toString(d, '20-Jan-YYYY'));
    }

    @isTest
    static void toTwoDigitalForSingleDayMonth() {
        Date d = Date.newInstance(2017, 11, 1);
        System.assertEquals('2017-11-01', DateUtils.toString(d, 'YYYY-01-20'));
    }

    @isTest
    static void returnEmptyStringIfDateNull() {
        Date d;
        System.assertEquals('', DateUtils.toString(d, '20-Jan-YYYY'));
    }

    @isTest
    static void shouldReturnCurrentWeekMonday() {
        Date d = Date.newInstance(2019, 1, 1);
        System.assertEquals('2018-12-31', DateUtils.toString(DateUtils.getCurrentWeekMonday(d), 'YYYY-01-20'));
    }

    @isTest
    static void shouldReturnFirstMondayOfMonth() {
        Date d = Date.newInstance(2019, 1, 12);
        System.assertEquals('2019-01-07', DateUtils.toString(DateUtils.getCurrentMonthFirstWeekMonDay(d), 'YYYY-01-20'));
    }

    @isTest
    static void shouldReturnFirstDayOfMonth() {
        Date d = Date.newInstance(2019, 7, 1);
        System.assertEquals('2019-07-01', DateUtils.toString(DateUtils.getCurrentMonthFirstWeekMonDay(d), 'YYYY-01-20'));
    }

    @isTest
    static void shouldReturnLastDayOfMonth() {
        Date d = Date.newInstance(2019, 2, 1);
        System.assertEquals('2019-02-28', DateUtils.toString(DateUtils.getCurrentMonthLastDay(d), 'YYYY-01-20'));
    }
}
