@IsTest(IsParallel=true)
private class MapUtilsTest {
    @IsTest
    static void testPutToValueSetWithSingleValue() {
        Map<Id, Set<Id>> testMap = new Map<Id, Set<Id>>();
        Id key = Id.valueOf('***************');
        Id value = Id.valueOf('***************');

        MapUtils.putToValueSet(testMap, key, value);

        System.assertEquals(1, testMap.size());
        System.assert(testMap.containsKey(key));
        System.assert(testMap.get(key).contains(value));
    }

    @IsTest
    static void testPutToValueSetWithMultipleValues() {
        Map<Id, Set<Id>> testMap = new Map<Id, Set<Id>>();
        Id key = Id.valueOf('***************');
        Set<Id> values = new Set<Id>{ Id.valueOf('***************'), Id.valueOf('***************') };

        MapUtils.putToValueSet(testMap, key, values);

        System.assertEquals(1, testMap.size());
        System.assert(testMap.containsKey(key));
        System.assert(testMap.get(key).containsAll(values));
    }

    @IsTest
    static void testPutToValueSetWithStringKeyAndSingleValue() {
        Map<String, Set<Id>> testMap = new Map<String, Set<Id>>();
        String key = 'testKey';
        Id value = Id.valueOf('***************');

        MapUtils.putToValueSet(testMap, key, value);

        System.assertEquals(1, testMap.size());
        System.assert(testMap.containsKey(key));
        System.assert(testMap.get(key).contains(value));
    }

    @IsTest
    static void testPutToValueSetWithStringKeyAndMultipleValues() {
        Map<String, Set<Id>> testMap = new Map<String, Set<Id>>();
        String key = 'testKey';
        Set<Id> values = new Set<Id>{ Id.valueOf('***************'), Id.valueOf('***************') };

        MapUtils.putToValueSet(testMap, key, values);

        System.assertEquals(1, testMap.size());
        System.assert(testMap.containsKey(key));
        System.assert(testMap.get(key).containsAll(values));
    }

    @IsTest
    static void testPutToValueListWithSingleValue() {
        Map<Id, List<SObject>> testMap = new Map<Id, List<SObject>>();
        Id key = Id.valueOf('***************');
        SObject value = new Account(Name = 'Test Account');

        MapUtils.putToValueList(testMap, key, value);

        System.assertEquals(1, testMap.size());
        System.assert(testMap.containsKey(key));
        System.assert(testMap.get(key).contains(value));
    }

    @IsTest
    static void testPutToValueListWithMultipleValues() {
        Map<Id, List<SObject>> testMap = new Map<Id, List<SObject>>();
        Id key = Id.valueOf('***************');
        List<SObject> values = new List<SObject>{ new Account(Name = 'Test Account 1'), new Account(Name = 'Test Account 2') };

        MapUtils.putToValueList(testMap, key, values);

        System.assertEquals(1, testMap.size());
        System.assert(testMap.containsKey(key));

        for (SObject val : values) {
            System.assert(testMap.get(key).contains(val));
        }
    }

    @IsTest
    static void testGetMapValueIdList() {
        Map<Id, List<Id>> testMap = new Map<Id, List<Id>>();
        List<Id> values1 = new List<Id>{ Id.valueOf('***************'), Id.valueOf('***************') };
        List<Id> values2 = new List<Id>{ Id.valueOf('***************'), Id.valueOf('***************') };

        testMap.put(Id.valueOf('***************'), values1);
        testMap.put(Id.valueOf('***************'), values2);

        List<Id> result = MapUtils.getMapValueIdList(testMap);

        System.assertEquals(4, result.size());

        for (Id val : values1) {
            System.assert(result.contains(val));
        }
        for (Id val : values2) {
            System.assert(result.contains(val));
        }
    }

    @IsTest
    static void testGetMapValueIdSet() {
        Map<Id, Set<Id>> testMap = new Map<Id, Set<Id>>();
        Set<Id> values1 = new Set<Id>{ Id.valueOf('***************'), Id.valueOf('***************') };
        Set<Id> values2 = new Set<Id>{ Id.valueOf('***************'), Id.valueOf('***************') };

        testMap.put(Id.valueOf('***************'), values1);
        testMap.put(Id.valueOf('***************'), values2);

        Set<Id> result = MapUtils.getMapValueIdSet(testMap);

        System.assertEquals(4, result.size());
        System.assert(result.containsAll(values1));
        System.assert(result.containsAll(values2));
    }

    @IsTest
    static void testMergeSetValueMap() {
        Map<Id, Set<Id>> map1 = new Map<Id, Set<Id>>();
        map1.put(Id.valueOf('***************'), new Set<Id>{ Id.valueOf('***************'), Id.valueOf('***************') });

        Map<Id, Set<Id>> map2 = new Map<Id, Set<Id>>();
        map2.put(Id.valueOf('***************'), new Set<Id>{ Id.valueOf('***************') });
        map2.put(Id.valueOf('***************'), new Set<Id>{ Id.valueOf('***************') });

        Map<Id, Set<Id>> result = MapUtils.mergeSetValueMap(map1, map2);

        System.assertEquals(2, result.size());
        System.assert(result.get(Id.valueOf('***************')).containsAll(new Set<Id>{ Id.valueOf('***************'), Id.valueOf('***************'), Id.valueOf('***************') }));
        System.assert(result.get(Id.valueOf('***************')).contains(Id.valueOf('***************')));
    }
}
