@IsTest(IsParallel=true)
private class TimeUtilsTest {
    @IsTest
    static void shouldReturnRealValueWhenCall() {
        System.assertEquals(Datetime.now(), TimeUtils.now());
        System.assertEquals(Date.today(), TimeUtils.today());
    }

    @IsTest
    static void shouldReturnMockValueWhenCall() {
        Datetime mockTime = Datetime.newInstance(2019, 1, 1).addHours(6);
        TimeUtils.now = mockTime;
        Date mockDate = Date.newInstance(2019, 1, 1);
        TimeUtils.today = mockDate;

        System.assertEquals(mockTime, TimeUtils.now());
        System.assertEquals(mockDate, TimeUtils.today());
    }
}
