@isTest
public with sharing class TestShareDMLUtils {
    private static TWPSA_TestDataFactory dataFactory = new TWPSA_TestDataFactory();

    private static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    private static Contact contact;

    private static pse__Proj__c project;

    private static pse__Milestone__c subproject;

    private final static User user1 = testFixture.prepareUser('PSA - Base', 'user1');
    private final static User user2 = testFixture.prepareUser('PSA - Base', 'user2');

    static void prepareData() {
        testFixture.preparePermissionControl(UserInfo.getUserId());
        testFixture.prepareUser();
        contact = testFixture.prepareContact(false);
        contact.Payroll_BU__c = 'TWUSA';
        insert contact;
        testFixture.prepareAccount(true, 'NOR_MAL');
        project = testFixture.prepareProject(false);
        project.Project_Code_FF__c = 'LEAVE';
        insert project;
        subproject = testFixture.prepareMilestone(false);
        subproject.Sub_Project_Code__c = 'ANNUAL_LV';
        insert subproject;
        testFixture.prepareAssignment(true);
    }

    @isTest
    static void ShouldGetRightErrorMessageWhenDeleteTimecardShare() {
        TriggerToggle.turnOff();
        prepareData();
        User systemAdmin = dataFactory.createUser('System Administrator', 'admin');
        systemAdmin.CommunityNickname = 'admin';
        systemAdmin.Alias = 'admin';
        pse__Timecard_Header__c timecard = testFixture.prepareTimecard(true, true);

        Id userId1 = user1.Id;
        Id userId2 = user2.Id;

        System.runAs(systemAdmin) {
            pse__Timecard_Header__Share timecardShare1 = GenerateShareEntity.generateTimeShare(GenerateShareEntity.EDIT, timecard.Id, userId1);
            insert timecardShare1;

            pse__Timecard_Header__Share timecardShare2 = GenerateShareEntity.generateTimeShare(GenerateShareEntity.EDIT, timecard.Id, userId2);
            timecardShare2.Id = '02c0100001rYvgrAAC';

            List<SObject> SObjectList = new List<SObject>{ timecardShare1, timecardShare2 };
            String objApi = SObjectList.get(0).getSObjectType().getDescribe().getName();

            System.Test.startTest();
            String errorMessage = ShareDMLUtils.getErrorMsgFromDeleteResult(Database.delete(SObjectList, false), SObjectList, objApi);
            String exceptedErrorMessage =
                'pse__Timecard_Header__Share delete failed. Error Content:\n' +
                'Error Message: entity type cannot be deleted\n' +
                'Id - 02c0100001rYvgrAAC' +
                ', ObjectId - ' +
                timecard.Id +
                ', UserOrGroupId - ' +
                userId2 +
                '\n';
            System.assertEquals(exceptedErrorMessage, errorMessage);
            System.Test.stopTest();
        }
    }

    @isTest
    static void ShouldGetRightErrorMessageWhenDeleteAccountShare() {
        TriggerToggle.turnOff();
        User systemAdmin = dataFactory.createUser('System Administrator', 'admin');
        systemAdmin.CommunityNickname = 'admin';
        systemAdmin.Alias = 'admin';
        Account account = testFixture.prepareAccount(true);

        Id userId1 = user1.Id;
        Id userId2 = user2.Id;

        System.runAs(systemAdmin) {
            Accountshare accountShare1 = GenerateShareEntity.generateAccountShare(GenerateShareEntity.EDIT, account.Id, userId1);
            insert accountShare1;

            Accountshare accountShare2 = GenerateShareEntity.generateAccountShare(GenerateShareEntity.EDIT, account.Id, userId2);
            insert accountShare2;

            Id accountShareId = accountShare2.Id;
            delete accountShare2;

            List<SObject> SObjectList = new List<SObject>{ accountShare1, accountShare2 };
            String objApi = SObjectList.get(0).getSObjectType().getDescribe().getName();

            System.Test.startTest();
            String errorMessage = ShareDMLUtils.getErrorMsgFromDeleteResult(Database.delete(SObjectList, false), SObjectList, objApi);
            String exceptedErrorMessage =
                'AccountShare delete failed. Error Content:\n' +
                'Error Message: id does not exist\n' +
                'Id - ' +
                accountShareId +
                ', ObjectId - ' +
                account.Id +
                ', UserOrGroupId - ' +
                userId2 +
                '\n';
            System.assertEquals(exceptedErrorMessage, errorMessage);
            System.Test.stopTest();
        }
    }

    @isTest
    static void ShouldGetRightErrorMessageWhenInsertTimecardShare() {
        TriggerToggle.turnOff();
        prepareData();
        User systemAdmin = dataFactory.createUser('System Administrator', 'admin');
        systemAdmin.CommunityNickname = 'admin';
        systemAdmin.Alias = 'admin';
        insert systemAdmin;

        pse__Timecard_Header__c timecard = testFixture.prepareTimecard(true, true);

        Id userId1 = user1.Id;
        Id userId2 = user2.Id;

        System.runAs(systemAdmin) {
            pse__Timecard_Header__Share timecardShare1 = GenerateShareEntity.generateTimeShare(GenerateShareEntity.EDIT, timecard.Id, userId1);
            pse__Timecard_Header__Share timecardShare2 = new pse__Timecard_Header__Share();

            timecardShare2.ParentId = timecard.Id;
            timecardShare2.UserOrGroupId = userId2;

            List<SObject> SObjectList = new List<SObject>{ timecardShare1, timecardShare2 };
            String objApi = SObjectList.get(0).getSObjectType().getDescribe().getName();

            System.Test.startTest();
            String errorMessage = ShareDMLUtils.getErrorMsgFromInsertResult(Database.insert(SObjectList, false), SObjectList, objApi);
            String exceptedErrorMessage =
                'pse__Timecard_Header__Share insert failed. Error Content:\n' +
                'Error Message: Required fields are missing: [AccessLevel]\n' +
                'ObjectId - ' +
                timecard.Id +
                ', UserOrGroupId - ' +
                userId2 +
                '\n';
            System.assertEquals(exceptedErrorMessage, errorMessage);
            System.Test.stopTest();
        }
    }

    @isTest
    static void ShouldGetRightErrorMessageWhenInsertAccountShare() {
        TriggerToggle.turnOff();
        User systemAdmin = dataFactory.createUser('System Administrator', 'admin');
        systemAdmin.CommunityNickname = 'admin';
        systemAdmin.Alias = 'admin';
        insert systemAdmin;
        Account account = testFixture.prepareAccount(true);

        Id userId1 = user1.Id;
        Id userId2 = user2.Id;

        System.runAs(systemAdmin) {
            Accountshare accountShare1 = GenerateShareEntity.generateAccountShare(GenerateShareEntity.EDIT, account.Id, userId1);

            Accountshare accountShare2 = new AccountShare();
            accountShare2.AccountAccessLevel = GenerateShareEntity.EDIT;
            accountShare2.AccountId = account.Id;
            accountShare2.UserOrGroupId = userId2;

            List<SObject> SObjectList = new List<SObject>{ accountShare1, accountShare2 };
            String objApi = SObjectList.get(0).getSObjectType().getDescribe().getName();

            System.Test.startTest();
            String errorMessage = ShareDMLUtils.getErrorMsgFromInsertResult(Database.insert(SObjectList, false), SObjectList, objApi);
            String exceptedErrorMessage =
                'AccountShare insert failed. Error Content:\n' +
                'Error Message: missing required field: [OpportunityAccessLevel]\n' +
                'ObjectId - ' +
                account.Id +
                ', UserOrGroupId - ' +
                userId2 +
                '\n';
            System.assertEquals(exceptedErrorMessage, errorMessage);
            System.Test.stopTest();
        }
    }

    @isTest
    static void ShouldGetRightErrorMessageWhenUpsertTimecardShare() {
        TriggerToggle.turnOff();
        prepareData();
        User systemAdmin = dataFactory.createUser('System Administrator', 'admin');
        systemAdmin.CommunityNickname = 'admin';
        systemAdmin.Alias = 'admin';
        insert systemAdmin;

        pse__Timecard_Header__c timecard = testFixture.prepareTimecard(true, true);

        Id userId1 = user1.Id;
        Id userId2 = user2.Id;

        System.runAs(systemAdmin) {
            pse__Timecard_Header__Share timecardShare1 = GenerateShareEntity.generateTimeShare(GenerateShareEntity.EDIT, timecard.Id, userId1);
            pse__Timecard_Header__Share timecardShare2 = new pse__Timecard_Header__Share();

            timecardShare2.ParentId = timecard.Id;
            timecardShare2.UserOrGroupId = userId2;

            List<SObject> SObjectList = new List<SObject>{ timecardShare1, timecardShare2 };
            String objApi = SObjectList.get(0).getSObjectType().getDescribe().getName();

            System.Test.startTest();
            String errorMessage = ShareDMLUtils.getErrorMsgFromUpsertResult(Database.upsert(SObjectList, false), SObjectList, objApi);
            String exceptedErrorMessage =
                'pse__Timecard_Header__Share upsert failed. Error Content:\n' +
                'Error Message: Required fields are missing: [AccessLevel]\n' +
                'ObjectId - ' +
                timecard.Id +
                ', UserOrGroupId - ' +
                userId2 +
                '\n';
            System.assertEquals(exceptedErrorMessage, errorMessage);
            System.Test.stopTest();
        }
    }

    @isTest
    static void ShouldGetRightErrorMessageWhenUpsertAccountShare() {
        TriggerToggle.turnOff();
        User systemAdmin = dataFactory.createUser('System Administrator', 'admin');
        systemAdmin.CommunityNickname = 'admin';
        systemAdmin.Alias = 'admin';
        insert systemAdmin;
        Account account = testFixture.prepareAccount(true);

        Id userId1 = user1.Id;
        Id userId2 = user2.Id;

        System.runAs(systemAdmin) {
            Accountshare accountShare1 = GenerateShareEntity.generateAccountShare(GenerateShareEntity.EDIT, account.Id, userId1);

            Accountshare accountShare2 = new AccountShare();
            accountShare2.AccountAccessLevel = GenerateShareEntity.EDIT;
            accountShare2.AccountId = account.Id;
            accountShare2.UserOrGroupId = userId2;

            List<SObject> SObjectList = new List<SObject>{ accountShare1, accountShare2 };
            String objApi = SObjectList.get(0).getSObjectType().getDescribe().getName();

            System.Test.startTest();
            String errorMessage = ShareDMLUtils.getErrorMsgFromUpsertResult(Database.upsert(SObjectList, false), SObjectList, objApi);
            String exceptedErrorMessage =
                'AccountShare upsert failed. Error Content:\n' +
                'Error Message: missing required field: [OpportunityAccessLevel]\n' +
                'ObjectId - ' +
                account.Id +
                ', UserOrGroupId - ' +
                userId2 +
                '\n';
            System.assertEquals(exceptedErrorMessage, errorMessage);
            System.Test.stopTest();
        }
    }
}
