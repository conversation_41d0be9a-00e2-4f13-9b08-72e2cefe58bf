/**
 * Test class for DMLService
 * Demonstrates how to mock the DML operations using fflib_ApexMocks
 */
@IsTest(IsParallel=true)
private class DMLServiceMockTest {
    @IsTest
    static void testDMLServiceWithMock() {
        // Create mocks
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        IDMLService mockDMLService = (IDMLService) mocks.mock(IDMLService.class);

        // Set the mock in the Application
        Application.Service.setMock(IDMLService.class, mockDMLService);

        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        List<Account> accounts = new List<Account>{ testAccount };
        Set<SObject> accountSet = new Set<SObject>{ testAccount };
        Logger logger = new Logger('Test');

        // Test DMLService directly
        Test.startTest();

        // Test insert
        DMLService.insertBySObjectType(accounts);

        // Test update
        DMLService.updateBySObjectType(accounts);

        // Test upsert
        DMLService.upsertAndGetErrorMsg(accounts, DMLService.UPSERT_DML);

        // Test delete
        DMLService.deleteAndGetErrorMsg(accounts);

        // Test other methods
        DMLService.insertBySObjectType(accountSet);
        DMLService.upsertAndLogErrorMsg(accounts, DMLService.UPSERT_DML, logger);
        DMLService.deleteAndLogErrorMsg(accounts, logger);

        Test.stopTest();

        // Verify method calls
        ((IDMLService) mocks.verify(mockDMLService)).insertBySObjectType(accounts);
        ((IDMLService) mocks.verify(mockDMLService)).updateBySObjectType(accounts);
        ((IDMLService) mocks.verify(mockDMLService)).upsertAndGetErrorMsg(accounts, DMLService.UPSERT_DML);
        ((IDMLService) mocks.verify(mockDMLService)).deleteAndGetErrorMsg(accounts);
        ((IDMLService) mocks.verify(mockDMLService)).insertBySObjectType(accountSet);
        ((IDMLService) mocks.verify(mockDMLService)).upsertAndLogErrorMsg(accounts, DMLService.UPSERT_DML, logger);
        ((IDMLService) mocks.verify(mockDMLService)).deleteAndLogErrorMsg(accounts, logger);
    }

    @IsTest
    static void testDMLServiceWithStubbing() {
        // Create mocks
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        IDMLService mockDMLService = (IDMLService) mocks.mock(IDMLService.class);

        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        List<Account> accounts = new List<Account>{ testAccount };
        Logger logger = new Logger('Test');

        // Set up stubbing
        String mockErrorMsg = 'Mock error message';
        Map<Id, String> mockErrorMap = new Map<Id, String>{ fflib_IDGenerator.generate(Account.SObjectType) => 'Error' };
        List<Id> mockSuccessIds = new List<Id>{ fflib_IDGenerator.generate(Account.SObjectType) };

        mocks.startStubbing();
        mocks.when(mockDMLService.upsertAndGetErrorMsg(accounts, DMLService.UPSERT_DML)).thenReturn(mockErrorMsg);
        mocks.when(mockDMLService.updateAndGetIdErrorMsgMap(accounts)).thenReturn(mockErrorMap);
        mocks.when(mockDMLService.partialUpdateAndGetSuccessIds(accounts, logger)).thenReturn(mockSuccessIds);
        mocks.stopStubbing();

        // Set the mock in the Application
        Application.Service.setMock(IDMLService.class, mockDMLService);

        // Test methods that return values
        Test.startTest();

        String errorMsg = DMLService.upsertAndGetErrorMsg(accounts, DMLService.UPSERT_DML);
        Map<Id, String> errorMap = DMLService.updateAndGetIdErrorMsgMap(accounts);
        List<Id> successIds = DMLService.partialUpdateAndGetSuccessIds(accounts, logger);

        Test.stopTest();

        // Verify results
        System.assertEquals(mockErrorMsg, errorMsg, 'Error message should match the stubbed value');
        System.assertEquals(mockErrorMap, errorMap, 'Error map should match the stubbed value');
        System.assertEquals(mockSuccessIds, successIds, 'Success IDs should match the stubbed value');
    }
}
