@IsTest(IsParallel=true)
private class CollectionUtilsTest {
    @IsTest
    static void testGroupSObjectsByTypeWithNonNullList() {
        // given
        List<SObject> records = new List<SObject>{
            new Account(Name = 'Acme'),
            new Account(Name = 'Widget Co.'),
            new Account(Name = 'Globes'),
            new Contact(FirstName = 'Test3', Email = '<EMAIL>'),
            new Contact(FirstName = 'Test4', Email = '<EMAIL>'),
            new Opportunity(Name = 'Oppo1'),
            new Opportunity(Name = 'Oppo2')
        };

        // when
        Map<SObjectType, List<SObject>> recordsByType = CollectionUtils.groupSObjectsByType(records);

        // then
        Integer expectedNumSObjectTypes = 3;
        Set<SObjectType> expectedSObjectTypes = new Set<SObjectType>{ Account.SObjectType, Contact.SObjectType, Opportunity.SObjectType };
        Integer expectedNumAccounts = 3;
        Integer expectedNumContacts = 2;
        Integer expectedNumOpportunities = 2;

        System.assertEquals(expectedNumSObjectTypes, recordsByType.keySet().size(), 'Unexpected number of SObjectTypes');
        System.assertEquals(expectedSObjectTypes, recordsByType.keySet(), 'Unexpected SObjectTypes');
        System.assertEquals(expectedNumAccounts, recordsByType.get(Account.SObjectType).size(), 'Unexpected number of Accounts');
        System.assertEquals(expectedNumContacts, recordsByType.get(Contact.SObjectType).size(), 'Unexpected number of Contacts');
        System.assertEquals(expectedNumOpportunities, recordsByType.get(Opportunity.SObjectType).size(), 'Unexpected number of Opportunities');
    }

    @IsTest
    static void testGroupSObjectsByTypeWithNullInput() {
        // given
        List<SObject> records = null;
        // when
        try {
            CollectionUtils.groupSObjectsByType(records);
            System.assert(false, 'Expected exception to be thrown but none was.');
        } catch (NullPointerException e) {
            // then
            System.assertEquals('Attempt to de-reference a null object', e.getMessage());
        }
    }

    @IsTest
    static void testGroupSObjectsByTypeWithEmptyList() {
        Map<SObjectType, List<SObject>> recordsByType = CollectionUtils.groupSObjectsByType(new List<SObject>());
        System.assertEquals(0, recordsByType.keySet().size());
    }

    @IsTest
    static void testGroupSObjectsByTypeWithNullRecord() {
        // given
        List<SObject> records = new List<SObject>{
            new Account(Name = 'Acme'),
            new Account(Name = 'Widget Co.'),
            new Account(Name = 'Globes'),
            null,
            new Contact(FirstName = 'Test3', Email = '<EMAIL>'),
            new Contact(FirstName = 'Test4', Email = '<EMAIL>'),
            null,
            new Opportunity(Name = 'Oppo1'),
            new Opportunity(Name = 'Oppo2')
        };

        // when
        Map<SObjectType, List<SObject>> recordsByType = CollectionUtils.groupSObjectsByType(records);

        // then
        Integer expectedNumSObjectTypes = 3;
        Set<SObjectType> expectedSObjectTypes = new Set<SObjectType>{ Account.SObjectType, Contact.SObjectType, Opportunity.SObjectType };
        Integer expectedNumAccounts = 3;
        Integer expectedNumContacts = 2;
        Integer expectedNumOpportunities = 2;

        System.assertEquals(expectedNumSObjectTypes, recordsByType.keySet().size(), 'Unexpected number of SObjectTypes');
        System.assertEquals(expectedSObjectTypes, recordsByType.keySet(), 'Unexpected SObjectTypes');
        System.assertEquals(expectedNumAccounts, recordsByType.get(Account.SObjectType).size(), 'Unexpected number of Accounts');
        System.assertEquals(expectedNumContacts, recordsByType.get(Contact.SObjectType).size(), 'Unexpected number of Contacts');
        System.assertEquals(expectedNumOpportunities, recordsByType.get(Opportunity.SObjectType).size(), 'Unexpected number of Opportunities');
    }

    @IsTest
    static void testGetIdsNotInMap() {
        // given
        pse__Timecard_Header__c tc = new pse__Timecard_Header__c(
            id = fflib_IDGenerator.generate(pse__Timecard_Header__c.SobjectType),
            pse__Milestone__c = fflib_IDGenerator.generate(pse__Milestone__c.SobjectType)
        );

        List<pse__Timecard_Header__c> tcList = new List<pse__Timecard_Header__c>{ tc };

        SObjectField fieldName = pse__Timecard_Header__c.pse__Milestone__c;
        Map<Id, SObject> checkTarget = new Map<Id, SObject>();

        //when
        Set<Id> idsNotInMap = CollectionUtils.getIdsNotInMap(tcList, fieldName, checkTarget);

        //then
        System.assertEquals(1, idsNotInMap.size());
    }

    @IsTest
    static void testGetValueByMapMapValue() {
        // given
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id getNotGetResultContactId = fflib_IDGenerator.generate(Contact.SObjectType);
        System.assertNotEquals(contactId, getNotGetResultContactId);
        Id oppoId = fflib_IDGenerator.generate(Opportunity.SObjectType);
        OpportunityContactRole ocr = new OpportunityContactRole(Id = fflib_IDGenerator.generate(OpportunityContactRole.SObjectType), ContactId = contactId, OpportunityId = oppoId);

        Map<Id, Map<Id, Object>> conIdOppoIdAndEarliestContactRoleMap = new Map<Id, Map<Id, OpportunityContactRole>>{ contactId => new Map<Id, OpportunityContactRole>{ oppoId => ocr } };

        //when
        OpportunityContactRole ocrGetByMapMap = (OpportunityContactRole) CollectionUtils.getMapMapValue(conIdOppoIdAndEarliestContactRoleMap, contactId, oppoId);
        OpportunityContactRole ocrCanNotGet = (OpportunityContactRole) CollectionUtils.getMapMapValue(conIdOppoIdAndEarliestContactRoleMap, getNotGetResultContactId, oppoId);

        //then
        System.assertEquals(ocr, ocrGetByMapMap);
        System.assertEquals(null, ocrCanNotGet);
    }

    @IsTest
    static void testConvertListToMapByUnionKey() {
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id oppoId = fflib_IDGenerator.generate(Opportunity.SObjectType);
        OpportunityContactRole ocr = new OpportunityContactRole(Id = fflib_IDGenerator.generate(OpportunityContactRole.SObjectType), ContactId = contactId, OpportunityId = oppoId);

        Map<String, SObject> ocrMap = CollectionUtils.convertListToMapByUnionKey(
            new List<OpportunityContactRole>{ ocr },
            new List<Schema.SObjectField>{ OpportunityContactRole.OpportunityId, OpportunityContactRole.ContactId }
        );
        System.assertEquals(oppoId.toString() + contactId.toString(), new List<String>(ocrMap.keySet())[0]);
    }

    @IsTest
    static void shouldReturnNullWhenGetLatestRecordByDateTimeFieldByEmptyList() {
        List<Account> targetList = new List<Account>();
        Schema.SObjectField timeField = Account.LastModifiedDate;
        SObject result = CollectionUtils.getLatestRecordByDateTimeField(targetList, timeField);
        System.assertEquals(null, result);
    }

    @IsTest
    static void shouldReturnLatestRecordWhenGiveMultipleRecordList() {
        List<Account> targetList = new List<Account>();
        targetList.add(new Account(Name = 'Test Account 1'));
        targetList.add(new Account(Name = 'Test Account 2', LastModifiedDate = Datetime.valueOf('2024-05-20 07:39:39')));
        targetList.add(new Account(Name = 'Test Account 3'));
        targetList.add(new Account(Name = 'Test Account 4', LastModifiedDate = Datetime.valueOf('2024-05-12 07:39:39')));
        Schema.SObjectField timeField = Account.LastModifiedDate;
        SObject result = CollectionUtils.getLatestRecordByDateTimeField(targetList, timeField);
        System.assertEquals(targetList[1], result);
    }

    @IsTest
    static void shouldConvertListToCustomKeySObjectMap() {
        List<Account> testAccounts = new List<Account>();
        for (Integer i = 0; i < 5; i++) {
            Account acc = new Account(Name = 'Test Account ' + i);
            testAccounts.add(acc);
        }

        Map<Object, SObject> accountMap = CollectionUtils.convertListToMap(testAccounts, Account.Name);

        System.assertEquals(testAccounts.size(), accountMap.size());
        for (Account acc : testAccounts) {
            System.assertEquals(acc.Id, accountMap.get(acc.Name).Id);
        }
    }

    @IsTest
    static void shouldSplitMultiPicklist() {
        String teststr = 'test1;test2;test3;';
        List<String> result = CollectionUtils.splitsMultiPicklist(teststr);

        Assert.areEqual(3, result.size());
    }

    @IsTest
    static void testAggregateFieldByGroup() {
        // given
        Id accountId1 = fflib_IDGenerator.generate(Account.SObjectType);
        Id accountId2 = fflib_IDGenerator.generate(Account.SObjectType);
        Id accountId3 = fflib_IDGenerator.generate(Account.SObjectType);
        Opportunity opp1 = new Opportunity(Opportunity_Contract_Value_in_USD__c = 10.0, AccountId = accountId1);
        Opportunity opp2 = new Opportunity(Opportunity_Contract_Value_in_USD__c = 20.0, AccountId = accountId1);
        Opportunity opp3 = new Opportunity(Opportunity_Contract_Value_in_USD__c = 30.0, AccountId = accountId2);
        Opportunity opp4 = new Opportunity(Opportunity_Contract_Value_in_USD__c = 40.0, AccountId = accountId2);
        Opportunity opp5 = new Opportunity(Opportunity_Contract_Value_in_USD__c = null, AccountId = accountId2);
        Opportunity opp6 = new Opportunity(Opportunity_Contract_Value_in_USD__c = null, AccountId = accountId3);

        List<Opportunity> records = new List<Opportunity>{ opp1, opp2, opp3, opp4, opp5, opp6 };

        // when
        Map<Id, Decimal> result = CollectionUtils.aggregateFieldByGroup(records, 'AccountId', 'Opportunity_Contract_Value_in_USD__c');

        // then
        System.assertEquals(2, result.size());
        System.assertEquals(30.0, result.get(accountId1));
        System.assertEquals(70.0, result.get(accountId2));
        System.assertEquals(null, result.get(accountId3));
    }

    @IsTest
    static void testAggregateEmptyRecords() {
        List<SObject> emptyRecords = new List<SObject>();
        Map<Id, Decimal> result = CollectionUtils.aggregateFieldByGroup(emptyRecords, 'CurrencyIsoCode', 'Opportunity_Contract_Value_in_USD__c');
        System.assertEquals(0, result.size());
    }

    @IsTest
    static void shouldReturnChangedRecords() {
        // Given
        List<SObject> newRecords = new List<SObject>();
        Map<Id, SObject> oldRecordMap = new Map<Id, SObject>();
        Set<String> fieldNames = new Set<String>{ 'StageName', 'CloseDate' };

        // Creating old Opportunity records
        Id oppoId1 = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Id oppoId2 = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Id oppoId3 = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Id oppoId4 = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Opportunity oldOpp1 = new Opportunity(Id = oppoId1, Name = 'Old Opportunity 1', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        Opportunity oldOpp2 = new Opportunity(Id = oppoId2, Name = 'Old Opportunity 2', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        Opportunity oldOpp3 = new Opportunity(Id = oppoId3, Name = 'Old Opportunity 3', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        Opportunity oldOpp4 = new Opportunity(Id = oppoId4, Name = 'Old Opportunity 4', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));

        oldRecordMap.putAll(new List<Opportunity>{ oldOpp1, oldOpp2, oldOpp3, oldOpp4 });

        // Creating new Opportunity records
        Opportunity newOpp1 = new Opportunity(Id = oppoId1, Name = 'New Opportunity 1', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        Opportunity newOpp2 = new Opportunity(Id = oppoId2, Name = 'New Opportunity 2', StageName = 'Closed Won', CloseDate = Date.newInstance(2023, 1, 1));
        Opportunity newOpp3 = new Opportunity(Id = oppoId3, Name = 'New Opportunity 3', StageName = 'Prospecting', CloseDate = Date.newInstance(2024, 1, 1));
        Opportunity newOpp4 = new Opportunity(Id = oppoId4, Name = 'New Opportunity 4', StageName = 'Closed Won', CloseDate = Date.newInstance(2024, 1, 1));

        newRecords.addAll(new List<Opportunity>{ newOpp1, newOpp2, newOpp3, newOpp4 });
        // When
        List<SObject> result = CollectionUtils.getChangedRecords(newRecords, oldRecordMap, fieldNames);

        // Then
        System.assertEquals(3, result.size());
        System.assertEquals(oppoId2, result[0].Id);
        System.assertEquals(oppoId3, result[1].Id);
        System.assertEquals(oppoId4, result[2].Id);
    }

    @IsTest
    static void shouldReturnEmptyWhenNoOldRecords() {
        // Given
        List<SObject> newRecords = new List<SObject>();
        Set<String> fieldNames = new Set<String>{ 'Name', 'StageName', 'CloseDate' };

        Opportunity newOpp1 = new Opportunity(Id = '0062w00000AXYZQAA5', Name = 'New Opportunity 1', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        newRecords.add(newOpp1);

        Opportunity newOpp2 = new Opportunity(Id = '0062w00000AXYZQAA6', Name = 'New Opportunity 2', StageName = 'Qualification', CloseDate = Date.newInstance(2023, 2, 1));
        newRecords.add(newOpp2);

        // When
        List<SObject> result = CollectionUtils.getChangedRecords(newRecords, null, fieldNames);

        // Then
        System.assertEquals(0, result.size());
    }

    @IsTest
    static void shouldReturnEmptyWhenNoFieldChanges() {
        // Given
        List<SObject> newRecords = new List<SObject>();
        Map<Id, SObject> oldRecordMap = new Map<Id, SObject>();
        Set<String> fieldNames = new Set<String>{ 'Name', 'StageName', 'CloseDate' };

        Opportunity oldOpp1 = new Opportunity(Id = '0062w00000AXYZQAA5', Name = 'Opportunity 1', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        oldRecordMap.put(oldOpp1.Id, oldOpp1);

        Opportunity newOpp1 = new Opportunity(Id = '0062w00000AXYZQAA5', Name = 'Opportunity 1', StageName = 'Prospecting', CloseDate = Date.newInstance(2023, 1, 1));
        newRecords.add(newOpp1);

        // When
        List<SObject> result = CollectionUtils.getChangedRecords(newRecords, oldRecordMap, fieldNames);

        // Then
        System.assertEquals(0, result.size());
    }

    @IsTest
    static void shouldReturnRecordsWithBlankFieldValues() {
        // Given
        List<SObject> records = new List<SObject>();
        Set<Schema.SObjectField> fields = new Set<Schema.SObjectField>{ Opportunity.Name, Opportunity.StageName, Opportunity.CloseDate };

        // Creating Opportunity records
        Opportunity opp1 = new Opportunity(Name = '', StageName = 'Prospecting', CloseDate = Date.today());
        Opportunity opp2 = new Opportunity(Name = 'Opportunity 2', StageName = '', CloseDate = Date.today());
        Opportunity opp3 = new Opportunity(Name = 'Opportunity 3', StageName = 'Qualification', CloseDate = null);
        Opportunity opp4 = new Opportunity(Name = 'Opportunity 4', StageName = 'Qualification', CloseDate = Date.today());
        records = new List<SObject>{ opp1, opp2, opp3, opp4 };

        // When
        List<SObject> result = CollectionUtils.getRecordsWithBlankFieldValues(records, fields);

        // Then
        System.assertEquals(3, result.size());
        System.assert(result.contains(opp1));
        System.assert(result.contains(opp2));
        System.assert(result.contains(opp3));
    }

    @IsTest
    static void shouldReturnEmptyWhenAllFieldsAreNotBlank() {
        // Given
        List<SObject> records = new List<SObject>();
        Set<Schema.SObjectField> fields = new Set<Schema.SObjectField>{ Opportunity.Name, Opportunity.StageName, Opportunity.CloseDate };

        Opportunity opp1 = new Opportunity(Name = 'Opportunity 1', StageName = 'Prospecting', CloseDate = Date.today());
        records.add(opp1);

        // When
        List<SObject> result = CollectionUtils.getRecordsWithBlankFieldValues(records, fields);

        // Then
        System.assertEquals(0, result.size());
    }

    @IsTest
    static void shouldHandleEmptyRecordsList() {
        // Given
        List<SObject> records = new List<SObject>();
        Set<Schema.SObjectField> fields = new Set<Schema.SObjectField>{ Opportunity.Name, Opportunity.StageName, Opportunity.CloseDate };

        // When
        List<SObject> result = CollectionUtils.getRecordsWithBlankFieldValues(records, fields);

        // Then
        System.assertEquals(0, result.size());
    }

    @IsTest
    static void shouldHandleEmptyFieldsSet() {
        // Given
        List<SObject> records = new List<SObject>();
        Set<Schema.SObjectField> fields = new Set<Schema.SObjectField>();

        Opportunity opp1 = new Opportunity(Name = '', StageName = 'Prospecting', CloseDate = Date.today());
        records.add(opp1);

        Opportunity opp2 = new Opportunity(Name = 'Opportunity 2', StageName = '', CloseDate = Date.today());
        records.add(opp2);

        // When
        List<SObject> result = CollectionUtils.getRecordsWithBlankFieldValues(records, fields);

        // Then
        System.assertEquals(0, result.size());
    }
}
