@IsTest(IsParallel=true)
private class UtilityJsonTest {
    @IsTest
    static void testSerializeRelationListWithEmptyMap() {
        Map<String, List<Object>> keyValuePairs = new Map<String, List<Object>>();

        String result = UtilityJson.serializeRelationList(keyValuePairs);
        System.assertEquals('{}', result);
    }

    @IsTest
    static void testSerializeRelationListWithEmptyList() {
        Map<String, List<Object>> keyValuePairs = new Map<String, List<Object>>();
        keyValuePairs.put('key1', new List<Object>());

        String result = UtilityJson.serializeRelationList(keyValuePairs);
        System.assertEquals('{"key1":{"totalSize": 0,"done": true,"records": []}}', result);
    }

    @IsTest
    static void testSerializeRelationListWithListWithNullValues() {
        Map<String, List<Object>> keyValuePairs = new Map<String, List<Object>>();
        keyValuePairs.put('key1', new List<Object>{ null, null });

        String result = UtilityJson.serializeRelationList(keyValuePairs);
        System.assertEquals('{"key1":{"totalSize": 0,"done": true,"records": []}}', result);
    }

    @IsTest
    static void testSerializeRelationListWithListWithNonNullValues() {
        Map<String, List<Object>> keyValuePairs = new Map<String, List<Object>>();
        keyValuePairs.put('key1', new List<Object>{ 'value1', 'value2' });

        String result = UtilityJson.serializeRelationList(keyValuePairs);
        System.assertEquals('{"key1":{"totalSize": 2,"done": true,"records": ["value1","value2"]}}', result);
    }

    @IsTest
    static void testSerializeRelationListWithMultipleKeysAndLists() {
        Map<String, List<Object>> keyValuePairs = new Map<String, List<Object>>();
        keyValuePairs.put('key1', new List<Object>{ 'value1', null, 'value2' });
        keyValuePairs.put('key2', new List<Object>{ 1, 2, 3 });

        String result = UtilityJson.serializeRelationList(keyValuePairs);
        System.assert(result.contains('"key1":{"totalSize": 2,"done": true,"records": ["value1","value2"]}'));
        System.assert(result.contains('"key2":{"totalSize": 3,"done": true,"records": [1,2,3]}'));
    }

    @IsTest
    static void testSerializeRelationListWithCustomObject() {
        Map<String, List<Object>> keyValuePairs = new Map<String, List<Object>>();

        Account custom1 = new Account();
        custom1.Name = 'Custom1';

        Account custom2 = new Account();
        custom2.Name = 'Custom2';

        keyValuePairs.put('key1', new List<Object>{ custom1, custom2 });

        String result = UtilityJson.serializeRelationList(keyValuePairs);
        String expectedCustom1 = '{"attributes":{"type":"Account"},"Name":"Custom1"}';
        String expectedCustom2 = '{"attributes":{"type":"Account"},"Name":"Custom2"}';
        String expected = '{"key1":{"totalSize": 2,"done": true,"records": [' + expectedCustom1 + ',' + expectedCustom2 + ']}}';
        System.assertEquals(expected, result);
    }
}
