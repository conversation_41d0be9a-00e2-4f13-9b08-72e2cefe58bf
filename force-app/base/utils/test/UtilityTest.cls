@IsTest(IsParallel=true)
private class UtilityTest {
    @IsTest
    static void testCloneRecordWithFields() {
        // given
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Account testAccount = new Account(Id = accountId, Name = 'Test Account', Industry = 'Technology', Phone = '555-1234', Effective_Date__c = Date.today());
        Set<String> fieldsToClone = new Set<String>{ 'Name', 'Phone', 'Effective_Date__c' };

        // when
        Account clonedAccount = (Account) Utility.cloneRecordWithFields(testAccount, fieldsToClone);

        // then
        System.assertEquals(null, clonedAccount.Id);
        System.assertEquals('Test Account', clonedAccount.Name);
        System.assertEquals(null, clonedAccount.Industry);
        System.assertEquals('555-1234', clonedAccount.Phone);
        System.assertEquals(Date.today(), clonedAccount.Effective_Date__c);
    }

    @IsTest
    static void testCloneRecordWithFields_NoValues() {
        // given
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Account testAccount = new Account(Id = accountId, Name = 'Test Account', Industry = 'Technology', Phone = '555-1234', Effective_Date__c = Date.today());
        Set<String> fieldsToClone = new Set<String>{ 'Description' };

        // when
        Account clonedAccount = (Account) Utility.cloneRecordWithFields(testAccount, fieldsToClone);

        // then
        System.assertEquals(true, clonedAccount.getPopulatedFieldsAsMap().isEmpty());
        System.assertEquals(new Account(), clonedAccount);
    }

    @IsTest
    static void shouldReturnDurationDateCorrectlyWhenDurationIsHalfYear() {
        Date dateOfFirstHalfYear = Date.newInstance(2024, 6, 30);
        Date startDateOfFirstHalfYear = Utility.getStartDateAndEndDate(Constants.HALF_YEAR_DURATION, dateOfFirstHalfYear).get(Constants.START_DATE);
        Date endDateOfFirstHalfYear = Utility.getStartDateAndEndDate(Constants.HALF_YEAR_DURATION, dateOfFirstHalfYear).get(Constants.END_DATE);

        Date dateOfSecondHalfYear = Date.newInstance(2024, 12, 31);
        Date startDateOfSecondHalfYear = Utility.getStartDateAndEndDate(Constants.HALF_YEAR_DURATION, dateOfSecondHalfYear).get(Constants.START_DATE);
        Date endDateOfSecondHalfYear = Utility.getStartDateAndEndDate(Constants.HALF_YEAR_DURATION, dateOfSecondHalfYear).get(Constants.END_DATE);

        System.assertEquals(Date.newInstance(2024, 1, 1), startDateOfFirstHalfYear);
        System.assertEquals(Date.newInstance(2024, 6, 30), endDateOfFirstHalfYear);
        System.assertEquals(Date.newInstance(2024, 7, 1), startDateOfSecondHalfYear);
        System.assertEquals(Date.newInstance(2024, 12, 31), endDateOfSecondHalfYear);
    }

    @IsTest
    static void shouldReturnDurationDateCorrectlyWhenDurationIsQuarter() {
        Date dateOfQuarterOne = Date.newInstance(2024, 3, 31);
        Date startDateOfQuarterOne = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterOne).get(Constants.START_DATE);
        Date endDateOfQuarterOne = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterOne).get(Constants.END_DATE);

        Date dateOfQuarterTwo = Date.newInstance(2024, 6, 30);
        Date startDateOfQuarterTwo = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterTwo).get(Constants.START_DATE);
        Date endDateOfQuarterTwo = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterTwo).get(Constants.END_DATE);

        Date dateOfQuarterThree = Date.newInstance(2024, 9, 30);
        Date startDateOfQuarterThree = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterThree).get(Constants.START_DATE);
        Date endDateOfQuarterThree = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterThree).get(Constants.END_DATE);

        Date dateOfQuarterFour = Date.newInstance(2024, 12, 31);
        Date startDateOfQuarterFour = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterFour).get(Constants.START_DATE);
        Date endDateOfQuarterFour = Utility.getStartDateAndEndDate(Constants.QUARTER_DURATION, dateOfQuarterFour).get(Constants.END_DATE);

        System.assertEquals(Date.newInstance(2024, 1, 1), startDateOfQuarterOne);
        System.assertEquals(Date.newInstance(2024, 3, 31), endDateOfQuarterOne);
        System.assertEquals(Date.newInstance(2024, 4, 1), startDateOfQuarterTwo);
        System.assertEquals(Date.newInstance(2024, 6, 30), endDateOfQuarterTwo);
        System.assertEquals(Date.newInstance(2024, 7, 1), startDateOfQuarterThree);
        System.assertEquals(Date.newInstance(2024, 9, 30), endDateOfQuarterThree);
        System.assertEquals(Date.newInstance(2024, 10, 1), startDateOfQuarterFour);
        System.assertEquals(Date.newInstance(2024, 12, 31), endDateOfQuarterFour);
    }

    @IsTest
    static void shouldFormatDecimalCorrectly() {
        String result1 = Utility.formatDecimal(Decimal.valueOf('12345'));
        String result2 = Utility.formatDecimal(Decimal.valueOf('12345.6'));
        String result3 = Utility.formatDecimal(Decimal.valueOf('12345.67'));
        String result4 = Utility.formatDecimal(Decimal.valueOf('12345.678'));
        String result5 = Utility.formatDecimal(null);
        String result6 = Utility.formatDecimal(Decimal.valueOf('123'));

        System.assertEquals('12,345.00', result1);
        System.assertEquals('12,345.60', result2);
        System.assertEquals('12,345.67', result3);
        System.assertEquals('12,345.68', result4);
        System.assertEquals('0.00', result5);
        System.assertEquals('123.00', result6);
    }
}
