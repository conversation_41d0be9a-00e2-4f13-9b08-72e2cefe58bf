/**
 * Application class for base utils
 * This class configures the dependency injection for the base utils package
 */
public class Application {
    // Configure and create the UnitOfWorkFactory for this Application
    public static final fflib_Application.UnitOfWorkFactory UnitOfWork = new fflib_Application.UnitOfWorkFactory(
        new List<SObjectType>{}
            // Add other SObjectTypes as needed
    );

    // Configure and create the ServiceFactory for this Application
    public static final fflib_Application.ServiceFactory Service = new fflib_Application.ServiceFactory(
        new Map<Type, Type>{
            IDMLService.class => DMLServiceImpl.class
            // Add other service interfaces and implementations here
        }
    );

    // Configure and create the SelectorFactory for this Application
    public static final fflib_Application.SelectorFactory Selector = new fflib_Application.SelectorFactory(
        new Map<SObjectType, Type>{}
            // Add selectors when needed
    );

    // Configure and create the DomainFactory for this Application
    public static final fflib_Application.DomainFactory Domain = new fflib_Application.DomainFactory(
        Application.Selector,
        new Map<SObjectType, Type>{}
            // Add domain classes when needed
    );
}
