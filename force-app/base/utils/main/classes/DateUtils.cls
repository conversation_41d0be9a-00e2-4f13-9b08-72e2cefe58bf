public without sharing class DateUtils {
    public static Map<Integer, String> ShortTextMonthMap = new Map<Integer, String>{
        1 => 'Jan',
        2 => 'Feb',
        3 => 'Mar',
        4 => 'Apr',
        5 => 'May',
        6 => 'Jun',
        7 => 'Jul',
        8 => 'Aug',
        9 => 'Sep',
        10 => 'Oct',
        11 => 'Nov',
        12 => 'Dec'
    };

    public static Map<String, Integer> ShortTextWeekdayMap = new Map<String, Integer>{ 'MON' => 1, 'TUE' => 2, 'WED' => 3, 'THU' => 4, 'FRI' => 5, 'SAT' => 6, 'SUN' => 7 };

    public static Integer daysBetweenTwoWeekday(String weekday1, String weekday2) {
        return ShortTextWeekdayMap.get(weekday1) - ShortTextWeekdayMap.get(weekday2);
    }

    public static Boolean validateDateString(String dateStr) {
        Pattern datePattern = Pattern.compile('^\\d{4}-\\d{1,2}-\\d{1,2}$');
        Matcher dateMatcher = datePattern.matcher(dateStr);
        String[] splitStr = dateStr.split('-');

        if (
            !dateMatcher.matches() ||
            Integer.valueOf(splitStr[0]) < 1700 ||
            Integer.valueOf(splitStr[1]) > 12 ||
            Integer.valueOf(splitStr[1]) < 1 ||
            Integer.valueOf(splitStr[2]) > 31 ||
            Integer.valueOf(splitStr[2]) < 1
        ) {
            return false;
        } else {
            return true;
        }
    }

    public static Date convertStringToDate(String dateStr) {
        try {
            if (validateDateString(dateStr)) {
                return Date.valueOf(dateStr);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static String toString(Date d, String format) {
        if (d == null)
            return '';

        Integer day = d.day();
        Integer month = d.month();
        Integer year = d.year();

        if (format == '20-Jan-YYYY') {
            return prefixZero(day) + '-' + ShortTextMonthMap.get(month) + '-' + year;
        }

        if (format == 'YYYY-01-20') {
            return year + '-' + prefixZero(month) + '-' + prefixZero(day);
        }

        if (format == 'YYYY-01') {
            return year + '-' + prefixZero(month);
        }
        return '';
    }

    public static String prefixZero(Integer num) {
        if (num < 10) {
            return '0' + num;
        } else {
            return String.valueOf(num);
        }
    }

    public static Date getCurrentWeekMonday(Date inputDate) {
        Date monday1985 = Date.newInstance(1985, 7, 1);
        Integer daysBetween1985Monday = monday1985.daysBetween(inputDate);
        Integer daysToThisWeekMonday = math.mod(daysBetween1985Monday, 7);
        return inputDate.addDays(-daysToThisWeekMonday);
    }

    public static Date getCurrentWeekSunday(Date inputDate) {
        Date currentWeekMonday = getCurrentWeekMonday(inputDate);
        return currentWeekMonday.addDays(6);
    }

    public static Date getCurrentMonthFirstWeekMonDay(Date inputDate) {
        Date firstDayOfMonth = inputDate.toStartOfMonth();
        Date firstMonday = getCurrentWeekMonday(firstDayOfMonth);
        if (firstMonday.daysBetween(firstDayOfMonth) <= 0) {
            return firstMonday;
        }
        return getCurrentWeekMonday(firstMonday.addDays(7));
    }

    public static Date getCurrentMonthLastDay(Date inputDate) {
        return inputDate.addMonths(1).toStartOfMonth().addDays(-1);
    }

    public static Date ddmmyyyyStrToDate(String inputDate) {
        List<String> ddmmyyy = inputDate.split('-');
        return Date.newinstance(Integer.valueOf(ddmmyyy.get(2)), Integer.valueOf(ddmmyyy.get(1)), Integer.valueOf(ddmmyyy.get(0)));
    }

    public static List<List<Date>> splitDatePeriodToWeek(Date startDate, Date endDate) {
        List<List<Date>> weeklyTimeRanges = new List<List<Date>>();

        Date currentDate = startDate;
        while (currentDate <= endDate) {
            Date weekStartDate = getCurrentWeekMonday(currentDate);
            Date weekEndDate = weekStartDate.addDays(6);
            List<Date> timeRange = new List<Date>{ weekStartDate, weekEndDate };
            weeklyTimeRanges.add(timeRange);
            currentDate = weekStartDate.addDays(7);
        }

        return weeklyTimeRanges;
    }

    public static Date getFirstDayOfNextQuarter(Date inputDate) {
        Integer currentQuarter = (inputDate.month() - 1) / 3 + 1;
        Integer nextQuarter = currentQuarter < 4 ? currentQuarter + 1 : 1;
        Integer yearOffset = currentQuarter < 4 ? 0 : 1;

        Integer nextQuarterMonth = (nextQuarter - 1) * 3 + 1;
        Integer nextQuarterYear = inputDate.year() + yearOffset;

        return Date.newInstance(nextQuarterYear, nextQuarterMonth, 1);
    }

    public static Date getLocalDate(Datetime currentUtc, Integer offsetHours) {
        Datetime localTime = currentUtc.addHours(offsetHours);
        return localTime.date();
    }

    // if the field is formula type, need to format it
    public static Date formatDate(Date formatDate) {
        return Date.newInstance(formatDate.year(), formatDate.month(), formatDate.day());
    }

    public static Boolean isSameDatetime(Datetime time1, Datetime time2) {
        return time1.year() == time2.year() &&
            time1.month() == time2.month() &&
            time1.day() == time2.day() &&
            time1.hour() == time2.hour() &&
            time1.minute() == time2.minute() &&
            time1.second() == time2.second();
    }

    public static String toGTMString(Datetime inputTime) {
        return inputTime != null ? inputTime.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'') : null;
    }

    public static String formatDatetime2String(Datetime formatDatetime) {
        return formatDatetime != null ? formatDatetime.formatGmt('yyyy/M/d \'UTC\' HH:mm:ss') : '';
    }
}
