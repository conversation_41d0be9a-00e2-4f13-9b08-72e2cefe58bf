public with sharing class MapUtils {
    public static void putToValueSet(Map<Id, Set<Id>> curMap, Id key, Id value) {
        putToValueSet(curMap, key, new Set<Id>{ value });
    }

    public static void putToValueSet(Map<Id, Set<Id>> curMap, Id key, Set<Id> values) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        if (CollectionUtils.isEmpty(curMap.get(key))) {
            curMap.put(key, values);
        } else {
            curMap.get(key).addAll(values);
        }
    }

    public static void putToValueSet(Map<String, Set<Id>> curMap, String key, Id value) {
        putToValueSet(curMap, key, new Set<Id>{ value });
    }

    public static void putToValueSet(Map<String, Set<Id>> curMap, String key, Set<Id> values) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        if (CollectionUtils.isEmpty(curMap.get(key))) {
            curMap.put(key, values);
        } else {
            curMap.get(key).addAll(values);
        }
    }

    public static void putToValueList(Map<Id, List<SObject>> curMap, Id key, SObject value) {
        putToValueList(curMap, key, new List<SObject>{ value });
    }

    public static void putToValueList(Map<Id, List<SObject>> curMap, Id key, List<SObject> values) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        if (CollectionUtils.isEmpty(curMap.get(key))) {
            curMap.put(key, values);
        } else {
            curMap.get(key).addAll(values);
        }
    }

    public static List<Id> getMapValueIdList(Map<Id, List<Id>> curMap) {
        List<Id> valueList = new List<Id>();
        for (List<Id> values : curMap.values()) {
            valueList.addAll(values);
        }
        return valueList;
    }

    public static Set<Id> getMapValueIdSet(Map<Id, Set<Id>> curMap) {
        Set<Id> valueList = new Set<Id>();
        for (Set<Id> values : curMap.values()) {
            valueList.addAll(values);
        }
        return valueList;
    }

    public static Map<Id, Set<Id>> mergeSetValueMap(Map<Id, Set<Id>> Map1, Map<Id, Set<Id>> Map2) {
        Map<Id, Set<Id>> mergedMap = Map2;
        if (Map1.isEmpty()) {
            return mergedMap;
        }
        for (Id map1Id : Map1.keySet()) {
            if (mergedMap.containsKey(map1Id)) {
                mergedMap.get(map1Id).addAll(Map1.get(map1Id));
            } else {
                mergedMap.put(map1Id, Map1.get(map1Id));
            }
        }
        return mergedMap;
    }
}
