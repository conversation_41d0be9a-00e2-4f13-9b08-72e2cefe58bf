/**
 * Interface for DML operations service
 * This interface allows for dependency injection and mocking in tests
 */
public interface IDMLService {
    void upsertAndSendEmailWhenErrorHappen(List<SObject> objects, String dmlType);
    void upsertAndThrowErrorException(List<SObject> objects, String dmlType);
    void upsertAndThrowErrorException(List<SObject> objects, String dmlType, Boolean allOrNone);
    void deleteAndThrowErrorException(List<SObject> objects);
    void deleteAndThrowErrorException(List<SObject> objects, Boolean allOrNone);
    String upsertAndGetErrorMsg(List<SObject> objects, String dmlType);
    Map<Id, String> updateAndGetIdErrorMsgMap(List<SObject> objects);
    String upsertAndGetErrorMsg(List<SObject> objects, String dmlType, Boolean allOrNone);
    void upsertAndLogErrorMsg(List<SObject> objects, String dmlType, Logger logger);
    Boolean upsertAndLogErrorMsgAndReturnStatus(List<SObject> objects, String dmlType, Boolean allOrNone, Logger logger);
    void upsertByExternalId(List<SObject> objects, String externalIdField);
    void upsertRecordsByExternalId(List<SObject> records, String externalIdField);
    void insertBySObjectType(List<SObject> records);
    void updateBySObjectType(List<SObject> records);
    void insertBySObjectType(Set<SObject> records);
    void deleteAndLogErrorMsg(List<SObject> objects, Logger logger);
    Boolean deleteAndLogErrorMsgAndReturnStatus(List<SObject> objects, Boolean allOrNone, Logger logger);
    String deleteAndgetErrorMsg(List<SObject> objects);
    String deleteAndGetErrorMsg(List<SObject> objects, Boolean allOrNone);
    String getErrorFromUpsertResult(Database.UpsertResult[] urList, String objectType, Set<Id> idsToBeUpserted, String dmlType);
    String getErrorFromSaveResult(Database.SaveResult[] srList, String objectType, Set<Id> idsToBeUpserted, String dmlType);
    Map<Id, String> getIdAndErrorFromUpdateResult(Database.SaveResult[] srList);
    String getErrorFromDeleteResult(Database.DeleteResult[] srList, String objectType, Set<Id> idsToBeDelete, String dmlType);
    String getFieldSetStringFromObjectListByObjApi(List<SObject> objects, String fieldApiName);
    String getMapStringFromObjectListByKeyFieldAndValueField(List<SObject> objects, String keyFieldApiName, String valueFieldApiName);
    String getUpdateErrorResult(Database.SaveResult[] updateResults, String objectType, List<SObject> needUpdateRecords);
    void updateRecordsAndSendFieldUpdateEventsIfNotUpdatable(List<SObject> recordsToUpdate, Set<String> fieldsToChange, Logger logger);
    List<Id> partialUpdateAndGetSuccessIds(List<SObject> sObjects, Logger logger);
}
