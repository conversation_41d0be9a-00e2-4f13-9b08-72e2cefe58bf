public class Utility {
    private final static Logger LOGGER = new Logger(Utility.class.getName(), TeamName.CRM);

    public static Decimal nullToZero(Decimal num) {
        if (null == num)
            return 0.0;
        return num;
    }

    public static Boolean isPersonalEmail(String email, Id leadContactId) {
        Boolean personalEmail = false;
        String emailDomain = email.substringAfter('@');
        try {
            if (Personal_Email_Provider_Domains__c.getInstance(emailDomain) != null) {
                personalEmail = true;
            }
        } catch (System.InvalidParameterValueException e) {
            LOGGER.error('The lead or contact ' + leadContactId + ' email domain exists InvalidParameterValueException when getting Personal Email Provider Domains: ' + e.getMessage());
        }
        return personalEmail;
    }

    public static SObject cloneRecordWithFields(SObject record, Set<String> fieldsToChange) {
        SObject recordToUpdate = record.getSObjectType().newSObject();
        Map<String, Object> fieldMap = record.getPopulatedFieldsAsMap();
        for (String field : fieldsToChange) {
            if (fieldMap.containsKey(field)) {
                recordToUpdate.put(field, fieldMap.get(field));
            }
        }
        return recordToUpdate;
    }

    public static SObjectType getSObjectType(String idString) {
        Id id = (Id) idString;
        return id.getSobjectType();
    }

    public static String getSObjectTypeName(String idString) {
        return getSObjectType(idString).getDescribe().getName();
    }

    public static String getFieldType(String objectName, String fieldName) {
        SObjectType objectType = Schema.getGlobalDescribe().get(objectName).newSObject().getSObjectType();
        DescribeSObjectResult objectDes = objectType.getDescribe();
        return String.valueOf(objectDes.fields.getMap().get(fieldName).getDescribe().getType());
    }

    public static List<String> getEventChangeFields(String changeFieldsString) {
        List<String> changeFields = new List<String>();
        if (changeFieldsString != null) {
            changeFields = changeFieldsString.split(',');
        }
        return changeFields;
    }

    public static String getEventChangedFieldsAsString(SObject oldObject, SObject newObject, List<String> fieldsToTrack) {
        List<String> changedFields = new List<String>();
        for (String fieldName : fieldsToTrack) {
            if (oldObject.get(fieldName) != newObject.get(fieldName)) {
                changedFields.add(fieldName);
            }
        }

        return String.join(changedFields, ',');
    }

    public static Map<String, Date> getStartDateAndEndDate(String duration, Date dateOfToday) {
        Integer currentYear = dateOfToday.year();
        Integer currentMonth = dateOfToday.month();

        Map<String, Date> startDateAndEndDate = new Map<String, Date>();

        Date startOfDuration = Date.newInstance(currentYear, 1, 1);
        Date endOfDuration = Date.newInstance(currentYear, 12, 31);

        if (duration.equals(Constants.HALF_YEAR_DURATION) && currentMonth <= 6) {
            endOfDuration = Date.newInstance(currentYear, 6, 30);
        }
        if (duration.equals(Constants.HALF_YEAR_DURATION) && currentMonth > 6) {
            startOfDuration = Date.newInstance(currentYear, 7, 1);
        }
        if (duration.equals(Constants.QUARTER_DURATION)) {
            Integer quarterStartMonth = ((currentMonth - 1) / 3) * 3 + 1;
            startOfDuration = Date.newInstance(currentYear, quarterStartMonth, 1);
            endOfDuration = startOfDuration.addMonths(3).addDays(-1);
        }

        startDateAndEndDate.put(Constants.START_DATE, startOfDuration);
        startDateAndEndDate.put(Constants.END_DATE, endOfDuration);
        return startDateAndEndDate;
    }

    public static String formatDecimal(Decimal value) {
        if (value == null) {
            return '0.00';
        }

        String formattedValue = value.setScale(2).format();
        if (!formattedValue.contains('.')) {
            formattedValue += '.00';
        } else {
            String[] parts = formattedValue.split('\\.');
            if (parts.size() == 2 && parts[1].length() == 1) {
                formattedValue += '0';
            }
        }

        return formattedValue;
    }
}
