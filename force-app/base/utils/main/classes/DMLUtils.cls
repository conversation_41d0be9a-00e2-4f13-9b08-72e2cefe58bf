public without sharing class DMLUtils {
    public static String UPDATE_DML = 'update';
    public static String INSERT_DML = 'insert';
    public static String UPSERT_DML = 'upsert';
    public static String DELETE_DML = 'delete';

    public static Boolean ALL_SUCCESS = true;
    public static Boolean PARTIAL_SUCCESS = false;

    private final static Logger LOGGER = new Logger(FlatExpenseAutomation.class.getName());

    static public void upsertAndSendEmailWhenErrorHappen(List<SObject> objects, String dmlType) {
        try {
            upsertAndThrowErrorException(objects, dmlType);
        } catch (Exception ex) {
            LOGGER.error(String.format('Error Caught When Upsert Objects With DMLUtils Method, exception: {0}', new List<Object>{ ex }));
        }
    }

    static public void upsertAndThrowErrorException(List<SObject> objects, String dmlType) {
        upsertAndThrowErrorException(objects, dmlType, PARTIAL_SUCCESS);
    }

    static public void upsertAndThrowErrorException(List<SObject> objects, String dmlType, Boolean allOrNone) {
        String errorMsg = upsertAndGetErrorMsg(objects, dmlType, allOrNone);
        if (errorMsg != '') {
            throw new DmlException(errorMsg);
        }
    }

    static public void deleteAndThrowErrorException(List<SObject> objects) {
        deleteAndThrowErrorException(objects, PARTIAL_SUCCESS);
    }

    static public void deleteAndThrowErrorException(List<SObject> objects, Boolean allOrNone) {
        String errorMsg = deleteAndGetErrorMsg(objects, allOrNone);
        if (errorMsg != '') {
            throw new DmlException(errorMsg);
        }
    }

    static public String upsertAndGetErrorMsg(List<SObject> objects, String dmlType) {
        return upsertAndGetErrorMsg(objects, dmlType, PARTIAL_SUCCESS);
    }

    public static Map<Id, String> updateAndGetIdErrorMsgMap(List<SObject> objects) {
        return getIdAndErrorFromUpdateResult(Database.update(objects, PARTIAL_SUCCESS));
    }

    static public String upsertAndGetErrorMsg(List<SObject> objects, String dmlType, Boolean allOrNone) {
        if (objects.isEmpty()) {
            return '';
        }
        String objApi = objects.get(0).getSObjectType().getDescribe().getName();
        if (dmlType == INSERT_DML) {
            return getErrorFromSaveResult(Database.insert(objects, allOrNone), objApi, (Set<Id>) JSON.deserialize(getFieldSetStringFromObjectListByObjApi(objects, 'Id'), Set<Id>.class), dmlType);
        } else if (dmlType == UPDATE_DML) {
            return getErrorFromSaveResult(Database.update(objects, allOrNone), objApi, (Set<Id>) JSON.deserialize(getFieldSetStringFromObjectListByObjApi(objects, 'Id'), Set<Id>.class), dmlType);
        } else if (dmlType == UPSERT_DML) {
            return getErrorFromUpsertResult(Database.upsert(objects, allOrNone), objApi, (Set<Id>) JSON.deserialize(getFieldSetStringFromObjectListByObjApi(objects, 'Id'), Set<Id>.class), dmlType);
        } else {
            throw new TypeException('Can not distinguish dmlType type of ' + dmlType);
        }
    }

    static public void upsertAndLogErrorMsg(List<SObject> objects, String dmlType, Logger LOGGER) {
        try {
            upsertAndThrowErrorException(objects, dmlType);
        } catch (Exception ex) {
            LOGGER.error(ex);
        }
    }

    static public Boolean upsertAndLogErrorMsgAndReturnStatus(List<SObject> objects, String dmlType, Boolean allOrNone, Logger LOGGER) {
        try {
            upsertAndThrowErrorException(objects, dmlType, allOrNone);
            return true;
        } catch (Exception ex) {
            LOGGER.error(ex);
            return false;
        }
    }

    public static void upsertByExternalId(List<SObject> objects, String externalIdField) {
        if (objects.isEmpty()) {
            return;
        }
        Map<SObjectType, SObject[]> objectsGroupedBySObjectType = CollectionUtils.groupSObjectsByType(objects);
        for (SObjectType sObjectType : objectsGroupedBySObjectType.keySet()) {
            SObjectField field = sObjectType.getDescribe().fields.getMap().get(externalIdField);
            SObject[] objectsToUpsert = objectsGroupedBySObjectType.get(sObjectType);
            if (!objectsToUpsert.isEmpty()) {
                Database.upsert(objectsToUpsert, field, true);
            }
        }
    }

    public static void upsertRecordsByExternalId(List<SObject> records, String externalIdField) {
        if (!records.isEmpty()) {
            SObjectField field = records[0].getSObjectType().getDescribe().fields.getMap().get(externalIdField);
            Database.upsert(records, field, true);
        }
    }

    public static void insertBySObjectType(List<SObject> records) {
        Map<SObjectType, List<SObject>> sobjectTypeToRecords = CollectionUtils.groupSObjectsByType(records);

        for (SObjectType sobjectType : sobjectTypeToRecords.keySet()) {
            List<SObject> recordsToInsert = sobjectTypeToRecords.get(sobjectType);
            if (!recordsToInsert.isEmpty()) {
                Database.insert(recordsToInsert, true);
            }
        }
    }

    public static void updateBySObjectType(List<SObject> records) {
        Map<SObjectType, List<SObject>> sobjectTypeToRecords = CollectionUtils.groupSObjectsByType(records);

        for (SObjectType sobjectType : sobjectTypeToRecords.keySet()) {
            List<SObject> recordsToUpdate = sobjectTypeToRecords.get(sobjectType);
            if (!recordsToUpdate.isEmpty()) {
                Database.update(recordsToUpdate, true);
            }
        }
    }

    public static void insertBySObjectType(Set<SObject> records) {
        insertBySObjectType(new List<SObject>(records));
    }

    static public void deleteAndLogErrorMsg(List<SObject> objects, Logger LOGGER) {
        try {
            deleteAndThrowErrorException(objects);
        } catch (Exception ex) {
            LOGGER.error(ex);
        }
    }

    static public Boolean deleteAndLogErrorMsgAndReturnStatus(List<SObject> objects, Boolean allOrNone, Logger LOGGER) {
        try {
            deleteAndThrowErrorException(objects);
            return true;
        } catch (Exception ex) {
            LOGGER.error(ex);
            return false;
        }
    }

    static public String deleteAndgetErrorMsg(List<SObject> objects) {
        return deleteAndGetErrorMsg(objects, false);
    }

    static public String deleteAndGetErrorMsg(List<SObject> objects, Boolean allOrNone) {
        if (objects.isEmpty()) {
            return '';
        }
        String objApi = objects.get(0).getSObjectType().getDescribe().getName();
        return getErrorFromDeleteResult(Database.delete(objects, allOrNone), objApi, (Set<Id>) JSON.deserialize(getFieldSetStringFromObjectListByObjApi(objects, 'Id'), Set<Id>.class), 'delete');
    }

    static public String getErrorFromUpsertResult(Database.UpsertResult[] urList, String objectType, Set<Id> idsToBeUpserted, String dmlType) {
        Set<Id> idsUpdatedSuccessfully = new Set<Id>();
        String errorMsg = '';
        for (Database.UpsertResult ur : urList) {
            if (!ur.isSuccess()) {
                for (Database.Error err : ur.getErrors()) {
                    errorMsg += +' ' + dmlType + ' failed, error message is ' + err.getMessage() + ' .These fields affected this error: ' + err.getFields() + '\n';
                }
            } else {
                idsUpdatedSuccessfully.add(ur.getId());
            }
        }
        idsToBeUpserted.removeAll(idsUpdatedSuccessfully);
        if (idsToBeUpserted.size() > 0) {
            errorMsg += 'These ' + objectType + ' ids ' + dmlType + ' failed: ' + String.join((List<String>) JSON.deserialize(JSON.serialize(idsToBeUpserted), List<String>.class), ',') + '\n';
        }
        return errorMsg;
    }

    static public String getErrorFromSaveResult(Database.SaveResult[] srList, String objectType, Set<Id> idsToBeUpserted, String dmlType) {
        Set<Id> idsUpdatedSuccessfully = new Set<Id>();
        String errorMsg = '';
        for (Database.SaveResult sr : srList) {
            if (!sr.isSuccess()) {
                for (Database.Error err : sr.getErrors()) {
                    errorMsg += objectType + ' ' + dmlType + ' failed, error message is ' + err.getMessage() + ' .These fields affected this error: ' + err.getFields() + '\n';
                }
            } else {
                idsUpdatedSuccessfully.add(sr.getId());
            }
        }
        idsToBeUpserted.removeAll(idsUpdatedSuccessfully);
        if (idsToBeUpserted.size() > 0) {
            errorMsg += 'These ' + objectType + ' ids ' + dmlType + ' failed: ' + String.join((List<String>) JSON.deserialize(JSON.serialize(idsToBeUpserted), List<String>.class), ',') + '\n';
        }
        return errorMsg;
    }

    public static Map<Id, String> getIdAndErrorFromUpdateResult(Database.SaveResult[] srList) {
        Map<Id, String> result = new Map<Id, String>{};
        for (Database.SaveResult sr : srList) {
            if (!sr.isSuccess()) {
                String errorMsg = '';
                for (Database.Error err : sr.getErrors()) {
                    errorMsg += err.getMessage() + ' \n';
                }
                result.put(sr.getId(), errorMsg);
            }
        }
        return result;
    }

    static public String getErrorFromDeleteResult(Database.DeleteResult[] srList, String objectType, Set<Id> idsToBeDelete, String dmlType) {
        Set<Id> idsDeletedSuccessfully = new Set<Id>();
        String errorMsg = '';
        for (Database.DeleteResult sr : srList) {
            if (!sr.isSuccess()) {
                for (Database.Error err : sr.getErrors()) {
                    errorMsg += objectType + ' ' + dmlType + ' failed, error message is ' + err.getMessage() + ' .These fields affected this error: ' + err.getFields() + '\n';
                }
            } else {
                idsDeletedSuccessfully.add(sr.getId());
            }
        }
        idsToBeDelete.removeAll(idsDeletedSuccessfully);
        if (idsToBeDelete.size() > 0) {
            errorMsg += 'These ' + objectType + ' ids ' + dmlType + ' failed: ' + String.join((List<String>) JSON.deserialize(JSON.serialize(idsToBeDelete), List<String>.class), ',') + '\n';
        }
        return errorMsg;
    }

    static public String getFieldSetStringFromObjectListByObjApi(List<SObject> objects, String fieldApiName) {
        Set<Object> fieldSet = new Set<Object>();
        if (!objects.isEmpty()) {
            String objApi = objects.get(0).getSObjectType().getDescribe().getName();
            Type objType = Type.forName(objApi);
            SObject objReferencer = (SObject) objType.newInstance();
            for (SObject obj : objects) {
                objReferencer = obj;
                fieldSet.add(objReferencer.get(fieldApiName));
            }
        }
        return JSON.serialize(fieldSet);
    }

    static public String getMapStringFromObjectListByKeyFieldAndValueField(List<SObject> objects, String keyFieldApiName, String valueFieldApiName) {
        Map<Object, Object> generatedMap = new Map<Object, Object>();
        if (!objects.isEmpty()) {
            String objApi = objects.get(0).getSObjectType().getDescribe().getName();
            Type objType = Type.forName(objApi);
            SObject objReferencer = (SObject) objType.newInstance();
            for (SObject obj : objects) {
                objReferencer = obj;
                generatedMap.put(objReferencer.get(keyFieldApiName), objReferencer.get(valueFieldApiName));
            }
        }
        return JSON.serialize(generatedMap);
    }

    static public String getUpdateErrorResult(Database.SaveResult[] updateResults, String objectType, List<SObject> needUpdateRecords) {
        String errorMsg = '';
        for (Integer i = 0; i < updateResults.size(); i++) {
            Database.SaveResult sr = updateResults[i];
            if (!sr.isSuccess()) {
                Id id = needUpdateRecords[i].Id;
                errorMsg += objectType + 'Id ' + id + 'update failed for: ';
                for (Database.Error err : sr.getErrors()) {
                    errorMsg += err.getStatusCode() + ': ' + err.getMessage() + '\n';
                }
            }
        }
        return errorMsg;
    }

    public static void updateRecordsAndSendFieldUpdateEventsIfNotUpdatable(List<SObject> recordsToUpdate, Set<String> fieldsToChange, Logger logger) {
        SObjectType sObjectType = recordsToUpdate.getSObjectType();
        if (!sObjectType.getDescribe().isUpdateable()) {
            FieldUpdateEventService.buildAndSendFieldUpdateEvents(recordsToUpdate, fieldsToChange);
            return;
        }

        Database.SaveResult[] updateResults = Database.update(recordsToUpdate, false);
        String errorMsg = DMLUtils.getUpdateErrorResult(updateResults, sObjectType.getDescribe().getName(), recordsToUpdate);
        if (errorMsg != '') {
            logger.warn(errorMsg);
        }
    }

    public static List<Id> partialUpdateAndGetSuccessIds(List<SObject> sObjects, Logger logger) {
        List<Id> successIds = new List<Id>();
        List<Database.SaveResult> saveResults = Database.update(sObjects, false);

        for (Database.SaveResult sr : saveResults) {
            if (sr.isSuccess()) {
                successIds.add(sr.getId());
            }
        }
        String errorMsg = getUpdateErrorResult(saveResults, sObjects.getSObjectType().getDescribe().getName(), sObjects);
        if (errorMsg != '') {
            logger.error(errorMsg);
        }
        return successIds;
    }
}
