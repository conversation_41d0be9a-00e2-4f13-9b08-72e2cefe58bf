/**
 * Service class for DML operations
 * This class provides static methods that delegate to the injected implementation
 */
public with sharing class DMLService {
    // Constants from DMLUtils
    public static String UPDATE_DML = 'update';
    public static String INSERT_DML = 'insert';
    public static String UPSERT_DML = 'upsert';
    public static String DELETE_DML = 'delete';

    public static Boolean ALL_SUCCESS = true;
    public static Boolean PARTIAL_SUCCESS = false;

    // Application class for dependency injection
    private static IDMLService service() {
        return (IDMLService) Application.Service.newInstance(IDMLService.class);
    }

    public static void upsertAndSendEmailWhenErrorHappen(List<SObject> objects, String dmlType) {
        service().upsertAndSendEmailWhenErrorHappen(objects, dmlType);
    }

    public static void upsertAndThrowErrorException(List<SObject> objects, String dmlType) {
        service().upsertAndThrowErrorException(objects, dmlType);
    }

    public static void upsertAndThrowErrorException(List<SObject> objects, String dmlType, Boolean allOrNone) {
        service().upsertAndThrowErrorException(objects, dmlType, allOrNone);
    }

    public static void deleteAndThrowErrorException(List<SObject> objects) {
        service().deleteAndThrowErrorException(objects);
    }

    public static void deleteAndThrowErrorException(List<SObject> objects, Boolean allOrNone) {
        service().deleteAndThrowErrorException(objects, allOrNone);
    }

    public static String upsertAndGetErrorMsg(List<SObject> objects, String dmlType) {
        return service().upsertAndGetErrorMsg(objects, dmlType);
    }

    public static Map<Id, String> updateAndGetIdErrorMsgMap(List<SObject> objects) {
        return service().updateAndGetIdErrorMsgMap(objects);
    }

    public static String upsertAndGetErrorMsg(List<SObject> objects, String dmlType, Boolean allOrNone) {
        return service().upsertAndGetErrorMsg(objects, dmlType, allOrNone);
    }

    public static void upsertAndLogErrorMsg(List<SObject> objects, String dmlType, Logger logger) {
        service().upsertAndLogErrorMsg(objects, dmlType, logger);
    }

    public static Boolean upsertAndLogErrorMsgAndReturnStatus(List<SObject> objects, String dmlType, Boolean allOrNone, Logger logger) {
        return service().upsertAndLogErrorMsgAndReturnStatus(objects, dmlType, allOrNone, logger);
    }

    public static void upsertByExternalId(List<SObject> objects, String externalIdField) {
        service().upsertByExternalId(objects, externalIdField);
    }

    public static void upsertRecordsByExternalId(List<SObject> records, String externalIdField) {
        service().upsertRecordsByExternalId(records, externalIdField);
    }

    public static void insertBySObjectType(List<SObject> records) {
        service().insertBySObjectType(records);
    }

    public static void updateBySObjectType(List<SObject> records) {
        service().updateBySObjectType(records);
    }

    public static void insertBySObjectType(Set<SObject> records) {
        service().insertBySObjectType(records);
    }

    public static void deleteAndLogErrorMsg(List<SObject> objects, Logger logger) {
        service().deleteAndLogErrorMsg(objects, logger);
    }

    public static Boolean deleteAndLogErrorMsgAndReturnStatus(List<SObject> objects, Boolean allOrNone, Logger logger) {
        return service().deleteAndLogErrorMsgAndReturnStatus(objects, allOrNone, logger);
    }

    public static String deleteAndgetErrorMsg(List<SObject> objects) {
        return service().deleteAndgetErrorMsg(objects);
    }

    public static String deleteAndGetErrorMsg(List<SObject> objects, Boolean allOrNone) {
        return service().deleteAndGetErrorMsg(objects, allOrNone);
    }

    public static String getErrorFromUpsertResult(Database.UpsertResult[] urList, String objectType, Set<Id> idsToBeUpserted, String dmlType) {
        return service().getErrorFromUpsertResult(urList, objectType, idsToBeUpserted, dmlType);
    }

    public static String getErrorFromSaveResult(Database.SaveResult[] srList, String objectType, Set<Id> idsToBeUpserted, String dmlType) {
        return service().getErrorFromSaveResult(srList, objectType, idsToBeUpserted, dmlType);
    }

    public static Map<Id, String> getIdAndErrorFromUpdateResult(Database.SaveResult[] srList) {
        return service().getIdAndErrorFromUpdateResult(srList);
    }

    public static String getErrorFromDeleteResult(Database.DeleteResult[] srList, String objectType, Set<Id> idsToBeDelete, String dmlType) {
        return service().getErrorFromDeleteResult(srList, objectType, idsToBeDelete, dmlType);
    }

    public static String getFieldSetStringFromObjectListByObjApi(List<SObject> objects, String fieldApiName) {
        return service().getFieldSetStringFromObjectListByObjApi(objects, fieldApiName);
    }

    public static String getMapStringFromObjectListByKeyFieldAndValueField(List<SObject> objects, String keyFieldApiName, String valueFieldApiName) {
        return service().getMapStringFromObjectListByKeyFieldAndValueField(objects, keyFieldApiName, valueFieldApiName);
    }

    public static String getUpdateErrorResult(Database.SaveResult[] updateResults, String objectType, List<SObject> needUpdateRecords) {
        return service().getUpdateErrorResult(updateResults, objectType, needUpdateRecords);
    }

    public static void updateRecordsAndSendFieldUpdateEventsIfNotUpdatable(List<SObject> recordsToUpdate, Set<String> fieldsToChange, Logger logger) {
        service().updateRecordsAndSendFieldUpdateEventsIfNotUpdatable(recordsToUpdate, fieldsToChange, logger);
    }

    public static List<Id> partialUpdateAndGetSuccessIds(List<SObject> sObjects, Logger logger) {
        return service().partialUpdateAndGetSuccessIds(sObjects, logger);
    }
}
