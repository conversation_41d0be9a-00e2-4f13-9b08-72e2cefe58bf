global class UtilityJson {
    public static String serializeRelationList(Map<String, List<Object>> keyValuePairs) {
        List<String> stringList = new List<String>();
        for (String key : keyValuePairs.keySet()) {
            List<Object> objectList = keyValuePairs.get(key);
            stringList.add(serializeObjectList(key, objectList));
        }
        return '{' + String.join(stringList, ',') + '}';
    }

    private static String serializeObjectList(String objectKey, List<Object> objectInstance) {
        List<Object> filteredList = new List<Object>();
        for (Object obj : objectInstance) {
            if (obj != null) {
                filteredList.add(obj);
            }
        }

        return '"' + objectKey + '":{"totalSize": ' + filteredList.size() + ',"done": true,"records": ' + JSON.serialize(filteredList) + '}';
    }
}
