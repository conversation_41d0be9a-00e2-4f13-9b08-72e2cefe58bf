public without sharing class ShareDMLUtils {
    private final static Map<String, String> ShareRecordsFieldNameMap = new Map<String, String>{ 'AccountShare' => 'AccountId' };

    static public void insertShareRecords(List<SObject> objectRecords, Logger LOGGER) {
        if (objectRecords.isEmpty()) {
            return;
        }

        String objApi = objectRecords.get(0).getSObjectType().getDescribe().getName();
        String message = getErrorMsgFromInsertResult(Database.insert(objectRecords, false), objectRecords, objApi);

        if (message != '') {
            LOGGER.error(message);
        }
    }

    static public void deleteShareRecords(List<SObject> objectRecords, Logger LOGGER) {
        if (objectRecords.isEmpty()) {
            return;
        }

        String objApi = objectRecords.get(0).getSObjectType().getDescribe().getName();
        String message = getErrorMsgFromDeleteResult(Database.delete(objectRecords, false), objectRecords, objApi);

        if (message != '') {
            LOGGER.error(message);
        }
    }

    static public void updateShareRecords(List<SObject> objectRecords, Logger LOGGER) {
        if (objectRecords.isEmpty()) {
            return;
        }

        String objApi = objectRecords.get(0).getSObjectType().getDescribe().getName();
        String message = getErrorMsgFromUpdateResult(Database.update(objectRecords, false), objectRecords, objApi);

        if (message != '') {
            LOGGER.error(message);
        }
    }

    static public void upsertShareRecords(List<SObject> objectRecords, Logger LOGGER) {
        if (objectRecords.isEmpty()) {
            return;
        }

        String objApi = objectRecords.get(0).getSObjectType().getDescribe().getName();
        String message = getErrorMsgFromUpsertResult(Database.upsert(objectRecords, false), objectRecords, objApi);

        if (message != '') {
            LOGGER.error(message);
        }
    }

    static public String getErrorMsgFromDeleteResult(Database.DeleteResult[] drList, List<SObject> objectRecords, String objectType) {
        String errorMsg = '';
        String shareObjectFieldName = getShareObjectFieldNameFromObject(objectType);

        for (Integer index = 0; index < drList.size(); index++) {
            Database.DeleteResult dr = drList.get(index);
            if (!dr.isSuccess()) {
                errorMsg += objectType + ' delete failed. Error Content:\nError Message: ';
                for (Database.Error err : dr.getErrors()) {
                    errorMsg += err.getMessage() + '\n';
                }
                SObject curFailRecord = objectRecords.get(index);
                errorMsg += 'Id - ' + curFailRecord.get('Id') + ', ObjectId - ' + curFailRecord.get(shareObjectFieldName) + ', UserOrGroupId - ' + curFailRecord.get('UserOrGroupId') + '\n';
            }
        }

        return errorMsg;
    }

    static public String getErrorMsgFromInsertResult(Database.SaveResult[] srList, List<SObject> objectRecords, String objectType) {
        return getErrorMsgFromSaveResult(srList, objectRecords, objectType, 'insert');
    }

    static public String getErrorMsgFromUpdateResult(Database.SaveResult[] srList, List<SObject> objectRecords, String objectType) {
        return getErrorMsgFromSaveResult(srList, objectRecords, objectType, 'update');
    }

    static private String getErrorMsgFromSaveResult(Database.SaveResult[] srList, List<SObject> objectRecords, String objectType, String dmlType) {
        String shareObjectFieldName = getShareObjectFieldNameFromObject(objectType);
        String errorMsg = '';

        for (Integer index = 0; index < srList.size(); index++) {
            Database.SaveResult sr = srList.get(index);
            if (!sr.isSuccess()) {
                errorMsg += objectType + ' ' + dmlType + ' failed. Error Content:\nError Message: ';
                for (Database.Error err : sr.getErrors()) {
                    errorMsg += err.getMessage() + '\n';
                }
                SObject curFailRecord = objectRecords.get(index);
                if (dmlType == 'insert') {
                    errorMsg += 'ObjectId - ' + curFailRecord.get(shareObjectFieldName) + ', UserOrGroupId - ' + curFailRecord.get('UserOrGroupId') + '\n';
                } else {
                    errorMsg += 'Id - ' + curFailRecord.get('Id') + ', ObjectId - ' + curFailRecord.get(shareObjectFieldName) + ', UserOrGroupId - ' + curFailRecord.get('UserOrGroupId') + '\n';
                }
            }
        }
        return errorMsg;
    }

    static public String getErrorMsgFromUpsertResult(Database.UpsertResult[] urList, List<SObject> objectRecords, String objectType) {
        String errorMsg = '';
        String shareObjectFieldName = getShareObjectFieldNameFromObject(objectType);

        for (Integer index = 0; index < urList.size(); index++) {
            Database.UpsertResult ur = urList.get(index);
            if (!ur.isSuccess()) {
                errorMsg += objectType + ' upsert failed. Error Content:\nError Message: ';
                for (Database.Error err : ur.getErrors()) {
                    errorMsg += err.getMessage() + '\n';
                }
                SObject curFailRecord = objectRecords.get(index);
                if (ur.isCreated()) {
                    errorMsg += 'ObjectId - ' + curFailRecord.get(shareObjectFieldName) + ', UserOrGroupId - ' + curFailRecord.get('UserOrGroupId') + '\n';
                } else {
                    errorMsg += 'Id - ' + curFailRecord.get('Id') + ', ObjectId - ' + curFailRecord.get(shareObjectFieldName) + ', UserOrGroupId - ' + curFailRecord.get('UserOrGroupId') + '\n';
                }
            }
        }

        return errorMsg;
    }

    static private String getShareObjectFieldNameFromObject(String objectType) {
        String shareRecordsFieldName = 'ParentId';
        if (ShareRecordsFieldNameMap.containsKey(objectType)) {
            shareRecordsFieldName = ShareRecordsFieldNameMap.get(objectType);
        }
        return shareRecordsFieldName;
    }
}
