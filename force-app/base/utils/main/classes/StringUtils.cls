public without sharing class StringUtils {
    public static String concatenateStringWithDelimiter(String str, String delimiter, String strToBeConcatenated, Boolean canConcatenateDuplicateStr) {
        String result = null;
        if (String.isEmpty(str)) {
            result = strToBeConcatenated;
        } else {
            List<String> strList = str.split(delimiter);
            if (canConcatenateDuplicateStr || !strList.contains(strToBeConcatenated)) {
                strList.add(strToBeConcatenated);
            }
            result = String.join(strList, delimiter);
        }
        return result;
    }
}
