public class CollectionUtils {
    public static boolean isEmpty(List<Object> coll) {
        return coll == null || coll.isEmpty();
    }

    public static boolean isNotEmpty(List<Object> coll) {
        return !isEmpty(coll);
    }

    public static boolean isEmpty(Set<String> coll) {
        return coll == null || coll.isEmpty();
    }

    public static boolean isNotEmpty(Set<String> coll) {
        return !isEmpty(coll);
    }

    public static boolean isEmpty(Set<Id> coll) {
        return coll == null || coll.isEmpty();
    }

    public static boolean isNotEmpty(Set<Id> coll) {
        return !isEmpty(coll);
    }

    public static List<String> getChangedFields(SObject newRecord, SObject oldRecord) {
        List<String> changedFields = new List<String>();
        Map<String, Schema.SObjectField> fields = newRecord.getSObjectType().getDescribe().fields.getMap();
        for (String fieldName : fields.keySet()) {
            if (oldRecord.get(fieldName) != newRecord.get(fieldName)) {
                changedFields.add(fieldName);
            }
        }
        return changedFields;
    }

    public static List<Id> getIdList(List<SObject> coll) {
        List<Id> ids = new List<Id>();
        if (isEmpty(coll)) {
            return ids;
        }
        for (SObject item : coll) {
            if (item.Id != null) {
                ids.add(item.Id);
            }
        }
        return ids;
    }

    public static Map<Id, SObject> convertListToMap(List<SObject> sObjects) {
        Map<Id, SObject> sObjectMap = new Map<Id, SObject>();
        for (SObject obj : sObjects) {
            sObjectMap.put(obj.Id, obj);
        }
        return sObjectMap;
    }

    // Include: Key is Null Situation
    public static Map<Object, SObject> convertListToMap(List<SObject> sObjects, Schema.SObjectField keyFieldName) {
        Map<Object, SObject> sObjectMap = new Map<Object, SObject>();
        for (SObject obj : sObjects) {
            sObjectMap.put(obj.get(keyFieldName), obj);
        }
        return sObjectMap;
    }

    public static Map<Object, Object> convertListToMap(List<SObject> sObjects, Schema.SObjectField keyFieldName, Schema.SObjectField valueFieldName) {
        Map<Object, Object> sObjectMap = new Map<Object, Object>();
        for (SObject obj : sObjects) {
            sObjectMap.put(obj.get(keyFieldName), obj.get(valueFieldName));
        }
        return sObjectMap;
    }

    public static Map<String, SObject> convertListToMapByUnionKey(List<SObject> sObjects, List<Schema.SObjectField> keyFieldNames) {
        Map<String, SObject> sObjectMap = new Map<String, SObject>();
        for (SObject obj : sObjects) {
            List<String> unionKeys = new List<String>();
            for (Schema.SObjectField keyFieldName : keyFieldNames) {
                Object value = obj.get(keyFieldName);
                if (value == null) {
                    continue;
                }
                unionKeys.add(obj.get(keyFieldName).toString());
            }
            String unionKey = String.join(unionKeys, '');
            sObjectMap.put(unionKey, obj);
        }
        return sObjectMap;
    }

    public static Set<Id> getIdSet(List<SObject> objects) {
        Set<Id> idSet = new Set<Id>();
        if (isEmpty(objects)) {
            return idSet;
        }
        for (SObject o : objects) {
            idSet.add(o.Id);
        }

        return idSet;
    }
    public static Set<Id> getIdSet(List<SObject> objects, String idFieldName) {
        Set<Id> idSet = new Set<Id>();
        if (isEmpty(objects)) {
            return idSet;
        }
        for (SObject o : objects) {
            Id id = (Id) o.get(idFieldName);
            if (id != null) {
                idSet.add(id);
            }
        }

        return idSet;
    }

    public static Set<Id> getIdSet(List<SObject> objects, Schema.SObjectField idFieldName) {
        return getIdSet(objects, idFieldName.getDescribe().getName());
    }

    public static List<Id> getIdList(List<SObject> coll, Schema.SObjectField idField) {
        return getIdList(coll, new List<Schema.SObjectField>{ idField });
    }

    public static List<Id> getIdList(List<SObject> coll, List<Schema.SObjectField> idFields) {
        List<Id> ids = new List<Id>();
        if (isEmpty(coll) || isEmpty(idFields)) {
            return ids;
        }
        for (SObject item : coll) {
            for (SObjectField idField : idFields) {
                Id id = (Id) item.get(idField);
                if (id != null) {
                    ids.add(id);
                }
            }
        }
        return ids;
    }

    public static Map<Id, List<SObject>> groupByIdField(List<SObject> coll, Schema.SObjectField idField) {
        Map<Id, List<SObject>> groupMap = new Map<Id, List<SObject>>();
        if (isEmpty(coll) || idField == null) {
            return groupMap;
        }
        for (SObject item : coll) {
            Id groupKey = (Id) item.get(idField);
            if (groupKey != null) {
                if (groupMap.containsKey(groupKey)) {
                    groupMap.get(groupKey).add(item);
                } else {
                    groupMap.put(groupKey, new List<SObject>{ item });
                }
            }
        }
        return groupMap;
    }

    public static Map<Id, List<SObject>> groupByIdField(List<SObject> coll, String fieldName) {
        Map<Id, List<SObject>> groupMap = new Map<Id, List<SObject>>();
        if (isEmpty(coll) || fieldName == null) {
            return groupMap;
        }
        for (SObject item : coll) {
            Id groupKey = (Id) item.get(fieldName);
            if (groupKey != null) {
                if (groupMap.containsKey(groupKey)) {
                    groupMap.get(groupKey).add(item);
                } else {
                    groupMap.put(groupKey, new List<SObject>{ item });
                }
            }
        }
        return groupMap;
    }

    public static Map<String, List<SObject>> groupByStringField(List<SObject> coll, String fieldName) {
        Map<String, List<SObject>> groupMap = new Map<String, List<SObject>>();
        if (isEmpty(coll) || fieldName == null) {
            return groupMap;
        }
        for (SObject item : coll) {
            String groupKey = (String) item.get(fieldName);
            if (groupKey != null) {
                if (groupMap.containsKey(groupKey)) {
                    groupMap.get(groupKey).add(item);
                } else {
                    groupMap.put(groupKey, new List<SObject>{ item });
                }
            }
        }
        return groupMap;
    }

    public static Map<Id, List<SObject>> groupByIdField(List<SObject> coll, List<String> idFields) {
        Map<Id, List<SObject>> groupMap = new Map<Id, List<SObject>>();
        if (idFields.size() == 0) {
            return groupMap;
        } else {
            for (SObject item : coll) {
                SObject value = item;
                for (Integer i = 0; i < idFields.size() - 1; i++) {
                    if (value != null) {
                        String field = idFields.get(i);
                        value = value.getSObject(field);
                    }
                }
                Id groupKey = (Id) value.get(idFields.get(idFields.size() - 1));
                if (groupKey != null) {
                    if (groupMap.containsKey(groupKey)) {
                        groupMap.get(groupKey).add(item);
                    } else {
                        groupMap.put(groupKey, new List<SObject>{ item });
                    }
                }
            }
            return groupMap;
        }
    }

    public static Map<Id, Set<Object>> groupByIdNotNullAndDistinctField(List<SObject> coll, Schema.SObjectField idField, Schema.SObjectField valueField) {
        Map<Id, Set<Object>> groupMap = new Map<Id, Set<Object>>();
        if (isEmpty(coll) || idField == null || valueField == null) {
            return groupMap;
        }
        for (SObject item : coll) {
            Id groupKey = (Id) item.get(idField);
            if (groupKey != null && item.get(valueField) != null) {
                if (groupMap.containsKey(groupKey)) {
                    Set<Object> temp = groupMap.get(groupKey);
                    temp.add(item.get(valueField));
                    groupMap.put(groupKey, temp);
                } else {
                    groupMap.put(groupKey, new Set<Object>{ item.get(valueField) });
                }
            }
        }
        return groupMap;
    }

    public static Map<Id, List<Object>> groupByFieldIdKeyNotNull(List<SObject> coll, Schema.SObjectField idField, Schema.SObjectField valueField) {
        Map<Id, List<Object>> groupMap = new Map<Id, List<Object>>();
        if (isEmpty(coll) || idField == null || valueField == null) {
            return groupMap;
        }
        for (SObject item : coll) {
            Id groupKey = (Id) item.get(idField);
            if (groupKey != null) {
                if (groupMap.containsKey(groupKey)) {
                    List<Object> temp = groupMap.get(groupKey);
                    temp.add(item.get(valueField));
                    groupMap.put(groupKey, temp);
                } else {
                    groupMap.put(groupKey, new List<Object>{ item.get(valueField) });
                }
            }
        }
        return groupMap;
    }

    public static Set<String> objToString(Set<Object> obj) {
        Set<String> res = new Set<String>();
        if (obj == null)
            return res;
        for (Object o : obj) {
            res.add(String.valueOf(o));
        }
        return res;
    }

    public static List<Id> getIdListForDiffValue(SObject newSObject, SObject oldSObject, List<Schema.SObjectField> compareFieldList) {
        List<Id> compareList = new List<Id>();
        for (Schema.SObjectField compareField : compareFieldList) {
            if (newSObject.get(compareField) != null && !newSObject.get(compareField).equals(oldSObject.get(compareField))) {
                compareList.add((Id) newSObject.get(compareField));
            }
        }
        return compareList;
    }

    public static List<SObject> getSObjectListForDiff(List<SObject> newSObjects, Map<Id, SObject> oldSObjects, Schema.SObjectField compareField) {
        return getSObjectListForDiff(newSObjects, oldSObjects, new List<Schema.SObjectField>{ compareField });
    }

    public static List<SObject> getSObjectListForDiff(List<SObject> newSObjects, Map<Id, SObject> oldSObjects, List<Schema.SObjectField> compareFields) {
        List<SObject> compareList = new List<SObject>();

        for (Schema.SObjectField compareField : compareFields) {
            for (SObject newSObject : newSObjects) {
                if (oldSObjects == null || oldSObjects.size() == 0) {
                    if (newSObject.get(compareField) != null) {
                        compareList.add(newSObject);
                    }
                } else {
                    if (newSObject.get(compareField) != null && !newSObject.get(compareField).equals(oldSObjects.get(newSObject.Id).get(compareField))) {
                        compareList.add(newSObject);
                    }
                }
            }
        }

        return getDeduplicatedById(compareList);
    }

    public static List<SObject> filterByAssociatedErrors(List<SObject> objectList) {
        List<SObject> filterdList = new List<SObject>();
        for (SObject obj : objectList) {
            if (!obj.hasErrors()) {
                filterdList.add(obj);
            }
        }
        return filterdList;
    }

    public static Set<Id> collectMapValuesSet(Map<Id, Set<Id>> idMap) {
        Set<Id> ids = new Set<Id>();
        for (Id id : idMap.keySet()) {
            ids.addAll(idMap.get(id));
        }
        return ids;
    }

    public static Set<String> collectSObjectStringIntoSet(List<SObject> objs, String fieldName) {
        Set<String> s = new Set<String>();
        for (SObject obj : objs) {
            String str = (String) obj.get(fieldName);
            if (str != null) {
                s.add(str);
            }
        }
        return s;
    }

    public static Map<SObjectType, List<SObject>> groupSObjectsByType(List<SObject> records) {
        Map<SObjectType, List<SObject>> recordsByType = new Map<SObjectType, List<SObject>>();
        for (SObject record : records) {
            if (record != null) {
                SObjectType type = record.getSObjectType();
                if (recordsByType.containsKey(type)) {
                    recordsByType.get(type).add(record);
                } else {
                    recordsByType.put(type, new List<SObject>{ record });
                }
            }
        }
        return recordsByType;
    }

    public static Set<Id> getIdsNotInMap(List<SObject> objList, Schema.SObjectField fieldName, Map<Id, SObject> checkTarget) {
        Set<Id> idsNeedToCheck = getIdSet(objList, fieldName);
        if (idsNeedToCheck.isEmpty()) {
            return new Set<Id>();
        }

        Set<Id> sObjNeedToGet = new Set<Id>();

        for (Id id : idsNeedToCheck) {
            if (!checkTarget.containsKey(id)) {
                sObjNeedToGet.add(id);
            }
        }
        return sObjNeedToGet;
    }

    public static Set<Id> flatListSetIds(List<Set<Id>> ids) {
        Set<Id> flatIds = new Set<Id>();
        for (Set<Id> setId : ids) {
            flatIds.addAll(setId);
        }
        return flatIds;
    }

    public static Set<Id> mergeTwoSets(Set<Id> coll1, Set<Id> coll2) {
        Set<Id> resultSet = new Set<Id>();
        if (!coll1.isEmpty()) {
            resultSet.addAll(coll1);
        }
        if (!coll2.isEmpty()) {
            resultSet.addAll(coll2);
        }
        return resultSet;
    }

    public static Set<String> mergeTwoSets(Set<String> coll1, Set<String> coll2) {
        Set<String> resultSet = new Set<String>();
        if (!coll1.isEmpty()) {
            resultSet.addAll(coll1);
        }
        if (!coll2.isEmpty()) {
            resultSet.addAll(coll2);
        }
        return resultSet;
    }

    public static Object getMapMapValue(Map<Id, Map<Id, Object>> mapMap, Id key1, Id key2) {
        if (mapMap == null || key1 == null || key2 == null) {
            return null;
        }
        if (mapMap.containsKey(key1)) {
            Map<Id, Object> innerMap = mapMap.get(key1);
            if (innerMap.containsKey(key2)) {
                return innerMap.get(key2);
            }
        }
        return null;
    }

    public static List<SObject> mergeTwoList(List<SObject> coll1, List<SObject> coll2) {
        List<SObject> resultList = new List<SObject>();
        if (isNotEmpty(coll1)) {
            resultList.addAll(coll1);
        }
        if (isNotEmpty(coll2)) {
            resultList.addAll(coll2);
        }
        return resultList;
    }

    private static List<Sobject> getDeduplicatedById(List<Sobject> targetList) {
        Map<Id, Sobject> uniqueMap = new Map<Id, Sobject>();

        for (Sobject obj : targetList) {
            uniqueMap.put(obj.Id, obj);
        }

        return uniqueMap.values();
    }

    public static Map<Object, List<SObject>> putSObjectIntoMap(Map<Object, List<SObject>> sObjectMap, Object itemKey, SObject item) {
        if (sObjectMap.containsKey(itemKey)) {
            List<SObject> items = sObjectMap.get(itemKey);
            items.add(item);
            sObjectMap.put(itemKey, items);
        } else {
            sObjectMap.put(itemKey, new List<SObject>{ item });
        }
        return sObjectMap;
    }

    // when datetime value is null, it is smallest
    public static SObject getLatestRecordByDateTimeField(List<SObject> targetList, Schema.SObjectField timeFiled) {
        if (targetList == null || targetList.isEmpty()) {
            return null;
        }
        SObject result = targetList.get(0);
        Datetime latestTime = (Datetime) result.get(timeFiled);
        for (SObject item : targetList) {
            Datetime currentTime = (Datetime) item.get(timeFiled);
            if ((latestTime == null && currentTime != null) || (latestTime != null && currentTime != null && currentTime > latestTime)) {
                latestTime = currentTime;
                result = item;
            }
        }
        return result;
    }

    public static List<String> splitsMultiPicklist(String str) {
        if (str == null) {
            return new List<String>();
        }
        return str.split(';');
    }

    public static List<SObject> getChangedRecords(List<SObject> newRecords, Map<Id, SObject> oldRecordMap, Set<String> fieldNames) {
        List<SObject> changedRecords = new List<SObject>();
        for (SObject newRecord : newRecords) {
            Id recordId = (Id) newRecord.get('Id');
            if (oldRecordMap == null || !oldRecordMap.containsKey(recordId)) {
                continue;
            }

            SObject oldRecord = oldRecordMap.get(recordId);
            for (String fieldName : fieldNames) {
                if (oldRecord.get(fieldName) != newRecord.get(fieldName)) {
                    changedRecords.add(newRecord);
                    break; // prevents the records from being added multiple times
                }
            }
        }
        return changedRecords;
    }

    public static List<SObject> getChangedRecords(List<SObject> newRecords, Map<Id, SObject> oldRecordMap, String fieldName) {
        return getChangedRecords(newRecords, oldRecordMap, new Set<String>{ fieldName });
    }

    public static List<SObject> getRecordsWithBlankFieldValues(List<SObject> records, Schema.SObjectField field) {
        return getRecordsWithBlankFieldValues(records, new Set<Schema.SObjectField>{ field });
    }

    public static List<SObject> getRecordsWithBlankFieldValues(List<SObject> records, Set<Schema.SObjectField> fields) {
        List<SObject> result = new List<SObject>();
        for (SObject record : records) {
            for (SObjectField field : fields) {
                if (String.isNotBlank(String.valueOf(record.get(field)))) {
                    continue;
                }

                result.add(record);
                break;
            }
        }
        return result;
    }

    public static Map<Id, Decimal> aggregateFieldByGroup(List<SObject> records, String groupByField, String aggregateField) {
        Map<Id, Decimal> aggregatedMap = new Map<Id, Decimal>();

        for (SObject record : records) {
            Id groupByFieldValue = (Id) record.get(groupByField);
            Decimal aggregateFieldValue = (Decimal) record.get(aggregateField);

            if (aggregateFieldValue != null) {
                if (aggregatedMap.containsKey(groupByFieldValue)) {
                    aggregatedMap.put(groupByFieldValue, aggregatedMap.get(groupByFieldValue) + aggregateFieldValue);
                } else {
                    aggregatedMap.put(groupByFieldValue, aggregateFieldValue);
                }
            }
        }

        return aggregatedMap;
    }

    public static List<SObject> flattenList(List<List<SObject>> nestedList) {
        List<SObject> flattenedList = new List<SObject>();
        if (isEmpty(nestedList)) {
            return flattenedList;
        }
        for (List<SObject> subList : nestedList) {
            if (isNotEmpty(subList)) {
                flattenedList.addAll(subList);
            }
        }
        return flattenedList;
    }

    public static List<String> getFilteredPicklistValues(Schema.DescribeFieldResult fieldDescribe, Set<String> excludedValues) {
        List<String> result = new List<String>();
        for (Schema.PicklistEntry entry : fieldDescribe.getPicklistValues()) {
            if (!excludedValues.contains(entry.getValue())) {
                result.add(entry.getValue());
            }
        }
        return result;
    }
}
