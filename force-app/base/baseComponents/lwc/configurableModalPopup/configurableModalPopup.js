import { LightningElement, api } from "lwc";

const TOAST_CONFIG_MAP = {
    info: {
        iconName: "utility:success",
        theme: "slds-theme_success",
        iconVariant: "inverse"
    },
    warning: {
        iconName: "utility:warning",
        theme: "slds-theme_warning",
        iconVariant: ""
    },
    error: {
        iconName: "utility:error",
        theme: "slds-theme_error",
        iconVariant: "inverse"
    }
};
export default class ConfigurableModalPopup extends LightningElement {
    @api name;
    @api actions = [];
    @api header = {
        text: "Modal Header"
    };
    @api overflowHeight = false;

    hasErrorStatus = false;
    errorMsg;

    get computedMainContentClass() {
        return this.overflowHeight ? "main-content over-flow-hidden" : "main-content";
    }

    saveAction = {
        text: "Submit",
        handler: () => {}
    };
    cancelAction = {
        text: "Cancel",
        handler: () => {}
    };
    extraActions = [];
    loading = false;
    isShowMsg = false;
    toast = {
        title: "Warning Message",
        message: "",
        iconName: "utility:warning",
        computedClass: "slds-notify slds-notify_toast slds-theme_warning",
        iconVariant: "warning"
    };

    connectedCallback() {
        this.validateParams();

        for (const [index, action] of this.actions.entries()) {
            if (index === 0) {
                this.cancelAction = action;
                continue;
            }

            if (index === 1) {
                if (action) {
                    this.saveAction = {
                        ...action,
                        handler: (event) => action.handler(event, this)
                    };
                }
                continue;
            }
            const extraAction = JSON.parse(JSON.stringify(action));
            extraAction.key = index;
            extraAction.classNames = this.fillClassNames(extraAction.classNames);
            extraAction.handler = (event) => action.handler(event, this);

            this.extraActions.push(extraAction);
        }
    }

    fillClassNames(classNames) {
        return classNames ? `slds-button ${classNames}` : "slds-button";
    }

    validateParams() {
        if (typeof this.actions === "string" || !typeof this.actions[Symbol.iterator] === "function") {
            throw new Error("actions must be iterable and not string");
        }
    }

    @api
    info(msg) {
        this.showMsg("info", msg);
    }

    @api
    warning(msg) {
        this.showMsg("warning", msg);
    }

    @api
    error(msg, errorMsg) {
        this.errorMsg = errorMsg;
        this.hasErrorStatus = true;
        this.showMsg("error", msg);
    }

    @api
    removeMsg() {
        this.errorMsg = "";
        this.hasErrorStatus = false;
        this.hideMsg();
    }

    showMsg(type, title) {
        const typeConfig = TOAST_CONFIG_MAP[type];
        if (!typeConfig) {
            throw new Error(`unsupported type for toast in configurableModalPopup: ${type}`);
        }

        this.toast = {
            title,
            iconName: typeConfig.iconName,
            computedClass: `slds-notify slds-notify_toast ${typeConfig.theme}`,
            iconVariant: typeConfig.iconVariant
        };

        this.isShowMsg = true;
    }

    hideMsg() {
        this.isShowMsg = false;
    }
}
