.modal-pop-up {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--slds-c-backdrop-color-background, var(--sds-c-backdrop-color-background, rgba(8, 7, 7, 0.6)));
    z-index: 9000;
}

.main-content {
    background-color: var(--lwc-colorBackgroundAlt, #ffffff);
}

.over-flow-hidden {
    overflow: hidden;
    overflow-y: auto;
}

.msg-container {
    display: flex;
    flex-direction: column;
}

.errors-list {
    list-style: none;
    color: var(--slds-g-color-error-base-40, #ba0517);
    line-height: var(--lwc-lineHeightText, 1.5);
    margin: 0;
}
