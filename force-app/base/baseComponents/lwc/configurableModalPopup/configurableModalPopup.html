<template>
    <div class="modal-pop-up">
        <c-modal-pop-up
            header-text={header.text}
            submit-text={saveAction.text}
            cancel-text={cancelAction.text}
            onsubmit={saveAction.handler}
            oncancel={cancelAction.handler}
            onclose={cancelAction.handler}
            has-error-status={hasErrorStatus}
            not-show-text-content="true"
        >
            <div slot="modalContent" class={computedMainContentClass}>
                <div class="msg-container slds-notify_container slds-is-relative slds-p-left_medium slds-p-right_medium" if:true={isShowMsg}>
                    <div class={toast.computedClass} role="status">
                        <div class="slds-icon_container slds-m-right_small slds-no-flex slds-align-top">
                            <lightning-icon class="msg-icon" size="small" icon-name={toast.iconName} alternative-text={toast.title} title={toast.title} variant={toast.iconVariant}> </lightning-icon>
                        </div>
                        <lightning-formatted-rich-text class="msg-title" value={toast.title}></lightning-formatted-rich-text>
                    </div>
                </div>
                <ul class="errors-list slds-p-left_large slds-p-right_large">
                    <li>{errorMsg}</li>
                </ul>
                <slot name="modalContent"></slot>
            </div>

            <span slot="extraButtons">
                <slot name="extraButtons">
                    <template for:each={extraActions} for:item="action">
                        <button key={action.key} class={action.classNames} onclick={action.handler}>{action.text}</button>
                    </template>
                </slot>
            </span>
        </c-modal-pop-up>
    </div>
</template>
