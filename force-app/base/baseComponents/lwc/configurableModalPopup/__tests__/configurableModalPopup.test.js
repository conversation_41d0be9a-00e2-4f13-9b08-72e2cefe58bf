import { createElement } from "lwc";
import ConfigurableModalPopup from "c/configurableModalPopup";

async function flushPromises() {
    return Promise.resolve();
}

describe("c-configurable-modal-popup", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    it("should show header, content, and footer", async () => {
        const element = createElement("c-configurable-modal-popup", {
            is: ConfigurableModalPopup
        });

        element.actions = [
            {
                text: "Cancel1",
                handler: () => {}
            },
            {
                text: "Submit1",
                handler: () => {}
            }
        ];
        element.header = {
            text: "TestHeader"
        };

        document.body.appendChild(element);
        await flushPromises();
        const innerModal = element.shadowRoot.querySelector("c-modal-pop-up");
        expect(innerModal.headerText).toBe("TestHeader");
        expect(innerModal.submitText).toBe("Submit1");
        expect(innerModal.cancelText).toBe("Cancel1");
    });

    it("should click pass-handler when click footer action", async () => {
        const element = createElement("c-configurable-modal-popup", {
            is: ConfigurableModalPopup
        });

        const extraFn = jest.fn();

        element.actions = [
            {
                text: "cancel1",
                handler: () => {}
            },
            {
                text: "submit1",
                handler: () => {}
            },
            {
                text: "extra1",
                handler: extraFn
            }
        ];

        document.body.appendChild(element);
        await flushPromises();
        const buttons = element.shadowRoot.querySelectorAll("button");
        for (const button of buttons) {
            button.click();
        }
        await flushPromises();
        expect(extraFn).toHaveBeenCalledTimes(1);
    });

    it("should show msg when call info, warning and error, hide msg when calling hide msg", async () => {
        const element = createElement("c-configurable-modal-popup", {
            is: ConfigurableModalPopup
        });

        document.body.appendChild(element);
        await flushPromises();
        element.info("info msg");
        await flushPromises();
        let msgTitle = element.shadowRoot.querySelector(".msg-title");
        let msgIcon = element.shadowRoot.querySelector(".msg-icon");

        expect(msgTitle.value).toBe("info msg");
        expect(msgIcon.variant).toBe("inverse");
        expect(msgIcon.iconName).toBe("utility:success");

        element.warning("warning msg");
        await flushPromises();

        expect(msgTitle.value).toBe("warning msg");
        expect(msgIcon.variant).toBe("");
        expect(msgIcon.iconName).toBe("utility:warning");

        element.error("error msg");
        await flushPromises();

        expect(msgTitle.value).toBe("error msg");
        expect(msgIcon.variant).toBe("inverse");
        expect(msgIcon.iconName).toBe("utility:error");

        element.removeMsg();
        await flushPromises();
        msgTitle = element.shadowRoot.querySelector(".msg-title");
        msgIcon = element.shadowRoot.querySelector(".msg-icon");

        expect(msgTitle).toBeNull();
        expect(msgIcon).toBeNull();
    });

    it("should throw error when actions type is string", async () => {
        const element = createElement("c-configurable-modal-popup", {
            is: ConfigurableModalPopup
        });
        element.header = {
            text: "TestHeader"
        };
        element.actions = "actions test";
        let errorInfo = {};
        try {
            document.body.appendChild(element);
        } catch (error) {
            errorInfo = error;
        }
        expect(errorInfo.message).toBe("actions must be iterable and not string");
    });
});
