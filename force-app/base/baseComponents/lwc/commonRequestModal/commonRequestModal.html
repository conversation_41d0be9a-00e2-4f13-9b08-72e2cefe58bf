<template>
    <div class="common-request-container">
        <lightning-modal-header label={modalHeader}></lightning-modal-header>
        <lightning-modal-body>
            <div class="slds-m-around_medium">
                <template lwc:if={showWarning}>
                    <c-tw-warning-info warning-content={warningText} wrapper-class="slds-m-bottom_large"></c-tw-warning-info>
                </template>
                <template lwc:if={isCGMAndBillingEntityFieldsVisible}>
                    <lightning-input
                        class="approvalCGM"
                        label="Approved CGM (%)"
                        type="number"
                        value={approvedCGM}
                        onchange={handleApprovedCGMChange}
                        step="0.01"
                        required
                    ></lightning-input>
                    <template lwc:if={quoteMarginText}>
                        <div class="slds-form-element__help slds-m-bottom_small">{quoteMarginText}</div>
                    </template>
                    <label class="slds-form-element__label slds-m-left_xx-small">
                        <abbr class="slds-required">*</abbr>
                        <span>TW Contracting Location</span>
                    </label>
                    <lightning-helptext content={twContractionLocationHelptext}></lightning-helptext>
                    <lightning-combobox
                        class="twContractingLocation slds-m-horizontal_xx-small slds-m-bottom_x-small"
                        value={twContractingLocation}
                        placeholder=""
                        variant="label-hidden"
                        options={locationOptions}
                        onchange={handleContractingLocationChange}
                        required
                    ></lightning-combobox>
                    <lightning-input
                        class="twContractingLegalEntity slds-m-horizontal_xx-small slds-m-bottom_x-small"
                        label="TW Contracting Legal Entity"
                        value={twContractingLegalEntity}
                        disabled
                    ></lightning-input>
                </template>
                <lightning-textarea name="Comments" label="Comments" class="text-area slds-m-left_xx-small" onchange={handleCommentsChange} max-length="300" placeholder={placeholder}></lightning-textarea>
            </div>
        </lightning-modal-body>
        <lightning-modal-footer>
            <button class="slds-button slds-button_neutral cancel-button" onclick={handleCancelClick}>Cancel</button>
            <button class="slds-button slds-button_brand" onclick={handleSubmitClick}>{submitButtonName}</button>
        </lightning-modal-footer>
        <c-tw-spinner loading={isLoading} type="modal"></c-tw-spinner>
    </div>
</template>
