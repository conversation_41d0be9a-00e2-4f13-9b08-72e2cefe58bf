import { api, wire, LightningElement } from "lwc";
import { CloseActionScreenEvent } from "lightning/actions";
import { getRecord, getFieldValue } from "lightning/uiRecordApi";
import { getObjectInfo } from "lightning/uiObjectInfoApi";
import OPPORTUNITY_OBJECT from "@salesforce/schema/Opportunity";
import OPPORTUNITY_LOCATION_PICKLIST from "@salesforce/schema/Opportunity.TW_Contracting_Location_Picklist__c";
import OPPORTUNITY_ID from "@salesforce/schema/Deal_Review__c.Opportunity__c";
import QUOTE_ID from "@salesforce/schema/Deal_Review__c.Quote__c";
import Approved_CGM from "@salesforce/schema/Deal_Review__c.Approved_CGM__c";
import CONTRACTING_LOCATION_MAPPING_ID from "@salesforce/schema/Deal_Review__c.Opportunity__r.TW_Contracting_Location__c";
import TW_CONTRACTING_LOCATION_PICKLIST from "@salesforce/schema/Deal_Review__c.Opportunity__r.TW_Contracting_Location_Picklist__c";
import TW_CONTRACTING_LEGAL_ENTITY from "@salesforce/schema/Deal_Review__c.Opportunity__r.TW_Contracting_Location__r.TW_Contracting_Legal_Entity__c";
import fetchAllLocationMappings from "@salesforce/apex/DealReviewController.fetchAllLocationMappings";
import getQuoteInfoWithMargin from "@salesforce/apex/QuoteController.getQuoteInfoWithMargin";
import { ShowToastEvent } from "lightning/platformShowToastEvent";

export default class CommonRequestModal extends LightningElement {
    @api recordId;
    @api modalHeader = "Request";
    @api submitButtonName = "Submit";
    @api isLoading = false;
    @api isFinanceCoach = false;
    @api placeholder = "Leave comments here for reviewers...";
    @api showWarning = false;
    @api warningText = "";
    comments = "";
    opportunityId;
    quoteId;
    approvedCGM;
    twContractingLocation;
    originTwContractingLocation;
    twContractingLegalEntity;
    twContractionLocationHelptext;
    locationMapping = new Map();
    locationOptions;
    quoteMarginText = '';

    @wire(getRecord, {recordId: "$recordId", fields: [OPPORTUNITY_ID, QUOTE_ID, CONTRACTING_LOCATION_MAPPING_ID, TW_CONTRACTING_LOCATION_PICKLIST, TW_CONTRACTING_LEGAL_ENTITY, Approved_CGM]})
    wireTwBillingEntity({error, data}) {
        if (data) {
            this.opportunityId = getFieldValue(data, OPPORTUNITY_ID);
            this.quoteId = getFieldValue(data, QUOTE_ID);
            this.approvedCGM = getFieldValue(data, Approved_CGM)
            this.twContractingLocation = getFieldValue(data, TW_CONTRACTING_LOCATION_PICKLIST);
            this.originTwContractingLocation = getFieldValue(data, TW_CONTRACTING_LOCATION_PICKLIST);
            this.twContractingLegalEntity = getFieldValue(data, TW_CONTRACTING_LEGAL_ENTITY);
            
            if (this.quoteId && this.isFinanceCoach) {
                this.loadQuoteMarginData();
            }
        } else if (error) {
            console.error("Failed to get opportunity id and contracting location mapping id: ", JSON.stringify(error));
        }
    }

    @wire(getObjectInfo, { objectApiName: OPPORTUNITY_OBJECT })
    wireHelptext({ error, data }) {
        if (data) {
            this.twContractionLocationHelptext = data.fields[OPPORTUNITY_LOCATION_PICKLIST.fieldApiName].inlineHelpText;
        } else if (error) {
            console.error(`Failed to get TW Contracting Location helptext: ${JSON.stringify(error)}`);
        }
    }

    get isCGMAndBillingEntityFieldsVisible() {
        return this.submitButtonName === "Approve" && this.isFinanceCoach;
    }

    connectedCallback() {
        fetchAllLocationMappings()
            .then((result) => {
                this.locationMapping.set("", "");
                result.forEach((mapping) => {
                    this.locationMapping.set(mapping.TW_Contracting_Location__r.Name, mapping.TW_Contracting_Legal_Entity__c);
                });
                this.locationOptions = result.map((mapping) => {
                    return { label: mapping.TW_Contracting_Location__r.Name, value: mapping.TW_Contracting_Location__r.Name };
                });
            })
            .catch((error) => {
                console.error(`Failed to get Contracting Location Mappings: ${JSON.stringify(error)}`);
            });
    }

    handleContractingLocationChange(event) {
        this.twContractingLocation = event.detail.value;
        this.twContractingLegalEntity = this.locationMapping.get(event.detail.value);
    }

    handleCommentsChange(event) {
        this.comments = event.detail.value;
    }

    handleCancelClick() {
        this.dispatchEvent(new CustomEvent("clickcancel"));
        this.dispatchEvent(
            new CloseActionScreenEvent({
                bubbles: true,
                composed: true
            })
        );
    }

    handleSubmitClick() {
        let approvalCGM = "";
        let twContractingLocation = "";
        let oppId = "";
        if (this.isCGMAndBillingEntityFieldsVisible) {
            const CGMElem = this.template.querySelector(".approvalCGM");
            const twContractingLocationElem = this.template.querySelector(".twContractingLocation");
            
            approvalCGM = this.approvedCGM;
            twContractingLocation = this.twContractingLocation;

            if (!CGMElem.reportValidity() || (!this.approvedCGM && this.approvedCGM !== 0) || !twContractingLocationElem.reportValidity() || !twContractingLocation) {
                return;
            }
            oppId = this.opportunityId;
        }
        this.dispatchEvent(
            new CustomEvent("clicksubmit", {
                detail: {
                    comments: this.comments,
                    approvalCGM: Number(approvalCGM),
                    opportunityId: oppId,
                    twContractingLocation: twContractingLocation,
                    originTwContractingLocation: this.originTwContractingLocation
                }
            })
        );
    }

    loadQuoteMarginData() {
        getQuoteInfoWithMargin({ quoteId: this.quoteId })
            .then((result) => {
                if (this.approvedCGM == null) {
                    this.approvedCGM = result.PSQ__MarginPercentRounded__c;
                }
                this.quoteMarginText = `Margin % calculated in quote is ${result.PSQ__MarginPercentRounded__c || 0}%`
            })
            .catch(() => {
                const event = new ShowToastEvent({
                    variant: "error",
                    title: "Failed to load Quote Margin Data."
                });
                this.dispatchEvent(event);
            });
    }

    handleApprovedCGMChange(event) {
        this.approvedCGM = event.target.value;
    }

}
