{"apiName": "Deal_Review__c", "childRelationships": {}, "eTag": "test-etag", "fields": {"Id": {"displayValue": null, "value": "a0X8c00000TestId"}, "Opportunity__c": {"displayValue": null, "value": "0068c00000TestOpp"}, "Quote__c": {"displayValue": null, "value": "a0Y8c00000TestQuote"}, "Opportunity__r": {"displayValue": null, "value": {"apiName": "Opportunity", "fields": {"TW_Contracting_Location_Picklist__c": {"displayValue": null, "value": "Atlanta"}, "TW_Contracting_Location__c": {"displayValue": null, "value": "33"}, "TW_Contracting_Location__r": {"displayValue": null, "value": {"apiName": "TW_Contracting_Location__c", "fields": {"TW_Contracting_Legal_Entity__c": {"displayValue": null, "value": "Atlanta test"}}}}}}}}, "id": "a0X8c00000TestId", "lastModifiedById": "0058c00000TestUser", "lastModifiedDate": "2024-01-01T00:00:00.000Z", "recordTypeId": "0128c00000TestRT", "recordTypeInfo": null, "systemModstamp": "2024-01-01T00:00:00.000Z"}