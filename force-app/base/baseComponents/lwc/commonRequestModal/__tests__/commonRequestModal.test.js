import { createElement } from "lwc";
import { flushPromises } from "c/utils";
import { CloseActionScreenEvent } from "lightning/actions";
import { getRecord } from "lightning/uiRecordApi";
import { registerLdsTestWireAdapter } from "@salesforce/sfdx-lwc-jest";
import CommonRequestModal from "c/commonRequestModal";
import fetchAllLocationMappings from "@salesforce/apex/DealReviewController.fetchAllLocationMappings";
import getQuoteInfoWithMargin from "@salesforce/apex/QuoteController.getQuoteInfoWithMargin";
import mockAllContractionLocation from "./data/allContractingLocation.json";
import mockDealReviewRecord from "./data/dealReviewRecord.json";
import mockQuoteMarginData from "./data/quoteMarginData.json";

jest.mock(
    "@salesforce/apex/DealReviewController.fetchAllLocationMappings",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

jest.mock(
    "@salesforce/apex/QuoteController.getQuoteInfoWithMargin",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

const getRecordAdapter = registerLdsTestWireAdapter(getRecord);

describe("c-common-request-modal", () => {
    let shadowRoot;
    let element;
    let modalHeader = "Request Modal";

    beforeEach(async () => {
        fetchAllLocationMappings.mockResolvedValue(mockAllContractionLocation);
        getQuoteInfoWithMargin.mockResolvedValue(mockQuoteMarginData);

        element = createElement("c-common-request-modal", {
            is: CommonRequestModal
        });
        element.modalHeader = modalHeader;
        element.recordId = "a0X8c00000TestId";
        shadowRoot = element.shadowRoot;
        document.body.appendChild(element);
        await flushPromises();
    });

    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    it("should render modal correctly and close the modal when click cancel button", async () => {
        const headerEl = shadowRoot.querySelector("lightning-modal-header");
        const textareaEl = shadowRoot.querySelector("lightning-textarea");
        expect(headerEl.label).toBe(modalHeader);
        expect(textareaEl).toBeTruthy();

        const dispatchEventSpy = jest.spyOn(element, "dispatchEvent");
        const cancelButton = element.shadowRoot.querySelector(".cancel-button");
        cancelButton.click();

        expect(dispatchEventSpy).toHaveBeenCalledWith(new CloseActionScreenEvent());
    });

    it("should dispatch clicksubmit event with comments when click Submit button click", () => {
        let commentContent = "New Comment";
        const mockTextChangeEvent = new CustomEvent("change", {
            detail: { value: commentContent }
        });
        const textareaElement = shadowRoot.querySelector("lightning-textarea");
        textareaElement.dispatchEvent(mockTextChangeEvent);

        const clickSubmit = jest.fn();
        element.addEventListener("clicksubmit", clickSubmit);

        const submitButtonEl = shadowRoot.querySelector(".slds-button_brand");
        submitButtonEl.click();

        expect(clickSubmit).toHaveBeenCalled();
        expect(clickSubmit.mock.calls[0][0].detail).toStrictEqual({
            approvalCGM: 0,
            comments: "New Comment",
            opportunityId: "",
            originTwContractingLocation: undefined,
            twContractingLocation: ""
        });
    });

    it("should show approval CGM, contracting location and dispatch clicksubmit event with comments when is approval action and click Submit button", async () => {
        getQuoteInfoWithMargin.mockResolvedValue(mockQuoteMarginData);

        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";

        getRecordAdapter.emit(mockDealReviewRecord);
        await flushPromises();

        let commentContent = "New Comment";
        const mockTextChangeEvent = new CustomEvent("change", {
            detail: { value: commentContent }
        });
        const textareaElement = shadowRoot.querySelector("lightning-textarea");
        textareaElement.dispatchEvent(mockTextChangeEvent);

        const approvalCgm = shadowRoot.querySelector("lightning-input.approvalCGM");
        approvalCgm.reportValidity = jest.fn(() => true);

        const mockPickListChangeEvent = new CustomEvent("change", {
            detail: { value: "Atlanta" }
        });
        const twContractingLocationElem = shadowRoot.querySelector(".twContractingLocation");
        twContractingLocationElem.reportValidity = jest.fn(() => true);
        twContractingLocationElem.dispatchEvent(mockPickListChangeEvent);

        await flushPromises();

        const clickSubmit = jest.fn();
        element.addEventListener("clicksubmit", clickSubmit);

        const submitButtonEl = shadowRoot.querySelector(".slds-button_brand");
        submitButtonEl.click();

        await flushPromises();

        expect(clickSubmit).toHaveBeenCalled();
        expect(clickSubmit.mock.calls[0][0].detail).toStrictEqual({
            approvalCGM: 15.26,
            comments: "New Comment",
            opportunityId: "0068c00000TestOpp",
            originTwContractingLocation: "Atlanta",
            twContractingLocation: "Atlanta"
        });
    });

    it("should_display_lightning_input_instead_of_record_edit_form_when_rendered_given_finance_coach_user", async () => {
        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";
        await flushPromises();

        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");

        expect(lightningInput).toBeTruthy();
    });

    it("should_load_quote_margin_data_when_deal_review_loaded_given_valid_quote_id_and_finance_coach", async () => {
        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";

        getRecordAdapter.emit(mockDealReviewRecord);
        await flushPromises();

        expect(getQuoteInfoWithMargin).toHaveBeenCalledWith({ quoteId: "a0Y8c00000TestQuote" });
        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");
        expect(lightningInput.value).toBe(15.26);

        const helpText = shadowRoot.querySelector(".slds-form-element__help");
        expect(helpText).toBeTruthy();
        expect(helpText.textContent).toBe("Margin % calculated in quote is 15.26%");
    });

    it("should_handle_quote_margin_loading_error_when_api_fails_given_invalid_quote_id", async () => {
        const dispatchEventSpy = jest.spyOn(element, "dispatchEvent");
        getQuoteInfoWithMargin.mockRejectedValue(new Error("Quote not found"));

        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";

        getRecordAdapter.emit(mockDealReviewRecord);
        await flushPromises();

        expect(getQuoteInfoWithMargin).toHaveBeenCalledWith({ quoteId: "a0Y8c00000TestQuote" });
        expect(dispatchEventSpy).toHaveBeenCalledWith(
            expect.objectContaining({
                type: "lightning__showtoast",
                detail: expect.objectContaining({
                    variant: "error",
                    title: "Failed to load Quote Margin Data."
                })
            })
        );

        const helpText = shadowRoot.querySelector(".slds-form-element__help");
        expect(helpText).toBeFalsy();

        dispatchEventSpy.mockRestore();
    });

    it("should_update_approved_cgm_value_when_user_inputs_new_value_given_valid_number", async () => {
        getQuoteInfoWithMargin.mockResolvedValue(mockQuoteMarginData);
        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";
        getRecordAdapter.emit(mockDealReviewRecord);
        await flushPromises();

        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");
        expect(lightningInput.value).toBe(15.26);

        await flushPromises();

        const mockInputEvent = new CustomEvent("change");
        Object.defineProperty(mockInputEvent, "target", {
            value: { value: "15.25" },
            enumerable: true
        });
        lightningInput.dispatchEvent(mockInputEvent);

        await flushPromises();

        expect(lightningInput.value).toBe("15.25");

        const clickSubmit = jest.fn();
        element.addEventListener("clicksubmit", clickSubmit);
        lightningInput.reportValidity = jest.fn(() => true);
        const twContractingLocationElem = shadowRoot.querySelector(".twContractingLocation");
        twContractingLocationElem.reportValidity = jest.fn(() => true);
        const mockPickListChangeEvent = new CustomEvent("change", {
            detail: { value: "Atlanta" }
        });
        twContractingLocationElem.dispatchEvent(mockPickListChangeEvent);

        const submitButtonEl = shadowRoot.querySelector(".slds-button_brand");
        submitButtonEl.click();

        expect(clickSubmit).toHaveBeenCalled();
        expect(clickSubmit.mock.calls[0][0].detail.approvalCGM).toBe(15.25);
    });

    it("should_display_error_message_when_user_inputs_number_given_over_two_decimal_input", async () => {
        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";
        await flushPromises();

        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");
        expect(lightningInput.step).toBe("0.01");

        lightningInput.value = "15.2567";
        lightningInput.reportValidity = jest.fn(() => false);
        lightningInput.setCustomValidity = jest.fn();

        const isValid = lightningInput.reportValidity();
        expect(isValid).toBe(false);
    });

    it("should_enable_auto_population_when_user_is_finance_coach_given_finance_coach_permission", async () => {
        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";

        getRecordAdapter.emit(mockDealReviewRecord);
        await flushPromises();

        expect(getQuoteInfoWithMargin).toHaveBeenCalledWith({ quoteId: "a0Y8c00000TestQuote" });

        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");
        expect(lightningInput.value).toBe(15.26);

        const helpText = shadowRoot.querySelector(".slds-form-element__help");
        expect(helpText).toBeTruthy();
        expect(helpText.textContent).toBe("Margin % calculated in quote is 15.26%");
    });

    it("should_disable_auto_population_when_user_is_not_finance_coach_given_regular_user_permission", async () => {
        element.isFinanceCoach = false;
        element.submitButtonName = "Submit";

        getRecordAdapter.emit(mockDealReviewRecord);
        await flushPromises();

        expect(getQuoteInfoWithMargin).not.toHaveBeenCalled();

        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");
        const helpText = shadowRoot.querySelector(".slds-form-element__help");

        expect(lightningInput).toBeFalsy();
        expect(helpText).toBeFalsy();
    });

    it("should_prevent_submission_when_approved_cgm_is_empty_and_required", async () => {
        element.isFinanceCoach = true;
        element.submitButtonName = "Approve";
        await flushPromises();

        const lightningInput = shadowRoot.querySelector("lightning-input.approvalCGM");
        const twContractingLocationElem = shadowRoot.querySelector(".twContractingLocation");

        lightningInput.value = "";
        expect(lightningInput.value).toBe("");

        lightningInput.reportValidity = jest.fn(() => false);
        twContractingLocationElem.reportValidity = jest.fn(() => true);

        const mockPickListChangeEvent = new CustomEvent("change", {
            detail: { value: "Atlanta" }
        });
        twContractingLocationElem.dispatchEvent(mockPickListChangeEvent);

        const clickSubmit = jest.fn();
        element.addEventListener("clicksubmit", clickSubmit);

        const submitButtonEl = shadowRoot.querySelector(".slds-button_brand");
        submitButtonEl.click();

        expect(clickSubmit).not.toHaveBeenCalled();
        expect(lightningInput.reportValidity).toHaveBeenCalled();
    });
});
