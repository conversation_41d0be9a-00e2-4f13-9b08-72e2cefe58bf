import { createElement } from "lwc";
import HighlightText from "c/highlightText";

describe("c-highlight-text", () => {
    it("renders the component with the correct text", () => {
        const textValue = "Hello, this is a test.";
        const element = createElement("c-highlight-text", {
            is: HighlightText
        });
        element.text = textValue;
        document.body.appendChild(element);

        const richTextElement = element.shadowRoot.querySelector("lightning-formatted-rich-text");
        expect(richTextElement.value).toBe(textValue);
    });

    it("highlights the wordToHighlight in the text", () => {
        const textValue = "Hello, this is a test.";
        const wordToHighlight = "test";
        const expectedFormattedText = "Hello, this is a <strong>test</strong>.";
        const element = createElement("c-highlight-text", {
            is: HighlightText
        });
        element.text = textValue;
        element.wordToHighlight = wordToHighlight;
        document.body.appendChild(element);

        const richTextElement = element.shadowRoot.querySelector("lightning-formatted-rich-text");
        expect(richTextElement.value).toBe(expectedFormattedText);
    });

    it("highlights the wordToHighlight in the text with case insensitive", () => {
        const textValue = "Hello, this is a TesT.";
        const wordToHighlight = "test";
        const expectedFormattedText = "Hello, this is a <strong>TesT</strong>.";
        const element = createElement("c-highlight-text", {
            is: HighlightText
        });
        element.text = textValue;
        element.wordToHighlight = wordToHighlight;
        document.body.appendChild(element);

        const richTextElement = element.shadowRoot.querySelector("lightning-formatted-rich-text");
        expect(richTextElement.value).toBe(expectedFormattedText);
    });

    it("does not highlight when wordToHighlight is not present in the text", () => {
        const textValue = "Hello, this is a test.";
        const wordToHighlight = "world";
        const element = createElement("c-highlight-text", {
            is: HighlightText
        });
        element.text = textValue;
        element.wordToHighlight = wordToHighlight;
        document.body.appendChild(element);

        const richTextElement = element.shadowRoot.querySelector("lightning-formatted-rich-text");
        expect(richTextElement.value).toBe(textValue);
    });

    it("highlights all occurrences of the case-insensitive wordToHighlight in the text", () => {
        const textValue = "Hello, this is a test. This is another Test.";
        const wordToHighlight = "test";
        const expectedFormattedText = "Hello, this is a <strong>test</strong>. This is another <strong>Test</strong>.";
        const expectedOccurrences = 2;
        const element = createElement("c-highlight-text", {
            is: HighlightText
        });
        element.text = textValue;
        element.wordToHighlight = wordToHighlight;
        document.body.appendChild(element);

        const richTextElement = element.shadowRoot.querySelector("lightning-formatted-rich-text");
        expect(richTextElement.value).toBe(expectedFormattedText);

        // Check if the occurrences match
        const occurrences = (richTextElement.value.match(new RegExp(wordToHighlight, "gi")) || []).length;
        expect(occurrences).toBe(expectedOccurrences);
    });
});
