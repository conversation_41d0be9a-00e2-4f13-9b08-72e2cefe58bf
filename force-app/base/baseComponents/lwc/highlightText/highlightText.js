import { api, LightningElement } from "lwc";

export default class HighlightText extends LightningElement {
    @api text = "";
    @api wordToHighlight = "";

    get formattedText() {
        if (!this.text || !this.wordToHighlight) {
            return this.text;
        }

        // Case-insensitive highlighting using RegExp
        const regex = new RegExp(this.wordToHighlight, "gi");

        return this.text.replace(regex, (match) => `<strong>${match}</strong>`);
    }
}
