import { LightningElement, api } from "lwc";
import { loadScript } from "lightning/platformResourceLoader";
import tabbableURL from "@salesforce/resourceUrl/tabbable";

export default class ModalPopUp extends LightningElement {
    @api name = "name";
    @api headerText = "Modal Header";
    @api contentText = "Modal Content";
    @api submitText = "Submit";
    @api cancelText = "Cancel";
    @api notShowTextContent = false;
    @api hasErrorStatus = false;
    @api noShowFooter = false;
    @api specifyContainerStyle = "";
    @api hideCloseButton = false;

    stateInternal = {};

    connectedCallback() {
        Promise.all([loadScript(this, tabbableURL)]).catch(() => {
            window.tabbable = () => [];
        });
    }

    renderedCallback() {
        if (!this.notShowTextContent) {
            this.template.querySelector('[data-id="modal-content-id-1"]').focus();
        }
    }

    handleClose() {
        this.dispatchEvent(new CustomEvent("close", { detail: { name: this.name } }));
    }

    handleSubmit() {
        this.dispatchEvent(new CustomEvent("submit", { detail: { name: this.name } }));
    }

    handleCancel() {
        this.dispatchEvent(new CustomEvent("cancel", { detail: { name: this.name } }));
    }

    isTabEvent(e) {
        return e.key === "Tab" || e.keyCode === 9;
    }

    isEscEvent(e) {
        return e.key === "Escape" || e.keyCode === 27;
    }

    updateTabbableNodes() {
        const tabbableNodes = window.tabbable(this.template.querySelector(".modal-container"));
        this.stateInternal.firstTabbableNode = tabbableNodes[0];
        this.stateInternal.lastTabbableNode = tabbableNodes[tabbableNodes.length - 1];
    }

    tryFocus(node) {
        node.focus();
    }

    checkTab(e) {
        this.updateTabbableNodes();
        if (e.shiftKey && e.target === this.stateInternal.firstTabbableNode) {
            e.preventDefault();
            this.tryFocus(this.stateInternal.lastTabbableNode);
            return;
        }
        if (!e.shiftKey && e.target === this.stateInternal.lastTabbableNode) {
            e.preventDefault();
            this.tryFocus(this.stateInternal.firstTabbableNode);
        }
    }

    @api
    handleKeyDown(e) {
        if (this.isTabEvent(e)) {
            this.checkTab(e);
        } else if (this.isEscEvent(e)) {
            this.handleClose();
        }
    }
}
