import { createElement } from "lwc";
import ModalPopUp from "c/modalPopUp";
import { flushPromises } from "c/utils";

describe("c-modal-pop-up", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should dispatch submit event when handle submit event", async () => {
        const submitEvent = jest.fn();

        const element = createElement("c-modal-pop-up", {
            is: ModalPopUp
        });
        element.addEventListener("submit", submitEvent);
        document.body.appendChild(element);
        await flushPromises();
        const submitButton = element.shadowRoot.querySelector(".slds-button_brand");
        submitButton.click();
        expect(submitEvent).toHaveBeenCalled();
    });

    it("should dispatch cancel event when handle cancel event", async () => {
        const cancelEvent = jest.fn();

        const element = createElement("c-modal-pop-up", {
            is: ModalPopUp
        });
        element.addEventListener("cancel", cancelEvent);
        document.body.appendChild(element);
        await flushPromises();
        const cancelButton = element.shadowRoot.querySelector(".cancel-button");
        cancelButton.click();
        expect(cancelEvent).toHaveBeenCalled();
    });

    it("should dispatch close event when handle close event", async () => {
        const closeEvent = jest.fn();

        const element = createElement("c-modal-pop-up", {
            is: ModalPopUp
        });
        element.addEventListener("close", closeEvent);
        document.body.appendChild(element);
        await flushPromises();
        const closeButton = element.shadowRoot.querySelector(".slds-modal__close");
        closeButton.click();
        expect(closeEvent).toHaveBeenCalled();
    });

    it("should dispatch close event when event.key is Escape", async () => {
        const closeEvent = jest.fn();

        const element = createElement("c-modal-pop-up", {
            is: ModalPopUp
        });
        element.addEventListener("close", closeEvent);
        document.body.appendChild(element);
        await flushPromises();
        const container = element.shadowRoot.querySelector(".slds-modal__container");
        const event = new KeyboardEvent("keydown", { key: "Escape" });
        container.dispatchEvent(event);
        expect(closeEvent).toHaveBeenCalled();
    });

    it("should focus last tab node when current focus in first tab node and event.key is shiftKey and tab", async () => {
        const firstTabNodeFocus = jest.fn();
        const lastTabNodeFocus = jest.fn();
        const secondTabNodeFocus = jest.fn();
        const firstTabNode = { name: "first tab node", focus: firstTabNodeFocus };
        const secondTabNode = { name: "second tab node", focus: secondTabNodeFocus };
        const lastTabNode = { name: "last tab node", focus: lastTabNodeFocus };
        window.tabbable = () => [firstTabNode, secondTabNode, lastTabNode];
        const element = createElement("c-modal-pop-up", {
            is: ModalPopUp
        });
        document.body.appendChild(element);
        await flushPromises();
        element.handleKeyDown({ key: "Tab", shiftKey: true, target: firstTabNode, preventDefault: jest.fn() });
        expect(lastTabNodeFocus).toHaveBeenCalled();
    });

    it("should focus first tab node when current focus in last tab node and event.key is tab", async () => {
        const firstTabNodeFocus = jest.fn();
        const lastTabNodeFocus = jest.fn();
        const secondTabNodeFocus = jest.fn();
        const firstTabNode = { name: "first tab node", focus: firstTabNodeFocus };
        const secondTabNode = { name: "second tab node", focus: secondTabNodeFocus };
        const lastTabNode = { name: "last tab node", focus: lastTabNodeFocus };
        window.tabbable = () => [firstTabNode, secondTabNode, lastTabNode];
        const element = createElement("c-modal-pop-up", {
            is: ModalPopUp
        });
        document.body.appendChild(element);
        await flushPromises();
        element.handleKeyDown({ key: "Tab", target: lastTabNode, preventDefault: jest.fn() });
        expect(firstTabNodeFocus).toHaveBeenCalled();
    });
});
