<template>
    <div role="dialog" aria-labelledby="modal-heading-01" aria-describedby="modal-content-id-1" style={specifyContainerStyle} class="slds-modal__container modal-container" onkeydown={handleKeyDown}>
        <header class="slds-modal__header">
            <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={handleClose} if:false={hideCloseButton}>
                <svg class="slds-button__icon slds-button__icon_large" aria-hidden="true">
                    <use xlink:href="/resource/SLDS0205/icons/utility-sprite/svg/symbols.svg#close"></use>
                </svg>
                <span class="slds-assistive-text">Close</span>
            </button>
            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate" tabindex="0">{headerText}</h2>
        </header>
        <div if:false={notShowTextContent} id="modal-content-id-1" class="slds-modal__content slds-p-around_medium" data-id="modal-content-id-1" tabindex="0">{contentText}</div>
        <slot name="modalContent"></slot>
        <footer if:false={noShowFooter} class="slds-modal__footer">
            <lightning-icon if:true={hasErrorStatus} icon-name="utility:error" alternative-text="Error!" title="Error" size="small" variant="error"></lightning-icon>
            <button class="slds-m-left_xx-small slds-button slds-button_neutral cancel-button" onclick={handleCancel}>{cancelText}</button>
            <slot name="extraButtons"></slot>
            <button class="slds-m-left_xx-small slds-button slds-button_brand" onclick={handleSubmit}>{submitText}</button>
        </footer>
    </div>
</template>
