/*
 * Copyright (c) 2019, salesforce.com, inc.
 * All rights reserved.
 * SPDX-License-Identifier: MIT
 * For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/MIT
 */

import { createElement } from "lwc";
import Element from "c/baseComboboxFormattedText";

const createComponent = (params = {}) => {
    const element = createElement("base-combobox-formatted-text", {
        is: Element
    });

    Object.assign(element, params);
    document.body.appendChild(element);
    return element;
};

describe("base-combobox-formatted-text", () => {
    it("render array text", () => {
        const element = createComponent({
            text: [
                { highlight: false, text: " text one" },
                { highlight: true, text: "text two" },
                { highlight: false, text: "text three" }
            ]
        });
        expect(element).toMatchSnapshot();
    });

    it("renders string test", () => {
        const element = createComponent({
            text: "test one"
        });
        expect(element).toMatchSnapshot();
    });
});
