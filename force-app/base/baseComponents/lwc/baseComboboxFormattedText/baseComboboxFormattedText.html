<template>
    <template if:true={hasParts}>
        <template for:each={text} for:item="item">
            <template if:true={item.part.highlight}>
                <strong key={item.key}>{item.part.text}</strong>
            </template>
            <template if:false={item.part.highlight}>{item.part.text}</template>
        </template>
    </template>
    <template if:false={hasParts}> {text} </template>
</template>
