import { createElement } from "lwc";
import GroupedCombobox from "c/groupedCombobox";
import { flushPromises } from "c/utils";

const GROUP_A_OPTIONS = [
    { label: "A Option 1", value: "A Option 1" },
    { label: "A Option 2", value: "A Option 2" },
    { label: "A Option 3", value: "A Option 3" }
];
const GROUP_B_OPTIONS = [
    { label: "B Option 1", value: "B Option 1" },
    { label: "B Option 2", value: "B Option 2" }
];
const OPTIONS = [
    { groupName: undefined, options: [{ label: "--none--", value: "" }] },
    { groupName: "Group A", options: GROUP_A_OPTIONS },
    { groupName: "Group B", options: GROUP_B_OPTIONS }
];
const PRE_SELECTED_VALUE = "A Option 1";

async function createComponent() {
    const element = createElement("c-grouped-combobox", {
        is: GroupedCombobox
    });
    element.label = "Combobox Label";
    element.required = "true";
    element.options = OPTIONS;
    element.selectedValue = PRE_SELECTED_VALUE;
    document.body.appendChild(element);

    await flushPromises();
    return element;
}

describe("c-grouped-combobox", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should render dropdown and check preselected option when click combobox button", async () => {
        const element = await createComponent();
        const buttonElem = element.shadowRoot.querySelector("button");
        buttonElem.click();
        await flushPromises();
        expect(element).toMatchSnapshot();
    });

    it("should select option,close dropdown and dispatch 'change' event when click option", async () => {
        const element = await createComponent();
        const buttonElem = element.shadowRoot.querySelector("button");
        buttonElem.click();
        await flushPromises();
        expect(element.selectedValue).toBe("A Option 1");

        const changeHandler = jest.fn();
        element.addEventListener("change", changeHandler);

        const optionToSelectElem = element.shadowRoot.querySelector('[data-value="B Option 2"]');
        optionToSelectElem.click();
        await flushPromises();

        const dropdownElem = element.shadowRoot.querySelector(".dropdown");
        expect(element.selectedValue).toBe("B Option 2");
        expect(dropdownElem).toBeFalsy();
        expect(changeHandler).toHaveBeenCalledTimes(1);
    });

    it("should close dropdown when click place outside dropdown", async () => {
        const element = await createComponent();
        const buttonElem = element.shadowRoot.querySelector("button");
        buttonElem.click();
        await flushPromises();

        const dropdownElemShouldOpen = element.shadowRoot.querySelector(".dropdown");
        dropdownElemShouldOpen.dispatchEvent(new CustomEvent("mouseenter"));
        await flushPromises();

        buttonElem.dispatchEvent(new CustomEvent("blur"));
        await flushPromises();

        expect(dropdownElemShouldOpen).toBeTruthy();

        dropdownElemShouldOpen.dispatchEvent(new CustomEvent("mouseleave"));
        await flushPromises();

        buttonElem.dispatchEvent(new CustomEvent("blur"));
        await flushPromises();
        const dropdownElemShouldClose = element.shadowRoot.querySelector(".dropdown");

        expect(dropdownElemShouldClose).toBeFalsy();
    });
});
