<template>
    <div class="slds-form-element">
        <label class="slds-form-element__label combobox-label" for="headerLabel" id="headerLabel">
            <template if:true={required}>
                <abbr class="slds-required" title="required">* </abbr>
            </template>
            {label}
        </label>
        <div class={comboboxTriggerClass}>
            <div class="slds-combobox__form-element slds-input-has-icon slds-input-has-icon_right" role="combobox">
                <button
                    onclick={handleToggleDropdown}
                    type="button"
                    class={buttonClass}
                    disabled={_readOnly}
                    onblur={handleComboboxBlur}
                    role="combobox"
                    aria-label={buttonAriaLabel}
                    aria-haspopup="listbox"
                    aria-expanded={showDropDown}
                >
                    <span class="slds-truncate" aria-hidden="true">{buttonText}</span>
                </button>
                <span class="slds-icon_container slds-icon-utility-down slds-input__icon slds-input__icon_right">
                    <svg class="slds-icon slds-icon slds-icon_x-small slds-icon-text-default">
                        <use xlink:href="/_slds/icons/utility-sprite/svg/symbols.svg#down"></use>
                    </svg>
                </span>
            </div>
            <div if:true={showErrorMsg} class="slds-text-color_error slds-form-element__help">{errorMsg}</div>
            <!--                        <lightning-button-icon class="slds-combobox__form-element" icon-name="utility:down"  ></lightning-button-icon>-->
            <div if:true={showDropDown} class={dropDownClass} role="listbox" onmouseenter={handleDropdownMouseEnter} onmouseleave={handleDropdownMouseLeave} onclick={handleDropdownClick}>
                <template for:each={_groupsAndOptions} for:item="group">
                    <ul key={group.groupName} class="slds-listbox slds-listbox_vertical" role="group">
                        <!-- GROUP LIST ITEM -->
                        <li if:true={group.groupName} role="presentation" class="slds-listbox__item">
                            <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small group-name-content" role="presentation">
                                <label class="group-name-label" role="presentation">{group.groupName}&nbsp;</label>
                                <lightning-helptext class="tool-tip" icon-name="utility:info" alternative-text={group.helpText} content={group.helpText}></lightning-helptext>
                            </div>
                        </li>
                        <!-- OPTIONS -->
                        <template for:each={group.options} for:item="option">
                            <li key={option.value} role="presentation" class="slds-listbox__item">
                                <div data-value={option.value} data-group-name={group.groupName} onclick={handleOptionClick}>
                                    <div if:true={option.selected} class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small slds-is-selected slds-has-focus" role="option">
                                        <span class="slds-icon_container slds-icon-utility-check slds-current-color slds-media__figure slds-listbox__option-icon">
                                            <svg class="slds-icon slds-icon_x-small">
                                                <use xlink:href="/_slds/icons/utility-sprite/svg/symbols.svg#check"></use>
                                            </svg>
                                        </span>
                                        <span class="slds-media__body">
                                            <span title={option.label}>{option.label}</span>
                                        </span>
                                    </div>
                                    <div if:false={option.selected} class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small" role="option">
                                        <span class="slds-media__figure slds-listbox__option-icon"></span>
                                        <span class="slds-media__body">
                                            <span title={option.label}>{option.label}</span>
                                        </span>
                                    </div>
                                </div>
                            </li>
                        </template>
                    </ul>
                </template>
            </div>
        </div>
    </div>
</template>
