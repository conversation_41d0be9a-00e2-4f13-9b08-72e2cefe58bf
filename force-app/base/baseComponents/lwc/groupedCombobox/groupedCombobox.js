import { LightningElement, api } from "lwc";

const NONE_VALUE = "--None--";

export default class GroupedCombobox extends LightningElement {
    @api label;
    @api required;
    @api placeHolder;
    @api errorMsg;
    // to fix drop down direction question
    @api isRelativePosition;
    showDropDown = false;
    _selectedValue;
    _groupsAndOptions = [];
    isMouseInDropdown = false;

    showErrorMsg = false;

    get comboboxTriggerClass() {
        let triggerClass = "slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click slds-is-open slds-combobox_container slds-form-element__control combobox";
        if (this.isRelativePosition) {
            triggerClass += " slds-is-relative";
        }
        if (this.showDropDown) {
            triggerClass += " slds-is-open";
        }
        return triggerClass;
    }

    get dropDownClass() {
        if (this.isRelativePosition) {
            return "slds-dropdown slds-dropdown_fluid slds-scrollable slds-is-absolute dropdown";
        }
        return "slds-dropdown slds-dropdown_fluid slds-scrollable slds-is-fixed dropdown";
    }

    adjustDropdownElementPosition() {
        const dropdownElement = this.template.querySelector(".dropdown");
        const input = this.template.querySelector("button");
        const oppoSelectBoxPosition = input.getBoundingClientRect();
        dropdownElement.style.top = oppoSelectBoxPosition.top + 30 + "px";
        dropdownElement.style.width = input.offsetWidth + "px";
    }

    @api
    get options() {
        return this._groupsAndOptions;
    }

    set options(value) {
        if (value) {
            this._groupsAndOptions = JSON.parse(JSON.stringify(value));
            this.setSelectedOnOptions();
            this.validateSelectedValue();
        }
    }

    get buttonClass() {
        let buttonClass = "";
        if (this.selectedValue) {
            buttonClass += "slds-input_faux slds-combobox__input slds-combobox__input-value";
        } else {
            buttonClass += "slds-input_faux slds-combobox__input";
        }
        return buttonClass;
    }

    get buttonText() {
        if (this.selectedValue) {
            return this.selectedLabel;
        }
        return this.placeHolder;
    }

    get selectedLabel() {
        let selectedLabel = "";

        this._groupsAndOptions.forEach((group) => {
            group.options.forEach((option) => {
                if (option.value === this.selectedValue) {
                    selectedLabel = option.label;
                }
            });
        });

        return selectedLabel;
    }
    @api
    get selectedValue() {
        return this._selectedValue;
    }

    set selectedValue(value) {
        if (!value) {
            return;
        }
        this.handleSelectedValue(value);
    }

    handleSelectedValue(value) {
        this._selectedValue = value;
        this.setSelectedOnOptions();
    }

    get buttonAriaLabel() {
        let required = this.required ? "required" : "";
        return this.label + "," + required + ",selected " + this.buttonText;
    }
    connectedCallback() {
        this.setSelectedOnOptions();
        this.validateSelectedValue(); //check that selected value exists in the _groupsAndOptions provided
    }

    validateSelectedValue() {
        let found = false;
        this._groupsAndOptions.forEach((group) => {
            group.options.forEach((option) => {
                if (option.value === this.selectedValue) {
                    found = true;
                }
            });
        });

        if (!found) {
            //selected value not found in the _groupsAndOptions list, therefore clearing the value field
            this.handleSelectedValue(null);
        }
    }

    fireOnChangeEvent(selectedValue, selectedGroupName) {
        if (this.selectedValue) {
            const onChangeEvent = new CustomEvent("change", {
                detail: { value: selectedValue, selectedGroupName }
            });

            this.dispatchEvent(onChangeEvent);
        }
    }

    setSelectedOnOptions() {
        this._groupsAndOptions.forEach((group) => {
            group.options.forEach((option) => {
                if (option.value === this.selectedValue) {
                    option.selected = true;
                } else {
                    option.selected = false;
                }
            });
        });
    }

    handleToggleDropdown() {
        this.showDropDown = !this.showDropDown;
        if (this.showDropDown && !this.isRelativePosition) {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                this.adjustDropdownElementPosition();
                window.addEventListener("resize", () => {
                    this.adjustDropdownElementPosition();
                });
            }, 1);
        }
    }

    handleOptionClick(event) {
        let selectedValue = event.currentTarget.dataset.value;
        let selectedGroupName = event.currentTarget.dataset.groupName;
        this.handleSelectedValue(event.currentTarget.dataset.value);
        this.handleToggleDropdown();
        this.fireOnChangeEvent(selectedValue, selectedGroupName);
        this.focusInput();
        this.handleShowErrorMsg();
    }

    handleDropdownMouseEnter() {
        this.isMouseInDropdown = true;
    }

    handleDropdownMouseLeave() {
        this.isMouseInDropdown = false;
    }

    handleComboboxBlur() {
        if (this.showDropDown && !this.isMouseInDropdown) {
            this.handleToggleDropdown();
            this.handleShowErrorMsg();
        }
    }

    @api
    focusInput() {
        const input = this.template.querySelector("button");
        input.focus();
    }

    handleDropdownClick() {
        this.focusInput();
    }

    @api
    validate() {
        this.handleShowErrorMsg();
    }

    handleShowErrorMsg() {
        this.showErrorMsg = this.required && !!this.errorMsg && (!this._selectedValue || this._selectedValue === NONE_VALUE);
    }
}
