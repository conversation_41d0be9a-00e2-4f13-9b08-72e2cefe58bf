import { trackCRMUserEvent, trackCRMUserClick } from "../matomoTracker";
jest.mock(
    "@salesforce/user/Id",
    () => {
        return { default: null };
    },
    { virtual: true }
);
global.fetch = jest.fn(() => Promise.resolve("success"));

describe("c-matomo-tracker", () => {
    beforeEach(() => {
        fetch.mockClear();
    });
    it("test trackCRMUserEvent when userId is null", () => {
        trackCRMUserEvent({
            category: "test category",
            action: "test action",
            name: "eventName"
        });
        expect(global.fetch).toHaveBeenCalledTimes(0);
    });

    it("test trackCRMUserClick", () => {
        trackCRMUserClick("test category", "test name");
        expect(global.fetch).toHaveBeenCalledTimes(0);
    });
});
