import { trackCRMUserEvent, trackCRMUserClick } from "../matomoTracker";
import { flushPromises } from "c/utils";
import getDetUserIdList from "@salesforce/apex/MKTUserController.getDetUserIdList";

jest.mock(
    "@salesforce/apex/MKTUserController.getDetUserIdList",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

jest.mock(
    "@salesforce/user/Id",
    () => {
        return { default: "0051T00000ZFwAwWED" };
    },
    { virtual: true }
);

describe("c-matomo-tracker", () => {
    beforeEach(() => {
        delete window.location;
        window.location = { origin: "https://thoughtworks--sfdcuat2.sandbox.lightning.force.com/" };
        const DetUserIdList = ["0051T00000BFwVuQAP", "0051T00000ZFASddPA"];
        getDetUserIdList.mockResolvedValue(DetUserIdList);
    });
    it("test trackCRMUserEvent when fetch success", async () => {
        global.fetch = jest.fn(() => Promise.resolve("success"));
        trackCRMUserEvent({
            category: "test category",
            action: "test action",
            name: "eventName"
        });
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(1);
        expect(global.fetch.mock.calls[0][0]).toStrictEqual("https://thoughtworks.innocraft.cloud/piwik.php?rec=1&idsite=78&uid=2021802797&e_c=test category&e_a=test action&e_n=eventName");
    });

    it("test trackCRMUserEvent not fetch when user is Det Member", async () => {
        const DetUserIdList = ["0051T00000ZFwAwWED", "0051T00000ZFASddPA"];
        getDetUserIdList.mockResolvedValue(DetUserIdList);
        global.fetch = jest.fn(() => Promise.resolve("success"));
        trackCRMUserEvent({
            category: "test category",
            action: "test action",
            name: "eventName"
        });
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(0);
    });
    it("test trackCRMUserEvent fetch success when get Det MemberList failed", async () => {
        getDetUserIdList.mockRejectedValue("get failed");
        global.fetch = jest.fn(() => Promise.resolve("success"));
        trackCRMUserEvent({
            category: "test category",
            action: "test action",
            name: "eventName"
        });
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(1);
        expect(global.fetch.mock.calls[0][0]).toStrictEqual("https://thoughtworks.innocraft.cloud/piwik.php?rec=1&idsite=78&uid=2021802797&e_c=test category&e_a=test action&e_n=eventName");
    });

    it("test trackCRMUserClick not fetch when user is Det Member", async () => {
        const DetUserIdList = ["0051T00000ZFwAwWED", "0051T00000ZFASddPA"];
        getDetUserIdList.mockResolvedValue(DetUserIdList);
        global.fetch = jest.fn(() => Promise.resolve("success"));
        trackCRMUserClick("test category", "test name");
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(0);
    });
    it("test trackCRMUserEvent when fetch failed", async () => {
        global.console.error = jest.fn();
        global.fetch = jest.fn(() => Promise.reject("error"));
        trackCRMUserEvent({
            category: "test category",
            action: "test action",
            name: "eventName"
        });
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(1);
        expect(console.error).toHaveBeenCalled();
    });

    it("test trackCRMUserClick when fetch failed", async () => {
        global.console.error = jest.fn();
        global.fetch = jest.fn(() => Promise.reject("error"));
        trackCRMUserClick("test category", "test name");
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(1);
        expect(console.error).toHaveBeenCalled();
    });

    it("test trackCRMUserEvent in production when fetch success", async () => {
        global.fetch = jest.fn(() => Promise.resolve("success"));
        delete window.location;
        window.location = { origin: "https://thoughtworks." };
        trackCRMUserEvent({
            category: "test category",
            action: "test action",
            name: "eventName"
        });
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(1);
        expect(global.fetch.mock.calls[0][0]).toStrictEqual("https://thoughtworks.innocraft.cloud/piwik.php?rec=1&idsite=77&uid=2021802797&e_c=test category&e_a=test action&e_n=eventName");
    });

    it("test trackCRMUserClick when fetch success", async () => {
        global.fetch = jest.fn(() => Promise.resolve("success"));
        trackCRMUserClick("test category", "test name");
        await flushPromises();
        expect(global.fetch).toHaveBeenCalledTimes(1);
        expect(global.fetch.mock.calls[0][0]).toStrictEqual("https://thoughtworks.innocraft.cloud/piwik.php?rec=1&idsite=78&uid=2021802797&e_c=test category&e_a=Clicked&e_n=test%20name");
    });
});
