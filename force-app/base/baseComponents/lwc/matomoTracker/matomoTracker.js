// https://developer.matomo.org/api-reference/tracking-api
// export { trackCRMUserClick, trackCRMUserEvent };

import USER_ID from "@salesforce/user/Id";
import getDetUserIdList from "@salesforce/apex/MKTUserController.getDetUserIdList";

export function trackCRMUserClick(category, actionName) {
    trackCRMUserEvent({ category: category, action: "Clicked", name: actionName });
}

export function trackCRMUserEvent(event) {
    const isProdEnv = window.location.origin.startsWith("https://thoughtworks.");
    // CRM-Production site id is 77
    // CRM-UAT site id is 78
    const siteId = isProdEnv ? "77" : "78";
    if (!USER_ID) return;
    let shouldTrack = true;
    getDetUserIdList()
        .then((res) => {
            if (res && res.includes(USER_ID)) {
                shouldTrack = false;
            }
        })
        .catch((err) => {
            console.error(err);
        })
        .finally(() => {
            if (shouldTrack) {
                const uid = hashCode(USER_ID);

                const baseUrl = "https://thoughtworks.innocraft.cloud/piwik.php?rec=1";
                const calloutUrl = event.value
                    ? `${baseUrl}&idsite=${siteId}&uid=${uid}&e_c=${event.category}&e_a=${event.action}&e_n=${encodeURIComponent(event.name)}&e_v=${event.value}`
                    : `${baseUrl}&idsite=${siteId}&uid=${uid}&e_c=${event.category}&e_a=${event.action}&e_n=${encodeURIComponent(event.name)}`;

                fetch(calloutUrl).catch((err) => {
                    console.error(err);
                });
            }
        });
}

function hashCode(s) {
    let h = 0;
    for (let i = 0; i < s.length; i++) h = (Math.imul(31, h) + s.charCodeAt(i)) | 0;
    return h;
}
