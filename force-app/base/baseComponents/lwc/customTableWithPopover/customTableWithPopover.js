import { api, LightningElement } from "lwc";
import { deepCopyObj } from "c/utils";
import FORM_FACTOR from "@salesforce/client/formFactor";
import { debounce } from "c/utils";
const OUTSIDE_VIEWPOINT_OFFSET = -2000;
const ROOT_FONT_SIZE = 16;
const COMMON_POPOVER_TRIGGER_DURATION = 0;
// nubbin offset(1.5rem - 0.5rem) + half of nubbin + border 1px
const NUBBIN_START_END_OFFSET = 16 + 8 + 1;
const POPOVER_SHOW_POSITION = ["top", "left", "left bottom", "left top", "right", "right bottom", "right top", "top", "top left", "top right", "bottom", "bottom right", "bottom left"];
export default class CustomTableWithPopover extends LightningElement {
    @api
    popoverColumns = [];
    @api
    tableData;
    columnsWithPopovers = [];
    @api
    sortedBy;
    @api
    sortedOrder;
    @api
    keyField;
    @api
    hideCheckboxColumn;
    @api
    popoverClass;
    @api
    hidePopoverCloseButton = false;

    mouseEnterHotSpot = false;
    mouseEnterPopover = false;

    _popoverClass;
    popoverPositionConfig = [];

    fromWeb = FORM_FACTOR !== "Small";

    popoverConfig = { top: 0, left: OUTSIDE_VIEWPOINT_OFFSET };

    popoverTriggerPositionCache;

    dispatchHoverEventWithDelay = debounce(this.dispatchHoverEvent, 500);

    _cloumns = [];

    @api
    set columns(value) {
        this._cloumns = value;
        this.renderColumns();
    }

    get columns() {
        return this._cloumns;
    }

    get toolTipStyle() {
        return `top: ${this.popoverConfig.top}px;
                left: ${this.popoverConfig.left}px;`;
    }

    get showTip() {
        return this.mouseEnterHotSpot || this.mouseEnterPopover;
    }

    positionMapper = {
        left: {
            top: "start",
            bottom: "end"
        },
        right: {
            top: "start",
            bottom: "end"
        },
        top: {
            left: "start",
            right: "end"
        },
        bottom: {
            left: "start",
            right: "end"
        }
    };

    get popoverColumnPosition() {
        const popoverColumns = {};
        this.popoverColumns.forEach((item) => {
            let popoverPosition;
            if (item.nubbinPosition === "center") {
                popoverPosition = item.nubbinDirection;
            } else {
                popoverPosition = `${item.nubbinDirection} ${item.nubbinPosition}`;
            }
            popoverColumns[item.fieldName] = { popoverPosition: popoverPosition, popoverSize: item.popoverSize || "" };
        });
        return popoverColumns;
    }

    connectedCallback() {
        this.renderColumns();
    }

    renderColumns() {
        if (this.popoverColumns.length > 0) {
            this.columnsWithPopovers = deepCopyObj(this.columns);
            this.columnsWithPopovers.forEach((col) => {
                if (Object.keys(this.popoverColumnPosition).includes(col.fieldName)) {
                    col.typeAttributes = {
                        ...col.typeAttributes,
                        showPopover: (ev) => {
                            if (!this.fromWeb) return;
                            this.showPopover(ev, col.fieldName);
                        },
                        disappearPopover: () => {
                            this.disappearPopover();
                        }
                    };
                }
            });
        }
    }

    parseNubbinPosition(popoverColumn) {
        let popoverPosition = popoverColumn.popoverPosition;
        if (!POPOVER_SHOW_POSITION.includes(popoverColumn.popoverPosition)) {
            console.error(`popover not support show in ${this.popOverPosition}, will change to ${POPOVER_SHOW_POSITION[0]}`);
            popoverPosition = POPOVER_SHOW_POSITION[0];
        }
        const positions = popoverPosition.split(" ");
        this._popoverClass = `${this.popoverClass} popover-section slds-is-absolute slds-popover ${popoverColumn.popoverSize} slds-nubbin_${positions.join("-")}`;
        let nubbinPosition = this.positionMapper[positions[0]][positions[1]];
        if (!nubbinPosition) {
            nubbinPosition = "center";
        }
        this.popoverPositionConfig = [positions[0], nubbinPosition];
    }

    showPopover(event, fieldName) {
        const srcElm = event.target;
        const dataIndex = srcElm.dataset.index;
        const hiddenPopover = srcElm.dataset.hiddenPopover;
        if (hiddenPopover && JSON.parse(hiddenPopover)) {
            return;
        }
        this.popoverConfig = { top: 0, left: OUTSIDE_VIEWPOINT_OFFSET };
        this.mouseEnterHotSpot = true;
        this.parseNubbinPosition(this.popoverColumnPosition[fieldName]);

        const popoverContainerRect = srcElm.getBoundingClientRect();
        this.popoverTriggerPositionCache = {
            top: popoverContainerRect.top,
            left: popoverContainerRect.left,
            width: popoverContainerRect.width,
            height: popoverContainerRect.height
        };
        this.dispatchHoverEventWithDelay(dataIndex, fieldName);
        this.doShowPopover(srcElm);
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => {
            this.tryFocusPopover();
        }, 100);
    }

    dispatchHoverEvent(index, fieldName) {
        if (!this.showTip) return;
        this.dispatchEvent(new CustomEvent("showpopover", { detail: { index, fieldName } }));
    }

    doShowPopover(popoverContainer) {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        this.showPopoverTimer = setTimeout(() => {
            this.movePopover(popoverContainer);
        }, 500);
    }

    popoverShowComputeMap = {
        left: (basePoint, popoverTriggerElm) => {
            const distance = popoverTriggerElm.clientWidth + ROOT_FONT_SIZE / 2 + COMMON_POPOVER_TRIGGER_DURATION;
            return {
                left: basePoint.left + distance + 5
            };
        },
        right: (basePoint, _, popoverElm) => {
            const distance = popoverElm.clientWidth + ROOT_FONT_SIZE / 2 + COMMON_POPOVER_TRIGGER_DURATION;
            return {
                left: basePoint.left - distance - 5
            };
        },
        top: (basePoint, popoverTriggerElm) => {
            const distance = popoverTriggerElm.clientHeight + ROOT_FONT_SIZE / 2 + COMMON_POPOVER_TRIGGER_DURATION;
            return {
                top: basePoint.top + distance + 5
            };
        },
        bottom: (basePoint, _, popoverElm) => {
            const distance = popoverElm.clientHeight + ROOT_FONT_SIZE / 2 + COMMON_POPOVER_TRIGGER_DURATION;
            return {
                top: basePoint.top - distance - 5
            };
        }
    };

    popoverNubbinDistanceGetterNames = {
        left: {
            direction: "top",
            getter: "offsetHeight"
        },
        right: {
            direction: "top",
            getter: "offsetHeight"
        },
        top: {
            direction: "left",
            getter: "offsetWidth"
        },
        bottom: {
            direction: "left",
            getter: "offsetWidth"
        }
    };

    popoverNubbinAlignCompute = {
        start: (basePoint, direction, popoverElm, popoverTriggerElm, distanceGetter) => {
            const popoverElmDistance = NUBBIN_START_END_OFFSET;
            const popoverTriggerDistance = popoverTriggerElm[distanceGetter] / 2;
            const distance = popoverTriggerDistance - popoverElmDistance;
            return {
                [direction]: basePoint[direction] + distance
            };
        },
        center: (basePoint, direction, popoverElm, popoverTriggerElm, distanceGetter) => {
            const popoverElmDistance = popoverElm[distanceGetter] / 2;
            const popoverTriggerDistance = popoverTriggerElm[distanceGetter] / 2;
            const distance = popoverTriggerDistance - popoverElmDistance;
            return {
                [direction]: basePoint[direction] + distance
            };
        },
        end: (basePoint, direction, popoverElm, popoverTriggerElm, distanceGetter) => {
            const popoverElmDistance = popoverElm[distanceGetter];
            const popoverTriggerDistance = popoverTriggerElm[distanceGetter] / 2;
            const distance = popoverTriggerDistance - popoverElmDistance + NUBBIN_START_END_OFFSET;
            return {
                [direction]: basePoint[direction] + distance
            };
        }
    };

    movePopover(popoverTriggerElm) {
        const customTableElm = this.template.querySelector(".table-container");
        const customTableRect = customTableElm.getBoundingClientRect();
        const popoverElm = this.template.querySelector(".popover-section");
        const popoverTriggerRect = popoverTriggerElm.getBoundingClientRect();
        // trigger elm left top point
        const baseTop = popoverTriggerRect.top - customTableRect.top;
        const baseLeft = popoverTriggerRect.left - customTableRect.left;
        const basePoint = {
            left: baseLeft,
            top: baseTop
        };

        const popoverShowDirection = this.popoverPositionConfig[0];
        const popoverNubbinPosition = this.popoverPositionConfig[1];

        const popoverFromTriggerOffset = this.popoverShowComputeMap[popoverShowDirection](basePoint, popoverTriggerElm, popoverElm);
        const distanceGetter = this.popoverNubbinDistanceGetterNames[popoverShowDirection];
        const popoverNubbinOffset = this.popoverNubbinAlignCompute[popoverNubbinPosition](basePoint, distanceGetter.direction, popoverElm, popoverTriggerElm, distanceGetter.getter);

        this.popoverConfig = {
            ...popoverNubbinOffset,
            ...popoverFromTriggerOffset
        };
    }

    keepTooltipShow() {
        this.mouseEnterPopover = true;
    }

    disappearPopover() {
        this.mouseEnterPopover = false;
        this.mouseEnterHotSpot = false;
    }

    tryFocusPopover() {
        this.template.querySelector("lightning-button-icon").focus();
    }

    handleSort(event) {
        this.dispatchEvent(new CustomEvent("sort", { detail: event.detail }));
    }

    handleRowAction(event) {
        this.dispatchEvent(new CustomEvent("rowaction", { detail: event.detail }));
    }
}
