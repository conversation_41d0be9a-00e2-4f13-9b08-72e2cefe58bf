<template>
    <div class="table-container slds-is-relative">
        <c-custom-data-table
            class="slds-max-medium-table_stacked survey-response-table"
            data={tableData}
            onrowaction={handleRowAction}
            columns={columnsWithPopovers}
            key-field={keyField}
            sorted-by={sortedBy}
            sorted-direction={sortedOrder}
            onsort={handleSort}
            hide-checkbox-column
        >
        </c-custom-data-table>
        <section class={_popoverClass} if:true={showTip} style={toolTipStyle} onmouseenter={keepTooltipShow} onmouseleave={disappearPopover}>
            <template if:false={hidePopoverCloseButton}>
                <lightning-button-icon
                    icon-name="utility:close"
                    variant="bare"
                    class="slds-button slds-button_icon slds-button_icon-small slds-float_right slds-popover__close"
                    title="Close button"
                    aria-labelledby="popoverContentHeader popoverContentLine1 popoverContentLine2"
                    onclick={disappearPopover}
                >
                </lightning-button-icon>
            </template>
            <div class="slds-popover__body resp_popover_style">
                <div class="slds-media">
                    <div class="slds-media__body">
                        <slot name="popoverContent"></slot>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>
