import { createElement } from "lwc";
import CustomTableWithPopover from "c/customTableWithPopover";
import { flushPromises } from "c/utils";

const createComponent = (params = {}) => {
    const element = createElement("c-custom-table-with-popover", {
        is: CustomTableWithPopover
    });
    element.popoverColumns = [
        { fieldName: "link", nubbinDirection: "top", nubbinPosition: "left" },
        { fieldName: "score", nubbinDirection: "left", nubbinPosition: "center" }
    ];
    element.columns = [
        {
            label: "Contact Name",
            fieldName: "link",
            type: "iconWithContactName",
            typeAttributes: {
                displayName: { fieldName: "displayName" },
                link: { fieldName: "link" },
                displayIconClass: { fieldName: "displayIconClass" },
                displayIconName: { fieldName: "displayIconName" },
                displayIconSize: { fieldName: "displayIconSize" }
            },
            wrapText: true,
            initialWidth: 240,
            sortable: true
        },
        {
            label: "Score",
            fieldName: "score",
            type: "iconWithHoverPopover",
            wrapText: true,
            sortable: true,
            initialWidth: 240,
            cellAttributes: { class: { fieldName: "scoreContentColor" } },
            typeAttributes: {
                score: { fieldName: "score" },
                iconClass: { fieldName: "iconClass" },
                iconName: { fieldName: "iconName" },
                popoverConfig: { fieldName: "popoverConfig" },
                index: { fieldName: "index" },
                lastSurveyResp: { fieldName: "lastSurveyResp" },
                trend: { fieldName: "trend" }
            }
        },
        {
            label: "Comment",
            fieldName: "comment",
            type: "text",
            sortable: true,
            wrapText: true
        }
    ];

    element.tableData = [
        { campaignStatus: "Completed", contactId: "003f000001rmIOoAAM", contactName: "Xinyu Warning22" },
        {
            campaignStatus: "Completed",
            contactId: "003f000001o1lEmAAI",
            contactName: "3333333 33333",
            response: {
                Contact__c: "003f000001o1lEmAAI",
                NPS_Comment__c: "I like it very much",
                NPS_Score__c: "10",
                Id: "a9jf0000000062vAAA",
                Contact__r: { Name: "3333333 33333", Id: "003f000001o1lEmAAI" }
            }
        },
        {
            campaignStatus: "Completed",
            contactId: "003f000001o1lEmAAI1",
            contactName: "3333333 33333",
            response: {
                Contact__c: "003f000001o1lEmAAI1",
                NPS_Comment__c: "I like it very much",
                NPS_Score__c: "5",
                Id: "a9jf0000000062vAAA",
                Contact__r: { Name: "3333333 33333", Id: "003f000001o1lEmAAI1" }
            }
        }
    ];

    Object.assign(element, params);
    document.body.appendChild(element);

    return element;
};

describe("c-custom-table-with-popover", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("show popover when mouseenter", async () => {
        const element = createComponent();

        const mockShowPopoverHandler = jest.fn();
        element.addEventListener("showpopover", mockShowPopoverHandler);
        const params = {
            target: {
                dataset: { index: 0, type: "score" },
                getBoundingClientRect: () => ({ left: 30, top: 30, height: 50, width: 50 }),
                clientWidth: 40,
                clientHeight: 40,
                offsetHeight: 40,
                offsetWidth: 40
            }
        };
        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.columns[1].typeAttributes.showPopover(params);

        await new Promise((resolve) => {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                resolve();
            }, 500);
        });

        const tooltip = element.shadowRoot.querySelector(".popover-section");

        expect(mockShowPopoverHandler).toHaveBeenCalledTimes(1);
        expect(tooltip.style._values.left).toBe("83px");
        expect(tooltip.style._values.top).toBe("50px");
    });

    it("show popover when mouse enter to popover", async () => {
        const element = createComponent();

        const mockShowPopoverHandler = jest.fn();
        element.addEventListener("showpopover", mockShowPopoverHandler);
        const params = {
            target: {
                dataset: { index: 0, type: "score" },
                getBoundingClientRect: () => ({ left: 30, top: 30, height: 50, width: 50 }),
                clientWidth: 40,
                clientHeight: 40,
                offsetHeight: 40,
                offsetWidth: 40
            }
        };
        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.columns[1].typeAttributes.showPopover(params);

        await new Promise((resolve) => {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                resolve();
            }, 500);
        });

        let tooltip = element.shadowRoot.querySelector(".popover-section");
        expect(tooltip.style._values.left).toBeTruthy();

        customTable.columns[1].typeAttributes.disappearPopover();
        tooltip.dispatchEvent(
            new CustomEvent("onmouseenter", {
                detail: {}
            })
        );
        tooltip = element.shadowRoot.querySelector(".popover-section");
        await flushPromises();
        expect(tooltip).toBeTruthy();
    });

    it("show popover when column position not in standard position", async () => {
        const element = createComponent({
            popoverColumns: [{ fieldName: "link", nubbinDirection: "top", nubbinPosition: "top" }]
        });

        const mockShowPopoverHandler = jest.fn();
        element.addEventListener("showpopover", mockShowPopoverHandler);
        const params = {
            target: {
                dataset: { index: 0, type: "score" },
                getBoundingClientRect: () => ({ left: 30, top: 30, height: 50, width: 50 }),
                clientWidth: 40,
                clientHeight: 40,
                offsetHeight: 40,
                offsetWidth: 40
            }
        };
        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.columns[0].typeAttributes.showPopover(params);

        await new Promise((resolve) => {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                resolve();
            }, 500);
        });

        const tooltip = element.shadowRoot.querySelector(".popover-section");

        expect(mockShowPopoverHandler).toHaveBeenCalledTimes(1);
        expect(tooltip.style._values.left).toBe("50px");
        expect(tooltip.style._values.top).toBe("83px");
    });

    it("disappear popover when mouseleave popover", async () => {
        const element = createComponent();
        const mockShowPopoverHandler = jest.fn();
        element.addEventListener("showpopover", mockShowPopoverHandler);
        const params = {
            target: {
                dataset: { index: 0, type: "score" },
                getBoundingClientRect: () => ({ left: 30, top: 30, height: 50, width: 50 }),
                clientWidth: 40,
                clientHeight: 40,
                offsetHeight: 40,
                offsetWidth: 40
            }
        };

        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.columns[0].typeAttributes.showPopover(params);

        await new Promise((resolve) => {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                resolve();
            }, 500);
        });

        let tooltip = element.shadowRoot.querySelector(".popover-section");
        tooltip.dispatchEvent(
            new CustomEvent("mouseleave", {
                detail: {}
            })
        );

        await flushPromises();
        tooltip = element.shadowRoot.querySelector(".popover-section");
        expect(tooltip).toBeFalsy();
    });

    it("disappear popover when mouseleave hot spot", async () => {
        const element = createComponent();
        const mockShowPopoverHandler = jest.fn();
        element.addEventListener("showpopover", mockShowPopoverHandler);
        const params = {
            target: {
                dataset: { index: 0, type: "score" },
                getBoundingClientRect: () => ({ left: 30, top: 30, height: 50, width: 50 }),
                clientWidth: 40,
                clientHeight: 40,
                offsetHeight: 40,
                offsetWidth: 40
            }
        };

        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.columns[0].typeAttributes.showPopover(params);

        await new Promise((resolve) => {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                resolve();
            }, 500);
        });
        let tooltip = element.shadowRoot.querySelector(".popover-section");

        expect(tooltip).toBeTruthy();

        customTable.columns[0].typeAttributes.disappearPopover();

        await flushPromises();
        tooltip = element.shadowRoot.querySelector(".popover-section");
        expect(tooltip).toBeFalsy();
    });

    it("should dispatch sort event when handle sort", async () => {
        const element = createComponent();
        const mockSortHandler = jest.fn();
        element.addEventListener("sort", mockSortHandler);
        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.dispatchEvent(
            new CustomEvent("sort", {
                detail: {
                    fieldName: "link",
                    sortDirection: "desc"
                }
            })
        );
        expect(mockSortHandler).toHaveBeenCalledTimes(1);
    });
});
