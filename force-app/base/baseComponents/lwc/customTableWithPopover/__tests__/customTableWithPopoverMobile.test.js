import { createElement } from "lwc";
import CustomTableWithPopover from "c/customTableWithPopover";

jest.mock(
    "@salesforce/client/formFactor",
    () => {
        return { default: "Small" };
    },
    { virtual: true }
);

const createComponent = (params = {}) => {
    const element = createElement("c-custom-table-with-popover", {
        is: CustomTableWithPopover
    });
    element.popoverColumns = [
        { fieldName: "link", nubbinDirection: "top", nubbinPosition: "center" },
        { fieldName: "score", nubbinDirection: "left", nubbinPosition: "center" }
    ];
    element.columns = [
        {
            label: "Contact Name",
            fieldName: "link",
            type: "iconWithContactName",
            typeAttributes: {
                displayName: { fieldName: "displayName" },
                link: { fieldName: "link" },
                displayIconClass: { fieldName: "displayIconClass" },
                displayIconName: { fieldName: "displayIconName" },
                displayIconSize: { fieldName: "displayIconSize" }
            },
            wrapText: true,
            initialWidth: 240,
            sortable: true
        },
        {
            label: "Score",
            fieldName: "score",
            type: "iconWithHoverPopover",
            wrapText: true,
            sortable: true,
            initialWidth: 240,
            cellAttributes: { class: { fieldName: "scoreContentColor" } },
            typeAttributes: {
                score: { fieldName: "score" },
                iconClass: { fieldName: "iconClass" },
                iconName: { fieldName: "iconName" },
                popoverConfig: { fieldName: "popoverConfig" },
                index: { fieldName: "index" },
                lastSurveyResp: { fieldName: "lastSurveyResp" },
                trend: { fieldName: "trend" }
            }
        },
        {
            label: "Comment",
            fieldName: "comment",
            type: "text",
            sortable: true,
            wrapText: true
        }
    ];

    element.tableData = [
        { campaignStatus: "Completed", contactId: "003f000001rmIOoAAM", contactName: "Xinyu Warning22" },
        {
            campaignStatus: "Completed",
            contactId: "003f000001o1lEmAAI",
            contactName: "3333333 33333",
            response: {
                Contact__c: "003f000001o1lEmAAI",
                NPS_Comment__c: "I like it very much",
                NPS_Score__c: "10",
                Id: "a9jf0000000062vAAA",
                Contact__r: { Name: "3333333 33333", Id: "003f000001o1lEmAAI" }
            }
        },
        {
            campaignStatus: "Completed",
            contactId: "003f000001o1lEmAAI1",
            contactName: "3333333 33333",
            response: {
                Contact__c: "003f000001o1lEmAAI1",
                NPS_Comment__c: "I like it very much",
                NPS_Score__c: "5",
                Id: "a9jf0000000062vAAA",
                Contact__r: { Name: "3333333 33333", Id: "003f000001o1lEmAAI1" }
            }
        }
    ];

    Object.assign(element, params);
    document.body.appendChild(element);

    return element;
};

describe("c-custom-table-with-popover", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("disappear popover when mouseenter in mobile", async () => {
        const element = createComponent();

        const mockShowPopoverHandler = jest.fn();
        element.addEventListener("showpopover", mockShowPopoverHandler);
        const params = {
            target: {
                dataset: { index: 0, type: "score" },
                getBoundingClientRect: () => ({ left: 30, top: 30, height: 50, width: 50 }),
                clientWidth: 40,
                clientHeight: 40,
                offsetHeight: 40,
                offsetWidth: 40
            }
        };
        let customTable = element.shadowRoot.querySelector("c-custom-data-table");
        customTable.columns[0].typeAttributes.showPopover(params);
        await new Promise((resolve) => {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                resolve();
            }, 500);
        });

        const tooltip = element.shadowRoot.querySelector(".popover-section");
        expect(tooltip).toBeFalsy();
    });
});
