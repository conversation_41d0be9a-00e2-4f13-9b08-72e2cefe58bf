import { LightningElement, api } from "lwc";
import { default as searchResultTemplate } from "./searchableComboboxItem.html";
import { default as actionTemplate } from "./searchableComboboxActionItem.html";
export default class SearchableComboboxItem extends LightningElement {
    @api item;
    @api type = "searchResult";
    @api highlightWord;

    render() {
        switch (this.type) {
            case "action":
                return actionTemplate;
            default:
                return searchResultTemplate;
        }
    }

    get computedItemClass() {
        return this.item.disable ? "slds-media slds-media_center item_disable" : "slds-media slds-media_center";
    }

    connectedCallback() {
        this.setAttribute("aria-selected", "false");
    }

    @api
    highlight() {
        this.toggleHighlight(true);
    }

    @api
    removeHighlight() {
        this.toggleHighlight(false);
    }

    toggleHighlight(highlighted) {
        this.setAttribute("aria-selected", highlighted ? "true" : "false");
        this.classList.toggle("slds-has-focus", highlighted);
    }
}
