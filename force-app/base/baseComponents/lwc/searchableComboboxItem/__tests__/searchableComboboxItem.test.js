import { createElement } from "lwc";
import Element from "c/searchableComboboxItem";

const createComponent = (params = {}) => {
    const element = createElement("c-searchable-combobox-item", {
        is: Element
    });

    Object.assign(element, params);
    document.body.appendChild(element);
    return element;
};

describe("c-searchable-combobox-item", () => {
    it("renders card", () => {
        const element = createComponent({
            item: {
                Id: "002",
                text: "Item ExEmple",
                subText: "Item example info",
                disable: false,
                iconName: "standard:contact",
                iconAlternativeText: "contact icon"
            },
            highlightWord: "Em"
        });
        expect(element).toMatchSnapshot();
    });

    it("renders disable card", () => {
        const element = createComponent({
            item: {
                text: "Item Example",
                subText: "Item example info",
                disable: true,
                iconName: "standard:contact",
                iconAlternativeText: "contact icon"
            },
            highlightWord: "Ex"
        });

        expect(element).toMatchSnapshot();
    });
});
