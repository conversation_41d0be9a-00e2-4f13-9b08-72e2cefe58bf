// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`c-searchable-combobox-item renders card 1`] = `
<c-searchable-combobox-item
  __lwc_scope_token__=""
  aria-selected="false"
>
  #shadow-root(open)
    <div
      __lwc_scope_token__=""
      class="slds-media slds-media_center"
    >
      <span
        __lwc_scope_token__=""
        class="slds-media__figure slds-listbox__option-icon"
      >
        <lightning-icon
          __lwc_scope_token__=""
        >
          #shadow-root(open)
        </lightning-icon>
      </span>
      <span
        __lwc_scope_token__=""
        class="slds-media__body"
      >
        <span
          __lwc_scope_token__=""
          class="slds-listbox__option-text_entity"
        >
          <c-highlight-text
            __lwc_scope_token__=""
            class="slds-truncate"
            title="Item ExEmple"
          >
            #shadow-root(open)
              <lightning-formatted-rich-text>
                #shadow-root(open)
              </lightning-formatted-rich-text>
          </c-highlight-text>
        </span>
        <span
          __lwc_scope_token__=""
          class="slds-listbox__option-meta"
        >
          <c-highlight-text
            __lwc_scope_token__=""
            class="slds-truncate"
            title="Item example info"
          >
            #shadow-root(open)
              <lightning-formatted-rich-text>
                #shadow-root(open)
              </lightning-formatted-rich-text>
          </c-highlight-text>
        </span>
      </span>
    </div>
</c-searchable-combobox-item>
`;

exports[`c-searchable-combobox-item renders disable card 1`] = `
<c-searchable-combobox-item
  __lwc_scope_token__=""
  aria-selected="false"
>
  #shadow-root(open)
    <div
      __lwc_scope_token__=""
      class="slds-media slds-media_center item_disable"
    >
      <span
        __lwc_scope_token__=""
        class="slds-media__figure slds-listbox__option-icon"
      >
        <lightning-icon
          __lwc_scope_token__=""
        >
          #shadow-root(open)
        </lightning-icon>
      </span>
      <span
        __lwc_scope_token__=""
        class="slds-media__body"
      >
        <span
          __lwc_scope_token__=""
          class="slds-listbox__option-text_entity"
        >
          <c-highlight-text
            __lwc_scope_token__=""
            class="slds-truncate"
            title="Item Example"
          >
            #shadow-root(open)
              <lightning-formatted-rich-text>
                #shadow-root(open)
              </lightning-formatted-rich-text>
          </c-highlight-text>
        </span>
        <span
          __lwc_scope_token__=""
          class="slds-listbox__option-meta"
        >
          <c-highlight-text
            __lwc_scope_token__=""
            class="slds-truncate"
            title="Item example info"
          >
            #shadow-root(open)
              <lightning-formatted-rich-text>
                #shadow-root(open)
              </lightning-formatted-rich-text>
          </c-highlight-text>
        </span>
      </span>
    </div>
</c-searchable-combobox-item>
`;
