import LightningModal from "lightning/modal";
import { api } from "lwc";

export default class UpdateFieldsModal extends LightningModal {
    @api
    modalLabel;

    @api
    recordId;

    @api
    objectApiName;

    @api
    fieldApiNames;

    isLoading = false;

    saveInProgress = false;

    get disableString() {
        return this.saveInProgress ? "disabled" : "";
    }

    handleClickCancel() {
        this.close({
            type: "cancel"
        });
    }

    handleClickSave(event) {
        this.isLoading = true;
        this.saveInProgress = true;
        this.disableClose = true;
        event.preventDefault();
        const fields = event.detail.fields;
        this.template.querySelector("lightning-record-edit-form").submit(fields);
    }

    handleSuccess() {
        this.isLoading = false;
        this.saveInProgress = false;
        this.disableClose = false;
        this.close({
            type: "success"
        });
    }

    handleLoad() {
        this.isLoading = false;
    }

    handleError(event) {
        console.error(event.detail);
        this.isLoading = false;
        this.saveInProgress = false;
        this.disableClose = false;
    }
}
