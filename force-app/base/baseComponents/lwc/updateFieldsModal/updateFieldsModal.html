<template>
    <lightning-modal-header label={modalLabel}></lightning-modal-header>
    <lightning-modal-body>
        <lightning-record-edit-form record-id={recordId} object-api-name={objectApiName} onsuccess={handleSuccess} onerror={handleError} onload={handleLoad}>
            <lightning-messages> </lightning-messages>
            <template for:each={fieldApiNames} for:item="fieldApiName">
                <lightning-input-field key={fieldApiName} field-name={fieldApiName}></lightning-input-field>
            </template>
        </lightning-record-edit-form>
    </lightning-modal-body>
    <lightning-modal-footer>
        <button class="slds-button slds-button_neutral cancel-button" onclick={handleClickCancel} disabled={disableString}>Cancel</button>
        <button class="slds-button slds-button_brand save-button" onclick={handleClickSave} disabled={disableString}>Save</button>
    </lightning-modal-footer>
    <c-tw-spinner loading={isLoading} type="modal"></c-tw-spinner>
</template>
