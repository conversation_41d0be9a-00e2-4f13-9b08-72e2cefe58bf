import { createElement } from "lwc";
import UpdateFieldsModal from "c/updateFieldsModal";
import { flushPromises } from "c/utils";

describe("c-update-fields-modal", () => {
    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    function createUpdateFieldsModal(props = {}) {
        const element = createElement("c-update-fields-modal", {
            is: UpdateFieldsModal
        });
        Object.assign(element, props);
        document.body.appendChild(element);
        return element;
    }

    it("renders lightning-modal-header with correct label", () => {
        // Given
        const element = createUpdateFieldsModal({ modalLabel: "Test Label" });

        // When
        const header = element.shadowRoot.querySelector("lightning-modal-header");

        // Then
        expect(header.label).toBe("Test Label");
    });

    it("renders lightning-record-edit-form with correct attributes", () => {
        // Given
        const props = {
            recordId: "001xx000003NGp0AAG",
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);

        // When
        const form = element.shadowRoot.querySelector("lightning-record-edit-form");

        // Then
        expect(form.recordId).toBe(props.recordId);
        expect(form.objectApiName).toBe(props.objectApiName);
    });

    it("renders lightning-input-field elements for each field", () => {
        // Given
        const props = {
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);

        // When
        const fields = element.shadowRoot.querySelectorAll("lightning-input-field");

        // Then
        expect(fields.length).toBe(props.fieldApiNames.length);
        props.fieldApiNames.forEach((fieldName, index) => {
            expect(fields[index].fieldName).toBe(fieldName);
        });
    });

    it("should show loading spinner until lightning-record-edit-form finishes loading", async () => {
        // Given
        const props = {
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);
        await flushPromises();

        // When
        const spinner = element.shadowRoot.querySelector("c-tw-spinner");
        expect(spinner.loading).toBeFalsy();
        const form = element.shadowRoot.querySelector("lightning-record-edit-form");
        form.dispatchEvent(new CustomEvent("load"));
        await flushPromises();

        // Then
        expect(spinner.loading).toBeFalsy();
    });

    it("sets isLoading to true and disable button when handleClickSave is called", async () => {
        // Given
        const props = {
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);
        const spinner = element.shadowRoot.querySelector("c-tw-spinner");
        await flushPromises();
        const form = element.shadowRoot.querySelector("lightning-record-edit-form");
        form.dispatchEvent(new CustomEvent("load"));
        await flushPromises();
        // When
        const saveBtn = element.shadowRoot.querySelector(".save-button");
        const cancelBtn = element.shadowRoot.querySelector(".cancel-button");
        expect(spinner.loading).toBeFalsy();
        expect(saveBtn.disabled).toBeFalsy();
        expect(cancelBtn.disabled).toBeFalsy();
        saveBtn.click();
        await flushPromises();

        // Then
        expect(spinner.loading).toBeTruthy();
        expect(saveBtn.disabled).toBeTruthy();
        expect(cancelBtn.disabled).toBeTruthy();
    });

    it("sets isLoading to false and closes with success on handleSuccess", async () => {
        // Given
        const props = {
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);
        await flushPromises();
        const form = element.shadowRoot.querySelector("lightning-record-edit-form");
        form.dispatchEvent(new CustomEvent("load"));
        await flushPromises();

        // When
        const saveBtn = element.shadowRoot.querySelector(".save-button");
        saveBtn.click();
        await flushPromises();
        form.dispatchEvent(new CustomEvent("success"));
        await flushPromises();
        const spinner = element.shadowRoot.querySelector("c-tw-spinner");

        // Then
        expect(spinner.loading).toBeFalsy();
        expect(element.closeValue).toEqual({ type: "success" });
    });

    it("should close when click close button", async () => {
        // Given
        const props = {
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);
        await flushPromises();

        // When
        const cancelBtn = element.shadowRoot.querySelector(".cancel-button");
        cancelBtn.click();
        await flushPromises();

        // Then
        expect(element.closeValue).toEqual({ type: "cancel" });
    });

    it("sets isLoading to false and make button available to click and logs error on handleError", async () => {
        // Given
        global.console.error = jest.fn();
        const props = {
            objectApiName: "Account",
            fieldApiNames: ["Name", "Phone"]
        };
        const element = createUpdateFieldsModal(props);
        await flushPromises();
        const form = element.shadowRoot.querySelector("lightning-record-edit-form");
        form.dispatchEvent(new CustomEvent("load"));
        await flushPromises();

        // When
        const spinner = element.shadowRoot.querySelector("c-tw-spinner");
        const saveBtn = element.shadowRoot.querySelector(".save-button");
        const cancelBtn = element.shadowRoot.querySelector(".cancel-button");
        saveBtn.click();
        await flushPromises();
        expect(spinner.loading).toBeTruthy();
        expect(saveBtn.disabled).toBeTruthy();
        expect(cancelBtn.disabled).toBeTruthy();

        const errorDetail = { message: "Test error" };
        form.dispatchEvent(new CustomEvent("error", { detail: errorDetail }));
        await flushPromises();

        // Then
        expect(spinner.loading).toBeFalsy();
        expect(saveBtn.disabled).toBeFalsy();
        expect(cancelBtn.disabled).toBeFalsy();
        expect(global.console.error).toHaveBeenCalledWith(errorDetail);
    });
});
