/**
 * Created by zijie.wang on 2023/5/30.
 */
import { createElement } from "lwc";
import MatomoPageTracker from "c/matomoPageTracker";
import { flushPromises } from "c/utils";
import { trackCRMUserEvent } from "c/matomoTracker";

describe("c-opportunity-progress-indicator", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    it("should render stage path", async () => {
        // Arrange
        const element = createElement("c-matomo-page-tracker", {
            is: MatomoPageTracker
        });
        element.recordId = "0066300000MKn9TAAT";
        element.matomoCategory = "matomo category";
        element.matomoAction = "matomo action";
        element.matomoName = "matomo name";
        // Act
        document.body.appendChild(element);
        await flushPromises();
        // Assert
        expect(trackCRMUserEvent).toHaveBeenCalledTimes(1);
        expect(trackCRMUserEvent.mock.calls[0][0]).toStrictEqual({
            category: "matomo category",
            action: "matomo action",
            name: "matomo name"
        });
    });
});
