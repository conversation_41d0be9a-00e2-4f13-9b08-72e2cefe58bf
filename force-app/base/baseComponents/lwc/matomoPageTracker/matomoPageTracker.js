/**
 * Created by zijie.wang on 2023/5/25.
 */

import { api, LightningElement } from "lwc";
import { trackCRMUserEvent } from "c/matomoTracker";

export default class MatomoPageTracker extends LightningElement {
    @api
    recordId;
    @api
    matomoCategory;
    @api
    matomoAction;
    @api
    matomoName;

    connectedCallback() {
        trackCRMUserEvent({
            category: this.matomoCategory,
            action: this.matomoAction,
            name: this.matomoName
        });
    }
}
