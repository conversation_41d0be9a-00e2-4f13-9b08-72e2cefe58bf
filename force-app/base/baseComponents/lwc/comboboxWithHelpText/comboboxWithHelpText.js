import { api, track, LightningElement } from "lwc";

export default class ComboboxWithHelpText extends LightningElement {
    @track showDropDownList = false;
    @track isMouseOnDropDownList = false;
    @track resultList = [];
    @track selectedOption = {};
    @api options = [];
    @api disabled;

    connectedCallback() {
        this.selectedOption = this.options[0];
    }

    handleComboboxClick(event) {
        event.target.closest(".combobox-button").focus();
        this.showDropDownList = !this.showDropDownList;
        this.isMouseOnDropDownList = false;
    }

    handleOutDropDownList() {
        this.isMouseOnDropDownList = false;
    }

    handleOverDropDownList() {
        this.isMouseOnDropDownList = true;
    }

    handleBlur() {
        if (!this.isMouseOnDropDownList) {
            this.showDropDownList = false;
        }
    }

    handleOptionClick(event) {
        this.showDropDownList = !this.showDropDownList;
        const optionElement = event.target.closest(".slds-listbox__option");
        const selectedOptionIndex = optionElement.getAttribute("option-index");
        this.selectedOption = this.options[selectedOptionIndex];
        this.dispatchEvent(new CustomEvent("select", { detail: this.options[selectedOptionIndex].value }));
    }

    get buttonText() {
        return `${this.disabled ? "slds-text-color_disabled button-text" : "slds-text-color_default button-text"}`;
    }
}
