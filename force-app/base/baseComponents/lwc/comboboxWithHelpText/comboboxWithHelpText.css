.combobox-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 2px 12px;
    height: 24px;
    background-color: white;
    border-color: #dddbda;
    border-style: solid;
    border-width: 1px;
    border-radius: 4px;
}

.button-help-text {
    list-style-type: "- ";
    list-style-position: inside;
    position: relative;
    top: 2px;
}

.down-icon {
    position: relative;
    margin-left: 8px;
}

.dropdown {
    font-size: 12px;
    padding: 0;
    white-space: nowrap;
    /* reset the transform and left of slds-dropdown to align dropdown and button to the left */
    transform: translateX(0);
    left: 0;
    z-index: auto;
}

.help-text {
    list-style-type: "- ";
    list-style-position: inside;
    position: relative;
    top: -2px;
}

.button-text {
    font-size: 12px;
}

.dropdown ul {
    padding: 4px 0 8px 0;
}

.dropdown li div {
    padding: 4px 32px 4px 12px;
}