<template>
    <div class="slds-combobox_container" style="display: block">
        <button
            role="combobox"
            class="slds-combobox__input combobox-button"
            aria-haspopup="listbox"
            aria-expanded={showDropDownList}
            onclick={handleComboboxClick}
            onblur={handleBlur}
            disabled={disabled}
        >
            <span class={buttonText}>{selectedOption.label}</span>
            <template if:true={selectedOption.helpText}>
                <lightning-helptext class="button-help-text" content={selectedOption.helpText}></lightning-helptext>
            </template>
            <lightning-icon class="down-icon" icon-name="utility:down" size="xx-small"></lightning-icon>
        </button>

        <template if:true={showDropDownList}>
            <div class="slds-listbox slds-listbox_vertical slds-dropdown dropdown" role="listbox" onclick={handleOptionClick} onmouseout={handleOutDropDownList} onmouseover={handleOverDropDownList}>
                <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                    <template for:each={options} for:item="option" for:index="optionIdx">
                        <li role="presentation" class="slds-listbox__item" key={option.label}>
                            <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small" role="option" option-index={optionIdx}>
                                {option.label}
                                <template if:true={option.helpText}>
                                    <lightning-helptext class="help-text" content={option.helpText}></lightning-helptext>
                                </template>
                            </div>
                        </li>
                    </template>
                </ul>
            </div>
        </template>
    </div>
</template>
