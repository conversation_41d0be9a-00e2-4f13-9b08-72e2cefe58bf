import { createElement } from "lwc";
import MessageDisplay from "c/messageDisplay";

describe("c-message-display", () => {
    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("displays correct icon and class for INFO type and renders passed message", () => {
        const MESSAGE = "Test Info Message";

        const element = createElement("c-message-display", {
            is: MessageDisplay
        });
        element.type = "info";
        element.message = MESSAGE;
        document.body.appendChild(element);

        const columns = element.shadowRoot.querySelectorAll(".slds-col");

        // Check for the presence of the icon in the first column
        const iconElement = columns[0].querySelector("lightning-icon");
        expect(iconElement).toBeTruthy();
        expect(iconElement.iconName).toBe("utility:info");
        expect(iconElement.classList.contains("info-icon")).toBe(true);

        // Check for the presence of the message in the second column
        expect(columns[1].textContent).toBe(MESSAGE);
    });

    it("displays correct icon and class for WARNING type and renders passed message", () => {
        const MESSAGE = "Test Warning Message";

        const element = createElement("c-message-display", {
            is: MessageDisplay
        });
        element.type = "warning";
        element.message = MESSAGE;
        document.body.appendChild(element);

        const columns = element.shadowRoot.querySelectorAll(".slds-col");

        // Check for the presence of the icon in the first column
        const iconElement = columns[0].querySelector("lightning-icon");
        expect(iconElement).toBeTruthy();
        expect(iconElement.iconName).toBe("utility:warning");
        expect(iconElement.classList.contains("warning-icon")).toBe(true);

        // Check for the presence of the message in the second column
        expect(columns[1].textContent).toBe(MESSAGE);
    });
});
