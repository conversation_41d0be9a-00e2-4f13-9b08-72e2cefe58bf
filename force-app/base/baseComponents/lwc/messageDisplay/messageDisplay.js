import { LightningElement, api } from "lwc";

const MESSAGE_TYPES = {
    INFO: "info",
    WARNING: "warning"
};

const TYPE_ATTRIBUTES = {
    [MESSAGE_TYPES.INFO]: {
        containerClass: "info-background",
        iconName: "utility:info",
        iconClass: "info-icon"
    },
    [MESSAGE_TYPES.WARNING]: {
        containerClass: "",
        iconName: "utility:warning",
        iconClass: "warning-icon"
    }
};

export default class MessageDisplay extends LightningElement {
    @api message = "";
    @api type = MESSAGE_TYPES.INFO;

    get containerClass() {
        const BASE_CLASS = "slds-grid slds-p-around_small";
        const typeClass = TYPE_ATTRIBUTES[this.type]?.containerClass || "";
        return `${BASE_CLASS} ${typeClass}`;
    }

    get iconName() {
        return TYPE_ATTRIBUTES[this.type]?.iconName || "utility:info";
    }

    get iconClass() {
        return TYPE_ATTRIBUTES[this.type]?.iconClass || "info-icon";
    }
}
