<template>
    <div class="slds-form-element search_combo_box">
        <label class="slds-form-element__label" if:true={labelName}><span if:true={required} class="form-require">* </span>{labelName}</label>
        <lightning-helptext if:true={helpTextContent} icon-name="utility:info" class="helptext-tooltip" alternative-text={helpTextContent} content={helpTextContent}> </lightning-helptext>
        <div class="slds-form-element__control">
            <div class="slds-combobox_container"></div>
            <div class={computedDropdownTriggerClass}>
                <div role="none" class={computedFormElementClass}>
                    <template if:true={hasInputPill}>
                        <lightning-icon icon-name={inputIcon} alternative-text="contact icon" size="x-small" class="slds-icon_container slds-combobox__input-entity-icon"></lightning-icon>
                    </template>
                    <input
                        id="input"
                        type="text"
                        role="combobox"
                        aria-expanded={dropdownVisible}
                        aria-haspopup="listbox"
                        class={computedInputClass}
                        value={inputValue}
                        aria-owns="This Account,Other Account"
                        placeholder={computedPlaceholder}
                        maxlength="100"
                        onfocus={handleFocus}
                        oninput={handleInput}
                        onblur={handleBlur}
                        disabled={disabledInput}
                        readonly={_inputReadOnly}
                        onkeydown={handleInputKeyDown}
                    />
                    <div class="slds-input__icon-group slds-input__icon-group_right">
                        <template if:true={showSearchLoading}>
                            <div role="status" class="slds-spinner slds-spinner_brand slds-spinner_x-small slds-input__spinner">
                                <span class="slds-assistive-text">loading</span>
                                <div class="slds-spinner__dot-a"></div>
                                <div class="slds-spinner__dot-b"></div>
                            </div>
                        </template>
                        <lightning-icon
                            aria-hidden="true"
                            if:false={hasInputPill}
                            alternative-text="search text"
                            icon-name="utility:search"
                            size="x-small"
                            class="slds-input__icon slds-input__icon_right"
                        ></lightning-icon>
                        <button onclick={handleClear} if:true={hasInputPill} aria-label="clear button" class="slds-button slds-button_icon slds-input__icon slds-input__icon_right">
                            <lightning-icon alternative-text="clear the selection" icon-name="utility:close" size="xx-small"></lightning-icon>
                        </button>
                    </div>
                </div>
                <div if:true={invalid} class="form-require">{errorMsg}</div>
                <div if:true={isTagsReachToMaxNum} class="slds-grid slds-grid_vertical-align-top tag-limit-hint">
                    <lightning-icon icon-name="utility:info_alt" alternative-text="info" size="xx-small" class="hint-icon"></lightning-icon>
                    <p>{tagLimitHint}</p>
                </div>
                <template if:true={dropdownVisible}>
                    <div
                        id="dropdown-element"
                        data-dropdown-element
                        class={computedDropdownClass}
                        onmousedown={handleDropdownMouseDown}
                        onmouseup={handleDropdownMouseUp}
                        onmouseleave={handleDropdownMouseLeave}
                        onclick={handleOptionClick}
                    >
                        <div class="background_color_search_info" role="presentation" id="search_info">
                            <lightning-icon
                                alternative-text="search icon"
                                icon-name="utility:search"
                                size="xx-small"
                                class="slds-input__icon slds-input__icon_right slds-m-right_small"
                            ></lightning-icon>
                            {searchInfo}
                        </div>
                        <template if:true={hasNewRecordAction}>
                            <div class="slds-listbox__item new-record-sticky" role="presentation">
                                <c-searchable-combobox-item
                                    onclick={handleNewRecord}
                                    item={newRecordConfig}
                                    type="action"
                                    id={addRecordId}
                                    data-item-id="input-0"
                                    class="slds-listbox__option listbox__option_plain new-record-option"
                                    role="option"
                                ></c-searchable-combobox-item>
                            </div>
                        </template>
                        <div class={computedListBoxClass} role="listbox" id="select_listbox" aria-label={searchInfo}>
                            <template for:each={searchResultItems} for:item="item">
                                <div key={item.id} aria-label={item.label} id={item.id}>
                                    <template if:false={item.items}>
                                        <c-searchable-combobox-item
                                            item={item}
                                            data-item-id={item.indexId}
                                            id={item.Id}
                                            data-id={item.Id}
                                            highlight-word={inputValue}
                                            data-icon={item.iconName}
                                            data-value={item.text}
                                            data-disable={item.disable}
                                            class="slds-listbox__option listbox__option_plain"
                                            role="option"
                                        ></c-searchable-combobox-item>
                                    </template>
                                    <template if:true={item.items}>
                                        <div role="presentation" class="slds-listbox__item">
                                            <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small group_title" role="presentation">
                                                <h3 role="presentation" title={item.label}>{item.label}</h3>
                                            </div>
                                        </div>
                                        <ul role="presentation">
                                            <template for:each={item.items} for:item="groupItem">
                                                <li key={groupItem.Id} class="slds-listbox__item" role="presentation">
                                                    <c-searchable-combobox-item
                                                        item={groupItem}
                                                        data-item-id={groupItem.indexId}
                                                        id={groupItem.Id}
                                                        data-id={groupItem.Id}
                                                        highlight-word={inputValue}
                                                        data-icon={groupItem.iconName}
                                                        data-value={groupItem.text}
                                                        data-disable={groupItem.disable}
                                                        class="slds-listbox__option listbox__option_plain"
                                                        role="option"
                                                    ></c-searchable-combobox-item>
                                                </li>
                                            </template>
                                        </ul>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
                <ul if:true={showPillContainer} class="slds-listbox slds-is-relative slds-p-top_xxx-small slds-grid slds-wrap pills-gap">
                    <template for:each={pills} for:item="item">
                        <li key={item.id}>
                            <lightning-pill label={item.label} href={item.href} onclick={handlePillClick} name={item.label} data-id={item.id} onremove={handleRemove}></lightning-pill>
                        </li>
                    </template>
                </ul>
            </div>
        </div>
    </div>
</template>
