@import "c/cssLibrary";

:host {
    --new-record-margin: 30px;
}

.dropdown_fluid {
    min-width: var(--lwc-sizeXSmall);
    max-width: 100%;
    min-height: 40px;
    height: max-content;
    width: 100%;
    max-height: 300px;
    padding-top: 0;
}
.icon_clear {
    width: var(--lwc-spacingXLarge);
    height: var(--lwc-spacingMedium);
    cursor: pointer;
}
.background_color_search_info {
    background-color: #ffffff;
    height: 36px;
    line-height: 36px;
    position: sticky;
    top: 0;
    padding: 0 var(--lwc-spacingSmall);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.group_title {
    font-weight: bold;
}
.listbox__option_plain {
    padding: var(--lwc-spacingXxSmall) var(--lwc-spacingSmall);
    display: block;
}

.drop-up {
    bottom: 100%;
    top: auto;
}

.drop-down {
    top: 30px;
}

.form-require {
    color: var(--slds-g-color-palette-red-40, #ba0517);
    font-size: var(--fontSize2);
}

input.input-require {
    border-color: var(--slds-g-color-palette-red-40, #ba0517);
    border-width: revert;
}

.slds-input {
    padding-right: var(--lwc-spacingLarge);
}

.new-record-sticky {
    position: sticky;
    top: calc(100% - var(--new-record-margin, 30px));
    margin-bottom: calc(-1 * var(--new-record-margin, 30px));
    background: var(--lwc-colorBackgroundAlt, #fff);
}

.new-record-sticky:hover {
    background-color: var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundRowHover, rgb(243, 243, 243)));
}

.slds-has-focus {
    background-color: var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundRowHover, rgb(243, 243, 243)));
}

.new-record-option {
    padding: var(--lwc-spacingXSmall, 0.5rem) var(--lwc-spacingSmall, 0.75rem);
}

.search-result-area {
    padding-bottom: calc(var(--new-record-margin, 30px) - 5px);
}
.fixed-dropdown {
    position: fixed;
}

.pills-gap {
    gap: var(--lwc-spacingXxSmall);
}

.tag-limit-hint {
    padding-top: 2px;
    padding-bottom: var(--lwc-spacingXSmall);
}

.hint-icon {
    margin-right: 2px;
    line-height: 1.5;
}
