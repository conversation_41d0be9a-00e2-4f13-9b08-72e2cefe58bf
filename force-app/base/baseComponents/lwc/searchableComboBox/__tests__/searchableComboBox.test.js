import { createElement } from "lwc";
import SearchableComboBox from "c/searchableComboBox";
import searchResult from "./data/searchResult.json";
import { handleKeyDownOnInput } from "../keyboard.js";
import { flushPromises } from "c/utils";

jest.mock("../keyboard", () => {
    return {
        handleKeyDownOnInput: jest.fn()
    };
});

describe("c-searchable-combo-box", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe("searchable combo box mode is default", () => {
        it("Show empty input", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.placeholder = "Search Contacts...";
            document.body.appendChild(element);

            await flushPromises();

            const label = element.shadowRoot.querySelector(".slds-form-element .slds-form-element__label");
            expect(label.textContent).toBe("Contacts");

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            expect(input.placeholder).toBe("Search Contacts...");

            const searchIcon = element.shadowRoot.querySelector(".slds-input__icon.slds-input__icon_right");
            expect(searchIcon.iconName).toBe("utility:search");
        });

        it("show default input value when searchable combo box have default value and mode is default", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.placeholder = "Search Contacts...";
            element.inputPill = { value: "test value", id: "001", iconName: "standard:contact" };
            document.body.appendChild(element);

            await flushPromises();

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            expect(input.value).toBe("test value");
            const searchIcon = element.shadowRoot.querySelector(".slds-combobox__input-entity-icon");
            expect(searchIcon.iconName).toBe("standard:contact");
            const clearIcon = element.shadowRoot.querySelector(".slds-button lightning-icon");
            expect(clearIcon).toBeTruthy();
        });

        it("Show loading when input two characters", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.value = "ch";
            input.dispatchEvent(new CustomEvent("input"));

            await flushPromises();

            const loadingIcon = element.shadowRoot.querySelector(".slds-input__icon.slds-input__icon_right");
            expect(loadingIcon.iconName).toBe("utility:search");
        });

        it("Show empty result when no search result found", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "ch";
            input.dispatchEvent(new CustomEvent("input"));
            element.searchResult = [];

            await flushPromises(500);

            const searchInfoBar = element.shadowRoot.querySelector(".background_color_search_info");
            expect(searchInfoBar.textContent).toBe(`We didn't find "${input.value}" amongst all contacts.`);

            const searchIcon = searchInfoBar.querySelector("lightning-icon");
            expect(searchIcon.iconName).toBe("utility:search");
        });

        it("should fixed Dropdown when isDropdownFixed equal true", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.isDropdownFixed = true;
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "ch";
            input.dispatchEvent(new CustomEvent("input"));
            element.searchResult = [];

            await flushPromises(500);

            const dropdown = element.shadowRoot.querySelector(".slds-dropdown");
            expect(dropdown.classList.contains("fixed-dropdown")).toBeTruthy();
        });

        it("Show customized search success info when search result found and searchInfoSuccessTemplate given", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Tags";
            element.searchInfoTemplates = {
                successTemplate: function (inputValue) {
                    return `"${inputValue}" in the tag name or tag description`;
                }
            };

            const searchText = jest.fn();
            element.addEventListener("searchtext", searchText);
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;
            await flushPromises(500);
            // assert
            const searchInfoBar = element.shadowRoot.querySelector(".background_color_search_info");
            expect(searchInfoBar.textContent).toBe(`"${input.value}" in the tag name or tag description`);
        });
        it("Show customized search fail info when no search result found and searchInfoFailTemplate given", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.searchInfoTemplates = {
                failTemplate: function (inputValue) {
                    return `We couldn't find "${inputValue}" as an open opportunity for this account.`;
                }
            };
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "ch";
            input.dispatchEvent(new CustomEvent("input"));
            element.searchResult = [];

            await flushPromises();

            const searchInfoBar = element.shadowRoot.querySelector(".background_color_search_info");
            expect(searchInfoBar.textContent).toBe(`We couldn't find "${input.value}" as an open opportunity for this account.`);

            const searchIcon = searchInfoBar.querySelector("lightning-icon");
            expect(searchIcon.iconName).toBe("utility:search");
        });

        it("Show result items when search has results", async () => {
            const searchText = jest.fn();
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);

            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;
            await flushPromises(500);

            const searchInfoBar = element.shadowRoot.querySelector(".background_color_search_info");
            expect(searchInfoBar.textContent).toBe(`"${input.value}" in contacts`);

            const resultGroups = element.shadowRoot.querySelectorAll(".slds-listbox.slds-listbox_vertical > div");
            expect(resultGroups.length).toBe(2);

            const thisAccountTitle = resultGroups[0].querySelector("h3");
            expect(thisAccountTitle.textContent).toBe(searchResult[0].label);
            const thisAccountLiItems = resultGroups[0].querySelectorAll("li");
            expect(thisAccountLiItems.length).toBe(searchResult[0].items.length);

            const otherAccountTitle = resultGroups[1].querySelector("h3");
            expect(otherAccountTitle.textContent).toBe(searchResult[1].label);
            const otherAccountLiItems = resultGroups[1].querySelectorAll("li");
            expect(otherAccountLiItems.length).toBe(searchResult[1].items.length);
        });

        it("should dispatch selectvalue event when user select one from search result", async () => {
            const searchText = jest.fn();
            const selectValue = jest.fn();
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);
            element.addEventListener("selectvalue", selectValue);
            document.body.appendChild(element);

            let input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;

            await flushPromises(500);

            const resultGroups = element.shadowRoot.querySelectorAll(".slds-listbox.slds-listbox_vertical ul");
            const firstResultItem = resultGroups[0].querySelector("c-searchable-combobox-item");
            firstResultItem.click();

            await flushPromises();

            expect(selectValue).toHaveBeenCalled();
            expect(selectValue.mock.calls[0][0].detail).toStrictEqual({ iconName: "standard:contact", id: "0033I00000TktewQAB", value: "bie biyun" });
        });

        it("Clear search result when click clear icon", async () => {
            const searchText = jest.fn();

            const handleClear = jest.fn();
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);
            element.addEventListener("selectvalue", handleClear);
            document.body.appendChild(element);

            let input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;

            await flushPromises(500);

            const resultGroups = element.shadowRoot.querySelectorAll(".slds-listbox.slds-listbox_vertical ul");
            const firstResultItem = resultGroups[0].querySelector("c-searchable-combobox-item");
            firstResultItem.click();

            await flushPromises();
            element.inputPill = {
                iconName: "standard:contact",
                id: "0033I00000TktewQAB",
                value: "bie biyun"
            };
            await flushPromises(1);

            const clearIcon = element.shadowRoot.querySelector(".slds-input__icon-group.slds-input__icon-group_right button");
            clearIcon.click();
            expect(handleClear).toHaveBeenCalledTimes(2);
            expect(handleClear.mock.calls[0][0].detail).toStrictEqual({
                iconName: "standard:contact",
                id: "0033I00000TktewQAB",
                value: "bie biyun"
            });
            expect(handleClear.mock.calls[1][0].detail).toStrictEqual({});
        });

        it("should show new record button when has new record config", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.newRecordConfig = {
                iconName: "contact",
                label: "new Contact"
            };
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "ch";
            input.dispatchEvent(new CustomEvent("input"));
            element.searchResult = [];
            await flushPromises();

            const newRecordAction = element.shadowRoot.querySelector(".new-record-option");
            expect(newRecordAction).toBeTruthy();
        });

        it("should emit new record event when click new record", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.newRecordConfig = {
                iconName: "contact",
                label: "new Contact"
            };
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "ch";
            input.dispatchEvent(new CustomEvent("input"));
            element.searchResult = [];

            const handleNewRecord = jest.fn();
            element.addEventListener("newrecord", handleNewRecord);

            await flushPromises(500);

            const newRecordAction = element.shadowRoot.querySelector(".new-record-option");
            newRecordAction.click();

            expect(handleNewRecord).toHaveBeenCalled();
        });

        it("should emit handleKeyDownOnInput event when handle keydown event when  no search result found", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.dispatchEvent(new CustomEvent("keydown"));

            await flushPromises();

            expect(handleKeyDownOnInput).toHaveBeenCalledTimes(1);

            expect(handleKeyDownOnInput.mock.calls[0][0].currentIndex).toBe(-1);
        });

        it("should emit handleKeyDownOnInput event and highlight the first option when handle keydown event when search has results", async () => {
            const searchText = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);

            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;
            await flushPromises();

            input.dispatchEvent(new CustomEvent("keydown"));

            await flushPromises(500);
            const dropdownKeyboardInterface = handleKeyDownOnInput.mock.calls[0][0];
            expect(handleKeyDownOnInput).toHaveBeenCalledTimes(1);

            expect(dropdownKeyboardInterface.dropdownInterface.getTotalOptions()).toBe(4);
            expect(dropdownKeyboardInterface.dropdownInterface.highlightOptionWithIndex).toBeTruthy();
            expect(dropdownKeyboardInterface.dropdownInterface.isDropdownVisible()).toBeTruthy();
            expect(dropdownKeyboardInterface.dropdownInterface.openDropdownIfNotEmpty).toBeTruthy();
            expect(dropdownKeyboardInterface.dropdownInterface.closeDropdown).toBeTruthy();
            expect(dropdownKeyboardInterface.dropdownInterface.getCurrentNewRecordActionIndex()).toBe(-1);
            expect(dropdownKeyboardInterface.currentIndex).toBe(1);
        });

        it("disappear dropdown and remove highlight when input blur and user handle mouseleave event", async () => {
            const searchText = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);

            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;
            await flushPromises(500);

            const resultGroups = element.shadowRoot.querySelectorAll(".slds-listbox.slds-listbox_vertical ul");
            const firstResultItem = resultGroups[0].querySelector("c-searchable-combobox-item");
            firstResultItem.removeHighlight = jest.fn();
            element.handleDropdownMouseDown({ button: 0 });
            input.dispatchEvent(new CustomEvent("blur"));
            input.dispatchEvent(new CustomEvent("keydown"));
            await flushPromises();
            let dropdown = element.shadowRoot.querySelector(".slds-dropdown");

            dropdown.dispatchEvent(new CustomEvent("mouseleave"));

            await flushPromises();
            dropdown = element.shadowRoot.querySelector(".slds-dropdown");

            expect(dropdown).toBeFalsy();
            expect(firstResultItem.removeHighlight).toHaveBeenCalled();
        });

        it("cancel input blur(show dropdown) when handle mousedown event", async () => {
            const searchText = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);

            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;
            await flushPromises(500);

            input.dispatchEvent(new CustomEvent("keydown"));
            await flushPromises();

            element.handleDropdownMouseDown({ button: 0 });
            input.dispatchEvent(new CustomEvent("blur"));

            await flushPromises();
            const dropdown = element.shadowRoot.querySelector(".slds-dropdown");
            expect(dropdown).toBeTruthy();
        });

        it("input blur(hidden dropdown) when handle mouseup event", async () => {
            const searchText = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);

            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;
            await flushPromises();

            input.dispatchEvent(new CustomEvent("keydown"));
            await flushPromises();
            let dropdown = element.shadowRoot.querySelector(".slds-dropdown");
            dropdown.dispatchEvent(new CustomEvent("mouseup"));
            input.dispatchEvent(new CustomEvent("blur"));

            await flushPromises();
            dropdown = element.shadowRoot.querySelector(".slds-dropdown");
            expect(dropdown).toBeFalsy();
        });

        it("show custom error msg when input value have error", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            document.body.appendChild(element);
            element.setCustomValidity("error");
            await flushPromises();

            let errorMsg = element.shadowRoot.querySelector(".form-require");
            expect(errorMsg.textContent).toBe("error");
        });

        it("show select error msg when input value have error", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            document.body.appendChild(element);
            await flushPromises();

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input"));
            element.checkValidity();

            await flushPromises();
            let errorMsg = element.shadowRoot.querySelector(".form-require");
            expect(errorMsg.textContent).toBe("Select an option from the picklist or remove the search term.");
        });

        it("show required error msg when input value is null", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.required = true;
            document.body.appendChild(element);
            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.dispatchEvent(new CustomEvent("blur"));
            await flushPromises();
            let errorMsg = element.shadowRoot.querySelectorAll(".form-require");
            expect(errorMsg[0].textContent).toBe("* ");
            expect(errorMsg[1].textContent).toBe("Complete this field.");
        });

        it("hide required error msg when input value is null and focus input", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.required = true;
            document.body.appendChild(element);
            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.dispatchEvent(new CustomEvent("blur"));
            await flushPromises();
            let errorMsg = element.shadowRoot.querySelectorAll(".form-require");
            expect(errorMsg[0].textContent).toBe("* ");
            expect(errorMsg[1].textContent).toBe("Complete this field.");

            input.dispatchEvent(new CustomEvent("focus"));
            await flushPromises();

            errorMsg = element.shadowRoot.querySelectorAll(".form-require");
            expect(errorMsg[1]).toBeFalsy();
        });

        it("Show result items and update dropdown direction when search has results", async () => {
            const searchText = jest.fn();
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.addEventListener("searchtext", searchText);

            document.body.appendChild(element);

            const input = element.shadowRoot.querySelector(".search_combo_box input");
            input.getBoundingClientRect = () => {
                return { top: 520 };
            };
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;

            await flushPromises(500);

            const dropUp = element.shadowRoot.querySelector(".drop-up");

            expect(dropUp).toBeTruthy();
        });
    });

    describe("searchable combo box mode is tags", () => {
        it("show default input value when searchable combo box have default value and mode is tags", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.labelName = "Contacts";
            element.placeholder = "Search Contacts...";
            element.mode = "tags";
            element.inputPill = [{ id: "001", href: `/link`, tagId: "0009", label: "labelName", isLink: true }];
            document.body.appendChild(element);

            await flushPromises();

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            expect(input.value).toBe("");
            const pills = element.shadowRoot.querySelectorAll("lightning-pill");
            expect(pills.length).toBe(1);
            expect(pills[0].label).toBe("labelName");
            expect(pills[0].href).toBe("/link");
        });
        it("show tag limit hint and disabled input when reach tag limit", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.mode = "tags";
            element.maxTagNum = 1;
            element.inputPill = [{ id: "001", href: `/link`, tagId: "0009", label: "labelName", isLink: true }];
            document.body.appendChild(element);
            const info = "The maximum tag limit of 1 has been reached. Remove a tag before adding a new one.";
            await flushPromises();

            const errorMsgEl = element.shadowRoot.querySelector(".tag-limit-hint");
            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            expect(errorMsgEl.textContent).toBe(info);
            expect(input.disabled).toBe(true);
        });

        it("should dispatch pill click event when mode is tags and click pill", async () => {
            const pillClickEvent = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.mode = "tags";
            element.inputPill = [
                { href: "/aA055000000W4rDCAS", id: "a9z55000000CsdhAAC", tagId: "aA055000000W4rDCAS", isLink: true, label: "TagName1" },
                { href: "/aA055000000W4rCCAS", id: "a9z55000000CsdmAAC", tagId: "aA055000000W4rCCAS", isLink: true, label: "TagName2" }
            ];
            element.addEventListener("tagclick", pillClickEvent);
            document.body.appendChild(element);

            const lightningPills = element.shadowRoot.querySelectorAll("lightning-pill");
            lightningPills[0].click();

            expect(pillClickEvent).toHaveBeenCalled();
        });

        it("should dispatch delete event when click remove pill button", async () => {
            const pillRemoveEvent = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.mode = "tags";
            element.inputPill = [
                { href: "/aA055000000W4rDCAS", id: "a9z55000000CsdhAAC", tagId: "aA055000000W4rDCAS", isLink: true, label: "TagName1" },
                { href: "/aA055000000W4rCCAS", id: "a9z55000000CsdmAAC", tagId: "aA055000000W4rCCAS", isLink: true, label: "TagName2" }
            ];

            element.addEventListener("tagdelete", pillRemoveEvent);
            document.body.appendChild(element);
            await flushPromises();

            const lightningPills = element.shadowRoot.querySelector("lightning-pill");
            lightningPills.dispatchEvent(
                new CustomEvent("remove", {
                    detail: {
                        name: "TagName1",
                        recordId: "a9z55000000CsdhAAC"
                    }
                })
            );

            await flushPromises();
            expect(pillRemoveEvent).toHaveBeenCalled();
            expect(pillRemoveEvent.mock.calls[0][0].detail).toStrictEqual({
                tagName: "TagName1",
                recordId: "a9z55000000CsdhAAC"
            });
        });

        it("should hidden select error msg when mode is tags and input have value and not select an option from the picklist", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.mode = "tags";
            document.body.appendChild(element);
            await flushPromises();

            const input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input"));
            await flushPromises();
            input.dispatchEvent(new CustomEvent("blur"));
            await flushPromises();
            let errorMsg = element.shadowRoot.querySelector(".form-require");

            expect(element.checkValidity()).toBeTruthy();
            expect(errorMsg).toBeFalsy();
        });

        it("should hidden label when labelName is null", async () => {
            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.mode = "tags";
            document.body.appendChild(element);
            await flushPromises();
            const label = element.shadowRoot.querySelector(".slds-form-element__label");
            expect(label).toBeFalsy();
        });

        it("should clear input value when mode is tags and user select one from search result", async () => {
            const searchText = jest.fn();

            const element = createElement("searchable-combo-box", { is: SearchableComboBox });
            element.mode = "tags";
            element.addEventListener("searchtext", searchText);
            document.body.appendChild(element);

            let input = element.shadowRoot.querySelector(".slds-form-element__control input");
            input.focus();
            input.value = "bie";
            input.dispatchEvent(new CustomEvent("input", { target: {} }));
            element.searchResult = searchResult;

            await flushPromises(500);

            const resultGroups = element.shadowRoot.querySelectorAll(".slds-listbox.slds-listbox_vertical ul");
            const firstResultItem = resultGroups[0].querySelector("c-searchable-combobox-item");
            firstResultItem.click();
            element.inputPill = [{ id: "0033I00000TktewQAB", href: `/link`, tagId: "0009", label: "labelName", isLink: true }];
            await flushPromises();

            input = element.shadowRoot.querySelector(".slds-form-element__control input");
            expect(input.value).toBe("");
        });
    });
});
