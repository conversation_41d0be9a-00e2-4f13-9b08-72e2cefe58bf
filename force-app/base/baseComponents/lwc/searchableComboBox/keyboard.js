/*
 * Copyright (c) 2019, salesforce.com, inc.
 * All rights reserved.
 * SPDX-License-Identifier: MIT
 * For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/MIT
 */

function preventDefaultAndStopPropagation(event) {
    event.preventDefault();
    event.stopPropagation();
}

function handleEnterKey({ event, currentIndex, dropdownInterface }) {
    preventDefaultAndStopPropagation(event);
    if (dropdownInterface.isDropdownVisible() && currentIndex >= 0) {
        dropdownInterface.selectByIndex(currentIndex);
    } else {
        dropdownInterface.openDropdownIfNotEmpty();
    }
}

function handleHomeOrEndKey({ event, dropdownInterface }) {
    if (!dropdownInterface.isInputReadOnly()) {
        return;
    }

    preventDefaultAndStopPropagation(event);

    if (!dropdownInterface.isDropdownVisible()) {
        dropdownInterface.openDropdownIfNotEmpty();
    }
    if (dropdownInterface.getTotalOptions() > 0) {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        requestAnimationFrame(() => {
            const highlightIndex = event.key === "Home" ? 0 : dropdownInterface.getTotalOptions() - 1;
            dropdownInterface.highlightOptionWithIndex(highlightIndex);
        });
    }
}

function handleUpOrDownKey({ event, currentIndex, dropdownInterface }) {
    preventDefaultAndStopPropagation(event);

    if (!dropdownInterface.isDropdownVisible()) {
        dropdownInterface.openDropdownIfNotEmpty();
    }

    const isUpKey = event.key === "Up" || event.key === "ArrowUp";
    let nextIndex;
    if (currentIndex >= 0) {
        nextIndex = isUpKey ? currentIndex - 1 : currentIndex + 1;
        if (nextIndex >= dropdownInterface.getTotalOptions()) {
            nextIndex = 0;
        } else if (nextIndex < 0) {
            nextIndex = dropdownInterface.getTotalOptions() - 1;
        }
    } else {
        nextIndex = isUpKey ? dropdownInterface.getTotalOptions() - 1 : 0;
    }

    if (dropdownInterface.getTotalOptions() > 0) {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        requestAnimationFrame(() => {
            dropdownInterface.highlightOptionWithIndex(nextIndex, event.key);
        });
    }
}

function handleEscapeOrTabKey({ event, currentIndex, dropdownInterface }) {
    if (dropdownInterface.isDropdownVisible()) {
        const newRecordActionIndex = dropdownInterface.getCurrentNewRecordActionIndex();
        if (event.key.toLowerCase() === "escape" || (event.key.toLowerCase() === "tab" && currentIndex === newRecordActionIndex)) {
            event.stopPropagation();
            dropdownInterface.closeDropdown();
        } else {
            preventDefaultAndStopPropagation(event);
            dropdownInterface.highlightOptionWithIndex(newRecordActionIndex);
        }
    }
}

const eventKeyToHandlerMap = {
    Enter: handleEnterKey,

    Home: handleHomeOrEndKey,
    End: handleHomeOrEndKey,

    Down: handleUpOrDownKey,
    Up: handleUpOrDownKey,
    ArrowUp: handleUpOrDownKey,
    ArrowDown: handleUpOrDownKey,

    Escape: handleEscapeOrTabKey,
    Tab: handleEscapeOrTabKey
};

export function handleKeyDownOnInput({ event, currentIndex, dropdownInterface }) {
    const parameters = { event, currentIndex, dropdownInterface };

    if (eventKeyToHandlerMap[event.key]) {
        eventKeyToHandlerMap[event.key](parameters);
    }
}
