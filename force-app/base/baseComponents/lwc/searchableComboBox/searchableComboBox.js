import { LightningElement, track, api } from "lwc";
import { handleKeyDownOnInput } from "./keyboard";
import { debounce } from "c/utils";
const ARIA_ACTIVEDESCENDANT = "aria-activedescendant";
const FIXED_NEW_RECORD_ACTION_INDEX = 0;
const NEW_RECORD_ID = "newRecordAction";
const REQUIRED_ERROR_MSG = "Complete this field.";
const SELECT_ERROR_MSG = "Select an option from the picklist or remove the search term.";
const MODE_TAGS = "tags";
export default class SearchableComboBox extends LightningElement {
    @api labelName;
    @api placeholder = "Please Search…";
    @api required = false;
    @api notFitDropdownDirection = false;
    @track searchResultItems;
    @api objectName = "contacts";
    @api isDropdownFixed = false;
    @api helpTextContent;
    @api mode;
    @api maxTagNum;
    @api searchInfoTemplates = {};
    @api disableInput = false;
    @api customRequiredErrMsg;
    @track pills = [];

    searchResultWithDelay = debounce(() => this.searchText(), 500);

    notHasSearchResult = true;
    newRecordId = NEW_RECORD_ID;
    addRecordId = "addRecordAction";
    hasNewRecordAction = false;
    _inputValue = "";
    inputIcon = "";
    dropdownVisible = false;
    blurCanceled = false;
    isDropdownDirectionDown = true;
    showSearchLoading = false;
    customErrorMsg = "";

    _errorMsg = "";
    _highlightedOptionElementId;
    _highlightedOptionElement;
    _activeElementDomId;
    _newRecordConfig;
    _searchResult;
    _inputPill;
    _inputHasFocus;

    get inputValue() {
        if (this.disableInput) {
            this._inputValue = "";
        }
        return this._inputValue;
    }

    get showPillContainer() {
        return this.mode === MODE_TAGS;
    }

    get requiredIsNull() {
        let hasInputPill = false;
        if (Array.isArray(this._inputPill)) {
            hasInputPill = this._inputPill.length > 0;
        } else {
            hasInputPill = this._inputPill && Object.keys(this._inputPill).length > 0;
        }
        return this.required && !hasInputPill;
    }

    get selectNoFinished() {
        return this._inputValue && this._inputValue.trim().length > 0 && !this.hasInputPill && !this._inputHasFocus && this.mode !== MODE_TAGS;
    }

    get isTagsReachToMaxNum() {
        if (this.pills && this.maxTagNum) {
            return this.pills.length >= this.maxTagNum;
        }
        return false;
    }

    get tagLimitHint() {
        return "The maximum tag limit of " + this.maxTagNum + " has been reached. Remove a tag before adding a new one.";
    }

    get disabledInput() {
        return this.isTagsReachToMaxNum || this.disableInput;
    }

    @api
    get newRecordConfig() {
        return this._newRecordConfig;
    }

    set newRecordConfig(value) {
        this._newRecordConfig = value;
        if (value && value.iconName && value.label) {
            this.hasNewRecordAction = true;
            if (!this._keyboardInterface) {
                this._keyboardInterface = this.dropdownKeyboardInterface();
            }
            this._keyboardInterface.getCurrentNewRecordActionIndex = () => FIXED_NEW_RECORD_ACTION_INDEX;
        }
    }

    @api
    get inputPill() {
        return this._inputPill;
    }

    set inputPill(item) {
        this._inputPill = item;
        if (Array.isArray(item)) {
            this.clearInputWhenAddPill(item, this.pills);
            this.pills = item;
        } else if (item && Object.keys(item).length > 0) {
            this._inputValue = item.value;
            this.inputIcon = item.iconName;
        } else if (this.hasInputPill) {
            this._inputValue = "";
            this.inputIcon = "";
        }
    }

    @api
    get searchResult() {
        return this._searchResult;
    }

    set searchResult(searchResult) {
        this._searchResult = searchResult;
        if (searchResult) {
            const result = JSON.parse(JSON.stringify(searchResult));
            this.handleSearchFinish(result, this._inputValue);
        }
    }

    get searchInfo() {
        const objectNameLowercase = this.objectName.toLowerCase();
        const searchInfoTemplates = {
            failTemplate:
                this.searchInfoTemplates.failTemplate ||
                function (inputValue) {
                    return `We didn't find "${inputValue}" amongst all ${objectNameLowercase}.`;
                },
            successTemplate:
                this.searchInfoTemplates.successTemplate ||
                function (inputValue) {
                    return `"${inputValue}" in ${objectNameLowercase}`;
                }
        };
        const selectedTemplate = this.notHasSearchResult ? searchInfoTemplates.failTemplate : searchInfoTemplates.successTemplate;
        return selectedTemplate(this._inputValue);
    }

    get computedPlaceholder() {
        if (this.placeholder != null) {
            return this.placeholder;
        }
        return `Search ${this.labelName}...`;
    }

    get hasInputPill() {
        return this.inputIcon;
    }

    get _inputReadOnly() {
        return this.hasInputPill;
    }

    get computedDropdownTriggerClass() {
        let className = "slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click";
        if (this.dropdownVisible) {
            className += " slds-is-open";
        }
        return className;
    }

    get computedListBoxClass() {
        let className = "slds-listbox slds-listbox_vertical";
        if (this.newRecordConfig) {
            className += " search-result-area";
        }
        return className;
    }

    get computedInputClass() {
        let className = "slds-input slds-combobox__input";
        if (this.hasInputPill) {
            className += " slds-combobox__input-value";
        }
        if (this.invalid) {
            className += " input-require";
        }
        return className;
    }

    get computedFormElementClass() {
        let className = "slds-combobox__form-element slds-input-has-icon";
        if (this.hasInputPill) {
            className += " slds-input-has-icon_left-right";
        }
        return className;
    }

    get computedDropdownClass() {
        let className = "slds-dropdown slds-dropdown_left dropdown_fluid slds-dropdown_length-with-icon-10";

        if (!this.isDropdownDirectionDown) {
            className += " drop-up";
        } else {
            className += " drop-down";
        }

        if (this.isDropdownFixed) {
            className += " fixed-dropdown";
        }

        return className;
    }

    get inputElement() {
        return this.template.querySelector("input");
    }

    get listBoxElement() {
        if (!this._listBoxElementCache) {
            this._listBoxElementCache = this.template.querySelector(".dropdown_fluid");
        }
        return this._listBoxElementCache;
    }

    get invalid() {
        return !!this.errorMsg && !this._inputHasFocus;
    }

    get errorMsg() {
        return this.customErrorMsg || this._errorMsg;
    }

    @api checkValidity() {
        if (this.selectNoFinished) {
            this._errorMsg = SELECT_ERROR_MSG;
        } else if (this.requiredIsNull) {
            this._errorMsg = this.customRequiredErrMsg || REQUIRED_ERROR_MSG;
        } else {
            this._errorMsg = "";
        }
        if (this.disabledInput) {
            this._errorMsg = "";
        }
        return !this._errorMsg;
    }

    @api setCustomValidity(msg) {
        this.customErrorMsg = msg;
    }

    @api
    focus() {
        this.inputElement.focus();
    }

    connectedCallback() {
        this._keyboardInterface = this.dropdownKeyboardInterface();
    }

    disconnectedCallback() {
        this._listBoxElementCache = undefined;
    }

    clearInputWhenAddPill(currentPills, previousPills) {
        if (currentPills.length > previousPills.length) {
            this._inputValue = "";
        }
    }

    searchText() {
        if (this._inputValue && this._inputValue.trim().length > 1) {
            this.showSearchLoading = true;
            this.dispatchEvent(
                new CustomEvent("searchtext", {
                    detail: {
                        searchKey: this._inputValue
                    }
                })
            );
        } else {
            this.closeDropdown();
        }
    }

    dropdownKeyboardInterface() {
        const that = this;
        return {
            getTotalOptions() {
                return that._selectableItems;
            },
            selectByIndex(index) {
                if (index === 0) {
                    that.dispatchEvent(new CustomEvent("newrecord"));
                } else {
                    that.selectOptionAndCloseDropdown(that.findOptionElementByIndex(index));
                }
            },
            highlightOptionWithIndex(index, key) {
                that.highlightOptionAndScrollIntoView(that.findOptionElementByIndex(index), key);
            },
            isDropdownVisible() {
                return that.dropdownVisible;
            },
            openDropdownIfNotEmpty() {
                that.openDropdownIfNotEmpty();
            },
            closeDropdown() {
                that.closeDropdown();
            },
            getCurrentNewRecordActionIndex: () => -1
        };
    }

    openDropdownIfNotEmpty() {
        if (this.dropdownVisible) {
            return;
        }
        this.openDropdown();
    }

    handleFocus() {
        this.updateDropdownDirection();
        this._inputHasFocus = true;
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => this.openDropdown(), 100);
    }

    handleBlur() {
        this.checkValidity();

        this._inputHasFocus = false;

        if (this.blurCanceled) {
            return;
        }
        this.closeDropdown();
    }

    handleClear() {
        this.updateDropdownDirection();
        if (this.hasInputPill) {
            this.dispatchEvent(new CustomEvent("selectvalue", { detail: {} }));
            this._inputValue = "";
            this.inputIcon = "";
            this.inputElement.focus();
        }
    }

    handleNewRecord(event) {
        event.stopPropagation();
        this.dispatchEvent(new CustomEvent("newrecord"));
    }

    handlePillClick() {
        this.dispatchEvent(new CustomEvent("tagclick"));
    }

    handleRemove(event) {
        const tagName = event.detail.name;
        const recordId = event.currentTarget.dataset.id;
        this.dispatchEvent(new CustomEvent("tagdelete", { detail: { tagName: tagName, recordId: recordId }, bubbles: true, composed: true }));
    }

    handleSearchFinish(searchResult, searchKey) {
        this.showSearchLoading = false;
        if (searchKey === this._inputValue) {
            this.notHasSearchResult = searchResult.length === 0;
            this.updateItems(searchResult);
            if (this._inputHasFocus) {
                this.openDropdown();
            }
        }
    }

    highlightDefaultItem() {
        // requestAnimationFrame(() => {
        this.highlightOptionAndScrollIntoView(this.findOptionElementByIndex(1));
        // });
    }

    findOptionElementByIndex(index) {
        return this.template.querySelector(`[data-item-id="${this.itemId(index)}"]`);
    }

    highlightOptionAndScrollIntoView(optionElement, key) {
        if (!optionElement) {
            return;
        }
        this.highlightOption(optionElement);

        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => {
            scrollIntoViewIfNeeded(optionElement, this.listBoxElement, key);
        }, 200);
    }

    updateItems(items) {
        this._selectableItems = 1;
        this.searchResultItems = items.map((item, index) => {
            if (item.items) {
                const groupCopy = {
                    label: item.label,
                    id: index
                };
                groupCopy.items = item.items.map((groupItem) => {
                    return this.processItem(groupItem);
                });
                return groupCopy;
            }
            return this.processItem(item);
        });
    }

    itemId(index) {
        return "input-" + index;
    }

    processItem(item) {
        const itemCopy = { ...item };
        itemCopy.index = this._selectableItems;
        itemCopy.indexId = this.itemId(itemCopy.index);
        this._selectableItems += 1;
        return itemCopy;
    }

    handleInput(event) {
        this._inputValue = event.target.value;
        this.dispatchEvent(new CustomEvent("searchinput", { detail: { _inputValue: this._inputValue }, bubbles: true, composed: true }));
        this.searchResultWithDelay();
    }

    @api
    handleDropdownMouseDown(event) {
        const mainButton = 0;
        if (event.button === mainButton) {
            this.cancelBlur();
        }
    }

    handleDropdownMouseUp() {
        this.allowBlur();
    }

    handleDropdownMouseLeave() {
        this.removeHighlight();

        if (!this._inputHasFocus) {
            this.closeDropdown();
        }
    }

    handleOptionClick(event) {
        event.stopPropagation();
        event.preventDefault();
        this.selectOptionAndCloseDropdown(event.target);
    }

    allowBlur() {
        this.blurCanceled = false;
    }

    cancelBlur() {
        this.blurCanceled = true;
    }

    selectOptionAndCloseDropdown(optionElement) {
        this.inputElement.focus();
        const selectedItemValue = optionElement.getAttribute("data-value");
        const iconName = optionElement.getAttribute("data-icon");
        const selectedItemId = optionElement.getAttribute("data-id");
        const selectedItemDisable = optionElement.getAttribute("data-disable") === "true";

        if (!selectedItemValue || selectedItemDisable) {
            return;
        }
        if (this.mode === MODE_TAGS) {
            this._inputValue = "";
        }

        this.dispatchEvent(new CustomEvent("selectvalue", { detail: { iconName: iconName, value: selectedItemValue, id: selectedItemId }, bubbles: true, composed: true }));
        this.closeDropdown();
    }

    closeDropdown() {
        if (!this.dropdownVisible) {
            return;
        }

        this.removeHighlight();
        this.dropdownVisible = false;
        this._listBoxElementCache = undefined;
    }

    getDropdownElementPosition() {
        const dropdownElement = this.template.querySelector(".dropdown_fluid");
        const input = this.template.querySelector("input");
        const searchComboBox = this.template.querySelector(".search_combo_box");
        if (!input) return;
        const oppoSelectBoxPosition = input.getBoundingClientRect();
        dropdownElement.style.top = oppoSelectBoxPosition.top + 30 + "px";
        dropdownElement.style.left = searchComboBox.offsetLeft + "px";
        dropdownElement.style.width = input.offsetWidth + "px";
    }

    openDropdown() {
        if (this._inputValue && this._inputValue.trim().length > 1 && !this.hasInputPill) {
            this.dropdownVisible = true;
            const searchComboBox = this.template.querySelector(".search_combo_box");
            searchComboBox.style.opacity = 0.0;
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                if (this.isDropdownFixed) {
                    this.getDropdownElementPosition();
                }
                searchComboBox.style.opacity = 1.0;
                this.highlightDefaultItem();
            }, 1);
        }
    }

    updateDropdownDirection() {
        if (this.notFitDropdownDirection) {
            return;
        }
        const windowHeight = window.innerHeight;
        const offsetTop = this.template.querySelector(".search_combo_box input").getBoundingClientRect().top;
        this.isDropdownDirectionDown = windowHeight - offsetTop > offsetTop;
    }

    getCurrentHighlightedOptionIndex() {
        let currentIndex = -1;
        if (this._highlightedOptionElementId && this._highlightedOptionElementId.length > 0) {
            currentIndex = this.itemIndexFromId(this._highlightedOptionElementId);
        }
        return currentIndex;
    }

    itemIndexFromId(id) {
        return parseInt(id.substring(id.lastIndexOf("-") + 1), 10);
    }

    highlightOption(option) {
        this.removeHighlight();
        if (option) {
            option.highlight();
            this._highlightedOptionElement = option;
            this._highlightedOptionElementId = option.getAttribute("data-item-id");

            this._activeElementDomId = option.id;
        }
        this.synchronizeA11y();
    }

    synchronizeA11y() {
        const input = this.template.querySelector("input");
        if (!input) {
            return;
        }
        synchronizeAttrs(input, {
            [ARIA_ACTIVEDESCENDANT]: this._activeElementDomId
        });
    }

    removeHighlight() {
        const option = this._highlightedOptionElement;
        if (option) {
            option.removeHighlight();
            this._highlightedOptionElement = null;
            this._highlightedOptionElementId = null;
            this._activeElementDomId = null;
        }
    }
    handleInputKeyDown(event) {
        if (!this.hasInputPill) {
            handleKeyDownOnInput({
                event,
                currentIndex: this.getCurrentHighlightedOptionIndex(),
                dropdownInterface: this._keyboardInterface
            });
        }
    }
}

function scrollIntoViewIfNeeded(element, scrollingParent, key) {
    if (!element || !scrollingParent) return;
    const parentRect = scrollingParent.getBoundingClientRect();
    const findMeRect = element.getBoundingClientRect();
    if (findMeRect.top < parentRect.top) {
        if (element.offsetTop + findMeRect.height >= parentRect.height || key === "Up" || key === "ArrowUp") {
            scrollingParent.scrollTop = element.offsetTop - 36;
        } else {
            scrollingParent.scrollTop = 0;
        }
    } else if (findMeRect.bottom > parentRect.bottom) {
        scrollingParent.scrollTop += findMeRect.bottom - parentRect.bottom + findMeRect.height;
    }
}

function synchronizeAttrs(element, values) {
    if (!element) {
        return;
    }
    const attributes = Object.keys(values);
    attributes.forEach((attribute) => {
        smartSetAttribute(element, attribute, values[attribute]);
    });
}

function smartSetAttribute(element, attribute, value) {
    if (element.tagName.match(/^C/i)) {
        attribute = attribute.replace(/-\w/g, (m) => m[1].toUpperCase());
        element[attribute] = value ? value : null;
    } else if (value) {
        element.setAttribute(attribute, value);
    } else {
        element.removeAttribute(attribute);
    }
}
