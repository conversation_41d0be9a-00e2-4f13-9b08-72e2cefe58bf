import { createElement } from "lwc";
import NumberInput from "c/numberInput";

describe("c-number-input", () => {
    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("display a lightning input with placeholder when component init with null value", () => {
        const element = createElement("c-number-input", { is: NumberInput });
        element.inputName = "name";
        element.inputNumber = null;
        element.curDate = "DEC 02";
        element.dayOfWeek = "Thursday";
        document.body.appendChild(element);

        const numberInput = element.shadowRoot.querySelector("input");
        expect(numberInput).not.toBeNull();
        expect(numberInput.name).toEqual("name");
        expect(numberInput.value).toEqual("");
    });

    it("display a lightning input with placeholder when component init with non null value", () => {
        const element = createElement("c-number-input", { is: NumberInput });
        element.inputNumber = "0";
        element.curDate = "DEC 02";
        element.dayOfWeek = "Thursday";
        document.body.appendChild(element);

        const numberInput = element.shadowRoot.querySelector("input");
        expect(numberInput).not.toBeNull();
        expect(numberInput.value).toEqual("0");
    });

    it("show number when input number", () => {
        const element = createElement("c-number-input", { is: NumberInput });
        element.inputNumber = "0";
        element.curDate = "DEC 02";
        element.dayOfWeek = "Thursday";
        document.body.appendChild(element);

        const numberInput = element.shadowRoot.querySelector("input");
        numberInput.value = "3";
        numberInput.dispatchEvent(new CustomEvent("input"));

        return Promise.resolve().then(() => {
            expect(numberInput.value).toEqual("3");
        });
    });

    it("should get correct ariaLabel value", () => {
        const element = createElement("c-number-input", { is: NumberInput });
        element.inputNumber = "0";
        element.curDate = "DEC 02";
        element.dayOfWeek = "Thursday";
        document.body.appendChild(element);

        const numberInput = element.shadowRoot.querySelector("input");
        expect(numberInput.ariaLabel).not.toBeNull();
        expect(numberInput.ariaLabel).toEqual("Thursday December 02");
    });
});
