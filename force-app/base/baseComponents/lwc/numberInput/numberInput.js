import { LightningElement, track, api } from "lwc";

const VALID_KEY = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", ".", "<PERSON>Left", "<PERSON>R<PERSON>", "Shift", "CapsLock", "Backspace", "Tab", "。", "｡"];
const MONTH_MAP = {
    JAN: "January",
    FEB: "February",
    MAR: "March",
    APR: "April",
    MAY: "May",
    JUN: "June",
    JUL: "July",
    AUG: "August",
    SEP: "September",
    OCT: "October",
    NOV: "November",
    DEC: "December"
};
export default class NumberInput extends LightningElement {
    @api
    dayOfWeek;
    @api
    curDate;

    @api disableInput;
    @api maxNumberValue;
    @api inputName;
    @api hasError = false;
    @api
    set inputNumber(number) {
        this._inputNumber = null == number ? null : number.toString();
    }
    get inputNumber() {
        return this._inputNumber;
    }

    get ariaLabelValue() {
        const curDateSplitStrings = this.curDate.split(" ");
        const monthName = MONTH_MAP[curDateSplitStrings[0]];
        const dayOfMonth = curDateSplitStrings[1];
        return `${this.dayOfWeek} ${monthName} ${dayOfMonth}`;
    }

    @track _inputNumber;

    isInputChanged = false;

    get numberInputCSSClass() {
        return (this.hasError ? "slds-input number-input show-error" : "slds-input number-input") + (this.disableInput ? " disable-input" : "");
    }

    handleKeyDown(event) {
        if (this.isInputInvalid(event.key)) {
            event.preventDefault();
        }
    }

    //edge case: input 34 => _inputNumber change to 24 => input 5(UI input is 245)
    // _inputNumber will still to be 24 which will not trigger UI refresh, then UI will be 245 unchanged
    // so change _inputNumber to 245 in handleInputChange, then update to 24 in handleKeyUp, which will
    // trigger UI refresh
    handleInputChange(event) {
        this._inputNumber = event.target.value;
        this.isInputChanged = true;
    }

    handleKeyUp(event) {
        this._inputNumber = this.trimInvalidInput(event.target.value);
        this.dispatchEvent(new CustomEvent("numberchange", { detail: { name: event.target.name, value: this.inputNumber } }));
    }

    handleBlur(event) {
        const inputNumber = parseFloat(this._inputNumber);
        this._inputNumber = isNaN(inputNumber) ? "" : this.trimInvalidInput(inputNumber.toString());
        if (this.isInputChanged) {
            this.dispatchEvent(new CustomEvent("numberchange", { detail: { name: event.target.name, value: this.inputNumber } }));
        }
    }

    isInputInvalid(keyValue) {
        return !VALID_KEY.includes(keyValue) || ((keyValue === "." || keyValue === "。" || keyValue === "｡") && this.inputNumber.includes("."));
    }

    trimInvalidInput(inputValue) {
        let resultValue = inputValue.replace(/[\u3002\uFF61]/g, ".").replace(/[^0-9.]/g, "");
        if (parseFloat(resultValue) > parseFloat(this.maxNumberValue)) {
            resultValue = this.maxNumberValue.toString();
        }
        resultValue = resultValue.replace(/\.+/g, ".");
        resultValue = resultValue.replace(/\..{1}\./g, (str) => str.slice(0, 2));
        resultValue = resultValue.replace(/\..{3,}/g, (str) => str.slice(0, 3));
        return resultValue;
    }
}
