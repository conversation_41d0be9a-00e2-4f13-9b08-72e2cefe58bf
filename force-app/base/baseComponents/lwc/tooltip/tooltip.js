import { api, LightningElement } from "lwc";
import { throttle } from "c/utils";

const ARROW_PLACEMENT = {
    BOTTOM: "bottom",
    BOTTOM_LEFT: "bottom-left",
    BOTTOM_RIGHT: "bottom-right"
};

export default class TooltipText extends LightningElement {
    @api disable = false;
    @api text;
    @api
    get tooltip() {
        return this._tooltip ?? this.text;
    }
    set tooltip(tooltipText) {
        this._tooltip = tooltipText;
    }

    @api
    get label() {
        return this._label ?? this.text;
    }
    set label(labelText) {
        this._label = labelText;
    }

    @api tooltipStyle;
    @api textStyle;
    @api ellipsis = false;
    arrowPlacement = ARROW_PLACEMENT.BOTTOM;
    _tooltip;
    _label;

    get showText() {
        return this.text !== null && this.text !== undefined;
    }

    connectedCallback() {
        window.addEventListener("scroll", this.hideTooltip.bind(this));
    }

    disconnectedCallback() {
        window.removeEventListener("scroll", this.hideTooltip.bind(this));
    }

    hideTooltip = throttle(() => {
        const tooltipElem = this.template.querySelector('[data-id="tooltip"]');
        if (!tooltipElem) {
            return;
        }
        tooltipElem.style.opacity = 0;
    }, 1000);

    setTooltipContainerStyle() {
        const tooltipElem = this.template.querySelector('[data-id="tooltip"]');
        const tooltip = tooltipElem?.getBoundingClientRect();
        const trigger = this.showText
            ? this.template.querySelector('[data-id="trigger"]')?.getBoundingClientRect()
            : this.template.querySelector('slot[name="content"]')?.assignedElements()?.[0]?.getBoundingClientRect();
        tooltipElem.style = `top:${trigger.y - tooltip.height - 12}px; left: ${trigger.x + trigger.width / 2}px`;

        const leftSpace = trigger.x + trigger.width / 2;
        const rightSpace = window.innerWidth - (trigger.x + trigger.width / 2);

        if (tooltip.width / 2 > leftSpace) {
            this.arrowPlacement = ARROW_PLACEMENT.BOTTOM_LEFT;
        } else if (tooltip.width / 2 > rightSpace) {
            this.arrowPlacement = ARROW_PLACEMENT.BOTTOM_RIGHT;
        } else {
            this.arrowPlacement = ARROW_PLACEMENT.BOTTOM;
        }
    }

    get textClass() {
        return this.ellipsis ? "text ellipsis" : "text";
    }

    get tooltipClass() {
        return `tooltip slds-popover slds-popover_tooltip slds-nubbin_${this.arrowPlacement}`;
    }
}
