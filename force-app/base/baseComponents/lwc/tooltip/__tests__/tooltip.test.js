import { createElement } from "lwc";
import Tooltip from "c/tooltip";
import { flushPromises } from "c/utils";

describe("c-tooltip", () => {
    let realInnerWidth;
    let element;

    beforeEach(() => {
        realInnerWidth = window.innerWidth;
        global.innerWidth = 100;
        element = createElement("c-tooltip", {
            is: Tooltip
        });
    });

    afterEach(() => {
        global.innerWidth = realInnerWidth;
        jest.clearAllMocks();
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should render the text and use text as tooltip", () => {
        element.text = "fieldText";
        element.tooltip = "Tooltip";
        element.label = "labelText";
        element.ellipsis = true;
        document.body.appendChild(element);

        expect(element).toMatchSnapshot();
    });

    it("should render the component with a slot", () => {
        const slot = document.createElement("slot");
        element.appendChild(slot);

        document.body.appendChild(element);

        expect(element).toMatchSnapshot();
    });

    it("should set correct position when hover text", () => {
        element.text = "fieldText";
        element.tooltip = "Tooltip";
        document.body.appendChild(element);

        const triggerElement = element.shadowRoot.querySelector('[data-id="trigger"]');

        jest.spyOn(triggerElement, "getBoundingClientRect").mockReturnValue({
            x: 100,
            y: 100,
            width: 50,
            height: 20,
            top: 100,
            right: 150,
            bottom: 120,
            left: 100
        });

        const mouseoverEvent = new MouseEvent("mouseover");

        triggerElement.dispatchEvent(mouseoverEvent);

        const tooltipElement = element.shadowRoot.querySelector('[data-id="tooltip"]');

        expect(tooltipElement.style.left).toBe("125px");
        expect(tooltipElement.style.top).toBe("88px");
        expect(tooltipElement.classList).toContain("slds-nubbin_bottom");
    });

    it("should set left arrow when left space not enough", async () => {
        element.text = "fieldText";
        element.tooltip = "Long Tooltip";
        document.body.appendChild(element);

        const triggerElement = element.shadowRoot.querySelector('[data-id="trigger"]');
        const tooltipElement = element.shadowRoot.querySelector('[data-id="tooltip"]');

        jest.spyOn(triggerElement, "getBoundingClientRect").mockReturnValue({
            x: 10,
            y: 10,
            width: 100,
            height: 20,
            top: 100,
            right: 150,
            bottom: 120,
            left: 100
        });

        jest.spyOn(tooltipElement, "getBoundingClientRect").mockReturnValue({
            width: 200,
            height: 20
        });

        const mouseoverEvent = new MouseEvent("mouseover");

        triggerElement.dispatchEvent(mouseoverEvent);
        await flushPromises();

        expect(tooltipElement.classList).toContain("slds-nubbin_bottom-left");
    });

    it("should set right arrow when right space not enough", async () => {
        element.text = "fieldText";
        element.tooltip = "Long Tooltip";
        document.body.appendChild(element);

        const triggerElement = element.shadowRoot.querySelector('[data-id="trigger"]');
        const tooltipElement = element.shadowRoot.querySelector('[data-id="tooltip"]');

        jest.spyOn(triggerElement, "getBoundingClientRect").mockReturnValue({
            x: 10,
            y: 10,
            width: 100,
            height: 20,
            top: 100,
            right: 150,
            bottom: 120,
            left: 100
        });

        jest.spyOn(tooltipElement, "getBoundingClientRect").mockReturnValue({
            width: 100,
            height: 20
        });

        const mouseoverEvent = new MouseEvent("mouseover");

        triggerElement.dispatchEvent(mouseoverEvent);
        await flushPromises();

        expect(tooltipElement.classList).toContain("slds-nubbin_bottom-right");
    });

    it("should hide tooltip on scroll", async () => {
        document.body.appendChild(element);

        const tooltipElem = element.shadowRoot.querySelector('[data-id="tooltip"]');
        expect(tooltipElem).not.toBeNull();

        window.dispatchEvent(
            new CustomEvent("scroll", {
                detail: {}
            })
        );

        await flushPromises();
        expect(tooltipElem.style.opacity).toBe("0");
    });
});
