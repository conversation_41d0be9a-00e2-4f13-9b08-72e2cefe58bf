// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`c-tooltip should render the component with a slot 1`] = `
<c-tooltip
  __lwc_scope_token__=""
>
  #shadow-root(open)
    <slot />
    <div
      __lwc_scope_token__=""
      class="container"
      role="text"
    >
      <slot
        __lwc_scope_token__=""
        name="content"
      />
      <div
        __lwc_scope_token__=""
        aria-hidden="true"
        class="tooltip slds-popover slds-popover_tooltip slds-nubbin_bottom"
        data-id="tooltip"
        id="tooltip-1"
        role="tooltip"
      >
        <div
          __lwc_scope_token__=""
          class="slds-popover__body"
          id="tooltipText-1"
        />
      </div>
    </div>
</c-tooltip>
`;

exports[`c-tooltip should render the text and use text as tooltip 1`] = `
<c-tooltip
  __lwc_scope_token__=""
>
  #shadow-root(open)
    <div
      __lwc_scope_token__=""
      aria-label="labelText"
      class="container"
      role="text"
    >
      <span
        __lwc_scope_token__=""
        aria-describedby="tooltip-0"
        aria-hidden="true"
        class="text ellipsis"
        data-id="trigger"
      >
        fieldText
      </span>
      <slot
        __lwc_scope_token__=""
        name="content"
      />
      <div
        __lwc_scope_token__=""
        aria-hidden="true"
        class="tooltip slds-popover slds-popover_tooltip slds-nubbin_bottom"
        data-id="tooltip"
        id="tooltip-0"
        role="tooltip"
      >
        <div
          __lwc_scope_token__=""
          class="slds-popover__body"
          id="tooltipText-0"
        >
          Tooltip
        </div>
      </div>
    </div>
</c-tooltip>
`;
