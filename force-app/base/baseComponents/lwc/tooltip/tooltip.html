<template>
    <div class="container" aria-label={label} role="text">
        <span lwc:if={showText} class={textClass} style={textStyle} aria-describedby="tooltip" aria-hidden="true" data-id="trigger" onmouseover={setTooltipContainerStyle}>{text}</span>
        <slot name="content" onmouseover={setTooltipContainerStyle}></slot>
        <div if:false={disable} class={tooltipClass} aria-hidden="true" role="tooltip" id="tooltip" data-id="tooltip" onmouseover={setTooltipContainerStyle}>
            <div class="slds-popover__body" style={tooltipStyle} id="tooltipText">{tooltip}</div>
        </div>
    </div>
</template>
