import { LightningElement, api } from "lwc";

export default class RelatedList extends LightningElement {
    @api title;
    @api iconName;
    @api relateList = [];
    @api isLoading;
    @api hideEdit;
    @api sumNum=0
    /*
    [
        {
            tabName: "tabName",
            emptyInfo:'No SOW linked to this contract.',
            items: [
                {
                    title: "title",
                    titleHref:'/',
                    fields: [
                        { label: "Name1", value: "Sow 1", type: "link", href: "/" },
                        { label: "Name2", value: "Draft", type: "text" }
                        { label: "Name3", value: "2023-02-02", type: "date" }
                        { label: "Name3", value: "123333333", type: "currency"，unit:''USD }
                        ...
                    ]
                }
            ]
        },
    ]
    */

    handleAction() {
        this.dispatchEvent(new CustomEvent("action"));
    }
}
