<template>
    <lightning-card class="card-container" variant="narrow" icon-name={iconName}>
        <lightning-button if:false={hideEdit} label="Edit Relationship" alternative-text="Edit Relationship" slot="actions" onclick={handleAction}></lightning-button>
        <h2 slot="title" class="slds-text-heading_small slds-m-right_x-small">{title} ({sumNum})</h2>
        <section class="related-content">
            <lightning-tabset>
                <template for:each={relateList} for:item="relate">
                    <lightning-tab key={relate.tabName} label={relate.tabName}>
                        <div class="related-items" lwc:if={relate.items.length}>
                            <template for:each={relate.items} for:item="item">
                                <c-related-list-item key={relate.item} item={item}></c-related-list-item>
                            </template>
                        </div>
                        <template lwc:else>
                            <c-tw-empty img-resource="/resource/LightningPageIcons/LightningPageIcons/empty.svg" description={relate.emptyInfo}></c-tw-empty>
                        </template>
                    </lightning-tab>
                </template>
            </lightning-tabset>
        </section>
        <c-tw-spinner type="partial" loading={isLoading}></c-tw-spinner>
    </lightning-card>
</template>
