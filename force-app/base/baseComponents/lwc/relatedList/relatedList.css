.card-container {
    --sds-c-card-color-background: var(--slds-g-color-neutral-base-95, rgb(243, 243, 243));
    --slds-c-card-header-spacing-block-start: var(--lwc-spacingMedium);
    --slds-c-card-header-spacing-block-end: var(--lwc-spacingXSmall);
    --slds-c-card-body-spacing-block-start: 0;
    --slds-c-card-body-spacing-block-end: 0;
    --slds-c-card-spacing-block-start: 0;
}
.related-content {
    background: #fff;
    padding: 0 var(--lwc-spacingMedium) var(--lwc-spacingMedium);
    border-top: 1px solid var(--slds-g-color-border-base-1, #e5e5e5);
    border-bottom-left-radius: var(--lwc-spacingXxSmall);
    border-bottom-right-radius: var(--lwc-spacingXxSmall);
    --sds-c-tabs-panel-spacing-block-end: 0;
    --sds-c-tabs-panel-spacing-block-start: 0;
}
.related-items {
    max-height: 230px;
    overflow-y: auto;
}
