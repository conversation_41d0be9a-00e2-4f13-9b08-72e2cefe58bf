import { createElement } from "lwc";
import RelatedList from "c/relatedList";

describe("c-related-list", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should dispatch action event when click edit Relationship button", async () => {
        // Arrange
        const element = createElement("c-related-list", {
            is: RelatedList
        });

        const handleAction = jest.fn();
        element.addEventListener("action", handleAction);
        // Act
        document.body.appendChild(element);

        // Assert
        const editButton = element.shadowRoot.querySelector("lightning-button");
        editButton.click();
        expect(handleAction).toHaveBeenCalled();
    });
});
