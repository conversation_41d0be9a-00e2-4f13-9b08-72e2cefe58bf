<template>
    <div class="search-input-component">
        <label if:true={label} class="search-input-label slds-form-element__label"><abbr if:true={required} class="slds-required" title="required">*</abbr>{label}</label>
        <div class="search-input-container">
            <div class={inputWrapperClassName}>
                <div class={inputClass}>
                    <lightning-icon if:true={inputIconName} icon-name={inputIconName} alternative-text="Search" size="x-small" class={inputIconClass}></lightning-icon>
                    <input
                        type="text"
                        role="combobox"
                        aria-label={inputLabel}
                        aria-autocomplete="none"
                        class={searchInputClassName}
                        value={inputValue}
                        disabled={disableInput}
                        autocomplete="nope"
                        oninput={handleInputChanged}
                        onfocus={handleInputFocus}
                        onblur={handleInputBlur}
                        onkeydown={handleKeyDown}
                        placeholder={inputPlaceholder}
                    />
                </div>
                <template if:true={errorMessage}>
                    <div class="error-message">{errorMessage}</div>
                </template>
            </div>
            <template if:true={showDropDownList}>
                <div id={inputLabel} class="search-result-list" role="listbox" onmouseout={handleOutDropDownList} onmouseover={handleOverDropDownList} onblur={handleInputBlur}>
                    <template for:each={resultList} for:item="option" for:index="optionIdx">
                        <template if:true={option.category}>
                            <p key={option.category} class="category-search-result-list-header">{option.category}</p>
                        </template>
                        <template if:true={option.name}>
                            <button
                                key={option.uniqueKey}
                                id={option.uniqueKey}
                                data-id={option.uniqueKey}
                                role="option"
                                class={option.itemClassStyleName}
                                value={optionIdx}
                                onclick={handleClickItem}
                                tabindex="-1"
                            >
                                {option.name}
                            </button>
                        </template>
                        <template if:true={option.noResultWord}><p key={option.uniqueKey} class="no-result-style">{option.noResultWord}</p></template>
                        <template if:true={option.showBottomLine}>
                            <hr key={option.uniqueKey} class="category-search-result-bottom-line" />
                        </template>
                    </template>
                </div>
            </template>
        </div>
    </div>
</template>
