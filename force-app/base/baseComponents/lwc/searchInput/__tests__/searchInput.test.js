import { createElement } from "lwc";
import SearchInput from "c/searchInput";

describe("c-search-input", () => {
    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("display a lightning input when component init without parent component config", () => {
        const element = createElement("c-search-input", { is: SearchInput });
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector("input");
        expect(searchInput).not.toBeNull();

        const searchResult = element.shadowRoot.querySelector(".search-result-list");
        expect(searchResult).toBeNull();
    });

    it("display a lightning input when component init with parent component config", () => {
        const element = createElement("c-search-input", { is: SearchInput });
        element.options = [
            {
                category: "All Location",
                items: [
                    { id: 1, name: "china" },
                    { id: 2, name: "chile" },
                    { id: 3, name: "uk" }
                ]
            }
        ];
        element.inputValue = "china";
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector("input");
        expect(searchInput).not.toBeNull();
        expect(searchInput.value).toEqual("china");

        const searchResult = element.shadowRoot.querySelector(".search-result-list");
        expect(searchResult).toBeNull();
    });

    it("display initial result list when search input focus with parent component config", () => {
        const element = createElement("c-search-input", { is: SearchInput });
        element.options = [
            {
                category: "All Location",
                items: [
                    { id: 1, name: "china" },
                    { id: 2, name: "chile" },
                    { id: 3, name: "uk" }
                ]
            }
        ];
        element.inputValue = "china";
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector("input");
        searchInput.dispatchEvent(new CustomEvent("focus"));

        return Promise.resolve().then(() => {
            const searchResult = element.shadowRoot.querySelector(".search-result-list");
            expect(searchResult).not.toBeNull();

            const resultList = searchResult.querySelectorAll(".search-result-list-item");
            expect(resultList.length).toEqual(1);
            expect(resultList[0].value).toEqual("0");
            expect(resultList[0].textContent).toEqual("china");
        });
    });

    it("dispaly inital result list when search input focus without parent component config", () => {
        const element = createElement("c-search-input", { is: SearchInput });
        element.options = [
            {
                category: "All Location",
                items: [
                    { id: 1, name: "china" },
                    { id: 2, name: "chile" },
                    { id: 3, name: "uk" }
                ]
            }
        ];
        element.initialInputValue = "";
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector("input");
        searchInput.dispatchEvent(new CustomEvent("focus"));

        return Promise.resolve().then(() => {
            const searchResult = element.shadowRoot.querySelector(".search-result-list");
            expect(searchResult).not.toBeNull();

            const resultList = searchResult.querySelectorAll(".search-result-list-item");
            expect(resultList.length).toEqual(3);
            expect(resultList[0].value).toEqual("0");
            expect(resultList[0].textContent).toEqual("china");
            expect(resultList[1].value).toEqual("1");
            expect(resultList[1].textContent).toEqual("chile");
            expect(resultList[2].value).toEqual("2");
            expect(resultList[2].textContent).toEqual("uk");
        });
    });

    it("dispaly search result list when searh input change", () => {
        const element = createElement("c-search-input", { is: SearchInput });
        element.options = [
            {
                category: "All Location",
                items: [
                    { id: 1, name: "china" },
                    { id: 2, name: "chile" },
                    { id: 3, name: "uk" }
                ]
            }
        ];
        element.initialInputValue = "";
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector("input");
        searchInput.dispatchEvent(new CustomEvent("focus"));
        searchInput.value = "ch";
        searchInput.dispatchEvent(new CustomEvent("input"));

        return Promise.resolve().then(() => {
            const searchResult = element.shadowRoot.querySelector(".search-result-list");
            expect(searchResult).not.toBeNull();

            const resultList = searchResult.querySelectorAll(".search-result-list-item");
            expect(resultList.length).toEqual(2);
            expect(resultList[0].value).toEqual("0");
            expect(resultList[0].textContent).toEqual("china");
            expect(resultList[1].value).toEqual("1");
            expect(resultList[1].textContent).toEqual("chile");
        });
    });

    it("hide search result list when search input lost focus", () => {
        const element = createElement("c-search-input", { is: SearchInput });
        element.options = [
            {
                category: "All Location",
                items: [
                    { id: 1, name: "china" },
                    { id: 2, name: "chile" },
                    { id: 3, name: "uk" }
                ]
            }
        ];
        element.initialInputValue = "";
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector("input");
        searchInput.value = "";
        searchInput.dispatchEvent(new CustomEvent("focus"));
        searchInput.dispatchEvent(new CustomEvent("blur"));

        return Promise.resolve().then(() => {
            const searchResult = element.shadowRoot.querySelector(".search-result-list");
            expect(searchResult).toBeNull();
        });
    });
});
