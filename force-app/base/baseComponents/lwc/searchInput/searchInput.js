import { LightningElement, track, api } from "lwc";

const LOCATION_CATEGORY = "RECENT LOCATIONS, OTHER LOCATIONS";
const CATEGORY_SHOULD_BE_DISPLAYED = "MY ASSIGNMENT, MY PROJECTS, RECENT PROJECTS, GLOBAL PROJECTS, AVAILABLE PROJECTS";
const NO_MATCHING_RECORDS = "No matching result found";
const ITEM_CLASS_STYLE = "slds-button search-result-list-item";
const SELECTED_ITEM_CLASS_STYLE = "slds-button search-result-list-item background-hover";

const InputIconLocation = {
    Left: "left",
    Right: "right"
};

export default class SearchInput extends LightningElement {
    @api inputLabel = "";
    @api inputValue = null;
    @api disableInput = false;
    @api errorMessage = "";
    @api options = [];
    @api focusInput = false;
    @track resultList = [];
    @track showDropDownList = false;

    @api label = "";
    @api inputPlaceholder = "";

    @api inputIconLocation = InputIconLocation.Right;
    @api inputIconName = "utility:down";

    @api required = false
    @api mergeCategoryWhenSearch = false

    get inputIconLocationClass() {
        if (this.inputIconLocation === InputIconLocation.Left) {
            return "slds-input-has-icon_left";
        } else {
            return "slds-input-has-icon_right";
        }
    }

    get inputIconClass() {
        return `slds-input__icon ${this.inputIconLocationClass}`;
    }

    get inputClass() {
        return `slds-input-has-icon slds-combobox__form-element ${this.inputIconLocationClass}`;
    }

    isMouseOnDropDownList = false;
    selectedItemIndex = null;

    renderedCallback() {
        if (this.focusInput && !this.errorMessage && !this.disableInput && this.template.querySelector("input")) {
            this.template.querySelector("input").focus();
        }
    }

    get inputWrapperClassName() {
        return this.errorMessage ? "search-input-wrapper show-error-message" : "search-input-wrapper";
    }

    get searchInputClassName() {
        let disableStyle = this.disableInput ? "disable-search-input" : "";
        let triangleStyle = !this.inputLabel ? "project-down-position" : this.inputLabel === "Sub Project" ? "sub-proj-down-position" : "location-down-position";
        return disableStyle + " slds-input search-input " + triangleStyle;
    }

    formatAndFilterOptions(options, searchKey) {
        let tempOptions = JSON.parse(JSON.stringify(options));
        searchKey = searchKey ? searchKey.replace(/(^\s*)|(\s*$)/g, "") : "";
        if (searchKey) {
            tempOptions.forEach((singleCategoryList) => {
                singleCategoryList.items = singleCategoryList.items.filter((item) => item.name.toLowerCase().includes(searchKey.toLowerCase()));
            });
        }
        let formatedOptions = [];
        let isMergeCategorySelector = false;
        for (let index = 0; index < tempOptions.length; index++) {
            let singleCategory = tempOptions[index];
            let category = singleCategory.category;
            let items = singleCategory.items;

            isMergeCategorySelector = LOCATION_CATEGORY.includes(category) || this.mergeCategoryWhenSearch;

            if (items.length === 0) {
                formatedOptions.push({
                    category: CATEGORY_SHOULD_BE_DISPLAYED.includes(category) ? category : "",
                    noResultWord: searchKey || !singleCategory.initNoResultWord ? NO_MATCHING_RECORDS : singleCategory.initNoResultWord,
                    showBottomLine: true,
                    uniqueKey: category.replace(/\s/g, "_") + index
                });
            } else {
                for (let i = 0; i < items.length; i++) {
                    let newItem = { ...items[i] };
                    newItem.uniqueKey = (category + newItem.name).replace(/\s/g, "_") + i;
                    if (i === 0) {
                        newItem.category = CATEGORY_SHOULD_BE_DISPLAYED.includes(category) ? category : "";
                    }

                    if (i === items.length - 1 && index !== options.length - 1) {
                        newItem.showBottomLine = true;
                    }
                    formatedOptions.push(newItem);
                }
            }
        }
        if (isMergeCategorySelector) {
            formatedOptions = formatedOptions.filter((item) => !item.noResultWord);
            if (formatedOptions.length === 0) {
                formatedOptions.push({
                    category: "",
                    noResultWord: NO_MATCHING_RECORDS,
                    uniqueKey: "no_location"
                });
            }
        }
        this.initItemClassStyle(formatedOptions);
        return formatedOptions;
    }

    initItemClassStyle(options) {
        for (let index = 0; index < options.length; index++) {
            if (options[index].name) {
                options[index].itemClassStyleName = ITEM_CLASS_STYLE + " option-order" + index;
            }
        }
    }

    handleInputChanged(event) {
        this.showDropDownList = true;
        this.inputValue = event.target.value;
        this.resultList = [...this.formatAndFilterOptions(this.options, this.inputValue)];
        this.dispatchEvent(new CustomEvent("change", { detail: this.inputValue }));
    }

    handleClickItem(event) {
        this.handleSelectItem(event.target.value);
        this.handleComboboxAriaExpanded(false);
        this.handleAriaControls(false);
        this.handleAriActivedescendant("");
    }

    handleInputFocus(event) {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(event.target.select.bind(event.target), 10);
        this.resultList = [...this.formatAndFilterOptions(this.options, this.inputValue)];
        this.showDropDownList = true;
        this.handleComboboxAriaExpanded(true);
        this.dispatchEvent(new CustomEvent("focus"));
    }

    handleKeyDown(event) {
        if ((event.key !== "ArrowDown" && event.key !== "ArrowUp" && event.key !== "Enter") || this.isEmptyResult()) {
            return;
        }

        let ariaCondition = event.key !== "Enter";
        this.handleComboboxAriaExpanded(ariaCondition);
        this.handleAriaControls(ariaCondition);

        if (event.key === "ArrowDown" && !this.showDropDownList) {
            this.showDropDownList = true;
            this.resultList = [...this.formatAndFilterOptions(this.options, this.inputValue)];
            return;
        }

        if (this.selectedItemIndex !== null) {
            if (event.key === "Enter") {
                this.handleSelectItem(this.selectedItemIndex);
                this.handleAriActivedescendant("");
                return;
            }
            this.resultList[this.selectedItemIndex].itemClassStyleName = this.resultList[this.selectedItemIndex].itemClassStyleName.replace("background-hover", "");
        }
        this.selectedItemIndex = this.isEffectiveIndex(this.selectedItemIndex, event.key);

        if (this.selectedItemIndex !== null) {
            if (!this.resultList[this.selectedItemIndex].itemClassStyleName.includes("background-hover")) {
                this.resultList[this.selectedItemIndex].itemClassStyleName = this.resultList[this.selectedItemIndex].itemClassStyleName.replace(ITEM_CLASS_STYLE, SELECTED_ITEM_CLASS_STYLE);

                let uniqueKey = this.resultList[this.selectedItemIndex].uniqueKey;
                let optionEleId = this.template.querySelector(`[data-id="${uniqueKey}"]`).getAttribute("id");
                this.handleAriActivedescendant(optionEleId);
            }

            // eslint-disable-next-line @lwc/lwc/no-async-operation
            setTimeout(() => {
                const className = ".option-order" + this.selectedItemIndex;
                if (window.navigator.userAgent.indexOf("Firefox") >= 0) {
                    this.template.querySelector(className).scrollIntoView({ block: "nearest" });
                } else {
                    this.template.querySelector(className).scrollIntoViewIfNeeded(false);
                }
            }, 50);
        }
    }

    handleSelectItem(selectedIndex) {
        const item = this.resultList[selectedIndex];
        this.showDropDownList = false;
        this.selectedItemIndex = null;
        this.isMouseOnDropDownList = false;
        this.dispatchEvent(new CustomEvent("select", { detail: item }));
    }

    isEffectiveIndex(selectedIndex, arrowDirection) {
        if ((selectedIndex === null || selectedIndex === this.resultList.length - 1) && arrowDirection === "ArrowDown") {
            selectedIndex = 0;
        } else if (arrowDirection === "ArrowUp" && !selectedIndex) {
            selectedIndex = this.resultList.length - 1;
        } else {
            selectedIndex = arrowDirection === "ArrowDown" ? Number(selectedIndex) + 1 : Number(selectedIndex) - 1;
        }

        if (selectedIndex >= this.resultList.length || selectedIndex < 0) {
            return null;
        }
        if (this.resultList[selectedIndex].name) {
            return selectedIndex;
        }
        return this.isEffectiveIndex(selectedIndex, arrowDirection);
    }

    handleInputBlur() {
        if (!this.isMouseOnDropDownList) {
            this.showDropDownList = false;
            this.handleComboboxAriaExpanded(false);
            this.handleAriaControls(false);
            this.handleAriActivedescendant("");
            this.selectedItemIndex = null;
            this.dispatchEvent(new CustomEvent("blur"));
            return;
        }
        if (this.selectedItemIndex !== null && this.resultList[this.selectedItemIndex].itemClassStyleName) {
            this.resultList[this.selectedItemIndex].itemClassStyleName = this.resultList[this.selectedItemIndex].itemClassStyleName.replace("background-hover", "");
            this.selectedItemIndex = null;
        }
    }

    handleOutDropDownList() {
        this.isMouseOnDropDownList = false;
    }

    handleOverDropDownList() {
        this.isMouseOnDropDownList = true;
    }

    // combobox: aria-expanded
    handleComboboxAriaExpanded(isExpanded) {
        this.template.querySelector('[role="combobox"]').setAttribute("aria-expanded", isExpanded.toString());
    }

    // combobox: aria-controls
    handleAriaControls(needAriaControls) {
        let comboboxEle = this.template.querySelector('[role="combobox"]');
        if (needAriaControls) {
            let listboxEleId = this.template.querySelector('[role="listbox"]').getAttribute("id");
            comboboxEle.setAttribute("aria-controls", listboxEleId);
        } else {
            comboboxEle.removeAttribute("aria-controls");
        }
    }

    // combobox: aria-activedescendant
    handleAriActivedescendant(optionEleId) {
        let comboboxEle = this.template.querySelector('[role="combobox"]');
        if (optionEleId) {
            comboboxEle.setAttribute("aria-activedescendant", optionEleId);
        } else {
            comboboxEle.removeAttribute("aria-activedescendant");
        }
    }

    isEmptyResult() {
        let tempResults = JSON.parse(JSON.stringify(this.resultList));
        tempResults = tempResults.filter((item) => !item.noResultWord);
        return tempResults.length === 0;
    }
}
