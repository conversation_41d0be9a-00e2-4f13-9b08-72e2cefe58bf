.search-input-container {
    position: relative;
    font-size: 0.8rem;
}

.search-result-list {
    position: absolute;
    border: 1px solid rgb(221, 219, 218);
    background: #ffffff;
    border-radius: 3px;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 1000;
    margin-top: 0.5rem;
    overflow-y: auto;
    max-height: 11.3rem;
    outline: none;
}

.category-search-result-list-header {
    font-size: 0.9rem;
    color: #5b5b5b;
    opacity: 0.55;
    display: block;
    height: 2rem;
    line-height: 2rem;
    padding: 0.3rem 0.75rem 0 0.75rem;
}

.search-result-list-item {
    width: 100%;
    text-align: left;
    margin-left: 0;
    color: #403d3c;
    display: block;
    padding: 0 0.75rem;
    border-radius: 0;
}

.search-result-list-item:hover {
    background: #f3f2f2;
}

.background-hover {
    background: #f3f2f2;
}

.category-search-result-bottom-line {
    margin: 0;
}

.category-search-result-bottom-line:last-child {
    display: none;
}

.search-input-wrapper .search-input {
    margin-bottom: 0;
}

.search-input-wrapper .error-message {
    display: none;
    color: #c23934;
    height: 1rem;
    line-height: 1rem;
    margin-bottom: 0.35rem;
}

.empty-error-message {
    color: transparent;
    visibility: hidden;
    padding-left: 0.2rem;
    height: 1rem;
    line-height: 1rem;
}

.search-input-wrapper.show-error-message .search-input {
    border: 1px solid #c23934;
}

.search-input-wrapper.show-error-message .error-message {
    display: block;
}

.project-down-position {
    background-position-x: 98.5%;
}

.sub-proj-down-position {
    background-position-x: 97%;
}

.location-down-position {
    background-position-x: 95%;
}

.no-result-style {
    width: 100%;
    text-align: left;
    margin-left: 0;
    color: rgba(64, 61, 60, 0.2);
    font-size: 0.75rem;
    line-height: 1.875rem;
    padding: 0 0.75rem;
}

.disable-search-input {
    opacity: 0.5;
}
