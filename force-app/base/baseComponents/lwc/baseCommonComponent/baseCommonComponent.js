import { LightningElement, api } from "lwc";

export default class BaseCommonComponent extends LightningElement {
    @api imgResource;
    @api description;
    @api title;
    @api imgAlt = "no data";
    @api size = "small"; //small/ large
    connectedCallbackTest;
    num = 0;

    get emptyContainerClass() {
        return `slds-illustration ${this.size === "small" ? "slds-illustration_small" : "slds-illustration_large"}`;
    }

    plusNum() {
        this.num++;
        this.dispatchEvent(new CustomEvent("changenum", { detail: this.num }));
    }

    minusNum() {
        this.num--;
        this.dispatchEvent(new CustomEvent("changenum", { detail: this.num }));
    }

    connectedCallback() {
        this.connectedCallbackTest = "connectedCallbackTest";
    }
}
