import { createElement } from "lwc";
import BaseCommonComponent from "c/baseCommonComponent";
import { flushPromises } from "c/utils";

describe("c-base-common-component", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should render component", () => {
        // Arrange
        const element = createElement("c-base-common-component", {
            is: BaseCommonComponent
        });
        element.title = "title";
        // Act
        document.body.appendChild(element);

        // Assert
        expect(element).toMatchSnapshot();
    });

    it("should change num", async () => {
        // Arrange
        const element = createElement("c-base-common-component", {
            is: BaseCommonComponent
        });
        element.title = "title";
        const changeNum = jest.fn();
        element.addEventListener("changenum", changeNum);
        // Act
        document.body.appendChild(element);

        const plus = element.shadowRoot.querySelectorAll("lightning-button");
        plus[0].click();

        await flushPromises();
        // Assert
        expect(changeNum).toHaveBeenCalledTimes(1);
        expect(changeNum.mock.calls[0][0].detail).toBe(1);

        plus[1].click();
        await flushPromises();

        expect(changeNum).toHaveBeenCalledTimes(2);
        expect(changeNum.mock.calls[1][0].detail).toBe(0);
    });
});
