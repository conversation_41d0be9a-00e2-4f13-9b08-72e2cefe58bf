// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`c-base-common-component should render component 1`] = `
<c-base-common-component
  __lwc_scope_token__=""
>
  #shadow-root(open)
    <section
      __lwc_scope_token__=""
      class="slds-illustration slds-illustration_small"
    >
      <img
        __lwc_scope_token__=""
        alt="no data"
        class="slds-illustration__svg color-test"
      />
      <div
        __lwc_scope_token__=""
        class="slds-text-longform"
      >
        <h3
          __lwc_scope_token__=""
          class="slds-text-heading_medium"
        >
          title1
        </h3>
      </div>
    </section>
    <lightning-button
      __lwc_scope_token__=""
    >
      #shadow-root(open)
    </lightning-button>
    <lightning-button
      __lwc_scope_token__=""
    >
      #shadow-root(open)
    </lightning-button>
</c-base-common-component>
`;
