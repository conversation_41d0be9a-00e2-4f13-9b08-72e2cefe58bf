<template>
    <section class={emptyContainerClass}>
        <img class="slds-illustration__svg color-test" src={imgResource} alt={imgAlt} />
        <div class="slds-text-longform">
            <h3 class="slds-text-heading_medium" lwc:if={title}>{title}1</h3>
            <p class="slds-text-body_regular" lwc:if={description}>{description}</p>
            <p class="slds-text-body_regular" lwc:if={imgResource}>{connectedCallbackTest}</p>
        </div>
    </section>
    <lightning-button onclick={plusNum} variant="brand" label="Plus"></lightning-button>
    <lightning-button onclick={minusNum} variant="destructive" label="Minus"></lightning-button>
</template>
