<template>
    <div class="slds-grid slds-grid_vertical-align-center">
        <div
            if:true={typeAttributes.displayIconName}
            onmouseenter={typeAttributes.showPopover}
            onmouseleave={typeAttributes.disappearPopover}
            data-index={typeAttributes.index}
            class="popover-container"
        >
            <lightning-icon
                title="Not at account"
                class={typeAttributes.displayIconClass}
                alternative-text="Not at account"
                icon-name={typeAttributes.displayIconName}
                size={typeAttributes.displayIconSize}
            ></lightning-icon>
        </div>
        <lightning-formatted-url
            lwc:if={wrapText}
            class="slds-hyphenate"
            value={typeAttributes.link}
            tooltip={typeAttributes.displayName}
            label={typeAttributes.displayName}
            onclick={typeAttributes.callback}
        ></lightning-formatted-url>
        <lightning-formatted-url
            lwc:else
            class="slds-truncate"
            value={typeAttributes.link}
            tooltip={typeAttributes.displayName}
            label={typeAttributes.displayName}
            onclick={typeAttributes.callback}
        ></lightning-formatted-url>
    </div>
</template>
