<template>
    <template lwc:if={typeAttributes.shouldMergeCell}></template>
    <template lwc:else>
        <section class="slds-grid slds-grid_vertical-align-center gap4">
            <template lwc:if={typeAttributes.iconAttrs}>
                <template lwc:if={typeAttributes.iconAttrs.isShowIcon}>
                    <div onmouseenter={typeAttributes.showPopover} onmouseleave={typeAttributes.disappearPopover} data-index={typeAttributes.index}>
                        <lightning-icon class={typeAttributes.iconAttrs.iconClass} icon-name={typeAttributes.iconAttrs.iconName} size={typeAttributes.iconAttrs.iconSize}></lightning-icon>
                    </div>
                </template>
            </template>
            <template lwc:if={typeAttributes.image}>
                <span class="slds-avatar slds-avatar_circle avatar-container">
                    <span class="slds-icon_container slds-icon-standard-user" title="Profile avatar">
                        <img alt="Profile avatar" src={typeAttributes.image} title="Profile photo avatar" />
                    </span>
                </span>
            </template>
            <template lwc:if={typeAttributes.isDefaultPhoto}>
                <span class="slds-avatar slds-avatar_circle slds-avatar_profile-image-small avatar-container">
                    <span class="slds-icon_container slds-icon-standard-user" title="Profile avatar"> </span>
                </span>
            </template>
            <template lwc:if={typeAttributes.linkAttrs}>
                <lightning-formatted-url
                    lwc:if={wrapText}
                    class="slds-hyphenate"
                    value={typeAttributes.linkAttrs.link}
                    tooltip={typeAttributes.linkAttrs.linkDisplayName}
                    title={typeAttributes.linkAttrs.linkDisplayName}
                    label={typeAttributes.linkAttrs.linkDisplayName}
                ></lightning-formatted-url>
                <lightning-formatted-url
                    lwc:else
                    class="slds-truncate"
                    value={typeAttributes.linkAttrs.link}
                    tooltip={typeAttributes.linkAttrs.linkDisplayName}
                    title={typeAttributes.linkAttrs.linkDisplayName}
                    label={typeAttributes.linkAttrs.linkDisplayName}
                ></lightning-formatted-url>
            </template>
            <template lwc:if={typeAttributes.dateRangeAttrs}>
                <lightning-formatted-date-time value={typeAttributes.dateRangeAttrs.startDate}></lightning-formatted-date-time>
                -
                <lightning-formatted-date-time value={typeAttributes.dateRangeAttrs.endDate}></lightning-formatted-date-time>
            </template>
            <template lwc:if={typeAttributes.inactiveText}>
                <span lwc:if={wrapText} class="slds-hyphenate inactiveText" title={typeAttributes.inactiveText}>{typeAttributes.inactiveText}</span>
                <span lwc:else class="slds-truncate inactiveText" title={typeAttributes.inactiveText}>{typeAttributes.inactiveText}</span>
            </template>
            <template lwc:if={typeAttributes.text}>
                <span lwc:if={wrapText} class="slds-hyphenate" title={typeAttributes.text}>{typeAttributes.text}</span>
                <span lwc:else class="slds-truncate" title={typeAttributes.text}>{typeAttributes.text}</span>
            </template>
        </section>
    </template>
</template>
