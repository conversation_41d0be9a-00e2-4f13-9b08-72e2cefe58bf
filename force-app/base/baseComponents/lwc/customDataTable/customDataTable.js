import LightningDatatable from "lightning/datatable";
import iconRichTextTemplate from "./iconRichText";
import disableCheckbox from "./disableCheckbox";
import iconWithHoverPopover from "./iconWithHoverPopover";
import iconOnlyWithHoverPopover from "./iconOnlyWithHoverPopover";
import iconWithHoverPopoverForContact from "./iconWithHoverPopoverForContact";
import textWithIcon from "./textWithIcon";
import iconWithContactName from "./iconWithContactName";
import statusWithTooptip from "./statusWithTooptip";
import urlOrTextColumn from "./urlOrTextColumn";
import customColumn from "./customColumn";
import tagsColumn from "./tagsColumn.html";
import urlWithExternalIcon from "./urlWithExternalIcon";

export default class CustomDataTable extends LightningDatatable {
    static customTypes = {
        urlOrTextColumn: {
            template: urlOrTextColumn,
            standardCellLayout: true,
            typeAttributes: ["isLink", "value", "label"]
        },
        iconRichText: {
            template: iconRichTextTemplate,
            standardCellLayout: false,
            typeAttributes: ["iconName", "name"]
        },
        disableCheckbox: {
            template: disableCheckbox,
            standardCellLayout: true,
            typeAttributes: ["checked", "disabled", "callback", "index", "assistiveText"]
        },
        iconWithHoverPopover: {
            template: iconWithHoverPopover,
            standardCellLayout: true,
            typeAttributes: ["score", "iconClass", "iconName", "hiddenPopover", "showPopover", "disappearPopover", "displayCallback", "index", "lastSurveyResp", "trend"]
        },
        textWithIcon: {
            template: textWithIcon,
            standardCellLayout: true,
            typeAttributes: ["value", "iconClass", "iconName"]
        },
        iconOnlyWithHoverPopover: {
            template: iconOnlyWithHoverPopover,
            standardCellLayout: true,
            typeAttributes: ["iconClass", "iconName", "hiddenPopover", "showPopover", "disappearPopover", "index", "trend"]
        },
        iconWithHoverPopoverForContact: {
            template: iconWithHoverPopoverForContact,
            standardCellLayout: true,
            typeAttributes: ["scoreRelated", "showPopover", "disappearPopover", "index", "hiddenPopover"]
        },
        iconWithContactName: {
            template: iconWithContactName,
            standardCellLayout: true,
            typeAttributes: ["displayName", "link", "callback", "displayIconClass", "displayIconName", "displayIconSize", "showPopover", "disappearPopover"]
        },
        statusWithTooptip: {
            template: statusWithTooptip,
            standardCellLayout: true,
            typeAttributes: ["name", "color", "tooltip"]
        },
        //TypeAttributes number can not exceed 11
        customColumn: {
            template: customColumn,
            standardCellLayout: true,
            typeAttributes: ["shouldMergeCell", "image", "isDefaultPhoto", "linkAttrs", "dateRangeAttrs", "inactiveText", "text", "iconAttrs", "index", "showPopover", "disappearPopover"]
        },
        tagsColumn: {
            template: tagsColumn,
            standardCellLayout: true,
            typeAttributes: ["tags"]
        },
        urlWithExternalIcon: {
            template: urlWithExternalIcon,
            standardCellLayout: true,
            typeAttributes: ["value", "label"]
        }
    };
}
