<template>
    <div
        class="slds-grid slds-grid_vertical-align-center popover-container"
        onmouseenter={typeAttributes.showPopover}
        onmouseleave={typeAttributes.disappearPopover}
        data-hidden-popover={typeAttributes.hiddenPopover}
        data-index={typeAttributes.index}
        aria-hidden="false"
        aria-label={typeAttributes.scoreRelated.content}
    >
        <div if:true={typeAttributes.scoreRelated.iconName} class="icon-container">
            <lightning-icon class={typeAttributes.scoreRelated.iconClass} icon-name={typeAttributes.scoreRelated.iconName} size="xx-small"></lightning-icon>
        </div>
        <div>
            <lightning-formatted-text value={typeAttributes.scoreRelated.score}></lightning-formatted-text>
        </div>
    </div>
</template>
