<template>
    <div>
        <template for:each={typeAttributes.tags.labels} for:item="label" for:index="index">
            <template if:true={typeAttributes.tags.isError}>
                <span key={label} class="tag-badge tag-badge-error slds-m-around_xxx-small"> {label} </span>
            </template>
            <template if:false={typeAttributes.tags.isError}>
                <span key={label} class="tag-badge tag-badge-default slds-m-around_xxx-small"> {label} </span>
            </template>
        </template>
    </div>
</template>
