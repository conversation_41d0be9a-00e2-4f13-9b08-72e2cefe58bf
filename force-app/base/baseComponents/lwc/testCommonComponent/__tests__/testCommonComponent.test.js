import { createElement } from "lwc";
import TestCommonComponent from "c/testCommonComponent";
import { flushPromises } from "c/utils";
describe("c-test-common-component", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("test c-base-common-component event", async () => {
        // Arrange
        const element = createElement("c-test-common-component", {
            is: TestCommonComponent
        });

        // Act
        document.body.appendChild(element);

        // Assert
        const baseComponent = element.shadowRoot.querySelector("c-base-common-component");
        baseComponent.dispatchEvent(new CustomEvent("changenum", { detail: 5 }));
        await flushPromises();
        const input = element.shadowRoot.querySelector("lightning-input");
        expect(input.value).toBe(5);
    });
});
