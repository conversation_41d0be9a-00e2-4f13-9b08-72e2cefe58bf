import { createElement } from "lwc";
import PopoverWithIcon from "c/popoverWithIcon";
import { flushPromises } from "c/utils";
import { HIDE_ANIMATION_TIME, SHOW_ANIMATION_TIME } from "c/popoverWithIcon";

describe("c-popover-with-icon", () => {
    let realInnerWidth;
    let realInnerHeight;
    let element;

    const mouseOverEvent = new CustomEvent("mouseover", {
        bubbles: true
    });
    const mouseLeaveEvent = new CustomEvent("mouseleave", {
        bubbles: true
    });
    const focusInEvent = new CustomEvent("focusin", {
        bubbles: true
    });

    beforeEach(() => {
        realInnerWidth = window.innerWidth;
        realInnerHeight = window.innerHeight;
        global.innerWidth = 1000;
        global.innerHeight = 800;
        element = createElement("c-popover-with-icon", {
            is: PopoverWithIcon
        });
    });

    afterEach(() => {
        global.innerWidth = realInnerWidth;
        global.innerHeight = realInnerHeight;
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should popover match snapshot when init", () => {
        // Act
        document.body.appendChild(element);

        // Assert
        expect(element).toMatchSnapshot();
    });

    it("should popover show content when icon click", async () => {
        // Act
        document.body.appendChild(element);

        const icon = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');
        const popoverContentToHidden = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');
        expect(popoverContentToHidden).toBeNull();

        icon.dispatchEvent(new CustomEvent("click"));
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverContentToShown = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');
        expect(popoverContentToShown).not.toBeNull();
    });

    it("popover show correct content when user moves the mouse", async () => {
        // Act
        document.body.appendChild(element);

        const icon = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');
        // icon hover

        icon.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverContentToShown = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');
        expect(popoverContentToShown).not.toBeNull();

        // icon leave
        icon.dispatchEvent(mouseLeaveEvent);
        await flushPromises(HIDE_ANIMATION_TIME);

        const popoverContentToHidden = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');
        expect(popoverContentToHidden).toBeNull();
    });

    it("should calculate and set custom position is right", async () => {
        document.body.appendChild(element);

        const iconInstance = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverInstance = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        iconInstance.getBoundingClientRect = jest.fn(() => ({
            x: screenWidth / 2 - 10,
            y: screenHeight / 2 - 10,
            width: 20,
            height: 20
        }));
        popoverInstance.getBoundingClientRect = jest.fn(() => ({
            width: 100,
            height: 50
        }));

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        expect(popoverInstance.style.right).toBe("380px");
        expect(popoverInstance.style.top).toBe("375px");

        iconInstance.dispatchEvent(mouseLeaveEvent);
    });

    it("should calculate and set custom position is left", async () => {
        document.body.appendChild(element);

        const iconInstance = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverInstance = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');
        const screenWidth = window.innerWidth;

        popoverInstance.getBoundingClientRect = jest.fn(() => ({
            width: 100,
            height: 50
        }));

        iconInstance.getBoundingClientRect = jest.fn(() => ({
            x: screenWidth - 30,
            y: 10,
            width: 20,
            height: 20
        }));

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        expect(popoverInstance.style.left).toBe("860px");
        expect(popoverInstance.style.top).toBe("5px");
    });

    it("should calculate and set custom position is top", async () => {
        document.body.appendChild(element);

        const iconInstance = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverInstance = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');

        iconInstance.getBoundingClientRect = jest.fn(() => ({
            x: 500,
            y: 100,
            width: 20,
            height: 20
        }));
        popoverInstance.getBoundingClientRect = jest.fn(() => ({
            width: 500,
            height: 50
        }));

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);
        expect(popoverInstance.style.left).toBe("260px");
        expect(popoverInstance.style.top).toBe("40px");
    });

    it("should calculate and set custom position is bottom", async () => {
        document.body.appendChild(element);

        const iconInstance = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverInstance = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');

        iconInstance.getBoundingClientRect = jest.fn(() => ({
            x: 500,
            y: 10,
            width: 20,
            height: 20
        }));
        popoverInstance.getBoundingClientRect = jest.fn(() => ({
            width: 500,
            height: 50
        }));

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);
        expect(popoverInstance.style.left).toBe("260px");
        expect(popoverInstance.style.bottom).toBe("710px");

        iconInstance.dispatchEvent(focusInEvent);
        element.resetPosition();
        await flushPromises(HIDE_ANIMATION_TIME);
    });

    it("should calculate and set custom position top is 0", async () => {
        global.innerHeight = 50;
        document.body.appendChild(element);

        const iconInstance = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popoverInstance = element.shadowRoot.querySelector('[data-id="popover-instance-id"]');

        iconInstance.getBoundingClientRect = jest.fn(() => ({
            x: 500,
            y: 10,
            width: 20,
            height: 20
        }));
        popoverInstance.getBoundingClientRect = jest.fn(() => ({
            width: 500,
            height: 50
        }));

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);
        expect(popoverInstance.style.left).toBe("260px");
        expect(popoverInstance.style.top).toBe("0px");
    });

    it("should hide tooltip on scroll", async () => {
        document.body.appendChild(element);

        const iconInstance = element.shadowRoot.querySelector('[data-id="popover-icon-instance-id"]');

        iconInstance.dispatchEvent(mouseOverEvent);
        await flushPromises(SHOW_ANIMATION_TIME);

        const popElemToShow = element.shadowRoot.querySelector(".popover-wrapper");
        expect(popElemToShow).not.toBeNull();

        window.dispatchEvent(
            new CustomEvent("scroll", {
                detail: {}
            })
        );

        await flushPromises(HIDE_ANIMATION_TIME);
        const popElemToHidden = element.shadowRoot.querySelector(".popover-wrapper");
        expect(popElemToHidden).toBeNull();
    });
});
