<template>
    <div class="popover-container">
        <div
            if:true={isShowPopover}
            style={customPosition}
            id="popover-body"
            data-id="popover-instance-id"
            onmouseover={handleContentMouseOver}
            onmouseleave={handleContentMouseLeave}
            class="popover-wrapper"
            role="dialog"
            tabindex="0"
        >
            <slot name="popover-content">popover content</slot>
        </div>

        <div
            data-id="popover-icon-instance-id"
            role="text"
            tabindex="0"
            onclick={showPopover}
            onfocusin={hidePopover}
            aria-label={triggerLabel}
            aria-haspopup="dialog"
            onmouseover={showPopover}
            onmouseleave={hidePopover}
        >
            <slot name="popover-icon">popover icon</slot>
        </div>
    </div>
</template>
