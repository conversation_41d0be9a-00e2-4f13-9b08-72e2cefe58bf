import { api, LightningElement } from "lwc";

const PADDING_WITh_ICON = 10;
const PADDING_WITh_WINDOW = 5;

export const SHOW_ANIMATION_TIME = 200;
export const HIDE_ANIMATION_TIME = 150;

export default class PopoverWithIcon extends LightningElement {
    isShowPopover = false;
    hideInterval = null;
    showInterval = null;
    customPosition = "";
    @api
    triggerLabel = "";
    onResetPosition;

    calculateVerticalPosition(icon, popOver, screenHeight) {
        const iconCenterYPosition = icon.y + icon.height / 2;
        const topSpace = iconCenterYPosition - (popOver.height / 2 + PADDING_WITh_WINDOW);
        const bottomSpace = screenHeight - iconCenterYPosition - (popOver.height / 2 + PADDING_WITh_WINDOW);

        if (topSpace < 0) {
            return `top:${PADDING_WITh_WINDOW}px`;
        }

        if (bottomSpace < 0) {
            return `bottom:${PADDING_WITh_WINDOW}px`;
        }

        return `top:${iconCenterYPosition - popOver.height / 2}px`;
    }

    calculateHorizonPosition(icon, popOver, screenWidth) {
        const iconCenterXPosition = icon.x + icon.width / 2;

        const lefSpace = iconCenterXPosition - (popOver.width / 2 + PADDING_WITh_WINDOW);
        const rightSpace = screenWidth - iconCenterXPosition - (popOver.width / 2 + PADDING_WITh_WINDOW);

        if (lefSpace < 0) {
            return `left:${PADDING_WITh_WINDOW}px`;
        }

        if (rightSpace < 0) {
            return `right:${PADDING_WITh_WINDOW}px`;
        }

        return `left:${iconCenterXPosition - popOver.width / 2}px`;
    }

    setPosition() {
        const popover = this.template.querySelector('[data-id="popover-instance-id"]').getBoundingClientRect();
        const icon = this.template.querySelector('[data-id="popover-icon-instance-id"]').getBoundingClientRect();
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        const rightSpace = screenWidth - (icon.x + icon.width + popover.width + PADDING_WITh_ICON + PADDING_WITh_WINDOW);
        const lefSpace = icon.x - (popover.width + PADDING_WITh_ICON + PADDING_WITh_WINDOW);

        if (rightSpace >= 0) {
            // right
            this.customPosition = `right: ${rightSpace + PADDING_WITh_WINDOW}px;` + this.calculateVerticalPosition(icon, popover, screenHeight);
        } else if (lefSpace >= 0) {
            // left
            this.customPosition = `left: ${lefSpace + PADDING_WITh_WINDOW}px;` + this.calculateVerticalPosition(icon, popover, screenHeight);
        } else {
            // top or bottom
            const topSpace = icon.y - (popover.height + PADDING_WITh_WINDOW + PADDING_WITh_ICON);
            const bottomSpace = screenHeight - (icon.y + icon.height + popover.height + PADDING_WITh_WINDOW + PADDING_WITh_ICON);

            if (topSpace >= 0) {
                // top
                this.customPosition = `top: ${topSpace + PADDING_WITh_WINDOW}px;` + this.calculateHorizonPosition(icon, popover, screenWidth);
            } else if (bottomSpace >= 0) {
                // bottom
                this.customPosition = `bottom: ${bottomSpace + PADDING_WITh_WINDOW}px;` + this.calculateHorizonPosition(icon, popover, screenWidth);
            } else {
                this.customPosition = "top: 0;" + this.calculateHorizonPosition(icon, popover, screenWidth);
            }
        }
    }

    @api
    resetPosition() {
        this.onResetPosition = true;
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        requestAnimationFrame(() => this.setPosition());
    }

    handleContentMouseOver = () => {
        clearTimeout(this.hideInterval);
        clearTimeout(this.showInterval);
        this.onResetPosition = false;
    };

    handleContentMouseLeave = () => {
        if (!this.onResetPosition) {
            this.hidePopover();
        }
    };

    hidePopover = () => {
        clearTimeout(this.showInterval);
        clearTimeout(this.hideInterval);

        if (this.isShowPopover) {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            this.hideInterval = setTimeout(() => {
                this.isShowPopover = false;
                window.removeEventListener("scroll", this.hidePopover);
            }, HIDE_ANIMATION_TIME);
        }
    };

    showPopover = () => {
        clearTimeout(this.hideInterval);
        clearTimeout(this.showInterval);
        if (!this.isShowPopover) {
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            this.showInterval = setTimeout(() => {
                this.dispatchEvent(new CustomEvent("hover"));

                // eslint-disable-next-line @lwc/lwc/no-async-operation
                requestAnimationFrame(() => {
                    this.template.querySelector(".popover-wrapper").focus();
                    this.setPosition();
                });
                this.isShowPopover = true;
                window.addEventListener("scroll", this.hidePopover);
            }, SHOW_ANIMATION_TIME);
        }
    };
}
