<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>58.0</apiVersion>
    <description>Matomo Page Tracker With User Profile</description>
    <isExposed>true</isExposed>
    <masterLabel>Matomo Page Tracker With User Profile</masterLabel>
    <targets>
        <target>lightning__AppPage</target>
        <target>lightning__HomePage</target>
        <target>lightning__RecordPage</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__AppPage">
            <property name="matomoCategory" type="String" label="Matomo Category" description="Matomo Category"/>
            <property name="matomoAction" type="String" label="Matomo Action" description="Matomo Action"/>
            <supportedFormFactors>
                <supportedFormFactor type="Large"/>
                <!-- <supportedFormFactor type="Small" /> -->
            </supportedFormFactors>
        </targetConfig>
        <targetConfig targets="lightning__HomePage">
            <property name="matomoCategory" type="String" label="Matomo Category" description="Matomo Category"/>
            <property name="matomoAction" type="String" label="Matomo Action" description="Matomo Action"/>
            <supportedFormFactors>
                <supportedFormFactor type="Large"/>
                <!-- <supportedFormFactor type="Small" /> -->
            </supportedFormFactors>
        </targetConfig>
        <targetConfig targets="lightning__RecordPage">
            <property name="matomoCategory" type="String" label="Matomo Category" description="Matomo Category"/>
            <property name="matomoAction" type="String" label="Matomo Action" description="Matomo Action"/>
            <supportedFormFactors>
                <supportedFormFactor type="Large"/>
                <!-- <supportedFormFactor type="Small" /> -->
            </supportedFormFactors>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
