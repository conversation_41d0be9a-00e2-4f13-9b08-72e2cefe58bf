/**
 * Created by ziji<PERSON>.wang on 2023/7/7.
 */

import { api, LightningElement } from "lwc";
import { trackCRMUserEvent } from "c/matomoTracker";
import OPERATOR_ID from "@salesforce/user/Id";
import getUserProfileNameAndEmployeeNumberAndRoleId from "@salesforce/apex/MKTUserController.getUserProfileNameAndEmployeeNumberAndRoleId";

export default class MatomoPageTrackerWithUserProfile extends LightningElement {
    @api
    recordId;
    @api
    matomoCategory;
    @api
    matomoAction;

    connectedCallback() {
        getUserProfileNameAndEmployeeNumberAndRoleId({ userId: OPERATOR_ID })
            .then((userProfileNameAndEmployeeNumber) => {
                trackCRMUserEvent({
                    category: this.matomoCategory,
                    action: this.matomoAction,
                    name: userProfileNameAndEmployeeNumber.ProfileName,
                    value: userProfileNameAndEmployeeNumber.EmployeeNumber
                });
            })
            .catch((error) => {
                console.error("Error getting user profile name:", error);
            });
    }
}
