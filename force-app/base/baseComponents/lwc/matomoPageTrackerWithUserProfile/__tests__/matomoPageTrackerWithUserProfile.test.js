/**
 * Created by ziji<PERSON>.wang on 2023/7/7.
 */

import { createElement } from "lwc";
import MatomoPageTrackerWithUserProfile from "c/matomoPageTrackerWithUserProfile";
import { flushPromises } from "c/utils";
import { trackCRMUserEvent } from "c/matomoTracker";
import getUserProfileNameAndEmployeeNumberAndRoleId from "@salesforce/apex/MKTUserController.getUserProfileNameAndEmployeeNumberAndRoleId";

jest.mock(
    "@salesforce/apex/MKTUserController.getUserProfileNameAndEmployeeNumberAndRoleId",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

describe("c-matomo-page-tracker-with-user-profile", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    it("should track matomo event", async () => {
        // Arrange
        getUserProfileNameAndEmployeeNumberAndRoleId.mockResolvedValue({
            ProfileName: "profile name",
            EmployeeNumber: "employee number"
        });
        const element = createElement("c-matomo-page-tracker-with-user-profile", {
            is: MatomoPageTrackerWithUserProfile
        });
        element.recordId = "0066300000MKn9TAAT";
        element.matomoCategory = "matomo category";
        element.matomoAction = "matomo action";
        // Act
        document.body.appendChild(element);
        await flushPromises();
        // Assert
        expect(trackCRMUserEvent).toHaveBeenCalledTimes(1);
        expect(trackCRMUserEvent.mock.calls[0][0]).toStrictEqual({
            category: "matomo category",
            action: "matomo action",
            name: "profile name",
            value: "employee number"
        });
    });
});
