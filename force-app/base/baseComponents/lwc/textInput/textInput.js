import { LightningElement, api, track } from "lwc";

const MAX_INPUT_LENGTH = 255;
export default class TextInput extends LightningElement {
    @api inputName;
    @api disableInput;

    @api
    set inputValue(value) {
        if (this.template.querySelector("textarea") && this.template.querySelector("textarea").value !== value) {
            this.template.querySelector("textarea").value = value ? value : "";
        }
        this._inputValue = value;
    }

    get inputValue() {
        return this._inputValue;
    }

    get textInputClassStyle() {
        return this.disableInput ? "text-area-style disable-style" : "text-area-style";
    }

    @track _inputValue;

    handleInputKeyUp(event) {
        this._inputValue = this.trimInvalidInput(event.target.value);
        this.dispatchEvent(new CustomEvent("textchange", { detail: { name: event.target.name, value: this.inputValue } }));
    }

    handleInputChange(event) {
        this._inputValue = event.target.value;
    }

    trimInvalidInput(inputText) {
        let value = inputText.replace(/[^\x00-\xff]/g, "");
        if (value.length > MAX_INPUT_LENGTH) {
            value = value.slice(0, MAX_INPUT_LENGTH);
        }
        return value;
    }
}
