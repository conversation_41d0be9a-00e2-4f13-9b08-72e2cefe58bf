.text-area-style {
    height: 2rem;
    padding: 0.35rem 0.5rem;
    background-color: rgb(255, 255, 255);
    border: 1px solid rgb(221, 219, 218);
    border-radius: 0.25rem;
    width: 100%;
    outline-width: 3px;
    resize: vertical;
    max-height: 80px;
    min-height: 32px;
    overflow-y: auto;
}

.disable-style {
    background-color: rgb(236, 235, 234);
    border-color: rgb(201, 199, 197);
    cursor: not-allowed;
    opacity: 0.5;
}

.text-area-style:focus {
    outline: 0;
    border-color: var(--lwc-colorBorderInputActive, rgb(21, 137, 238));
    background-color: var(--lwc-colorBackgroundInputActive, rgb(255, 255, 255));
    box-shadow: var(--lwc-shadowButtonFocus, 0 0 3px #0070d2);
}
