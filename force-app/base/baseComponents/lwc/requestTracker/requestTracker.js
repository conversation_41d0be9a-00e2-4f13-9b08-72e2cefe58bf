/**
 * 请求追踪器
 * 用于管理请求ID和验证请求有效性
 */
export class RequestTracker {
    constructor() {
        this._requestIds = new Map();
    }

    /**
     * 生成新的请求ID
     * @param {String} key - 请求的唯一标识符
     * @returns {Number} 请求ID
     */
    generateRequestId(key) {
        const requestId = Date.now();
        this._requestIds.set(key, requestId);
        return requestId;
    }

    /**
     * 验证请求ID是否是最新的
     * @param {String} key - 请求的唯一标识符
     * @param {Number} requestId - 要验证的请求ID
     * @returns {Boolean} 是否是最新请求
     */
    isLatestRequest(key, requestId) {
        return this._requestIds.get(key) === requestId;
    }

    /**
     * 包装异步请求函数
     * @param {String} key - 请求的唯一标识符
     * @param {Function} requestFn - 异步请求函数
     * @param {Object} options - 配置选项
     * @returns {Promise} 包装后的Promise
     */
    wrapRequest(key, requestFn, options = {}) {
        const requestId = this.generateRequestId(key);
        const { onSuccess, onError, validateResponse = true } = options;

        return new Promise((resolve, reject) => {
            requestFn()
                .then(result => {
                    // 如果不需要验证响应或者是最新请求，则处理结果
                    if (!validateResponse || this.isLatestRequest(key, requestId)) {
                        if (onSuccess) {
                            onSuccess(result);
                        }
                        resolve(result);
                    }
                })
                .catch(error => {
                    // 如果不需要验证响应或者是最新请求，则处理错误
                    if (!validateResponse || this.isLatestRequest(key, requestId)) {
                        if (onError) {
                            console.error('Error:', error);
                            onError(error);
                        }
                        reject(error);
                    }
                });
        });
    }

    /**
     * 包装批量请求函数
     * @param {String} key - 请求的唯一标识符
     * @param {Function} batchRequestFn - 批量请求函数
     * @param {Object} options - 配置选项
     * @returns {Promise} 包装后的Promise
     */
    wrapBatchRequest(key, batchRequestFn, options = {}) {
        const requestId = this.generateRequestId(key);
        const {
            onSuccess,
            onError,
            onProgress,
            validateResponse = true,
            accumulator = (total, current) => total + (current.count || 0),
            initialValue = 0
        } = options;

        let total = initialValue;

        const executeBatch = async (params) => {
            try {
                const result = await batchRequestFn(params);
                
                // 如果不是最新请求，直接返回
                if (validateResponse && !this.isLatestRequest(key, requestId)) {
                    return null;
                }

                // 累加结果
                total = accumulator(total, result);

                // 通知进度
                if (onProgress) {
                    onProgress(total, result.hasMoreRecords);
                }

                // 如果还有更多记录，继续请求
                if (result.hasMoreRecords && result.lastId) {
                    return executeBatch({ ...params, lastId: result.lastId });
                }

                // 完成所有批次
                if (onSuccess) {
                    onSuccess(total);
                }
                return total;
            } catch (error) {
                // 如果不是最新请求，忽略错误
                if (validateResponse && !this.isLatestRequest(key, requestId)) {
                    return null;
                }

                if (onError) {
                    onError(error);
                }
                throw error;
            }
        };

        return executeBatch;
    }

    /**
     * 清除请求ID
     * @param {String} key - 请求的唯一标识符
     */
    clearRequest(key) {
        this._requestIds.delete(key);
    }

    /**
     * 清除所有请求ID
     */
    clearAllRequests() {
        this._requestIds.clear();
    }
}

// 创建单例实例
const requestTracker = new RequestTracker();
export default requestTracker; 