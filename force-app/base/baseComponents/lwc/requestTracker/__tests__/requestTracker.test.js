import { RequestTracker } from "c/requestTracker";
import { flushPromises } from "c/utils";

describe("c-request-tracker", () => {
    let requestTracker;

    // Set up a fresh RequestTracker instance before each test
    beforeEach(() => {
        requestTracker = new RequestTracker();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    // Test generateRequestId method
    it("given key when generateRequestId then returns new requestId and stores it", () => {
        // Mock Date.now() to return a consistent value for testing
        const mockDateNow = jest.spyOn(Date, "now").mockReturnValue(12345);
        
        const key = "testRequest";
        const requestId = requestTracker.generateRequestId(key);
        
        expect(requestId).toBe(12345);
        expect(requestTracker._requestIds.get(key)).toBe(12345);
        
        mockDateNow.mockRestore();
    });

    // Test isLatestRequest method
    it("given key and requestId when isLatestRequest then returns true for latest request", () => {
        const key = "testRequest";
        const requestId = 12345;
        
        // Setup - store the requestId
        requestTracker._requestIds.set(key, requestId);
        
        // When/Then
        expect(requestTracker.isLatestRequest(key, requestId)).toBe(true);
        expect(requestTracker.isLatestRequest(key, 54321)).toBe(false);
        expect(requestTracker.isLatestRequest("otherKey", requestId)).toBe(false);
    });

    // Test wrapRequest method with successful request
    it("given successful request when wrapRequest then resolves with result for latest request", async () => {
        const key = "testRequest";
        const mockResult = { data: "test data" };
        const requestFn = jest.fn().mockResolvedValue(mockResult);
        const onSuccess = jest.fn();
        
        // Mock generateRequestId to return a known value
        jest.spyOn(requestTracker, "generateRequestId").mockReturnValue(12345);
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(true);
        
        const promise = requestTracker.wrapRequest(key, requestFn, { onSuccess });
        
        await flushPromises();
        
        expect(requestFn).toHaveBeenCalled();
        expect(onSuccess).toHaveBeenCalledWith(mockResult);
        
        const result = await promise;
        expect(result).toBe(mockResult);
    });

    // Test wrapRequest method with failed request
    it("given failed request when wrapRequest then rejects with error for latest request", async () => {
        const key = "testRequest";
        const mockError = new Error("Test error");
        const requestFn = jest.fn().mockRejectedValue(mockError);
        const onError = jest.fn();
        
        // Mock console.error to prevent test output noise
        jest.spyOn(console, "error").mockImplementation(() => {});
        
        // Mock isLatestRequest to return true
        jest.spyOn(requestTracker, "generateRequestId").mockReturnValue(12345);
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(true);
        
        const promise = requestTracker.wrapRequest(key, requestFn, { onError });
        
        await expect(promise).rejects.toEqual(mockError);
        expect(requestFn).toHaveBeenCalled();
        expect(onError).toHaveBeenCalledWith(mockError);
        expect(console.error).toHaveBeenCalled();
    });

    // Test wrapRequest method with outdated request
    it("given outdated request when wrapRequest then ignores request result", async () => {
        const key = "testRequest";
        const mockResult = { data: "test data" };
        const requestFn = jest.fn().mockResolvedValue(mockResult);
        const onSuccess = jest.fn();
        
        // Mock generateRequestId but make isLatestRequest return false
        jest.spyOn(requestTracker, "generateRequestId").mockReturnValue(12345);
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(false);
        
        const promise = requestTracker.wrapRequest(key, requestFn, { onSuccess });
        
        await flushPromises();
        
        expect(requestFn).toHaveBeenCalled();
        expect(onSuccess).not.toHaveBeenCalled();
        
        // The promise doesn't resolve or reject, it just hangs
        // Need to use a race with a timeout to test this
        const result = await Promise.race([
            promise,
            // eslint-disable-next-line @lwc/lwc/no-async-operation
            new Promise(resolve => setTimeout(() => resolve("timeout"), 100))
        ]);
        
        expect(result).toBe("timeout");
    });

    // Test wrapRequest with validateResponse=false
    it("given request with validateResponse=false when wrapRequest then always processes result", async () => {
        const key = "testRequest";
        const mockResult = { data: "test data" };
        const requestFn = jest.fn().mockResolvedValue(mockResult);
        const onSuccess = jest.fn();
        
        // Mock isLatestRequest to return false, but we'll set validateResponse to false
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(false);
        
        const result = await requestTracker.wrapRequest(key, requestFn, { 
            onSuccess, 
            validateResponse: false 
        });
        
        expect(requestFn).toHaveBeenCalled();
        expect(onSuccess).toHaveBeenCalledWith(mockResult);
        expect(result).toBe(mockResult);
    });

    // Test wrapBatchRequest method
    it("given batch request when wrapBatchRequest then returns batch executor function", async () => {
        const key = "batchRequest";
        const batchRequestFn = jest.fn();
        const onSuccess = jest.fn();
        const onError = jest.fn();
        const onProgress = jest.fn();
        
        // Mock generateRequestId
        jest.spyOn(requestTracker, "generateRequestId").mockReturnValue(12345);
        
        const batchExecutor = requestTracker.wrapBatchRequest(key, batchRequestFn, {
            onSuccess,
            onError,
            onProgress
        });
        
        expect(typeof batchExecutor).toBe("function");
        expect(requestTracker.generateRequestId).toHaveBeenCalledWith(key);
    });

    // Test wrapBatchRequest execution with single batch
    it("given single batch request when executeBatch then processes result correctly", async () => {
        const key = "batchRequest";
        const mockResult = { 
            count: 10, 
            hasMoreRecords: false 
        };
        const batchRequestFn = jest.fn().mockResolvedValue(mockResult);
        const onSuccess = jest.fn();
        const onProgress = jest.fn();
        
        // Mock isLatestRequest to return true
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(true);
        
        const batchExecutor = requestTracker.wrapBatchRequest(key, batchRequestFn, {
            onSuccess,
            onProgress
        });
        
        const params = { size: 10 };
        const result = await batchExecutor(params);
        
        expect(batchRequestFn).toHaveBeenCalledWith(params);
        expect(onProgress).toHaveBeenCalledWith(10, false);
        expect(onSuccess).toHaveBeenCalledWith(10);
        expect(result).toBe(10);
    });

    // Test wrapBatchRequest execution with multiple batches
    it("given multiple batch requests when executeBatch then processes all batches", async () => {
        const key = "batchRequest";
        const mockResults = [
            { count: 10, hasMoreRecords: true, lastId: "id1" },
            { count: 5, hasMoreRecords: false }
        ];
        
        const batchRequestFn = jest.fn()
            .mockResolvedValueOnce(mockResults[0])
            .mockResolvedValueOnce(mockResults[1]);
            
        const onSuccess = jest.fn();
        const onProgress = jest.fn();
        
        // Mock isLatestRequest to return true
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(true);
        
        const batchExecutor = requestTracker.wrapBatchRequest(key, batchRequestFn, {
            onSuccess,
            onProgress,
            initialValue: 0
        });
        
        const params = { size: 10 };
        const result = await batchExecutor(params);
        
        expect(batchRequestFn).toHaveBeenCalledTimes(2);
        expect(batchRequestFn).toHaveBeenNthCalledWith(1, params);
        expect(batchRequestFn).toHaveBeenNthCalledWith(2, { ...params, lastId: "id1" });
        
        expect(onProgress).toHaveBeenCalledTimes(2);
        expect(onProgress).toHaveBeenNthCalledWith(1, 10, true);
        expect(onProgress).toHaveBeenNthCalledWith(2, 15, false);
        
        expect(onSuccess).toHaveBeenCalledWith(15);
        expect(result).toBe(15);
    });

    // Test wrapBatchRequest with custom accumulator
    it("given custom accumulator when wrapBatchRequest then uses custom accumulation logic", async () => {
        const key = "batchRequest";
        const mockResults = [
            { items: [1, 2, 3], hasMoreRecords: true, lastId: "id1" },
            { items: [4, 5], hasMoreRecords: false }
        ];
        
        const batchRequestFn = jest.fn()
            .mockResolvedValueOnce(mockResults[0])
            .mockResolvedValueOnce(mockResults[1]);
            
        const customAccumulator = (total, current) => [...total, ...current.items];
        
        // Mock isLatestRequest to return true
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(true);
        
        const batchExecutor = requestTracker.wrapBatchRequest(key, batchRequestFn, {
            accumulator: customAccumulator,
            initialValue: []
        });
        
        const result = await batchExecutor({});
        
        expect(result).toEqual([1, 2, 3, 4, 5]);
    });

    // Test wrapBatchRequest with error
    it("given batch request error when executeBatch then handles error correctly", async () => {
        const key = "batchRequest";
        const mockError = new Error("Batch request failed");
        const batchRequestFn = jest.fn().mockRejectedValue(mockError);
        const onError = jest.fn();
        
        // Mock isLatestRequest to return true
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(true);
        
        const batchExecutor = requestTracker.wrapBatchRequest(key, batchRequestFn, {
            onError
        });
        
        await expect(batchExecutor({})).rejects.toEqual(mockError);
        expect(onError).toHaveBeenCalledWith(mockError);
    });

    // Test wrapBatchRequest with outdated request
    it("given outdated batch request when executeBatch then ignores results", async () => {
        const key = "batchRequest";
        const mockResult = { count: 10, hasMoreRecords: false };
        const batchRequestFn = jest.fn().mockResolvedValue(mockResult);
        const onSuccess = jest.fn();
        const onProgress = jest.fn();
        
        // Mock isLatestRequest to return false
        jest.spyOn(requestTracker, "isLatestRequest").mockReturnValue(false);
        
        const batchExecutor = requestTracker.wrapBatchRequest(key, batchRequestFn, {
            onSuccess,
            onProgress
        });
        
        const result = await batchExecutor({});
        
        expect(batchRequestFn).toHaveBeenCalled();
        expect(onProgress).not.toHaveBeenCalled();
        expect(onSuccess).not.toHaveBeenCalled();
        expect(result).toBeNull();
    });

    // Test clearRequest method
    it("given key when clearRequest then removes request from tracker", () => {
        const key = "testRequest";
        requestTracker._requestIds.set(key, 12345);
        
        requestTracker.clearRequest(key);
        
        expect(requestTracker._requestIds.has(key)).toBe(false);
    });

    // Test clearAllRequests method
    it("given multiple requests when clearAllRequests then removes all requests", () => {
        requestTracker._requestIds.set("request1", 12345);
        requestTracker._requestIds.set("request2", 67890);
        
        requestTracker.clearAllRequests();
        
        expect(requestTracker._requestIds.size).toBe(0);
    });
});

// Test the default singleton instance
describe("c-request-tracker default instance", () => {
    it("given default import when used then is singleton instance", async () => {
        // Import the default export (the singleton instance)
        const defaultRequestTracker = require("c/requestTracker").default;
        
        // Import the RequestTracker class to create a new instance for comparison
        const newRequestTracker = new RequestTracker();
        
        // The two should be different instances
        expect(defaultRequestTracker).not.toBe(newRequestTracker);
        
        // But they should have the same methods
        expect(typeof defaultRequestTracker.generateRequestId).toBe("function");
        expect(typeof defaultRequestTracker.isLatestRequest).toBe("function");
        expect(typeof defaultRequestTracker.wrapRequest).toBe("function");
        expect(typeof defaultRequestTracker.wrapBatchRequest).toBe("function");
        expect(typeof defaultRequestTracker.clearRequest).toBe("function");
        expect(typeof defaultRequestTracker.clearAllRequests).toBe("function");
    });
}); 