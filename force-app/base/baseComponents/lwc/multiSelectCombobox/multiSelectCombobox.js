import { LightningElement, api, track } from "lwc";
export default class MultiSelectCombobox extends LightningElement {
    @api name;
    @track inputValue;
    @track inputOptions;
    defaultPlaceholder = this.placeholder;
    value = [];
    hasRendered = false;
    dropDownInFocus = false;
    _disabled = false;
    _validated = true;

    @api
    get disabled() {
        return this._disabled;
    }
    set disabled(value) {
        this._disabled = value;
        this.handleDisabled();
    }

    @api
    get validated() {
        return this._validated;
    }
    set validated(value) {
        this.handleValidated(value);
    }

    @api
    get placeholder() {
        return this.inputValue;
    }
    set placeholder(value) {
        this.inputValue = value;
        this.defaultPlaceholder = value;
    }

    @api
    get options() {
        return this.inputOptions;
    }
    set options(value) {
        this.inputOptions = value;
    }

    @api
    clear() {
        this.value = [];
        let listBoxOptions = this.template.querySelectorAll(".slds-is-selected");
        for (let option of listBoxOptions) {
            option.classList.remove("slds-is-selected");
        }
        this.closeDropbox();
    }

    renderedCallback() {
        if (!this.hasRendered) {
            //  we call the logic once, when page rendered first time
            this.handleDisabled();
            this.handleValidated(this._validated);
        }
        this.hasRendered = true;
    }

    handleDisabled() {
        let input = this.template.querySelector("input");
        if (input) {
            input.disabled = this.disabled;
        }
    }

    handleValidated(value) {
        this._validated = value;
        let input = this.template.querySelector("input");
        if (input) {
            if (this._validated) {
                input.classList.remove("has-error");
            } else {
                input.classList.add("has-error");
            }
        }
    }

    handleClick() {
        let sldsCombobox = this.template.querySelector(".slds-combobox");
        sldsCombobox.classList.toggle("slds-is-open");
    }

    handleSelection(event) {
        let value = event.currentTarget.dataset.value;
        this.handleOption(event, value);

        let input = this.template.querySelector("input");
        input.focus();
        this.handleValidated(true);
        this.sendValues();
    }

    sendValues() {
        let values = [];
        for (const valueObject of this.value) {
            values.push({
                label: valueObject.label,
                value: valueObject.value
            });
        }
        this.dispatchEvent(
            new CustomEvent("valuechange", {
                detail: { values }
            })
        );
    }

    handleOption(event, value) {
        let listBoxOption = event.currentTarget.firstChild;
        if (listBoxOption.classList.contains("slds-is-selected")) {
            this.value = this.value.filter((option) => option.value !== value);
        } else {
            let option = this.options.find((option) => option.value === value);
            this.value.push(option);
        }

        if (this.value.length > 1) {
            this.inputValue = this.value.length + " options selected";
        } else if (this.value.length === 1) {
            this.inputValue = this.value[0].label;
        } else {
            this.inputValue = this.defaultPlaceholder;
        }
        listBoxOption.classList.toggle("slds-is-selected");
    }

    handleBlur() {
        if (!this.dropDownInFocus) {
            this.closeDropbox();
        }
    }

    handleMouseleave() {
        this.dropDownInFocus = false;
    }

    handleMouseEnter() {
        this.dropDownInFocus = true;
    }

    closeDropbox() {
        let sldsCombobox = this.template.querySelector(".slds-combobox");
        sldsCombobox.classList.remove("slds-is-open");
    }
}
