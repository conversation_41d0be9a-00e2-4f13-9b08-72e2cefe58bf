// test help method
import TIME_ZONE from "@salesforce/i18n/timeZone";
import { TABLE_SORT_VALUE_SUFFIX } from "c/constants";

export async function flushPromises(time) {
    const timer = time || 0;
    return new Promise((resolve) => {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => resolve(), timer);
    });
}

export function debounce(fn, wait) {
    let timer = null;
    return function (...args) {
        if (timer !== null) {
            clearTimeout(timer);
        }
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        timer = setTimeout(() => {
            fn.apply(this, args);
            timer = null;
        }, wait);
    };
}

export function debounceWithLead(fn, wait, leading = false) {
    let timer = null;
    return function () {
        let callNow = leading && !timer;
        if (timer !== null) {
            clearTimeout(timer);
        }
        if (callNow) {
            fn();
        }
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        timer = setTimeout(() => {
            timer = null;
            if (!leading) {
                fn();
            }
        }, wait);
    };
}

const timeReached = Symbol();
function timeout(time) {
    return new Promise((resolve) => {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => {
            resolve(timeReached);
        }, time);
    });
}

export function promiseWithMinimumDuration(requestFn, minimumDuration) {
    return Promise.all([requestFn, flushPromises(minimumDuration)]);
}

export function delayLoading(callback, startLoadingFn) {
    Promise.race([callback, timeout(400)])
        .then((val) => {
            if (val === timeReached) {
                startLoadingFn();
            }
        })
        .catch((err) => {
            console.log(err);
        });
    return callback;
}

export function delayAndLoadingWithAtLeastTime(callback, startLoadingFn, endLoadingFn) {
    let isLoading = false;

    const startLoadingTimer = timeout(400);
    Promise.race([callback, startLoadingTimer]).then((val) => {
        if (val === timeReached) {
            isLoading = true;
            startLoadingFn();
        }
    });

    const unloadingTimer = timeout(800);
    Promise.race([callback, unloadingTimer]).then((val) => {
        if (isLoading && val === timeReached) {
            callback.then(() => {
                endLoadingFn();
            });
        }
        if (isLoading && val !== timeReached) {
            unloadingTimer.then(() => {
                endLoadingFn();
            });
        }
    });
    return callback;
}

export function isProd() {
    return window.location.origin.startsWith("https://thoughtworks.");
}

export function deepCopyObj(obj) {
    return JSON.parse(JSON.stringify(obj));
}

export function parseError(err) {
    switch (err.status) {
        case 400:
            return parseBadRequestError(err.body);
        case 500:
            return parseIntervalExceptionError(err.body);
        default:
            console.error(err);
            return {};
    }
}

function parseBadRequestError(errorBody) {
    let message = "",
        errCode = "",
        detail = {};
    if (!errorBody) {
        return { message, errCode, detail };
    }
    message = errorBody.message;
    const errorOutput = errorBody.output;
    // check errors
    if (errorOutput.errors && errorOutput.errors.length > 0) {
        const firstError = errorOutput.errors[0];
        message = firstError.message;
        errCode = firstError.errorCode;
        detail = deepCopyObj(firstError);
    } else if (errorOutput.fieldErrors && Object.keys(errorOutput.fieldErrors).length > 0) {
        // check fieldError
        return tryParseFirstFieldError(errorOutput.fieldErrors);
    }
    return { message, errCode, detail };
}

function tryParseFirstFieldError(fieldErrors) {
    const allFieldErrorKey = Object.keys(fieldErrors);
    const firstFieldError = fieldErrors[allFieldErrorKey[0]][0];
    const detail = deepCopyObj(firstFieldError);
    detail.field = allFieldErrorKey[0];
    return {
        message: firstFieldError.message,
        errCode: firstFieldError.errorCode || firstFieldError.statusCode,
        detail
    };
}

function parseIntervalExceptionError(errorBody) {
    let message = "",
        errCode = "",
        detail = {};
    if (!errorBody) {
        return { message, errCode, detail };
    }

    if (errorBody.fieldErrors && Object.keys(errorBody.fieldErrors).length > 0) {
        return tryParseFirstFieldError(errorBody.fieldErrors);
    }
    message = errorBody.message;

    return { message, errCode, detail };
}

export function tryGetFirstErrorMsg(error) {
    const parsedError = parseError(error);
    const detail = parsedError.detail;
    const errCode = parsedError.errCode;
    try {
        switch (errCode) {
            case "DUPLICATES_DETECTED":
                parsedError.message = getDuplicateErrorMsg(detail.duplicateRecordError.matchResults[0]);
                break;
            case "FIELD_CUSTOM_VALIDATION_EXCEPTION":
                parsedError.message = `Failed by Validation Rule, field: ${detail.field}, message: ${detail.message}.`;
                break;
            default:
                break;
        }
    } catch (errors) {
        //ignore error
        console.log(`parse error failed: ${errors}`);
    }
    return parsedError;
}

function getDuplicateErrorMsg(matchRecord) {
    switch (matchRecord.apiName) {
        case "Contact":
            return "You can't save this record because a duplicate contact already exists. Try selecting the contact instead.";
        default:
            return "You can't save this record because a duplicate lead already exists. Try adding the lead to an account to convert them as a contact.";
    }
}

export function createUUID() {
    let dt = new Date().getTime();
    const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c === "x" ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
}

export function formatValidationErrorToArray(error) {
    let errorMsgItems = [];
    if (error.body) {
        processErrors(errorMsgItems, error.body.output?.errors);
        processFieldErrors(errorMsgItems, error.body.output?.fieldErrors);

        processFieldErrors(errorMsgItems, error.body.fieldErrors);
        processErrors(errorMsgItems, error.body.pageErrors);

        fetchError(errorMsgItems, error.body);
    }
    if (errorMsgItems.length === 0) {
        const errMsg = "Unknown error, please contact your administrator";
        addErrorMsgItem(errorMsgItems, errMsg);
    }
    return errorMsgItems;
}

//AuraHandledException and require field is null
function fetchError(errorMsgItems, body) {
    if (body.message) {
        const errorData = getErrorData(body.message);
        if (typeof errorData === "string") {
            addErrorMsgItem(errorMsgItems, errorData);
        } else if (typeof errorData === "object" && errorData.code) {
            addErrorMsgItem(errorMsgItems, errorData.errorInfo.message);
        }
    }
}

function getErrorData(message) {
    let errorData;
    try {
        errorData = JSON.parse(message);
    } catch (e) {
        errorData = message;
    }
    return errorData;
}

// validation rule

function processErrors(errorMsgItems, errors) {
    if (errors) {
        errors.forEach((element) => {
            addErrorMsgItem(errorMsgItems, element.message);
        });
    }
}

function processFieldErrors(errorMsgItems, fieldErrors) {
    if (fieldErrors) {
        for (const [, value] of Object.entries(fieldErrors)) {
            for (let i = 0; i < value.length; i++) {
                addErrorMsgItem(errorMsgItems, value[i].message);
            }
        }
    }
}

function addErrorMsgItem(errorMsgItems, message) {
    errorMsgItems.push({
        key: createUUID(),
        msg: message
    });
}

export function preventBackgroundScroll() {
    document.body.style.overflow = "hidden";
}

export function restoreBackgroundScroll() {
    document.body.style.overflow = "auto";
}

export async function copyToClipboard(textToCopy) {
    if (navigator.clipboard) {
        await navigator.clipboard.writeText(textToCopy).catch((err) => console.error(JSON.stringify(err)));
    } else {
        const input = document.createElement("textarea");
        // eslint-disable-next-line @lwc/lwc/no-inner-html
        input.innerHTML = textToCopy;
        document.body.appendChild(input);
        input.select();

        document.execCommand("copy");

        document.body.removeChild(input);
    }
}

export const formValidate = {
    setInputFieldError: (element, fieldName, errorMessage) => {
        element.setErrors({
            body: {
                output: {
                    fieldErrors: {
                        [fieldName]: [{ message: errorMessage }]
                    }
                }
            }
        });
        element.reportValidity();
    }
};

export const clearInputFieldError = {
    setInputFieldError: (element, fieldName) => {
        element.setErrors({
            body: {
                output: {
                    fieldErrors: {
                        [fieldName]: [{ message: "" }]
                    }
                }
            }
        });
        element.reportValidity();
    }
};

//get date format like "2023-01-01"
export function getFormattedLocaleDate(userLocale) {
    const localeDate = new Date().toLocaleDateString(userLocale ?? "en-US", { year: "numeric", month: "2-digit", day: "2-digit" });
    const parts = localeDate.split("/");
    return parts[2] + "-" + parts[0].padStart(2, "0") + "-" + parts[1].padStart(2, "0");
}

export const toLowerCase = (fieldValue) => (typeof fieldValue === "string" ? fieldValue.toLowerCase() : fieldValue);

export function roundWithPrecision(number, precision) {
    if (precision) {
        const value = Math.round(`${number}e${precision}`);
        return +`${value}e${0 - precision}`;
    }
    return Math.round(number);
}

export function toThousandAmount(num) {
    return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ","));
}

export function isCurrentMonth(date) {
    if (date === undefined) {
        return false;
    }
    const nowDate = new Date();
    return date.getFullYear() === nowDate.getFullYear() && date.getMonth() === nowDate.getMonth();
}

export function isPreviousMonth(date) {
    if (date === undefined) {
        return false;
    }
    const nowDate = new Date();
    return date.getFullYear() < nowDate.getFullYear() || (date.getFullYear() === nowDate.getFullYear() && date.getMonth() < nowDate.getMonth());
}

export function groupListByKey(list, getKey) {
    if (!list?.length) return new Map();
    return list.reduce((result, item) => {
        const key = getKey(item);
        if (!result.has(key)) {
            result.set(key, []);
        }
        result.get(key).push(item);
        return result;
    }, new Map());
}

export function addDays(inputDate, days) {
    return new Date(inputDate.setDate(inputDate.getDate() + days));
}

export function setTooltipPosition(point, offsets, size) {
    const gap = 20;
    let x;
    let y;

    const [pointX, pointY] = point;

    const containerLeftToWindowLeft = offsets.x;
    const containerTopToWindowTop = offsets.y;

    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    const [toolTipWidth, toolTipHeight] = size.contentSize;

    if (windowWidth / 2 <= containerLeftToWindowLeft + pointX) {
        x = pointX - gap - toolTipWidth;
    } else {
        x = pointX + gap;
    }

    if (windowHeight - containerTopToWindowTop - pointY <= toolTipHeight) {
        y = pointY + gap - toolTipHeight;
    } else {
        y = pointY - gap;
    }

    return [x, y];
}

/**
 * Format the amount
 * @param {number} amount
 * @param {boolean} [isSplitUnitAndValue=false]
 * @param {number} [precision=1]
 * @returns {string | Object}
 */
export const formatAmount = (amount, isSplitUnitAndValue = false, precision = 1) => {
    let value = "";
    let unit = "";
    if (isNaN(amount) || !amount) {
        value = "0";
    } else if (Math.abs(amount) < 1000) {
        value = roundWithPrecision(amount, precision);
        unit = "";
    } else if (Math.abs(amount) < 1000000) {
        const val = roundWithPrecision(amount / 1000, precision);
        value = Math.abs(val) < 1000 ? val : val / 1000;
        unit = Math.abs(val) < 1000 ? "K" : "M";
    } else if (Math.abs(amount) < 1000000000) {
        const val = roundWithPrecision(amount / 1000000, precision);
        value = Math.abs(val) < 1000 ? val : val / 1000;
        unit = Math.abs(val) < 1000 ? "M" : "B";
    } else {
        value = roundWithPrecision(amount / 1000000000, precision);
        unit = "B";
    }
    unit = Number(value) === 0 ? "" : unit;
    return isSplitUnitAndValue ? { value, unit } : value + unit;
};

/**
 * fetch data once.
 * @param {Promise<Object>}
 */
export const fetchDataWithMemory = (fetchFunction) => {
    let data = null;
    let fetchPromise = null;

    const fetchData = async (params) => {
        try {
            data = await fetchFunction(params);
            return data;
        } catch (error) {
            console.error("Error loading data", error);
            throw error;
        } finally {
            fetchPromise = null;
        }
    };

    return async (params) => {
        if (data) {
            return data;
        }

        if (fetchPromise) {
            return fetchPromise;
        }

        fetchPromise = fetchData(params);

        try {
            const result = await fetchPromise;
            return result;
        } finally {
            fetchPromise = null;
        }
    };
};

export const throttle = (callback, delay) => {
    let lastExecutionTime = 0;
    let timeoutId;

    return function (...args) {
        const currentTime = Date.now();
        const elapsedTime = currentTime - lastExecutionTime;

        if (!lastExecutionTime || elapsedTime >= delay) {
            callback.apply(this, args);
            lastExecutionTime = currentTime;
        } else {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // eslint-disable-next-line @lwc/lwc/no-async-operation
            timeoutId = setTimeout(() => {
                callback.apply(this, args);
                lastExecutionTime = Date.now();
            }, delay - elapsedTime);
        }
    };
};

export function deepEqual(obj1, obj2, ignoreKeys) {
    if (!obj1 || !obj2) {
        return obj1 === obj2;
    }
    if (typeof obj1 !== typeof obj2 || Object.keys(obj1).length !== Object.keys(obj2).length) {
        return false;
    }
    for (let key in obj1) {
        if ((ignoreKeys || []).includes(key)) {
            continue;
        }
        if (typeof obj1[key] === "object") {
            if (!deepEqual(obj1[key], obj2[key], ignoreKeys)) {
                return false;
            }
        } else if (obj1[key] !== obj2[key]) {
            return false;
        }
    }
    return true;
}

export const getAllMatchValuesByRegex = (pattern, text, splitSymbol) => {
    const matches = text.match(pattern);
    if (matches && matches.length > 0) {
        return matches.reduce(
            (result, match) =>
                result.concat(
                    match
                        .split(splitSymbol)
                        .map((i) => i.trim())
                        .filter((i) => i !== "")
                ),
            []
        );
    }
    return [];
};

export function isActionToday(date) {
    return new Date().toDateString() === new Date(date).toDateString();
}

export function isActivityToday(date, useTimeZone = true) {
    const current = new Date().toLocaleDateString("en-US", { timeZone: TIME_ZONE });
    return useTimeZone ? current === new Date(date).toLocaleDateString("en-US", { timeZone: TIME_ZONE }) : current === new Date(date).toLocaleDateString("en-US", { timeZone: "UTC" });
}

export function isSameYear(date, useTimeZone = true) {
    const current = new Date().toLocaleString("en-US", { timeZone: TIME_ZONE, year: "numeric" });
    return useTimeZone
        ? current === new Date(date).toLocaleString("en-US", { timeZone: TIME_ZONE, year: "numeric" })
        : current === new Date(date).toLocaleString("en-US", { timeZone: "UTC", year: "numeric" });
}

export function calculateDaysBetween(date1, date2) {
    const date1Time = new Date(date1).getTime();
    const date2Time = new Date(date2).getTime();

    const timeDifference = Math.abs(date2Time - date1Time);
    return Math.ceil(timeDifference / (1000 * 60 * 60 * 24));
}

export function getWeekendDays(startDate, endDate) {
    const weekends = [];
    let currentDate = new Date(startDate);
    const end = new Date(endDate);

    while (currentDate <= end) {
        const dayOfWeek = currentDate.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            weekends.push(new Date(currentDate)); // 添加到 weekends 数组
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }
    return weekends;
}

export function sortBy(field, reverse, primer) {
    const key = primer ? (x) => primer(x[field], x) : (x) => x[field];

    return (a, b) => {
        const valueA = key(a);
        const valueB = key(b);

        // Handle undefined: always place it at the end
        if (valueA === undefined && valueB === undefined) return 0; // Both are undefined
        if (valueA === undefined) return 1; // `a` is undefined, so it goes to the end
        if (valueB === undefined) return -1; // `b` is undefined, so it goes to the end

        // Standard comparison for non-undefined values
        return reverse * ((valueA > valueB) - (valueB > valueA));
    };
}

export function parseUrlQueryParams(params, separator = ",") {
    return typeof params === "string"
        ? params
              .split(separator)
              .map((item) => decodeURIComponent(item).trim())
              .filter((item) => item.length > 0)
        : null;
}

export function datatableSort(tableData, column, sortedBy, sortDirection) {
    tableData = [...tableData];
    const dateCompare = (l, r) => (sortDirection === "asc" ? new Date(l) - new Date(r) : new Date(r) - new Date(l));
    const numberCompare = (l, r) => (sortDirection === "asc" ? l - r : r - l);
    const stringCompare = (l, r) => {
        l = l ? l.toString().toLowerCase() : "";
        r = r ? r.toString().toLowerCase() : "";
        return sortDirection === "asc" ? l.localeCompare(r) : r.localeCompare(l);
    };
    tableData.sort((a, b) => {
        const useSortValueField = Object.hasOwn(a, sortedBy + TABLE_SORT_VALUE_SUFFIX);
        const actualSortedBy = useSortValueField ? sortedBy + TABLE_SORT_VALUE_SUFFIX : sortedBy;
        let valueA = a[actualSortedBy];
        let valueB = b[actualSortedBy];

        if (valueA === undefined || valueA === null || valueA === "") valueA = null;
        if (valueB === undefined || valueB === null || valueB === "") valueB = null;

        let compareFun = stringCompare;
        if (useSortValueField) {
            if (valueA instanceof Date) {
                compareFun = dateCompare;
            } else if (typeof valueA === "number") {
                compareFun = numberCompare;
            }
        } else {
            if (column.type === "date") {
                compareFun = dateCompare;
            } else if (column.type === "number" || column.type === "currency") {
                compareFun = numberCompare;
            }
        }

        return compareFun(valueA, valueB);
    });
    return tableData;
}
