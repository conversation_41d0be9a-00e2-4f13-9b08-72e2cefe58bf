{"update_record_FIELD_CUSTOM_VALIDATION_EXCEPTION": {"status": 400, "body": {"message": "An error occurred while trying to update the record. Please try again.", "statusCode": 400, "enhancedErrorType": "RecordError", "output": {"errors": [], "fieldErrors": {"AccountId": [{"constituentField": null, "duplicateRecordError": null, "errorCode": "FIELD_CUSTOM_VALIDATION_EXCEPTION", "field": "AccountId", "fieldLabel": "Account ID", "message": "You must enter the Account"}]}}}, "headers": {}}, "apex_action_FIELD_CUSTOM_VALIDATION_EXCEPTION": {"status": 500, "body": {"fieldErrors": {"AccountId": [{"statusCode": "FIELD_CUSTOM_VALIDATION_EXCEPTION", "message": "You must enter the Account"}]}, "pageErrors": [], "index": null, "duplicateResults": []}, "headers": {}}, "CONTACT_DUPLICATES_DETECTED": {"status": 400, "body": {"message": "An error occurred while trying to update the record. Please try again.", "statusCode": 400, "enhancedErrorType": "RecordError", "output": {"errors": [{"constituentField": null, "duplicateRecordError": {"matchResults": [{"apiName": "Contact", "isAllowSave": false, "matchRecordIds": ["0037j00000hTVYVAA4"], "matchRule": "New_Contact_Matching_Rule", "objectLabel": "Contact", "objectLabelPlural": "Contacts", "themeInfo": {"color": "A094ED", "iconUrl": "https://thoughtworks--junldev.my.salesforce.com/img/icon/t4v35/standard/contact_120.png"}}]}, "errorCode": "DUPLICATES_DETECTED", "field": null, "fieldLabel": null, "message": "You're creating/editing a duplicate record.  Contact sales systems or your local marketing team for assistance."}], "fieldErrors": {}}}, "headers": {}}, "apex_action_EXCEPTION": {"status": 500, "body": {"exceptionType": "System.AuraException", "isUserDefinedException": false, "message": "One Exception", "stackTrace": "Class.ContactController.clearReportsToFieldForContacts: line 13, column 1"}, "headers": {}}}