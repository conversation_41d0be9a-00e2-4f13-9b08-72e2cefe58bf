import {
    parseError,
    tryGetFirstErrorMsg,
    delayLoading,
    flushPromises,
    formatValidationErrorToArray,
    roundWithPrecision,
    isPreviousMonth,
    toThousandAmount,
    isCurrentMonth,
    groupListByKey,
    addDays,
    delayAndLoadingWithAtLeastTime,
    formatAmount,
    setTooltipPosition,
    fetchDataWithMemory,
    throttle,
    deepEqual,
    getAllMatchValuesByRegex,
    isActivityToday,
    isSameYear,
    sortBy
} from "c/utils";
import { RECEIVER_EMAIL_PATTERN } from "c/constants";

jest.mock("@salesforce/i18n/timeZone", () => ({
    default: "Pacific/Marquesas"
}));

const mockErrorData = require("./data/errors.json");

const mockDate = (mockedToday) => {
    const originalDate = global.Date;
    return jest.spyOn(global, "Date").mockImplementation(function () {
        if (arguments.length === 0) {
            return mockedToday;
        }
        return new originalDate(...arguments);
    });
};

describe("utils", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it("Should parse field error when field failed against validation rule when use apex action", () => {
        const fieldError = mockErrorData.apex_action_FIELD_CUSTOM_VALIDATION_EXCEPTION;
        const { message, errCode, detail } = parseError(fieldError);

        expect(message).toBe("You must enter the Account");
        expect(errCode).toBe("FIELD_CUSTOM_VALIDATION_EXCEPTION");
        expect(detail).toStrictEqual({
            statusCode: "FIELD_CUSTOM_VALIDATION_EXCEPTION",
            field: "AccountId",
            message: "You must enter the Account"
        });
    });

    it("Should parse field error when field failed against validation rule when use update record", () => {
        const fieldError = mockErrorData.update_record_FIELD_CUSTOM_VALIDATION_EXCEPTION;
        const { message, errCode, detail } = parseError(fieldError);

        expect(message).toBe("You must enter the Account");
        expect(errCode).toBe("FIELD_CUSTOM_VALIDATION_EXCEPTION");
        expect(detail).toStrictEqual({
            constituentField: null,
            duplicateRecordError: null,
            errorCode: "FIELD_CUSTOM_VALIDATION_EXCEPTION",
            field: "AccountId",
            fieldLabel: "Account ID",
            message: "You must enter the Account"
        });
    });

    it("Should parse error when field failed against exception when use apex action", () => {
        const fieldError = mockErrorData.apex_action_EXCEPTION;
        const { message, errCode, detail } = parseError(fieldError);

        expect(message).toBe("One Exception");
        expect(errCode).toBe("");
        expect(detail).toStrictEqual({});
    });

    it("Should return first field error when field failed", () => {
        const fieldError = mockErrorData.apex_action_FIELD_CUSTOM_VALIDATION_EXCEPTION;
        const parsedErr = tryGetFirstErrorMsg(fieldError);
        expect(parsedErr.errCode).toBe("FIELD_CUSTOM_VALIDATION_EXCEPTION");
        expect(parsedErr.message).toBe("Failed by Validation Rule, field: AccountId, message: You must enter the Account.");
    });

    it("Should return first field error when field failed update record", () => {
        const fieldError = mockErrorData.update_record_FIELD_CUSTOM_VALIDATION_EXCEPTION;
        const parsedErr = tryGetFirstErrorMsg(fieldError);
        expect(parsedErr.errCode).toBe("FIELD_CUSTOM_VALIDATION_EXCEPTION");
        expect(parsedErr.message).toBe("Failed by Validation Rule, field: AccountId, message: You must enter the Account.");
    });

    it("Should return duplicate error msg when parse duplicate error msg", () => {
        const fieldError = mockErrorData.CONTACT_DUPLICATES_DETECTED;
        const parsedErr = tryGetFirstErrorMsg(fieldError);
        expect(parsedErr.errCode).toBe("DUPLICATES_DETECTED");
        expect(parsedErr.message).toBe("You can't save this record because a duplicate contact already exists. Try selecting the contact instead.");
    });

    it("Should call startLoadingFn when callback delay great then 400ms", async () => {
        const callback = () =>
            new Promise((resolve) => {
                // eslint-disable-next-line @lwc/lwc/no-async-operation
                setTimeout(() => {
                    resolve("callback");
                }, 1000);
            });
        const startLoadingFn = jest.fn();
        delayLoading(callback(), startLoadingFn);
        await flushPromises(1000);
        expect(startLoadingFn).toHaveBeenCalled();
    });

    it("Should not call startLoadingFn when callback delay less then 400ms", async () => {
        const callback = () =>
            new Promise((resolve) => {
                // eslint-disable-next-line @lwc/lwc/no-async-operation
                setTimeout(() => {
                    resolve("callback");
                }, 300);
            });
        const startLoadingFn = jest.fn();
        delayLoading(callback(), startLoadingFn);
        await flushPromises(1000);
        expect(startLoadingFn).toHaveBeenCalledTimes(0);
    });

    it("Should not loading when callback delay less then 400ms", async () => {
        const callback = () =>
            new Promise((resolve) => {
                // eslint-disable-next-line @lwc/lwc/no-async-operation
                setTimeout(() => {
                    resolve("callback");
                }, 200);
            });
        const startLoadingFn = jest.fn();
        const endLoadingFn = jest.fn();
        delayAndLoadingWithAtLeastTime(callback(), startLoadingFn, endLoadingFn);
        await flushPromises(1000);
        expect(startLoadingFn).toHaveBeenCalledTimes(0);
        expect(endLoadingFn).toHaveBeenCalledTimes(0);
    });

    it("Should end loading when callback delay more then 400ms", async () => {
        const callback = () =>
            new Promise((resolve) => {
                // eslint-disable-next-line @lwc/lwc/no-async-operation
                setTimeout(() => {
                    resolve("callback");
                }, 500);
            });
        const startLoadingFn = jest.fn();
        const endLoadingFn = jest.fn();
        delayAndLoadingWithAtLeastTime(callback(), startLoadingFn, endLoadingFn);
        await flushPromises(1000);
        expect(startLoadingFn).toHaveBeenCalledTimes(1);
        expect(endLoadingFn).toHaveBeenCalledTimes(1);
    });

    it("Should end loading when callback delay more then 800ms", async () => {
        const callback = () =>
            new Promise((resolve) => {
                // eslint-disable-next-line @lwc/lwc/no-async-operation
                setTimeout(() => {
                    resolve("callback");
                }, 900);
            });
        const startLoadingFn = jest.fn();
        const endLoadingFn = jest.fn();
        delayAndLoadingWithAtLeastTime(callback(), startLoadingFn, endLoadingFn);
        await flushPromises(1000);
        expect(startLoadingFn).toHaveBeenCalledTimes(1);
        expect(endLoadingFn).toHaveBeenCalledTimes(1);
    });

    it("Should return output errors msg when catch error", () => {
        const error = {
            body: {
                output: {
                    errors: [{ message: "err" }]
                }
            }
        };
        const outputErr = formatValidationErrorToArray(error);
        expect(outputErr[0].msg).toBe("err");
    });

    it("Should return output fields errors msg when catch error", () => {
        const error = {
            body: {
                output: {
                    fieldErrors: {
                        field1: [{ message: "err1" }, { message: "err2" }]
                    }
                }
            }
        };
        const outputFieldsErr = formatValidationErrorToArray(error);
        expect(outputFieldsErr[0].msg).toBe("err1");
        expect(outputFieldsErr[1].msg).toBe("err2");
    });

    it("Should return page errors msg when catch error", () => {
        const error = {
            body: {
                pageErrors: [
                    {
                        message: "err1"
                    }
                ]
            }
        };
        const outputPageErr = formatValidationErrorToArray(error);
        expect(outputPageErr[0].msg).toBe("err1");
    });

    it("Should return fields errors msg when catch error", () => {
        const error = {
            body: {
                fieldErrors: {
                    field1 : [{ message: "field1err1" }, { message: "field1err2" }],
                    field2: [{ message: "field2err1" }, { message: "field2err2" }]
                }
            }
        };
        const outputPageErr = formatValidationErrorToArray(error);
        expect(outputPageErr[0].msg).toBe("field1err1");
        expect(outputPageErr[1].msg).toBe("field1err2");
        expect(outputPageErr[2].msg).toBe("field2err1");
        expect(outputPageErr[3].msg).toBe("field2err2");
    });

    it("Should return unknown errors msg when catch error", () => {
        const error = {
            body: {
                unknown: ""
            }
        };
        const unknownErr = formatValidationErrorToArray(error);
        expect(unknownErr[0].msg).toBe("Unknown error, please contact your administrator");
    });

    it("Should round value and format with precision 0", () => {
        const roundedValue = roundWithPrecision(200, 1);
        expect(roundedValue).toBe(200);
    });

    it("Should round value and format with precision 0 when number after decimal point is 0", () => {
        const roundedValue = roundWithPrecision(200.0, 1);
        expect(roundedValue).toBe(200);
    });

    it("Should return original value and format with precision 1", () => {
        const roundedValue = roundWithPrecision(200.1, 1);
        expect(roundedValue).toBe(200.1);
    });

    it("Should round value and format with precision 1", () => {
        const roundedValue = roundWithPrecision(200.16, 1);
        expect(roundedValue).toBe(200.2);
    });

    it("Should round value and format without precision", () => {
        const roundedValue = roundWithPrecision(200.96);
        expect(roundedValue).toBe(201);
    });

    it("should format number to thousand amount", () => {
        expect(toThousandAmount(123456789)).toBe("123,456,789");
        expect(toThousandAmount(-987654321)).toBe("-987,654,321");
        expect(toThousandAmount(1234567.89)).toBe("1,234,567.89");
        expect(toThousandAmount("987654321")).toBe("987,654,321");
        expect(toThousandAmount("12345.67")).toBe("12,345.67");
        expect(toThousandAmount("abc")).toBe("0");
    });

    test("Should return true if the date is in the current month", () => {
        const currentDate = new Date();
        expect(isCurrentMonth(currentDate)).toBe(true);
    });

    test("Should return false if the date is not in the current month", () => {
        const previousMonthDate = new Date();
        previousMonthDate.setMonth(previousMonthDate.getMonth() - 10);
        expect(isCurrentMonth(previousMonthDate)).toBe(false);
    });

    test("Should return false if the date is in the future", () => {
        const futureDate = new Date();
        futureDate.setMonth(futureDate.getMonth() + 10);
        expect(isCurrentMonth(futureDate)).toBe(false);
    });

    test("Should return true if the date is in a previous month", () => {
        const previousMonthDate = new Date();
        previousMonthDate.setMonth(previousMonthDate.getMonth() - 10);
        expect(isPreviousMonth(previousMonthDate)).toBe(true);
    });

    test("Should return false if the date is in the current month", () => {
        const currentDate = new Date();
        expect(isPreviousMonth(currentDate)).toBe(false);
    });

    test("Should return false if the date is undefined", () => {
        expect(isPreviousMonth(undefined)).toBe(false);
        expect(isCurrentMonth(undefined)).toBe(false);
    });

    test("should group items by key", () => {
        const list = [
            { id: 1, category: "A" },
            { id: 2, category: "B" },
            { id: 3, category: "A" },
            { id: 4, category: "C" },
            { id: 5, category: "B" }
        ];
        const result = groupListByKey(list, (item) => item.category);
        expect(result.get("A")).toEqual([
            { id: 1, category: "A" },
            { id: 3, category: "A" }
        ]);
        expect(result.get("B")).toEqual([
            { id: 2, category: "B" },
            { id: 5, category: "B" }
        ]);
        expect(result.get("C")).toEqual([{ id: 4, category: "C" }]);
    });

    test("should return an empty map for empty list or undefined", () => {
        const list = [];
        const getKey = (item) => item.category;
        const result1 = groupListByKey(list, getKey);
        const result2 = groupListByKey(undefined, getKey);
        expect(result1.size).toBe(0);
        expect(result2.size).toBe(0);
    });

    test("should calculate later date to input date", () => {
        const inputDate = new Date("2021-01-01");
        const result = addDays(inputDate, 3);
        expect(result).toEqual(new Date("2021-01-04"));
    });

    test("should calculate previous date to input date", () => {
        const inputDate = new Date("2021-01-01");
        const result = addDays(inputDate, -3);
        expect(result).toEqual(new Date("2020-12-29"));
    });

    test("should return 0 when amount is null or abs amount less than 100", () => {
        expect(formatAmount(0)).toEqual("0");
        expect(formatAmount()).toEqual("0");
        expect(formatAmount(undefined)).toEqual("0");
        expect(formatAmount(null)).toEqual("0");
        expect(formatAmount("")).toEqual("0");
        expect(formatAmount("invalid")).toEqual("0");
        expect(formatAmount(10)).toEqual("10");
    });

    test("should return format number when amount more than 100", () => {
        expect(formatAmount(99.9)).toEqual("99.9");
        expect(formatAmount(100)).toEqual("100");
        expect(formatAmount(900)).toEqual("900");
        expect(formatAmount(-800)).toEqual("-800");
        expect(formatAmount(1000)).toEqual("1K");
        expect(formatAmount(10000)).toEqual("10K");
        expect(formatAmount(100000)).toEqual("100K");
        expect(formatAmount(999555)).toEqual("999.6K");
        expect(formatAmount(-999555)).toEqual("-999.6K");
        expect(formatAmount(999999)).toEqual("1M");
        expect(formatAmount(-999999)).toEqual("-1M");
        expect(formatAmount(1000000)).toEqual("1M");
        expect(formatAmount(-1000000)).toEqual("-1M");
        expect(formatAmount(999999999)).toEqual("1B");
        expect(formatAmount(-999999999)).toEqual("-1B");
        expect(formatAmount(1000000000)).toEqual("1B");
        expect(formatAmount(-1000000000)).toEqual("-1B");
    });

    test("should tooltip return right when point at the left corner of window", () => {
        let point = [299, 299];
        let offset = { x: 200, y: 200 };
        let size = { contentSize: [50, 200] };
        let windowSpy;
        windowSpy = jest.spyOn(window, "window", "get");
        windowSpy.mockImplementation(() => ({
            innerWidth: 1000,
            innerHeight: 1000
        }));

        expect(setTooltipPosition(point, offset, size)).toEqual([319, 279]);
        windowSpy.mockRestore();
    });

    test("should tooltip return left when point at the right corner of window", () => {
        let point = [301, 299];
        let offset = { x: 200, y: 200 };
        let size = { contentSize: [50, 200] };
        let windowSpy;
        windowSpy = jest.spyOn(window, "window", "get");
        windowSpy.mockImplementation(() => ({
            innerWidth: 1000,
            innerHeight: 1000
        }));

        expect(setTooltipPosition(point, offset, size)).toEqual([231, 279]);
        windowSpy.mockRestore();
    });

    test("should tooltip return bottom when have space to window bottom", () => {
        let point = [299, 599];
        let offset = { x: 200, y: 200 };
        let size = { contentSize: [50, 200] };
        let windowSpy;
        windowSpy = jest.spyOn(window, "window", "get");
        windowSpy.mockImplementation(() => ({
            innerWidth: 1000,
            innerHeight: 1000
        }));

        expect(setTooltipPosition(point, offset, size)).toEqual([319, 579]);
        windowSpy.mockRestore();
    });

    test("should tooltip return top when have no space to window bottom", () => {
        let point = [299, 600];
        let offset = { x: 200, y: 200 };
        let size = { contentSize: [50, 200] };
        let windowSpy;
        windowSpy = jest.spyOn(window, "window", "get");
        windowSpy.mockImplementation(() => ({
            innerWidth: 1000,
            innerHeight: 1000
        }));

        expect(setTooltipPosition(point, offset, size)).toEqual([319, 420]);
        windowSpy.mockRestore();
    });

    test("should return cached result on repeated calls", async () => {
        const mockPromiseWithConcurrentRequest = jest.fn(() => Promise.resolve());
        const mockPromiseWithCachedData = jest.fn(() => Promise.resolve("result"));

        const mockPromiseMemoryWithConcurrentRequest = fetchDataWithMemory(mockPromiseWithConcurrentRequest);
        mockPromiseMemoryWithConcurrentRequest();
        mockPromiseMemoryWithConcurrentRequest();
        mockPromiseMemoryWithConcurrentRequest();

        expect(mockPromiseWithConcurrentRequest).toHaveBeenCalledTimes(1);

        await flushPromises();

        const mockPromiseMemoryWithCachedData = fetchDataWithMemory(mockPromiseWithCachedData);
        await mockPromiseMemoryWithCachedData();
        mockPromiseMemoryWithCachedData();

        expect(mockPromiseWithCachedData).toHaveBeenCalledTimes(1);
    });

    test("should throw error when request fail", async () => {
        const mockPromiseWithConcurrentRequest = jest.fn().mockRejectedValue(new Error("Fetch error"));
        let error;
        const mockPromiseMemoryWithConcurrentRequest = fetchDataWithMemory(mockPromiseWithConcurrentRequest);

        try {
            await mockPromiseMemoryWithConcurrentRequest();
        } catch (err) {
            error = err;
        }

        expect(error).toBeDefined();
        expect(error.message).toBe("Fetch error");
    });

    test("should execute the callback immediately on the first call", () => {
        const callback = jest.fn();
        const throttledFunction = throttle(callback, 100);

        throttledFunction();

        expect(callback).toHaveBeenCalled();
    });

    test("should return appropriate value given input value when compare object is equal", () => {
        expect(deepEqual({ a: 1, b: 2 }, { a: 1, b: 2 })).toEqual(true);
        expect(deepEqual({ a: 1, b: 2 }, { a: 2, b: 3 })).toEqual(false);
        expect(deepEqual({ a: 1, b: 2 }, { a: 1, b: 3 })).toEqual(false);
        expect(deepEqual({ a: { c: 3 } }, { a: { c: 3 } })).toEqual(true);

        expect(deepEqual({ a: { c: 3 } }, { a: { c: 4 } })).toEqual(false);
        expect(deepEqual({ a: 1, b: 2 }, { a: 1, b: 3 }, ["b"])).toEqual(true);
        expect(deepEqual({ a: { d: 4 }, b: 2 }, { a: { d: 4 }, b: 3 }, ["b"])).toEqual(true);
        expect(deepEqual({ a: 1, b: 2 }, { a: 1, b: 2 }, null)).toEqual(true);

        expect(deepEqual(1, 1)).toEqual(true);
        expect(deepEqual(1, "1")).toEqual(false);

        expect(deepEqual(null, null)).toEqual(true);
        expect(deepEqual(undefined, undefined)).toEqual(true);
        expect(deepEqual(null, undefined)).toEqual(false);

        expect(deepEqual(null, {})).toEqual(false);

        expect(deepEqual({ a: 1, b: 2 }, { a: 1 })).toEqual(false);
    });

    test("should return all match values by regex", () => {
        const matchText = "To: <EMAIL>; <EMAIL>\nCC: <EMAIL>\nBCC: <EMAIL>\n";
        const allMatchValues = getAllMatchValuesByRegex(RECEIVER_EMAIL_PATTERN, matchText, ";");
        expect(allMatchValues).toEqual(["<EMAIL>", "<EMAIL>", "<EMAIL>"]);

        const noMatchValue = getAllMatchValuesByRegex(RECEIVER_EMAIL_PATTERN, "", ";");
        expect(noMatchValue).toEqual([]);
    });

    test("isActivityToday returns true when date is today with user time zone", () => {
        const mockedToday = new Date("2024-01-29T10:30:00.000+0000");
        const spy = mockDate(mockedToday);

        const input = "2024-01-29T09:30:00.000Z";
        expect(isActivityToday(input)).toBe(true);

        spy.mockRestore();
    });

    test("isActivityToday returns false when date is not today with user time zone", () => {
        const mockedToday = new Date("2024-01-29T08:30:00.000+0000");
        const spy = mockDate(mockedToday);

        const input = "2024-01-29T09:30:00.000Z";
        expect(isActivityToday(input)).toBe(false);

        spy.mockRestore();
    });

    test("isActivityToday returns true when date is today by UTC", () => {
        const mockedToday = new Date("2024-01-29T10:30:00.000+0000");
        const spy = mockDate(mockedToday);

        const input = "2024-01-29";
        expect(isActivityToday(input, false)).toBe(true);

        spy.mockRestore();
    });

    test("isActivityToday returns false when date is not today by UTC", () => {
        const mockedToday = new Date("2024-01-29T08:30:00.000+0000");
        const spy = mockDate(mockedToday);

        const input = "2024-01-29";
        expect(isActivityToday(input, false)).toBe(false);

        spy.mockRestore();
    });

    test("isSameYear returns true with user time zone", () => {
        const mockedNow = new Date("2024-01-01T10:30:00.000+0000");
        const spy = mockDate(mockedNow);

        const input = "2024-01-01T09:30:00.000Z";
        expect(isSameYear(input)).toBe(true);

        spy.mockRestore();
    });

    test("isSameYear returns false  with user time zone", () => {
        const mockedNow = new Date("2024-01-01T08:30:00.000+0000");
        const spy = mockDate(mockedNow);

        const input = "2024-01-01T09:30:00.000Z";
        expect(isSameYear(input)).toBe(false);

        spy.mockRestore();
    });

    test("isSameYear returns true  with UTC time zone", () => {
        const mockedNow = new Date("2024-01-01T10:30:00.000+0000");
        const spy = mockDate(mockedNow);

        const input = "2024-01-01";
        expect(isSameYear(input, false)).toBe(true);

        spy.mockRestore();
    });

    test("isSameYear returns false  with UTC time zone", () => {
        const mockedNow = new Date("2024-01-01T08:30:00.000+0000");
        const spy = mockDate(mockedNow);

        const input = "2024-01-01";
        expect(isSameYear(input, false)).toBe(false);

        spy.mockRestore();
    });
});

describe("sortBy", () => {
    it("should sort numbers in ascending order", () => {
        const data = [{ value: 3 }, { value: 1 }, { value: 2 }];
        const sorted = data.sort(sortBy("value", 1));
        expect(sorted).toEqual([{ value: 1 }, { value: 2 }, { value: 3 }]);
    });

    it("should sort numbers in descending order", () => {
        const data = [{ value: 3 }, { value: 1 }, { value: 2 }];
        const sorted = data.sort(sortBy("value", -1));
        expect(sorted).toEqual([{ value: 3 }, { value: 2 }, { value: 1 }]);
    });

    it("should handle strings with primer function", () => {
        const data = [{ value: "banana" }, { value: "apple" }, { value: "cherry" }];
        const sorted = data.sort(sortBy("value", 1, (x) => x.toLowerCase()));
        expect(sorted).toEqual([{ value: "apple" }, { value: "banana" }, { value: "cherry" }]);
    });

    it("should place undefined values at the end", () => {
        const data = [{ value: 2 }, { value: undefined }, { value: 1 }];
        const sorted = data.sort(sortBy("value", 1));
        expect(sorted).toEqual([{ value: 1 }, { value: 2 }, { value: undefined }]);
    });

    it("should handle all undefined values gracefully", () => {
        const data = [{ value: undefined }, { value: undefined }];
        const sorted = data.sort(sortBy("value", 1));
        expect(sorted).toEqual([{ value: undefined }, { value: undefined }]);
    });

    it("should sort mixed data types with a primer", () => {
        const data = [{ value: "20" }, { value: "100" }, { value: "5" }];
        const sorted = data.sort(sortBy("value", 1, (x) => parseInt(x, 10)));
        expect(sorted).toEqual([{ value: "5" }, { value: "20" }, { value: "100" }]);
    });
});
