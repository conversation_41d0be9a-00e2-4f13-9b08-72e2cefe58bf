import { createElement } from "lwc";
import { flushPromises } from "c/utils";
import ConfirmModal from "c/confirmModal";
import { loadStyle } from "lightning/platformResourceLoader";

async function createComponent() {
    const element = createElement("c-confirm-modal", {
        is: ConfirmModal
    });
    element.cancelBtn = "cancel";
    document.body.appendChild(element);
    // Mock opportunity id
    element.oppoId = "006000000000001";

    await flushPromises();
    return element;
}

describe("c-confirm-modal", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("TODO: test case generated by CLI command, please fill in test logic", () => {
        // Arrange
        const element = createElement("c-confirm-modal", {
            is: ConfirmModal
        });

        // Act
        document.body.appendChild(element);

        // Assert
        // const div = element.shadowRoot.querySelector('div');
        expect(1).toBe(1);
    });

    it("should close modal when click cancel button", async () => {
        const element = await createComponent();
        expect(element.closeValue).not.toBeDefined();
        const cancelButtonEl = element.shadowRoot.querySelector("lightning-button");
        cancelButtonEl.click();
        await flushPromises();
        expect(element.closeValue).toBe("cancel");
    });

    it("should close modal when click confirm button", async () => {
        const element = await createComponent();
        expect(element.closeValue).not.toBeDefined();
        const confirmButtonEl = element.shadowRoot.querySelectorAll("lightning-button")[1];
        confirmButtonEl.click();
        await flushPromises();
        expect(element.closeValue).toBe("confirm");
    });

    it("should call loadStyle when render confirm modal", async () => {
        global.console.log = jest.fn();
        await createComponent();
        expect(loadStyle).toHaveBeenCalled();
        expect(global.console.log).toHaveBeenCalledWith("Loaded Successfully");
    });
});
