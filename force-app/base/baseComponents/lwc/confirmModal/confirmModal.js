import { api } from "lwc";
import LightningModal from "lightning/modal";
import { loadScript, loadStyle } from "lightning/platformResourceLoader";
import crmGlobeStyle from "@salesforce/resourceUrl/crmGlobeStyle";
import removeAllStylesheets from "@salesforce/resourceUrl/removeStylesheet";

export default class ConfirmModal extends LightningModal {
    @api content;
    @api title;
    @api cancelBtn;
    @api confirmBtn;

    connectedCallback() {
        loadStyle(this, crmGlobeStyle)
            .then(() => {
                console.log("Loaded Successfully");
            })
            .catch(() => {
                /* istanbul ignore next */
                console.error("Error in loading the colors");
            });
    }

    disconnectedCallback() {
        loadScript(this, removeAllStylesheets)
            .then(() => {
                console.log("Loaded removeAllStylesheets Successfully");
            })
            .catch((err) => {
                console.log(`Load removeAllStylesheets script fail, err:${err}.`);
            });
    }

    handleCancel() {
        this.close("cancel");
    }

    handleConfirm() {
        this.close("confirm");
    }
}
