import { api, LightningElement } from "lwc";

export default class SingleDataSelector extends LightningElement {
    @api required;
    @api optionList;
    @api placeholder;
    @api isDropdownFixed;

    @api
    set existingValue(value) {
        this.selectedOption = value;
    }

    get existingValue() {
        return this.selectedOption;
    }

    selectedOption = "";
    dropdownVisible = false;
    blurCanceled = false;
    searchKey = "";
    isFullMatch = true;

    handleBlur() {
        if (this.blurCanceled) {
            return;
        }
        this.dropdownVisible = false;
    }

    handleFocus(event) {
        this.searchByKeyword(event);
        this.dropdownVisible = true;
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => {
            this.getDropdownElementPosition();
        }, 10);
    }

    searchByKeyword(event) {
        this.searchKey = event.target.value.trim();
        this.selectedOption = this.searchKey;
        this.dispatchEvent(new CustomEvent("optionselect", { detail: this.selectedOption }));

        let isFullMatch = false;
        if (this.searchKey.length < 2) {
            this.optionSearchList = [...this.optionList];
            this.isFullMatch = true;
            return;
        }
        let reg = new RegExp(this.searchKey, "i");
        let resArr = [];
        this.optionList.forEach((item) => {
            if (item.name.match(reg)) {
                resArr.push(item);
            }
            if (item.name.toLowerCase() === this.searchKey.toLowerCase()) {
                isFullMatch = true;
            }
        });
        this.isFullMatch = isFullMatch;
        this.optionSearchList = resArr;
    }

    handleClickOption(event) {
        this.selectedOption = event.target.dataset.name;
        this.dropdownVisible = false;
        this.dispatchEvent(new CustomEvent("optionselect", { detail: this.selectedOption }));
    }

    handleAddNewOption(event) {
        this.selectedOption = event.currentTarget.dataset.name;
        this.dropdownVisible = false;
        this.dispatchEvent(new CustomEvent("optionselect", { detail: this.selectedOption }));
    }

    handleDropdownMouseDown() {
        this.blurCanceled = true;
    }

    handleDropdownMouseUp() {
        this.blurCanceled = false;
    }

    getDropdownElementPosition() {
        const dropdownElement = this.template.querySelector(".slds-dropdown");
        const input = this.template.querySelector("input");
        if (!input || !dropdownElement) return;
        const oppoSelectBoxPosition = input.getBoundingClientRect();
        dropdownElement.style.top = oppoSelectBoxPosition.top + 30 + "px";
        dropdownElement.style.width = input.offsetWidth + "px";
    }

    get inputRequiredStyle() {
        if (this.isInputRequired) {
            return "slds-has-error";
        }
        return "";
    }

    get isInputRequired() {
        return this.required && this.selectedOption === "" && !this.dropdownVisible;
    }

    get optionWithIsCheckedList() {
        return this.optionSearchList.map((item) => {
            return {
                ...item,
                isChecked: this.selectedOption === item.name
            };
        });
    }

    get dropdownClass() {
        let className = "slds-dropdown slds-dropdown_length-7 slds-dropdown_fluid";
        if (this.isDropdownFixed) {
            return className + " fixed-dropdown";
        }
        return className;
    }
}
