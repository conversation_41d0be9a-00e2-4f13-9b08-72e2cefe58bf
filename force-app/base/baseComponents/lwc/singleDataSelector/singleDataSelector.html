<template>
    <article>
        <div class="slds-combobox slds-dropdown-trigger_click slds-is-open">
            <div class={inputRequiredStyle}>
                <div class="slds-combobox__form-element slds-input-has-icon slds-input-has-icon_right" role="none">
                    <input
                        type="text"
                        onfocus={handleFocus}
                        onblur={handleBlur}
                        oninput={searchByKeyword}
                        class="slds-input slds-combobox__input slds-combobox__input-value"
                        role="combobox"
                        placeholder={placeholder}
                        value={selectedOption}
                    />
                    <span class="slds-icon_container slds-icon-utility-down slds-input__icon slds-input__icon_right">
                        <lightning-icon icon-name="utility:search" size="x-small"></lightning-icon>
                    </span>
                </div>
                <div if:true={isInputRequired} class="slds-form-element__help">Complete this field.</div>
            </div>

            <div if:true={dropdownVisible} class={dropdownClass} role="listbox" onclick={handleClickOption} onmousedown={handleDropdownMouseDown} onmouseup={handleDropdownMouseUp}>
                <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                    <li if:false={isFullMatch} class="slds-listbox__item" data-id="null" data-name={searchKey} id="newOption" onclick={handleAddNewOption}>
                        <div aria-checked="true" class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small slds-is-selected" role="option">
                            <span class="slds-media__figure slds-listbox__option-icon"></span>
                            <span class="slds-media__body">
                                <span class="slds-truncate" title="">{searchKey}</span>
                            </span>
                            <span class="slds-media__body slds-text-align_right">
                                <span class="slds-truncate slds-text-color_weak" title="">Use a New Value</span>
                            </span>
                        </div>
                    </li>
                    <li role="presentation" class="slds-listbox__item" for:each={optionWithIsCheckedList} for:item="data" key={data.index}>
                        <c-data-selector-item item={data} highlight-word={searchKey} data-id={data.index} data-name={data.name}></c-data-selector-item>
                    </li>
                </ul>
            </div>
        </div>
    </article>
</template>
