import { createElement } from "lwc";
import { flushPromises } from "c/utils";
import SingleDataSelector from "c/singleDataSelector";

const optionList = [
    {
        index: 0,
        name: "Account Led",
        value: "Account led"
    },
    {
        index: 1,
        name: "General",
        value: "General"
    },
    {
        index: 2,
        name: "Sales Led",
        value: "Sales led"
    }
];
describe("c-single-data-selector", () => {
    let element;
    let shadowRoot;
    beforeEach(() => {
        element = createElement("c-single-data-selector", {
            is: SingleDataSelector
        });
        element.optionList = optionList;
        shadowRoot = element.shadowRoot;
        document.body.appendChild(element);
    });

    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    test("should show dropdown when focus input", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        const dropdown = shadowRoot.querySelector(".slds-dropdown");
        expect(dropdown).toBeTruthy();
    });

    test("should hide dropdown when blur input", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        let dropdown = shadowRoot.querySelector(".slds-dropdown");
        expect(dropdown).toBeTruthy();

        input.dispatchEvent(new CustomEvent("blur"));
        await flushPromises();

        dropdown = shadowRoot.querySelector(".slds-dropdown");

        expect(dropdown).toBeFalsy();
    });

    test("should show dropdown when blur input and mousedown in dropdown", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        let dropdown = shadowRoot.querySelector(".slds-dropdown");
        dropdown.dispatchEvent(new CustomEvent("mousedown"));
        await flushPromises();

        input.dispatchEvent(new CustomEvent("blur"));
        await flushPromises();

        dropdown = shadowRoot.querySelector(".slds-dropdown");
        expect(dropdown).toBeTruthy();
    });

    test("should hide dropdown when blur input and mouseup in dropdown", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        let dropdown = shadowRoot.querySelector(".slds-dropdown");
        dropdown.dispatchEvent(new CustomEvent("mouseup"));
        await flushPromises();

        input.dispatchEvent(new CustomEvent("blur"));
        await flushPromises();

        dropdown = shadowRoot.querySelector(".slds-dropdown");
        expect(dropdown).toBeFalsy();
    });

    test("should show full and part match option success when input keyword to search", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        const inputEvent = new CustomEvent("input", {
            target: { value: "account" }
        });
        input.value = "account";
        input.dispatchEvent(inputEvent);
        await flushPromises();

        const dataSelectorItems = element.shadowRoot.querySelectorAll("c-data-selector-item");
        expect(dataSelectorItems.length).toBe(1);
        expect(dataSelectorItems[0].item).toStrictEqual({ index: 0, isChecked: false, name: "Account Led", value: "Account led" });

        const addItemInfo = shadowRoot.querySelectorAll(".slds-truncate");
        expect(addItemInfo[0].textContent).toBe("account");
        expect(addItemInfo[1].textContent).toBe("Use a New Value");
    });

    test("should select option success when click option", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        const optionSelect = jest.fn();
        element.addEventListener("optionselect", optionSelect);

        let inputEvent = new CustomEvent("input", {
            target: { value: "Account Led" }
        });
        input.value = "account";
        input.dispatchEvent(inputEvent);
        await flushPromises();

        const dropdownList = shadowRoot.querySelector(".slds-dropdown");
        dropdownList.dispatchEvent(new CustomEvent("click", { event: { target: "Account Led" } }));
        expect(optionSelect).toHaveBeenCalledTimes(2);
    });

    test("should use new value success when click new value", async () => {
        const input = shadowRoot.querySelector("input");
        input.dispatchEvent(new CustomEvent("focus"));
        await flushPromises();

        const optionSelect = jest.fn();
        element.addEventListener("optionselect", optionSelect);

        const inputEvent = new CustomEvent("input", {
            target: { value: "new value" }
        });
        input.value = "new value";
        input.dispatchEvent(inputEvent);
        await flushPromises();

        const newOption = shadowRoot.querySelectorAll(".slds-listbox__item")[0];
        newOption.dispatchEvent(new CustomEvent("click", { event: { target: "new value" } }));
        expect(optionSelect).toHaveBeenCalledTimes(2);
    });
});
