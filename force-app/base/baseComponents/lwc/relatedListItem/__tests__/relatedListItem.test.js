import { createElement } from "lwc";
import RelatedListItem from "c/relatedListItem";

describe("c-related-list-item", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should render related list item when item have value", () => {
        // Arrange
        const element = createElement("c-related-list-item", {
            is: RelatedListItem
        });
        element.item = {
            title: "title",
            titleHref: "/",
            fields: [
                { label: "Name1", value: "Sow 1", type: "link", href: "/" },
                { label: "Name2", value: "Draft", type: "text" },
                { label: "Name3", value: "2023-02-02", type: "date" },
                { label: "Name4", value: "10000000", type: "currency", unit: "USD" }
            ]
        }; // Act
        document.body.appendChild(element);

        // Assert
        expect(element).toMatchSnapshot();
    });

    it("should render related list item when item is empty", () => {
        // Arrange
        const element = createElement("c-related-list-item", {
            is: RelatedListItem
        });
        // Act
        document.body.appendChild(element);

        // Assert
        expect(element).toMatchSnapshot();
    });
});
