// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`c-related-list-item should render related list item when item have value 1`] = `
<c-related-list-item
  __lwc_scope_token__=""
>
  #shadow-root(open)
    <section
      __lwc_scope_token__=""
      class="slds-m-top_small"
    >
      <a
        __lwc_scope_token__=""
        class="slds-truncate title"
        href="/"
        title="title"
      >
        title
      </a>
      <dl
        __lwc_scope_token__=""
        class="slds-list_horizontal slds-wrap"
      >
        <dt
          __lwc_scope_token__=""
          class="slds-item_label slds-text-color_weak slds-truncate"
          title="Name1"
        >
          Name1:
        </dt>
        <dd
          __lwc_scope_token__=""
          class="slds-item_detail slds-truncate"
        >
          <a
            __lwc_scope_token__=""
            href="/"
            title="Sow 1"
          >
            Sow 1
          </a>
        </dd>
      </dl>
      <dl
        __lwc_scope_token__=""
        class="slds-list_horizontal slds-wrap"
      >
        <dt
          __lwc_scope_token__=""
          class="slds-item_label slds-text-color_weak slds-truncate"
          title="Name2"
        >
          Name2:
        </dt>
        <dd
          __lwc_scope_token__=""
          class="slds-item_detail slds-truncate"
        >
          <span
            __lwc_scope_token__=""
            title="Draft"
          >
             Draft 
          </span>
        </dd>
      </dl>
      <dl
        __lwc_scope_token__=""
        class="slds-list_horizontal slds-wrap"
      >
        <dt
          __lwc_scope_token__=""
          class="slds-item_label slds-text-color_weak slds-truncate"
          title="Name3"
        >
          Name3:
        </dt>
        <dd
          __lwc_scope_token__=""
          class="slds-item_detail slds-truncate"
        >
          <lightning-formatted-date-time
            __lwc_scope_token__=""
          >
            #shadow-root(open)
          </lightning-formatted-date-time>
        </dd>
      </dl>
      <dl
        __lwc_scope_token__=""
        class="slds-list_horizontal slds-wrap"
      >
        <dt
          __lwc_scope_token__=""
          class="slds-item_label slds-text-color_weak slds-truncate"
          title="Name4"
        >
          Name4:
        </dt>
        <dd
          __lwc_scope_token__=""
          class="slds-item_detail slds-truncate"
        >
          <lightning-formatted-number
            __lwc_scope_token__=""
          >
            #shadow-root(open)
          </lightning-formatted-number>
        </dd>
      </dl>
    </section>
</c-related-list-item>
`;

exports[`c-related-list-item should render related list item when item is empty 1`] = `
<c-related-list-item
  __lwc_scope_token__=""
>
  #shadow-root(open)
    <section
      __lwc_scope_token__=""
      class="slds-m-top_small"
    >
      <a
        __lwc_scope_token__=""
        class="slds-truncate title"
      />
    </section>
</c-related-list-item>
`;
