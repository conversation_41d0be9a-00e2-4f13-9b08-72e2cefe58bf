import { LightningElement, api } from "lwc";

export default class RelatedListItem extends LightningElement {
    @api item = {};

    get _item() {
        if (this.item && this.item.fields) {
            return {
                title: this.item.title,
                titleHref: this.item.titleHref,
                fields: this.item.fields.map((field) => ({
                    label: field.label,
                    value: field.value,
                    href: field.href,
                    unit: field.unit,
                    isCurrencyType: field.type === "currency",
                    isLinkType: field.type === "link",
                    isTextType: field.type === "text",
                    isDateType: field.type === "date"
                }))
            };
        }
        return {};
    }
}
