/* Generated by apex-mocks-generator version 4.0.1 */
@isTest
public class fflib_Mocks {
    public class Mockfflib_MyList implements fflib_MyList.IList {
        private fflib_ApexMocks mocks;

        public Mockfflib_MyList(fflib_ApexMocks mocks) {
            this.mocks = mocks;
        }

        public void add(String value) {
            mocks.mockVoidMethod(this, 'add', new List<Type>{ System.Type.forName('String') }, new List<Object>{ value });
        }

        public void add(String value1, String value2, String value3, String value4) {
            mocks.mockVoidMethod(
                this,
                'add',
                new List<Type>{ System.Type.forName('String'), System.Type.forName('String'), System.Type.forName('String'), System.Type.forName('String') },
                new List<Object>{ value1, value2, value3, value4 }
            );
        }

        public void addMore(String value) {
            mocks.mockVoidMethod(this, 'addMore', new List<Type>{ System.Type.forName('String') }, new List<Object>{ value });
        }

        public void add(String[] value) {
            mocks.mockVoidMethod(this, 'add', new List<Type>{ System.Type.forName('String[]') }, new List<Object>{ value });
        }

        public String get(Integer index) {
            return (String) mocks.mockNonVoidMethod(this, 'get', new List<Type>{ System.Type.forName('Integer') }, new List<Object>{ index });
        }

        public String get2(Integer index, String value) {
            return (String) mocks.mockNonVoidMethod(this, 'get2', new List<Type>{ System.Type.forName('Integer'), System.Type.forName('String') }, new List<Object>{ index, value });
        }

        public void clear() {
            mocks.mockVoidMethod(this, 'clear', new List<Type>{}, new List<Object>{});
        }

        public Boolean isEmpty() {
            return (Boolean) mocks.mockNonVoidMethod(this, 'isEmpty', new List<Type>{}, new List<Object>{});
        }

        public void set(Integer index, Object value) {
            mocks.mockVoidMethod(this, 'set', new List<Type>{ System.Type.forName('Integer'), System.Type.forName('Object') }, new List<Object>{ index, value });
        }
    }
}
