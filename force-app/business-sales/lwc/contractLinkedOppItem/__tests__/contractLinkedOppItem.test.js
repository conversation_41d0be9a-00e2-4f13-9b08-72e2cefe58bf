import { createElement } from "lwc";
import ContractLinkedOppItem from "c/contractLinkedOppItem";
import getOpportunitiesById from "@salesforce/apex/OpportunityController.getOpportunitiesById";
import { flushPromises } from "c/utils";
import opportunityData from "./data/opportunity-data.json";


jest.mock(
    "@salesforce/apex/OpportunityController.getOpportunitiesById",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

describe("c-contract-linked-opp-item", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    it("should default render one opportunity item", async () => {
        // Arrange
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.associatedOpportunityItems = [
            {
                order: 1,
                id: "64810cf6-a9f1-4a0c-8fa5-4aef505022cd"
            }
        ];

        document.body.appendChild(element);
        await flushPromises();
        // Assert
        const oppItem = element.shadowRoot.querySelectorAll(".opp-item");
        const orderValue = element.shadowRoot.querySelectorAll(".order-value");
        expect(oppItem.length).toBe(1);
        expect(orderValue[0].textContent).toBe("1");
    });

    it("should render opportunity item when associatedOpportunityItems have value", async () => {
        // Arrange
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.associatedOpportunityItems = [
            {
                opportunityId: "0066300000MTXt9AAH",
                opportunityCurrency: "USD",
                opportunityContractValue: 12345,
                allocateAmount: 10,
                percentage: 2,
                allocateExpense: 3
            }
        ];

        document.body.appendChild(element);
        await flushPromises();
        // Assert
        const oppItem = element.shadowRoot.querySelectorAll(".opp-item");
        const orderValue = element.shadowRoot.querySelectorAll(".order-value");
        const opp = element.shadowRoot.querySelectorAll("lightning-record-picker");
        const allocateAmount = element.shadowRoot.querySelectorAll('c-tw-currency-input[data-type="allocateAmount"]');
        const percentInput = element.shadowRoot.querySelectorAll('lightning-input[data-type="percentage"]');
        const allocateExpense = element.shadowRoot.querySelectorAll('lightning-input[data-type="allocateExpense"]');
        const opportunityContractValue = element.shadowRoot.querySelectorAll('c-tw-currency-input[data-type="opportunityContractValue"]');

        expect(oppItem.length).toBe(1);
        expect(orderValue[0].textContent).toBe("1");
        expect(opp[0].value).toBe("0066300000MTXt9AAH");
        expect(allocateAmount[0].value).toBe(10);
        expect(percentInput[0].value).toBe(2);
        expect(allocateExpense[0].value).toBe(3);
        expect(opportunityContractValue[0].value).toBe(12345);
    });

    it("should handle multi fields change successfully", async () => {
        getOpportunitiesById.mockResolvedValue(opportunityData);
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.contract = {
            amount: 100,
            estimatedDiscount: 100,
            expense: 100
        };
        element.associatedOpportunityItems = [
            {
                order: 1,
                id: "64810cf6-a9f1-4a0c-8fa5-4aef505022cd"
            }
        ];
        document.body.appendChild(element);
        await flushPromises();
        const handleContractOpportunityUpdate = jest.fn();
        element.addEventListener("contractopportunityupdate", handleContractOpportunityUpdate);

        let percentInput = element.shadowRoot.querySelectorAll('lightning-input[data-type="percentage"]');
        percentInput[0].dispatchEvent(new CustomEvent("change", { detail: { value: 3 }, target: { dataset: { type: "percentage" } } }));
        await flushPromises();

        percentInput = element.shadowRoot.querySelectorAll('lightning-input[data-type="percentage"]');
        const allocateAmount = element.shadowRoot.querySelectorAll('c-tw-currency-input[data-type="allocateAmount"]');
        const allocateExpense = element.shadowRoot.querySelectorAll('lightning-input[data-type="allocateExpense"]');
        const allocateEstimatedDiscount = element.shadowRoot.querySelectorAll('lightning-input[data-type="allocateEstimatedDiscount"]');
        expect(percentInput[0].value).toBe(3);
        expect(allocateAmount[0].value).toBe(3);
        expect(allocateExpense[0].value).toBe(3);
        expect(allocateEstimatedDiscount[0].value).toBe(3);

        let opp = element.shadowRoot.querySelectorAll("lightning-record-picker");
        opp[0].dispatchEvent(new CustomEvent("change", { detail: { recordId: "0061900000BcBqaAAH" }, target: { dataset: { type: "opportunityId", index: 1 } } }));
        await flushPromises();
        opp = element.shadowRoot.querySelectorAll("lightning-record-picker");
        const opportunityContractValue = element.shadowRoot.querySelectorAll('c-tw-currency-input[data-type="opportunityContractValue"]');
        expect(opp[0].value).toBe("0061900000BcBqaAAH");
        expect(opportunityContractValue[0].value).toBe(333);

        expect(handleContractOpportunityUpdate).toHaveBeenCalled();
    });

    it("should add and delete item successfully", async () => {
        getOpportunitiesById.mockResolvedValue(opportunityData);
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.contract = {
            amount: 100,
            estimatedDiscount: 100,
            expense: 100
        };
        element.associatedOpportunityItems = [
            {
                order: 1,
                id: "64810cf6-a9f1-4a0c-8fa5-4aef505022cd"
            }
        ];
        const handleContractOpportunityUpdate = jest.fn();
        element.addEventListener("contractopportunityupdate", handleContractOpportunityUpdate);

        document.body.appendChild(element);
        await flushPromises();
        let oppItem = element.shadowRoot.querySelectorAll(".opp-item");
        expect(oppItem.length).toBe(1);

        const addNewItemButton = element.shadowRoot.querySelector("lightning-button");
        addNewItemButton.click();
        await flushPromises();
        oppItem = element.shadowRoot.querySelectorAll(".opp-item");
        expect(oppItem.length).toBe(2);
        expect(handleContractOpportunityUpdate).toHaveBeenCalledTimes(1);

        const deleteItemButton = element.shadowRoot.querySelector("lightning-button-icon");
        deleteItemButton.click();
        await flushPromises();
        oppItem = element.shadowRoot.querySelectorAll(".opp-item");
        expect(oppItem.length).toBe(1);

        expect(handleContractOpportunityUpdate).toHaveBeenCalledTimes(2);
    });

    it("should valid success successfully", async () => {
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.associatedOpportunityItems = [
            {
                opportunityId: "0066300000MTXt9AAH",
                opportunityCurrency: "USD",
                opportunityContractValue: 10,
                allocateAmount: 10,
                percentage: 2,
                allocateExpense: 3,
                allocateEstimatedDiscount: 4
            }
        ];
        element.contract = {
            amount: 100,
            estimatedDiscount: 100,
            expense: 100
        };

        document.body.appendChild(element);

        element.shadowRoot.querySelectorAll(".required-input").forEach((item) => {
            item.reportValidity = jest.fn(() => true);
        });
        await flushPromises();
        const reportValidity = element.reportValidity();
        const errInfo = element.shadowRoot.querySelectorAll(".error-list li");
        expect(reportValidity).toBe(true);
        expect(errInfo.length).toBe(0);
    });

    it("should valid success successfully when negative amount", async () => {
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.associatedOpportunityItems = [
            {
                opportunityId: "0066300000MTXt9AAH",
                opportunityCurrency: "USD",
                opportunityContractValue: 10,
                allocateAmount: -10,
                percentage: 2,
                allocateExpense: 3,
                allocateEstimatedDiscount: 4
            }
        ];
        element.contract = {
            amount: -100,
            estimatedDiscount: 100,
            expense: 100
        };

        document.body.appendChild(element);

        element.shadowRoot.querySelectorAll(".required-input").forEach((item) => {
            item.reportValidity = jest.fn(() => true);
        });
        await flushPromises();
        const reportValidity = element.reportValidity();
        const errInfo = element.shadowRoot.querySelectorAll(".error-list li");
        expect(reportValidity).toBe(true);
        expect(errInfo.length).toBe(0);
    });

    it("should valid failed successfully", async () => {
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.associatedOpportunityItems = [
            {
                opportunityId: "0066300000MTXt9AAH",
                opportunityCurrency: "USD",
                opportunityContractValue: 1000,
                allocateAmount: 1000,
                allocateEstimatedDiscount: 1000,
                percentage: 20000,
                allocateExpense: 300
            }
        ];
        element.contract = {
            amount: 100,
            estimatedDiscount: 100,
            expense: 100,
            currency: "USD"
        };

        document.body.appendChild(element);

        element.shadowRoot.querySelectorAll(".required-input").forEach((item) => {
            item.reportValidity = jest.fn(() => true);
        });
        const reportValidity = element.reportValidity();
        await flushPromises();
        const errInfo = element.shadowRoot.querySelectorAll(".error-list li");
        expect(reportValidity).toBe(false);
        expect(errInfo.length).toBe(3);
    });

    it("should valid failed successfully when negative amount", async () => {
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.associatedOpportunityItems = [
            {
                opportunityId: "0066300000MTXt9AAH",
                opportunityCurrency: "USD",
                opportunityContractValue: 1000,
                allocateAmount: -1000,
                allocateEstimatedDiscount: 1000,
                percentage: 20000,
                allocateExpense: 300
            }
        ];
        element.contract = {
            amount: -100,
            estimatedDiscount: 100,
            expense: 100,
            currency: "USD"
        };

        document.body.appendChild(element);

        element.shadowRoot.querySelectorAll(".required-input").forEach((item) => {
            item.reportValidity = jest.fn(() => true);
        });
        const reportValidity = element.reportValidity();
        await flushPromises();
        const errInfo = element.shadowRoot.querySelectorAll(".error-list li");
        expect(reportValidity).toBe(false);
        expect(errInfo.length).toBe(3);
    });

    it("should remove opportunity amount and currency when remove chosen opportunity", async () => {
        getOpportunitiesById.mockResolvedValue(opportunityData);
        const element = createElement("c-contract-linked-opp-item", {
            is: ContractLinkedOppItem
        });
        element.contract = {
            amount: 100,
            estimatedDiscount: 100,
            expense: 100
        };
        element.associatedOpportunityItems = [
            {
                order: 1,
                id: "64810cf6-a9f1-4a0c-8fa5-4aef505022cd"
            }
        ];
        document.body.appendChild(element);
        await flushPromises();

        let recordPicker = element.shadowRoot.querySelectorAll("lightning-record-picker");
        recordPicker[0].dispatchEvent(new CustomEvent("change", { detail: { recordId: "" }, target: { dataset: { type: "opportunityId", index: 1 } } }));
        await flushPromises();
        let opportunityContractValue = element.shadowRoot.querySelectorAll("c-tw-currency-input");
        expect(recordPicker[0].value).toBe("");
        expect(opportunityContractValue[0].value).toBe("");
        expect(opportunityContractValue[0].currency).toBe("");

        recordPicker[0].dispatchEvent(new CustomEvent("change", { detail: { recordId: "0061900000BcBqaAAH" }, target: { dataset: { type: "opportunityId", index: 1 } } }));
        await flushPromises();
        expect(recordPicker[0].value).toBe("0061900000BcBqaAAH");
        expect(opportunityContractValue[0].value).toBe(333);
        expect(opportunityContractValue[0].currency).toBe("USD");
    });
});
