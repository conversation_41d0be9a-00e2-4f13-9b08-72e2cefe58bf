import { createElement } from "lwc";
import UpdateContractType from "c/updateContractType";
import { getRecord } from "lightning/uiRecordApi";
import { getPicklistValues } from "lightning/uiObjectInfoApi";
import { registerLdsTestWireAdapter } from "@salesforce/sfdx-lwc-jest";
import { flushPromises } from "c/utils";
import { trackCRMUserEvent } from "c/matomoTracker";
import contractTypePopUp from "../../contractTypePopUp/contractTypePopUp";
import amountAdjustmentSchedulePopUp from "../../amountAdjustmentSchedulePopUp/amountAdjustmentSchedulePopUp";
import { ShowToastEventName } from "lightning/platformShowToastEvent";

jest.mock(
    "@salesforce/apex/OpportunityHistoryController.getOpportunityHistoryDTOListById",
    () => {
        return {
            default: jest.fn()
        };
    },
    {
        virtual: true
    }
);

const mockOpportunityRecord = require("./data/OpportunityRecord.json");
const mockMiscOpportunityRecord = require("./data/MiscOpportunityRecord.json");
const mockContractTypePickList = require("./data/ContractTypePickList.json");
// eslint-disable-next-line @lwc/lwc/no-unexpected-wire-adapter-usages
const getRecordAdapter = registerLdsTestWireAdapter(getRecord);

// eslint-disable-next-line @lwc/lwc/no-unexpected-wire-adapter-usages
const getPickListAdapter = registerLdsTestWireAdapter(getPicklistValues);

describe("c-update-contract-type", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        // Prevent data saved on mocks from leaking between tests
        jest.clearAllMocks();
    });

    it("should in view model and", async () => {
        // Arrange
        const element = createElement("c-update-contract-type", {
            is: UpdateContractType
        });

        // Act
        document.body.appendChild(element);

        getRecordAdapter.emit(mockOpportunityRecord);
        getPickListAdapter.emit(mockContractTypePickList);

        await flushPromises();

        // Assert
        const twFieldView = element.shadowRoot.querySelector("c-tw-field-view");
        expect(twFieldView.value).toBe(mockOpportunityRecord.fields.Contract_Type__c.value);
    });

    it("should open and close modal when click edit icon for contract type and then click cancel", async () => {
        // Arrange
        const element = createElement("c-update-contract-type", {
            is: UpdateContractType
        });
        document.body.appendChild(element);
        getRecordAdapter.emit(mockOpportunityRecord);
        getPickListAdapter.emit(mockContractTypePickList);
        await flushPromises();

        const twFieldView = element.shadowRoot.querySelector("c-tw-field-view");
        contractTypePopUp.open = jest.fn().mockReturnValue({
            type: "cancel"
        });

        // Act
        twFieldView.dispatchEvent(new CustomEvent('clickedit'));
        await flushPromises();

        // Assert
        expect(contractTypePopUp.open.mock.calls).toHaveLength(1);
        expect(trackCRMUserEvent).toHaveBeenCalledTimes(2);
        expect(trackCRMUserEvent.mock.calls[0][0]).toStrictEqual({
            category: "Opportunity details",
            action: "Contract type",
            name: "Edit Contract Type"
        });
        expect(trackCRMUserEvent.mock.calls[1][0]).toStrictEqual({
            category: "Opportunity details",
            action: "Contract type",
            name: "Cancel"
        });
    });

    it("should save contract type and amount adjustment schedule success when click done in contract type modal", async () => {
        // Arrange
        const element = createElement("c-update-contract-type", {
            is: UpdateContractType
        });
        document.body.appendChild(element);
        getRecordAdapter.emit(mockOpportunityRecord);
        getPickListAdapter.emit(mockContractTypePickList);
        await flushPromises();

        const twFieldView = element.shadowRoot.querySelector("c-tw-field-view");
        let scheduleEl = element.shadowRoot.querySelector(".schedule");

        expect(twFieldView.value).toBe("Time & Materials");
        expect(scheduleEl).toBeFalsy();

        contractTypePopUp.open = jest.fn().mockReturnValue({
            type: "done",
            detail: {
                contractType: "Miscellaneous",
                fixedBidSchedules: [{ amount: 111111, date: "2023-04-13", office: "Barcelona", key: "0g5pb688jr" }]
            }
        });

        // Act
        twFieldView.dispatchEvent(new CustomEvent('clickedit'));
        await flushPromises();

        // Assert
        expect(contractTypePopUp.open.mock.calls).toHaveLength(1);
        expect(twFieldView.value).toBe("Miscellaneous");

        scheduleEl = element.shadowRoot.querySelector(".schedule");
        expect(scheduleEl).toBeTruthy();

        const data = element.shadowRoot.querySelector("lightning-formatted-date-time");
        expect(data.value).toBe("2023-04-13");

        expect(trackCRMUserEvent).toHaveBeenCalledTimes(1);
    });

    it("should open amount adjustment schedule modal when click edit icon for schedule", async () => {
        // Arrange
        const element = createElement("c-update-contract-type", {
            is: UpdateContractType
        });
        document.body.appendChild(element);
        getRecordAdapter.emit(mockMiscOpportunityRecord);
        getPickListAdapter.emit(mockContractTypePickList);
        await flushPromises();

        let scheduleEl = element.shadowRoot.querySelector(".schedule");
        expect(scheduleEl).toBeTruthy();

        const scheduleIcon = element.shadowRoot.querySelector(".edit_schedule_icon");
        amountAdjustmentSchedulePopUp.open = jest.fn().mockResolvedValue({
            type: "cancel",
            detail: [
                {
                    amount: 123,
                    date: "2022-02-13",
                    office: "Wuhan"
                }
            ]
        });

        // Act
        scheduleIcon.click();
        await flushPromises();

        // Assert
        expect(amountAdjustmentSchedulePopUp.open.mock.calls).toHaveLength(1);
        expect(trackCRMUserEvent).toHaveBeenCalledTimes(1);
        expect(trackCRMUserEvent.mock.calls[0][0]).toStrictEqual({
            category: "Opportunity details",
            action: "Contract type",
            name: "Edit Amount Adjustment Schedule"
        });
    });

    it("should save amount adjustment schedule success when click done in schedule modal", async () => {
        // Arrange
        const element = createElement("c-update-contract-type", {
            is: UpdateContractType
        });
        document.body.appendChild(element);
        getRecordAdapter.emit(mockMiscOpportunityRecord);
        getPickListAdapter.emit(mockContractTypePickList);
        const handlerSuccess = jest.fn();
        element.addEventListener(ShowToastEventName, handlerSuccess);
        await flushPromises();

        const scheduleIcon = element.shadowRoot.querySelector(".edit_schedule_icon");
        amountAdjustmentSchedulePopUp.open = jest.fn().mockResolvedValue({
            type: "done",
            detail: [
                {
                    amount: 123,
                    date: "2022-02-13",
                    office: "Wuhan"
                }
            ]
        });

        // Act
        scheduleIcon.click();
        await flushPromises();

        // Assert
        expect(amountAdjustmentSchedulePopUp.open.mock.calls).toHaveLength(1);
        const data = element.shadowRoot.querySelector("lightning-formatted-date-time");
        expect(data.value).toBe("2022-02-13");
        expect(handlerSuccess).toHaveBeenCalledTimes(1);
        expect(handlerSuccess.mock.calls[0][0].detail.variant).toBe("success");
        expect(trackCRMUserEvent).toHaveBeenCalledTimes(2);
    });
});
