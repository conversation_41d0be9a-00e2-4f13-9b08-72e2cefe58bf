
import { createElement } from "lwc";
import { flushPromises } from "c/utils";
import ContractSowOrOtherValueForm from "c/contractSowOrOtherValueForm";
import { trackCRMUserEvent } from "c/matomoTracker";

const mockNoAmountContract = require("./data/mockNoAmountContract.json");
const mockOppSearchableComboBox = require("./data/mockOppSearchableComboBox.json");
const mockOneOpportunity = require("./data/mockOneOpportunity.json");

describe("c-contract-sow-or-other-value-form", () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
        jest.clearAllMocks();
    });

    describe("should show correct layout for different subtype", () => {
        it("should add error when account is invalid", async () => {
            const setCustomValidity = jest.fn();
            const reportValidity = jest.fn();
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "No Amount",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                accountId: "0015500001cafpwAAA"
            };
            element.shadowRoot.querySelector = jest.fn().mockReturnValue({
                setCustomValidity,
                reportValidity
            });
            element.workingAccountIds = ["0015500001cafpwAAB"];
            document.body.appendChild(element);
            await flushPromises(300);
            expect(setCustomValidity).toHaveBeenCalled();
            expect(reportValidity).toHaveBeenCalled();
            expect(setCustomValidity).toHaveBeenCalledWith(
                "You cannot create contract under account you are not managing. Contact the account owner or CLTs to add you to account team as a CLT or CP/BDM."
            );
        });

        it("should not add error when account is invalid and user is admin", async () => {
            const setCustomValidity = jest.fn();
            const reportValidity = jest.fn();
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "No Amount",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                accountId: "0015500001cafpwAAA"
            };
            element.shadowRoot.querySelector = jest.fn().mockReturnValue({
                setCustomValidity,
                reportValidity
            });
            element.isAdminOrPowerUser = true;
            element.workingAccountIds = ["0015500001cafpwAAB"];
            document.body.appendChild(element);
            await flushPromises(300);
            expect(setCustomValidity).toHaveBeenCalledTimes(0);
            expect(reportValidity).toHaveBeenCalledTimes(0);
        });

        it("should not add error when account is valid", async () => {
            const setCustomValidity = jest.fn();
            const reportValidity = jest.fn();
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "No Amount",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                accountId: "0015500001cafpwAAA"
            };
            element.shadowRoot.querySelector = jest.fn().mockReturnValue({
                setCustomValidity,
                reportValidity
            });
            element.workingAccountIds = ["0015500001cafpwAAA"];
            document.body.appendChild(element);
            await flushPromises(300);
            expect(setCustomValidity).toHaveBeenCalled();
            expect(reportValidity).toHaveBeenCalled();
            expect(setCustomValidity).toHaveBeenCalledWith("");
        });

        it("should add error when account is null", async () => {
            const setCustomValidity = jest.fn();
            const reportValidity = jest.fn();
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "No Amount",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                accountId: ""
            };
            element.shadowRoot.querySelector = jest.fn().mockReturnValue({
                setCustomValidity,
                reportValidity
            });
            element.workingAccountIds = ["0015500001cafpwAAA"];
            document.body.appendChild(element);
            await flushPromises(300);
            expect(setCustomValidity).toHaveBeenCalled();
            expect(reportValidity).toHaveBeenCalled();
            expect(setCustomValidity).toHaveBeenCalledWith("Complete this field.");
        });

        it("should show default value with Estimated Spend subtype when associatedOpportunity have value", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                recordType: "Statement of Work",
                subtype: "Estimated Spend",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                accountId: "0015500001cafpwAAA",
                name: "testA"
            };
            element.associatedOpportunity = { isLinkOneOpp: false, oppItems: [{ opportunityId: "oppoId", opportunityAmount: 100, opportunityCurrency: "USD", order: 1 }] };
            document.body.appendChild(element);
            await flushPromises();

            const contractValueElement = element.shadowRoot.querySelector("c-contract-value-timeframe");
            const oppSelectorElement = element.shadowRoot.querySelector("c-contract-linked-opportunity-selector");
            const oppSearchableComboBoxElement = element.shadowRoot.querySelector("c-contract-opp-searchable-combo-box");
            const expendElements = element.shadowRoot.querySelectorAll("c-tw-expandable-section");
            expect(contractValueElement).toBeTruthy();
            expect(oppSelectorElement).toBeTruthy();
            expect(oppSearchableComboBoxElement).toBeFalsy();
            expect(expendElements.length).toBe(2);
            expect(oppSelectorElement.associatedOpportunity).toStrictEqual({
                isLinkOneOpp: false,
                oppItems: [{ opportunityAmount: 100, opportunityCurrency: "USD", opportunityId: "oppoId", order: 1 }]
            });

            const inputElements = element.shadowRoot.querySelectorAll("lightning-combobox");
            const accountElement = element.shadowRoot.querySelector("lightning-record-picker");
            expect(inputElements.length).toBe(1);
            expect(inputElements[0].value).toBe("Estimated Spend");
            expect(accountElement.value).toBe("0015500001cafpwAAA");

            expect(trackCRMUserEvent).toHaveBeenCalledTimes(1);
            expect(trackCRMUserEvent.mock.calls[0][0]).toStrictEqual({
                category: "Contract Creation",
                action: "Create SOW contract",
                name: "Enter SOW Contract Value page"
            });
        });

        it("should show default value with no amount subtype when contract has some field value", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = mockNoAmountContract;
            element.associatedOpportunity = mockOppSearchableComboBox;
            document.body.appendChild(element);
            await flushPromises();

            const inputPills = [
                { href: "/0065500000M6sBIAAJ", id: "0065500000M6sBIAAJ", isLink: true, label: "test1", tagId: "0065500000M6sBIAAJ" },
                { href: "/0065500000M6sBIAAK", id: "0065500000M6sBIAAK", isLink: true, label: "test2", tagId: "0065500000M6sBIAAK" }
            ];

            const contractValueElement = element.shadowRoot.querySelector("c-contract-value-timeframe");
            const oppSelectorElement = element.shadowRoot.querySelector("c-contract-linked-opportunity-selector");
            const linkedOppElement = element.shadowRoot.querySelector("c-contract-linked-opportunity");
            const oppSearchableComboBoxElement = linkedOppElement.shadowRoot.querySelector("c-contract-opp-searchable-combo-box");
            expect(contractValueElement).toBeFalsy();
            expect(oppSelectorElement).toBeFalsy();
            expect(linkedOppElement).toBeTruthy();
            expect(oppSearchableComboBoxElement).toBeTruthy();
            expect(oppSearchableComboBoxElement.inputPill).toMatchObject(inputPills);

            const comboboxElements = element.shadowRoot.querySelectorAll("lightning-combobox");
            const dateRangeElement = element.shadowRoot.querySelector("c-tw-date-range")
            const accountElement = element.shadowRoot.querySelector("lightning-record-picker");
            expect(comboboxElements.length).toBe(1);
            expect(dateRangeElement.startDateValue).toBe("2023-10-18");
            expect(dateRangeElement.endDateValue).toBe("2023-10-31");
            expect(accountElement.value).toBe("0015500001cafpwAAA");
        });

        it("should show default value with no subtype for back and next scenario", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                accountId: "0015500001cafpwAAA",
                name: "testA"
            };
            element.associatedOpportunity = { isLinkOneOpp: false, oppItems: [{ opportunityId: "oppoId", opportunityAmount: 100, opportunityCurrency: "USD", order: 1 }] };
            document.body.appendChild(element);
            await flushPromises();

            const contractValueElement = element.shadowRoot.querySelector("c-contract-value-timeframe");
            const oppSelectorElement = element.shadowRoot.querySelector("c-contract-linked-opportunity-selector");
            const oppSearchableComboBoxElement = element.shadowRoot.querySelector("c-contract-opp-searchable-combo-box");
            const expendElements = element.shadowRoot.querySelector("c-tw-expandable-section");
            expect(contractValueElement).toBeFalsy();
            expect(oppSelectorElement).toBeFalsy();
            expect(expendElements).toBeFalsy();
            expect(oppSearchableComboBoxElement).toBeFalsy();

            const comboboxElements = element.shadowRoot.querySelectorAll("lightning-combobox");
            const accountElement = element.shadowRoot.querySelector("lightning-record-picker");
            expect(comboboxElements.length).toBe(1);
            expect(accountElement.value).toBe("0015500001cafpwAAA");
        });
    });

    function setFormInputValidity(shadowRoot, mockValue) {
        shadowRoot.querySelectorAll("lightning-input-field").forEach((inputElement) => {
            jest.spyOn(inputElement, "reportValidity", null).mockReturnValue(mockValue);
        });
    }

    describe("should return correct value for user behaviors", () => {
        it("should dispatch the input value event and clear Value when change subtype", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "Estimated Spend",
                startDate: "2023-10-31",
                endDate: "2023-11-07",
                name: "testA",
                accountId: "0015500001cafpwAAA"
            };
            element.associatedOpportunity = { isLinkOneOpp: false, oppItems: [{ opportunityId: "oppoId", opportunityAmount: 100, opportunityCurrency: "USD", order: 1 }] };
            document.body.appendChild(element);
            const handleInputChange = jest.fn();
            element.addEventListener("contractfieldupdate", handleInputChange);
            await flushPromises();

            const formEl = element.shadowRoot.querySelectorAll("lightning-record-edit-form")[0];
            formEl.value = "No Amount";
            formEl.fieldName = undefined;
            formEl.dataset.type = "subtype";
            formEl.dispatchEvent(new CustomEvent("change"));
            await flushPromises();

            const linkOpp = element.shadowRoot.querySelector("c-contract-linked-opportunity");
            const searchable = linkOpp.shadowRoot.querySelector("c-contract-opp-searchable-combo-box");
            expect(searchable).toBeTruthy();
            expect(searchable.inputPill).toStrictEqual([]);
            await flushPromises();

            formEl.value = "Estimated Spend";
            formEl.dispatchEvent(new CustomEvent("change"));
            await flushPromises();
            expect(handleInputChange).toHaveBeenCalledTimes(3);
            expect(handleInputChange.mock.calls[0][0].detail.subtype).toBe("No Amount");
            expect(handleInputChange.mock.calls[1][0].detail.startDate).toBe("");
            expect(handleInputChange.mock.calls[1][0].detail.endDate).toBe("");
            expect(handleInputChange.mock.calls[2][0].detail.subtype).toBe("Estimated Spend");

            formEl.value = "Intend to Spend (Target Spend)";
            formEl.dispatchEvent(new CustomEvent("change"));
            await flushPromises();
            expect(handleInputChange).toHaveBeenCalledTimes(4);
            expect(handleInputChange.mock.calls[3][0].detail.subtype).toBe("Intend to Spend (Target Spend)");
        });

        it("should select opportunity for no amount subtype", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                subtype: "No Amount",
                name: "testA",
                accountId: "0015500001cafpwAAA"
            };
            element.associatedOpportunity = {
                isLinkOneOpp: true,
                oppItems: [
                    {
                        opportunityId: "oppoId",
                        opportunityName: "UISmoke-ALB-FB"
                    }
                ]
            };
            document.body.appendChild(element);

            const handleOppChange = jest.fn();
            element.addEventListener("contractopportunityupdate", handleOppChange);
            await flushPromises();
            const linkOpp = element.shadowRoot.querySelector("c-contract-linked-opportunity");
            const searchable = linkOpp.shadowRoot.querySelector("c-contract-opp-searchable-combo-box");
            expect(searchable).toBeTruthy();
            expect(searchable.inputPill).toStrictEqual([
                {
                    id: "oppoId",
                    href: "/oppoId",
                    tagId: "oppoId",
                    label: "UISmoke-ALB-FB",
                    isLink: true
                }
            ]);

            searchable.dispatchEvent(new CustomEvent("selectvalue", { detail: { id: "oppoId", value: "name" } }));
            await flushPromises();
            expect(handleOppChange).toHaveBeenCalledTimes(1);
            expect(searchable.inputPill).toStrictEqual([
                {
                    id: "oppoId",
                    href: "/oppoId",
                    tagId: "oppoId",
                    label: "UISmoke-ALB-FB",
                    isLink: true
                },
                {
                    id: "oppoId",
                    href: "/oppoId",
                    tagId: "oppoId",
                    label: "name",
                    isLink: true
                }
            ]);
        });

        it("should dispatch the input value for input fields", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.associatedOpportunity = mockOneOpportunity;
            document.body.appendChild(element);
            const handleInputChange = jest.fn();
            element.addEventListener("contractfieldupdate", handleInputChange);
            await flushPromises();

            const formEl = element.shadowRoot.querySelectorAll("lightning-record-edit-form")[0];
            const comboboxElements = element.shadowRoot.querySelectorAll("lightning-combobox");
            const subtypeEl = comboboxElements[0];
            subtypeEl.value = "No Amount";
            formEl.dispatchEvent(new CustomEvent("change"));
            await flushPromises();
            expect(handleInputChange).toHaveBeenCalledTimes(1);
        });

        it("should return false when subtype is No Amount and field invalid", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.associatedOpportunity = mockOneOpportunity;

            document.body.appendChild(element);
            await flushPromises();

            const isValid = element.isValid();
            expect(isValid).toBe(false);
        });

        it("should return false when account invalid", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.associatedAccounts = [];
            document.body.appendChild(element);
            await flushPromises();

            setFormInputValidity(element.shadowRoot, true);

            const recordPicker = element.shadowRoot.querySelector("lightning-record-picker");
            recordPicker.reportValidity = jest.fn(() => false);

            await flushPromises();

            const isValid = element.isValid();
            expect(isValid).toBe(false);
        });

        it("should return true when when subtype is No Amount and all fields are legal", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = mockNoAmountContract;
            element.associatedOpportunity = mockOppSearchableComboBox;
            document.body.appendChild(element);
            await flushPromises();

            setFormInputValidity(element.shadowRoot, true);

            const accountEle = element.shadowRoot.querySelector("lightning-record-picker");
            accountEle.reportValidity = jest.fn(() => true);
            const dateRangeElement = element.shadowRoot.querySelector("c-tw-date-range")
            dateRangeElement.reportValidity = jest.fn(() => true)
            const contractLinkedOppElement = element.shadowRoot.querySelector("c-contract-linked-opportunity");
            contractLinkedOppElement.checkValidity = jest.fn(() => true);
            await flushPromises();
            const isValid = element.isValid();
            expect(isValid).toBe(true);
        });

        it("should return true when when subtype is not No Amount and all fields are legal", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                breakdownItems: [],
                allocationItems: [],
                soldBy: "0051T000009kJTzQAM",
                currency: "CNY",
                recordType: "Statement of Work",
                subtype: "Estimated Spend",
                startDate: "2023-10-18",
                endDate: "2023-10-31",
                accountId: "0015500001cafpwAAA"
            };
            element.associatedOpportunity = { isLinkOneOpp: false, oppItems: [{ opportunityId: "oppoId", opportunityAmount: 100, opportunityCurrency: "USD", order: 1 }] };
            document.body.appendChild(element);
            await flushPromises();

            setFormInputValidity(element.shadowRoot, true);

            element.shadowRoot.querySelector("c-contract-value-timeframe").isValid = jest.fn(() => true);
            element.shadowRoot.querySelector("c-contract-linked-opportunity-selector").reportValidity = jest.fn(() => true);
            const accountEle = element.shadowRoot.querySelector("lightning-record-picker");
            accountEle.reportValidity = jest.fn(() => true);
            const comboboxElements = element.shadowRoot.querySelectorAll("lightning-combobox");
            comboboxElements.checkValidity = jest.fn(() => true);
            await flushPromises();
            const isValid = element.isValid();
            expect(isValid).toBe(true);
        });

        it("should return false when when subtype is not No Amount and all fields are Illegal", async () => {
            const element = createElement("c-contract-sow-or-other-value-form", {
                is: ContractSowOrOtherValueForm
            });
            element.contract = {
                breakdownItems: [],
                allocationItems: [],
                soldBy: "0051T000009kJTzQAM",
                currency: "CNY",
                recordType: "Statement of Work",
                subtype: "Estimated Spend",
                startDate: "2023-10-18",
                endDate: "2023-10-31",
                accountId: "0015500001cafpwAAA"
            };
            element.associatedOpportunity = { isLinkOneOpp: false, oppItems: [{ opportunityId: "oppoId", opportunityAmount: 100, opportunityCurrency: "USD", order: 1 }] };
            document.body.appendChild(element);
            await flushPromises();

            setFormInputValidity(element.shadowRoot, true);

            element.shadowRoot.querySelector("c-contract-value-timeframe").isValid = jest.fn(() => false);
            element.shadowRoot.querySelector("c-contract-linked-opportunity-selector").reportValidity = jest.fn(() => false);
            const comboboxElements = element.shadowRoot.querySelectorAll("lightning-combobox");
            comboboxElements.checkValidity = jest.fn(() => true);
            await flushPromises();
            const isValid = element.isValid();
            expect(isValid).toBe(false);
        });
    });
});
