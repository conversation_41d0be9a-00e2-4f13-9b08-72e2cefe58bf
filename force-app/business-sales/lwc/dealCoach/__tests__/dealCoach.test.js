import { createElement } from "lwc";
import DealCoach from "c/dealCoach";
import getDealCoachesAccess from "@salesforce/apex/DealReviewController.getDealCoachesAccess";
import mockOpportunity from "./data/mockOpportunity.json";
import { flushPromises } from "c/utils";
import { registerLdsTestWireAdapter } from "@salesforce/sfdx-lwc-jest";
import { getRecord } from "lightning/uiRecordApi";


jest.mock(
    "@salesforce/apex/DealReviewController.getDealCoachesAccess",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

jest.mock(
    "@salesforce/apex/OpportunityController.getOpportunityCoachNamesById",
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);

const getRecordAdapter = registerLdsTestWireAdapter(getRecord);
describe("c-deal-coach", () => {
    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should show deal coaches and show edit icon when have access", async () => {
        getDealCoachesAccess.mockResolvedValue({
            hasDeliveryCoachAccess: true,
            hasFinanceCoachAccess: true,
            hasDemandCoachAccess: true,
            hasLegalCoachAccess: true
        });
        const element = createElement("c-deal-coach", {
            is: DealCoach
        });
        document.body.appendChild(element);
        getRecordAdapter.emit(mockOpportunity);

        await flushPromises();
        const fieldViews = element.shadowRoot.querySelectorAll("c-tw-field-view");
        expect(fieldViews.length).toEqual(4);
        expect(fieldViews[0].isHideEditButton).toBeFalsy();
        expect(fieldViews[1].isHideEditButton).toBeFalsy();
        expect(fieldViews[2].isHideEditButton).toBeFalsy();
        expect(fieldViews[3].isHideEditButton).toBeFalsy();
        const coaches = element.shadowRoot.querySelectorAll(".slds-text-link");
        expect(coaches.length).toEqual(4);
        expect(coaches[0].textContent).toBe("Delivery Approver");
        expect(coaches[1].textContent).toBe("Finance Approver");
        expect(coaches[2].textContent).toBe("Demand Approver");
        expect(coaches[3].textContent).toBe("Legal Approver");
    });

    it("should not show edit icon when have no access", async () => {
        getDealCoachesAccess.mockResolvedValue({
            hasDeliveryCoachAccess: false,
            hasFinanceCoachAccess: false,
            hasDemandCoachAccess: false,
            hasLegalCoachAccess: false
        });
        const element = createElement("c-deal-coach", {
            is: DealCoach
        });
        element.recordId = "001f000001xsee9AAA";
        document.body.appendChild(element);
        getRecordAdapter.emit(mockOpportunity);

        await flushPromises();
        const coaches = element.shadowRoot.querySelectorAll("c-tw-field-view");
        expect(coaches.length).toEqual(4);
        expect(coaches[0].isHideEditButton).toBeTruthy();
        expect(coaches[1].isHideEditButton).toBeTruthy();
        expect(coaches[2].isHideEditButton).toBeTruthy();
        expect(coaches[3].isHideEditButton).toBeTruthy();
    });
});
