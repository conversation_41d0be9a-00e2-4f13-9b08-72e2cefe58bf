({
    clickUrl: function (component, event) {
        const href = event.srcElement.href;
        window.open(href, "_blank");
        event.preventDefault();

        // track click
        const urlName = event.srcElement.name;
        const matomoTracker = component.find("idMatomoTracker");
        matomoTracker.trackCRMUserEvent({
            category: "Welcome card",
            action: "Click External Link",
            name: urlName
        });
    }
});
