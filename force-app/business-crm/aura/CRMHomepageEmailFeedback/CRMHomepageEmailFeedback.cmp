<aura:component
    implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global"
>
    <c:matomoTracker aura:id="idMatomoTracker" />
    <article class="slds-card">
        <div class="slds-container--medium slds-p-horizontal_small slds-p-vertical_small content">
            <span class="slds-card__header-title"><b>Welcome to CRM</b></span
            ><br />
            The Thoughtworks CRM team would love to hear from you!
            <br />
            <br />
            <ul class="slds-list_dotted">
                <li>Email us: <a name="Send email" href="mailto: <EMAIL>" onclick="{!c.clickUrl}"><EMAIL></a></li>
                <li>Send a chat message in the <a name="CRM Support" href="https://chat.google.com/room/AAAAOafrzvY?cls=1" onclick="{!c.clickUrl}">support chat room</a></li>
            </ul>
            <br />
            <b>Learn more about CRM</b>
            <br />
            <br />
            <ul class="slds-list_dotted">
                <li>Check out our <a name="Campus channel" href="https://campus.thoughtworks.net/learn/dashboard/channel/54" onclick="{!c.clickUrl}">Campus channel </a>for onboarding material</li>
                <li>
                    Use the
                    <a name="Data dictionary" href="https://docs.google.com/document/d/1lbblXHdBktpA11kWjyq9TAHllddmDp3W0NEj_wdE6xg/edit#heading=h.2cybl66bgdg0" onclick="{!c.clickUrl}"
                        >data dictionary
                    </a>
                    to learn more about what data you can find in CRM
                </li>
                <li>
                    Read through the
                    <a name="Thoughtworks CRM Guide" href="https://docs.google.com/document/d/1lbblXHdBktpA11kWjyq9TAHllddmDp3W0NEj_wdE6xg/edit#heading=h.eek03gr725r0" onclick="{!c.clickUrl}"
                        >Thoughtworks CRM Guide
                    </a>
                </li>
            </ul>
        </div>
    </article>
</aura:component>
