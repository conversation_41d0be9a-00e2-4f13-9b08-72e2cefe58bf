<aura:component implements="flexipage:availableForAllPageTypes" access="global" controller="CRMHelpPageController">
    <aura:attribute name="Tabs" type="Object[]"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    
    <aura:if isTrue="{!v.Tabs.length > 0}">
        <div class="slds-tabs_card">
            <lightning:tabset >
                <aura:iteration items="{!v.Tabs}" var="tab">
                    <lightning:tab label="{!tab.TabName}" class="tabHeader">
                        <c:CRMHelpPageTab HelpDocs="{!tab.HelpDocs}" HelpType="{!tab.TabName}" />
                    </lightning:tab>
                </aura:iteration>
            </lightning:tabset>
        </div>
    </aura:if>
</aura:component>