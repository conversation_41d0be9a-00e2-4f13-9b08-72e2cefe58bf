<design:component label="Two Columns with Three Region in Right Side">
    <flexipage:template>
        <flexipage:region name="notification" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="mainBody" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="sideTop" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="sideMiddle" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="sideBottom" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="oldVersionTopLeft" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="oldVersionTopRight" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="oldVersionBottomLeft" defaultWidth="Large"></flexipage:region>
        <flexipage:region name="oldVersionBottomRight" defaultWidth="Large"></flexipage:region>
    </flexipage:template>
</design:component>
