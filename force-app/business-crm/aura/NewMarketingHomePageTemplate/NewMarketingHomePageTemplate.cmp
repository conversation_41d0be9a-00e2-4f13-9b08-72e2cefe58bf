<aura:component implements="lightning:homeTemplate" description="Full-width header above two columns with three region in right side">
    <aura:attribute name="notification" type="Aura.Component[]" />
    <aura:attribute name="mainBody" type="Aura.Component[]" />
    <aura:attribute name="sideTop" type="Aura.Component[]" />
    <aura:attribute name="sideMiddle" type="Aura.Component[]" />
    <aura:attribute name="sideBottom" type="Aura.Component[]" />
    <aura:attribute name="oldVersionTopLeft" type="Aura.Component[]" />
    <aura:attribute name="oldVersionTopRight" type="Aura.Component[]" />
    <aura:attribute name="oldVersionBottomLeft" type="Aura.Component[]" />
    <aura:attribute name="oldVersionBottomRight" type="Aura.Component[]" />
    <aura:attribute name="user" type="Object" />
    <force:recordData aura:id="recordLoader" recordId="{!$SObjectType.CurrentUser.Id}" fields="Old_Homepage_Version__c,Homepage_Sales_Pilot_User__c" targetFields="{!v.user}" />

    <div>
        <lightning:layout class="fixed-notification">
            <lightning:layoutItem class="slds-size_1-of-1"> {!v.notification} </lightning:layoutItem>
        </lightning:layout>
        <div class="old-version slds-size_1-of-1">
            <aura:if isTrue="{! and(not(empty(v.user)), or(not(v.user.Homepage_Sales_Pilot_User__c), v.user.Old_Homepage_Version__c)) }">
                <lightning:layout class="slds-wrap">
                    <lightning:layoutItem class="slds-var-p-bottom_small slds-var-p-right_small slds-size_2-of-3"> {!v.oldVersionTopLeft} </lightning:layoutItem>
                    <lightning:layoutItem class="slds-size_1-of-3"> {!v.oldVersionTopRight} </lightning:layoutItem>
                    <lightning:layoutItem class="slds-var-p-bottom_small slds-var-p-right_small slds-size_1-of-3"> {!v.oldVersionBottomLeft} </lightning:layoutItem>
                    <lightning:layoutItem class="slds-var-p-right_small slds-size_1-of-3"> {!v.oldVersionBottomRight} </lightning:layoutItem>
                </lightning:layout>
            </aura:if>
        </div>

        <div class="new-version">
            <aura:if isTrue="{! and(not(empty(v.user)), and(v.user.Homepage_Sales_Pilot_User__c, not(v.user.Old_Homepage_Version__c))) }">
                <lightning:layout class="slds-wrap">
                    <lightning:layout class="slds-wrap slds-size_1-of-1 slds-medium-size_1-of-2 slds-large-size_3-of-4">
                        <lightning:layoutItem class="item-padding-bottom item-padding-right slds-size_1-of-1"> {!v.mainBody} </lightning:layoutItem>
                    </lightning:layout>

                    <lightning:layout class="slds-wrap slds-size_1-of-1 slds-medium-size_1-of-2 slds-large-size_1-of-4">
                        <lightning:layoutItem class="item-padding-bottom slds-size_1-of-1"> {!v.sideTop} </lightning:layoutItem>
                        <lightning:layoutItem class="item-padding-bottom slds-size_1-of-1"> {!v.sideMiddle} </lightning:layoutItem>
                        <lightning:layoutItem class="item-padding-bottom slds-size_1-of-1"> {!v.sideBottom} </lightning:layoutItem>
                    </lightning:layout>
                </lightning:layout>
            </aura:if>
        </div>
    </div>
</aura:component>
