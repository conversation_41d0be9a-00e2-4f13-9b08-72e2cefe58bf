.THIS {
}
.THIS .slds-page-header__title {
    font-size: 1.125rem;
    font-weight: bold;
    color: #3e3148;
}
.THIS .slds-page-header {
    padding: 2rem 1rem 1rem 2rem;
}
.THIS .faq .slds-page-header {
    padding: 2rem 1rem 0 2rem;
    background-color: inherit;
}
.THIS .slds-grid-pull-padded-large {
    padding-left: 8rem;
    padding-right: 4rem;
}
.THIS .video-area {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 2rem 3rem 2rem;
    max-width: 1366px;
    margin: 0 auto;
}
.THIS .text-wrapper {
    padding: 1rem 2rem;
    flex: 1 0 400px;
}
.THIS .video-wrapper {
    padding: 1rem 2rem;
    flex: 0 0 480px;
}
.THIS .video-intro-title {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 0.8rem;
}
.THIS .document-table {
    margin-top: 1rem;
    width: 100%;
    padding: 0.6rem 3rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    background-color: #f7feff;
}
.THIS .document-table a {
    color: #3e3148;
    padding-left: 1rem;
    font-size: 0.875rem;
}
.THIS .document-table .single-doc {
    margin: 1.4rem 0;
    border-right: solid 1px rgba(151, 151, 151, 0.23);
    float: left;
    display: inline-block;
    width: 33%;
    padding: 1.2rem 7%;
    border-right: solid 1px rgba(151, 151, 151, 0.23);
}
.THIS .document-table .last-of-line {
    position: relative;
    border: none;
}
.THIS .document-table .last-of-line:after {
    content: "";
    position: absolute;
    display: block;
    bottom: -1.2rem;
    right: 0;
    width: 300%;
    border-bottom: solid 1px rgba(151, 151, 151, 0.23);
}
.THIS .document-table .no-board {
    border: none;
}

.THIS .document-table .slds-icon_container {
    vertical-align: middle;
}

.THIS .video-intro-detail p a {
    font-size: 0.875rem !important;
    color: rgb(0, 112, 210) !important;
}
.THIS .video-intro-detail p a:hover {
    text-decoration: underline !important;
    color: rgb(0, 95, 178) !important;
}

.THIS .answer-detail span,
.THIS .video-intro-detail span,
.THIS .answer-detail p {
    color: #626262 !important;
}

.THIS .slds-section {
    margin: 0;
}
.THIS .answers button {
    padding: 1rem 0;
    outline: none;
    box-shadow: none;
    border-bottom: solid 1px rgba(151, 151, 151, 0.23);
}
.THIS .slds-button {
    border-radius: 0 !important;
}
.THIS .answers .slds-section__title-action {
    background-color: inherit;
}
.THIS .answers .slds-section__title-action:hover {
    background-color: rgb(238, 241, 246);
}
