<aura:component >
    <aura:attribute name="HelpDocs" type="Object[]" />
    <aura:attribute name="HelpType" type="String" />
    <div class="container">
        <aura:if isTrue="{!v.HelpDocs.length > 0}">
            <div class="help-document slds-card">
                <div class="slds-page-header onclick-white">
                    <div class="slds-media slds-no-space slds-grow">
                        <div class="slds-media__figure">
                            <span class="slds-icon_container">
                            <div class="slds-icon forceEntityIcon">
                                <img src="/resource/SLDS0205/icons/utility/description_120.png"/>
                            </div>
                        </span>
                        </div>
                        <div class="slds-media__body slds-align-middle">
                            <h2 class="header-title-container">
                            <span class="slds-page-header__title slds-text-heading_samller slds-text-color--default slds-truncate">{!v.HelpType}</span>
                        </h2>
                        </div>
                    </div>
                </div>
                <aura:iteration items="{!v.HelpDocs}" var="HelpDoc">
                        <div class="text-wrapper">
                            <p class="video-intro-detail">
                                <aura:unescapedHtml value="{!HelpDoc.description__c}" />
                            </p>
                        </div>
                </aura:iteration>
            </div>
        </aura:if> 
    </div>
</aura:component>