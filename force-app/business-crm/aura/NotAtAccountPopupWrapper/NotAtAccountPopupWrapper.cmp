<aura:component implements="force:lightningQuickActionWithoutHeader, force:hasRecordId" access="global">
    <aura:attribute name="recordId" type="String" />
    <aura:html tag="style">
        .cuf-content { padding: 0 0rem !important; } .slds-modal__content{ overflow-y:hidden !important; height:unset !important; max-height:unset !important; }
    </aura:html>
    <c:notAtAccountPopup recordId="{!v.recordId}" onclose="{!c.handleClose}" onrefresh="{!c.handleRefresh}" />
</aura:component>
