({
    handleClose: function (component, event, helper) {
        helper.handleCloseByLwc();
    },

    handleDestroy: function (component, event, helper) {
        helper.recordDestroyByAura(component);
    },

    handleToggleCloseButton: function () {
        var closeButtonList = document.getElementsByClassName("cContactAddToOpportunityWrapper");
        for (var i = 0; i < closeButtonList.length; i++) {
            if (closeButtonList[i].tagName === "STYLE") {
                const hideCloseButtonStyleStr = ".slds-modal__close{ display: none; }";
                if (closeButtonList[i].innerHTML.includes(hideCloseButtonStyleStr)) {
                    closeButtonList[i].innerHTML = closeButtonList[i].innerHTML.replace(hideCloseButtonStyleStr, "");
                } else {
                    closeButtonList[i].innerHTML += hideCloseButtonStyleStr;
                }
            }
        }
    }
});
