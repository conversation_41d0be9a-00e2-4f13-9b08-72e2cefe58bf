({
    destroyEventSource: "aura",

    handleCloseByLwc: function () {
        this.destroyEventSource = "lwc";
        $A.get("e.force:closeQuickAction").fire();
    },

    recordDestroyByAura: function (component) {
        if (this.destroyEventSource == "aura") {
            const matomoTracker = component.find("idMatomoTracker");
            matomoTracker.trackCRMUserEvent({
                category: "Add to Opportunity",
                action: "Add Contact to Opportunity",
                name: "Cancel"
            });
        }
        this.destroyEventSource = "aura";
    }
});
