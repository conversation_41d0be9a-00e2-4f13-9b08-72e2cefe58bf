<aura:component implements="force:lightningQuickActionWithoutHeader, force:hasRecordId" access="public">
    <aura:attribute name="recordId" type="String" />
    <aura:handler name="destroy" value="{!this}" action="{!c.handleDestroy}" />
    <c:matomoTracker aura:id="idMatomoTracker" />
    <c:addToOpportunityAndMergeContact contactId="{!v.recordId}" onclose="{!c.handleClose}" ontoggleclosebutton="{!c.handleToggleCloseButton}" />
    <aura:html tag="style">
        .cuf-content { padding:0 0rem !important; } .slds-modal__content{ overflow-y:hidden !important; height:unset !important; max-height:unset !important; padding:0;} .slds-modal__container{
        max-width:70rem !important; width:55% !important; } .slds-textarea { height:2rem !important; line-height:normal !important; } .contact-id-input .slds-input__icon_right { display:none
        !important; }
    </aura:html>
</aura:component>
