({
    handleConvertClose: function () {
        $A.get("e.force:closeQuickAction").fire();
    },

    handleToggleCloseButton: function () {
        var closeButtonList = document.getElementsByClassName("cLeadAddToAccountWrapper");
        for (var i = 0; i < closeButtonList.length; i++) {
            if (closeButtonList[i].tagName === "STYLE") {
                const hideCloseButtonStyleStr = ".slds-modal__close{ display: none; }";
                if (closeButtonList[i].innerHTML.includes(hideCloseButtonStyleStr)) {
                    closeButtonList[i].innerHTML = closeButtonList[i].innerHTML.replace(hideCloseButtonStyleStr, "");
                } else {
                    closeButtonList[i].innerHTML += hideCloseButtonStyleStr;
                }
            }
        }
    },

    handleConvertFinish: function (component, event) {
        const convertedContactId = event.getParam("id");
        component.set("v.convertedContactId", convertedContactId);
    },

    handleMergeFinish: function (component, event) {
        component.set("v.eventName", event.getName());
    },

    handleDestroy: function (component, event, helper) {
        if (component.get("v.eventName") === "mergefinish") {
            return;
        }
        const contactId = component.get("v.convertedContactId");
        if (contactId) {
            helper.navigateToContactPage(contactId);
        }
    }
});
