<aura:component implements="force:lightningQuickActionWithoutHeader, force:hasRecordId" access="global">
    <aura:attribute name="recordId" type="String" />
    <aura:attribute name="convertedContactId" type="String" />
    <aura:attribute name="eventName" type="String" />
    <aura:handler name="destroy" value="{!this}" action="{!c.handleDestroy}" />
    <aura:html tag="style">
        .cuf-content { padding: 0 0rem !important; } .slds-modal__content{ overflow-y:hidden !important; height:unset !important; max-height:unset !important; padding:0;} .slds-modal__container{
        max-width: 70rem !important; width:55% !important; }
    </aura:html>

    <c:addToAccountAndMergeContact
        convertLeadId="{!v.recordId}"
        onconvertclose="{!c.handleConvertClose}"
        ontoggleclosebutton="{!c.handleToggleCloseButton}"
        onconvertfinish="{!c.handleConvertFinish}"
        onmergefinish="{!c.handleMergeFinish}"
    />
</aura:component>
