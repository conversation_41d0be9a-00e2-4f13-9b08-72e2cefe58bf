<aura:component implements="lightning:actionOverride,force:lightningQuickAction" access="global">
    <aura:attribute name="isDisabled" type="Boolean" default="false" />
    <c:matomoTracker aura:id="idMatomoTracker" />
    <section id="modal-popup" role="dialog" class="slds-modal fix-slds-modal slds-modal_small slds-fade-in-open">
        <div class="slds-modal__container">
            <lightning:buttonIcon
                iconName="utility:close"
                class="slds-modal__close icon-close"
                variant="bare"
                size="large"
                disabled="{!v.isDisabled}"
                onclick="{!c.handleClickCloseButton }"
                title="close"
            />
            <c:newTag
                text='A tag name with a clear structure like "category sub-category" will be better for managing the tags (e.g., "MU Must Win").'
                helpText="It's strongly recommended to give a bit of context about the tag for the adoption, such as who could use, what opps could be assigned to."
                title="New Tag"
                entrance="tag list page"
                onclose="{!c.handleClose}"
                ondisableclose="{!c.toggleDisableClose}"
            />
        </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
</aura:component>
