({
    handleClose: function (component, event, helper) {
        helper.backToPreviousPage();
    },

    handleClickCloseButton: function (component, event, helper) {
        const matomoTracker = component.find("idMatomoTracker");
        matomoTracker.trackCRMUserEvent({
            category: "Tag",
            action: "Create tag",
            name: "Cancel create tag"
        });
        helper.backToPreviousPage();
    },

    toggleDisableClose: function (component, event) {
        component.set("v.isDisabled", event.detail);
    }
});
