<aura:component implements="force:lightningQuickActionWithoutHeader, force:hasRecordId, force:hasSObjectName" access="public">
    <aura:attribute name="recordId" type="String" />
    <aura:attribute name="sObjectName" type="String" />
    <aura:handler name="destroy" value="{!this}" action="{!c.handleDestroy}"/> 
    <c:matomoTracker aura:id="idMatomoTracker"/>  
    <c:qualifyOutLeadAndContact recordId="{!v.recordId}" sObjectName="{!v.sObjectName}" onclose="{!c.handleClose}" onchange="{!c.setReminderFlag}"/>
    <aura:html tag="style">
        .cuf-content { padding: 0 0rem !important; } 
        .slds-modal__content{ overflow-y:hidden !important; height:unset !important; max-height:unset !important; }
        .slds-modal__container{ max-width: 70rem !important; width:55% !important; }
    </aura:html>
</aura:component>
