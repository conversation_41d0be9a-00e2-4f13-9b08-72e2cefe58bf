({
    destroyEventSource: "aura",
    setReminderPageFlag: false,

    handleCloseByLwc: function () {
        this.destroyEventSource = "lwc";
        $A.get("e.force:closeQuickAction").fire();
    },

    recordDestroyByAura: function (component) {
        const matomoTracker = component.find("idMatomoTracker");
        const objectName = component.get("v.sObjectName").toLowerCase();
        if (this.destroyEventSource === "aura") {
            if (this.setReminderPageFlag) {
                matomoTracker.trackCRMUserEvent({
                    category: "Qualify Out",
                    action: `Set Reminder after qualify out ${objectName} record`,
                    name: "Reminder setting skipped"
                });
            } else {
                matomoTracker.trackCRMUserEvent({
                    category: "Qualify Out",
                    action: `Select ${objectName} record qualify out reason`,
                    name: "Cancel Qualify Out"
                });
            }
        }
        this.destroyEventSource = "aura";
        this.setReminderPageFlag = false;
    },

    markReminder: function () {
        this.setReminderPageFlag = true;
    }
});
