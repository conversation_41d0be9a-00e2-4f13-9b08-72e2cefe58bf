@IsTest(IsParallel=true)
private class MKTUserServiceTest {
    private static final fflib_ApexMocks mocks;
    private static final IProfileSelector mockProfileSelector;
    private static final IPermissionSetAssignmentSelector mockPermissionSetAssignmentSelector;

    static {
        mocks = new fflib_ApexMocks();
        mockProfileSelector = (IProfileSelector) mocks.mock(IProfileSelector.class);
        mockPermissionSetAssignmentSelector = (IPermissionSetAssignmentSelector) mocks.mock(IPermissionSetAssignmentSelector.class);
    }

    @IsTest
    static void backupUserPermissionSetWhenLicenseChanged() {
        MKTUserService mktServiceForTest = new MKTUserService(mockProfileSelector, mockPermissionSetAssignmentSelector, null);

        Id userId1 = fflib_IDGenerator.generate(User.SObjectType);
        Id userId2 = fflib_IDGenerator.generate(User.SObjectType);
        Id userId3 = fflib_IDGenerator.generate(User.SObjectType);
        Id userId4 = fflib_IDGenerator.generate(User.SObjectType);

        Profile crmProfile = new Profile(Id = fflib_IDGenerator.generate(Profile.SObjectType), UserLicenseId = fflib_IDGenerator.generate(UserLicense.SObjectType));
        Profile otherProfile = new Profile(Id = fflib_IDGenerator.generate(Profile.SObjectType), UserLicenseId = fflib_IDGenerator.generate(UserLicense.SObjectType));

        Map<Id, User> oldUsermap = new Map<Id, User>{
            userId1 => new User(Id = userId1, ProfileId = crmProfile.Id),
            userId2 => new User(Id = userId2, ProfileId = otherProfile.Id),
            userId3 => new User(Id = userId3, ProfileId = otherProfile.Id),
            userId4 => new User(Id = userId4, ProfileId = crmProfile.Id)
        };
        Map<Id, User> newUsermap = new Map<Id, User>{
            userId1 => new User(Id = userId1, ProfileId = otherProfile.Id),
            userId2 => new User(Id = userId2, ProfileId = crmProfile.Id),
            userId3 => new User(Id = userId3, ProfileId = otherProfile.Id),
            userId4 => new User(Id = userId4, ProfileId = crmProfile.Id)
        };

        PermissionSet crmProfilePermissionSet = new PermissionSet(Id = fflib_IDGenerator.generate(PermissionSet.SObjectType));
        PermissionSet otherProfilePermissionSet = new PermissionSet(Id = fflib_IDGenerator.generate(PermissionSet.SObjectType));

        List<PermissionSetAssignment> allUserPermissionSet = new List<PermissionSetAssignment>{
            new PermissionSetAssignment(AssigneeId = userId1, PermissionSetId = crmProfilePermissionSet.Id),
            new PermissionSetAssignment(AssigneeId = userId2, PermissionSetId = otherProfilePermissionSet.Id)
        };

        Set<Id> allProfiledIds = new Set<Id>{ crmProfile.Id, otherProfile.Id };

        mocks.startStubbing();
        mocks.when(mockProfileSelector.getProfilesByNames(Constants.CRM_USER_PROFILE_NAMES)).thenReturn(new Map<Id, Profile>{ crmProfile.Id => crmProfile });
        mocks.when(mockProfileSelector.getProfileUserLicenseMap(allProfiledIds))
            .thenReturn(new Map<Id, Id>{ crmProfile.Id => crmProfile.UserLicenseId, otherProfile.Id => otherProfile.UserLicenseId });
        mocks.when(mockPermissionSetAssignmentSelector.getAllByUserIds(new List<Id>{ userId1, userId2 })).thenReturn(allUserPermissionSet);
        mocks.stopStubbing();

        Test.startTest();
        mktServiceForTest.temporailyBackupCRMUserPermission(oldUsermap, newUsermap);
        Test.stopTest();

        Assert.areEqual(2, MKTUserService.crmUserPermissionSetBackup.size());
        Assert.areEqual(
            new Map<Id, Set<Id>>{ userId1 => new Set<Id>{ crmProfilePermissionSet.Id }, userId2 => new Set<Id>{ otherProfilePermissionSet.Id } },
            MKTUserService.crmUserPermissionSetBackup
        );
    }
}
