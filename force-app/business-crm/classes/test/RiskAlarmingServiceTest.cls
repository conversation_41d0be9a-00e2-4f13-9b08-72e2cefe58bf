@IsTest(IsParallel=true)
private class RiskAlarmingServiceTest {
    @IsTest
    static void givenOwnerIdsAndMarketNames_whenGetWorkAtRiskAmount_thenReturnTotalBillableAmount() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        IOpportunitySelector opportunitySelector = (IOpportunitySelector) mocks.mock(IOpportunitySelector.class);
        ITimecardSelector timecardSelector = (ITimecardSelector) mocks.mock(ITimecardSelector.class);
        ICurrencySelector currencySelector = (ICurrencySelector) mocks.mock(ICurrencySelector.class);

        // Ids
        Id oppId = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Id projectId = fflib_IDGenerator.generate(pse__Proj__c.SObjectType);
        Id userId = fflib_IDGenerator.generate(User.SObjectType);
        List<String> accountSegments = new List<String>{ 'Unspecified' };
        // Test Data
        Opportunity opp = new Opportunity(Id = oppId, pse__Primary_Project__c = projectId);

        pse__Timecard_Header__c timecard = new pse__Timecard_Header__c(pse__Project__c = projectId, CurrencyIsoCode = 'EUR');
        timecard = (pse__Timecard_Header__c) fflib_ApexMocksUtils.setReadOnlyFields(
            timecard,
            pse__Timecard_Header__c.class,
            new Map<SObjectField, Object>{ pse__Timecard_Header__c.pse__Total_Billable_Amount__c => 110 }
        );

        CurrencyType currencyType = new CurrencyType(IsoCode = 'EUR', ConversionRate = 1.1);

        // Stub Selectors
        List<Opportunity> oppList = new List<Opportunity>{ opp };
        List<pse__Timecard_Header__c> timecardList = new List<pse__Timecard_Header__c>{ timecard };
        List<CurrencyType> currencyList = new List<CurrencyType>{ currencyType };

        mocks.startStubbing();
        mocks.when(opportunitySelector.getOpenedPsOpportunitiesAndOcvGreaterThanZeroByOwnerIdsAndMarketNames(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'Unspecified' })).thenReturn(oppList);
        mocks.when(timecardSelector.getTimeCardHeaderByProjectIdAndFirstBillableDate(new Set<Id>{ projectId })).thenReturn(timecardList);
        mocks.when(currencySelector.getCurrencyType()).thenReturn(currencyList);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(opportunitySelector, timecardSelector, currencySelector);
        Decimal result = service.getWorkAtRiskAmount(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'Unspecified' });

        // Assert
        System.assertEquals(100.00, result); // 110 / 1.1
    }

    @IsTest
    static void givenOwnerIdsAndMarketNames_whenGetWorkAsRiskList_thenReturnWorkAsRiskDTOList() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        IOpportunitySelector opportunitySelector = (IOpportunitySelector) mocks.mock(IOpportunitySelector.class);
        ITimecardSelector timecardSelector = (ITimecardSelector) mocks.mock(ITimecardSelector.class);
        ICurrencySelector currencySelector = (ICurrencySelector) mocks.mock(ICurrencySelector.class);

        Id oppId = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Id projectId = fflib_IDGenerator.generate(pse__Proj__c.SObjectType);
        Id userId = fflib_IDGenerator.generate(User.SObjectType);

        Opportunity opp = new Opportunity(Id = oppId, Name = 'Test Opp', pse__Primary_Project__c = projectId);

        pse__Timecard_Header__c timecard = new pse__Timecard_Header__c(pse__Project__c = projectId, CurrencyIsoCode = 'EUR');
        timecard = (pse__Timecard_Header__c) fflib_ApexMocksUtils.setReadOnlyFields(
            timecard,
            pse__Timecard_Header__c.class,
            new Map<SObjectField, Object>{ pse__Timecard_Header__c.pse__Total_Billable_Amount__c => 220 }
        );

        CurrencyType currencyType = new CurrencyType(IsoCode = 'EUR', ConversionRate = 1.1);

        List<Opportunity> oppList = new List<Opportunity>{ opp };
        List<pse__Timecard_Header__c> timecardList = new List<pse__Timecard_Header__c>{ timecard };
        List<CurrencyType> currencyList = new List<CurrencyType>{ currencyType };

        mocks.startStubbing();
        mocks.when(opportunitySelector.getOpenedPsOpportunitiesAndOcvGreaterThanZeroByOwnerIdsAndMarketNames(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'Unspecified' })).thenReturn(oppList);
        mocks.when(timecardSelector.getTimeCardHeaderByProjectIdAndFirstBillableDate(new Set<Id>{ projectId })).thenReturn(timecardList);
        mocks.when(currencySelector.getCurrencyType()).thenReturn(currencyList);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(opportunitySelector, timecardSelector, currencySelector);
        List<WorkAsRiskDTO> resultList = service.getWorkAsRiskList(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'Unspecified' });

        // Assert
        System.assertEquals(1, resultList.size());
        System.assertEquals(200.00, resultList[0].totalBillableAmountInUSD); // 220 / 1.1
        System.assertEquals(oppId, resultList[0].opportunityId);
    }

    @IsTest
    static void givenOwnerIdsAndMarketNames_whenGetOverdueInvoiceAmount_thenReturnTotalUnpaidAmount() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        ISalesInvoiceSelector invoiceSelector = (ISalesInvoiceSelector) mocks.mock(ISalesInvoiceSelector.class);
        ICurrencySelector currencySelector = (ICurrencySelector) mocks.mock(ICurrencySelector.class);

        // Ids
        Id invoiceId = fflib_IDGenerator.generate(Sales_Invoice__c.SObjectType);
        Id userId = fflib_IDGenerator.generate(User.SObjectType);

        // Test Data
        Sales_Invoice__c invoice = new Sales_Invoice__c(Id = invoiceId, CurrencyIsoCode = 'EUR', Final_Total__c = 220);
        invoice = (Sales_Invoice__c) fflib_ApexMocksUtils.setReadOnlyFields(invoice, Sales_Invoice__c.class, new Map<SObjectField, Object>{ Sales_Invoice__c.Amount_Unpaid__c => 220 });

        CurrencyType currencyType = new CurrencyType(IsoCode = 'EUR', ConversionRate = 1.1);

        // Stub Selectors
        List<Sales_Invoice__c> invoiceList = new List<Sales_Invoice__c>{ invoice };
        List<CurrencyType> currencyList = new List<CurrencyType>{ currencyType };

        mocks.startStubbing();
        mocks.when(invoiceSelector.getOverdueSalesInvoiceByAccountOwnerOrAccountMarketWithStatus(new Set<Id>{ userId }, new Set<String>{ 'US' }, new List<String>{ 'Unspecified' })).thenReturn(invoiceList);
        mocks.when(currencySelector.getCurrencyType()).thenReturn(currencyList);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(invoiceSelector, currencySelector);
        Decimal result = service.getOverdueInvoiceAmount(new Set<Id>{ userId }, new Set<String>{ 'US' }, new List<String>{ 'Unspecified' });

        // Assert
        System.assertEquals(200.00, result); // 220 / 1.1
    }

    @IsTest
    static void givenOwnerIdsAndMarketNames_whenGetOverdueInvoiceList_thenReturnInvoiceOverDueDTOList() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        ISalesInvoiceSelector invoiceSelector = (ISalesInvoiceSelector) mocks.mock(ISalesInvoiceSelector.class);
        ICurrencySelector currencySelector = (ICurrencySelector) mocks.mock(ICurrencySelector.class);

        // Ids
        Id invoiceId = fflib_IDGenerator.generate(Sales_Invoice__c.SObjectType);
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id userId = fflib_IDGenerator.generate(User.SObjectType);

        // Test Data
        Sales_Invoice__c invoice = new Sales_Invoice__c(Id = invoiceId, CurrencyIsoCode = 'EUR', Final_Total__c = 330, Account__c = accountId, Invoice_Status__c = 'Invoice Posted to AR');
        invoice = (Sales_Invoice__c) fflib_ApexMocksUtils.setReadOnlyFields(
            invoice,
            Sales_Invoice__c.class,
            new Map<SObjectField, Object>{ Sales_Invoice__c.Amount_Unpaid__c => 220, Sales_Invoice__c.Name => 'Test Invoice' }
        );

        CurrencyType currencyType = new CurrencyType(IsoCode = 'EUR', ConversionRate = 1.1);

        // Stub Selectors
        List<Sales_Invoice__c> invoiceList = new List<Sales_Invoice__c>{ invoice };
        List<CurrencyType> currencyList = new List<CurrencyType>{ currencyType };

        mocks.startStubbing();
        mocks.when(invoiceSelector.getOverdueSalesInvoiceByAccountOwnerOrAccountMarketWithStatus(new Set<Id>{ userId }, new Set<String>{ 'US' }, new List<String>{ 'Unspecified' })).thenReturn(invoiceList);
        mocks.when(currencySelector.getCurrencyType()).thenReturn(currencyList);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(invoiceSelector, currencySelector);
        List<InvoiceOverDueDTO> resultList = service.getOverdueInvoiceList(new Set<Id>{ userId }, new Set<String>{ 'US' }, new List<String>{ 'Unspecified' });

        // Assert
        System.assertEquals(1, resultList.size());
        System.assertEquals(200.00, resultList[0].unpaidAmountInUSD); // 220 / 1.1
        System.assertEquals(300.00, resultList[0].finalTotalInUSD); // 330 / 1.1
        System.assertEquals(invoiceId, resultList[0].id);
        System.assertEquals('Test Invoice', resultList[0].name);
        System.assertEquals('Invoice Posted to AR', resultList[0].invoiceStatus);
    }

    @IsTest
    static void givenOwnerIdsAndMarketNames_whenGetHygieneQualityRating_thenReturnQualityRating() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        IOpportunitySelector opportunitySelector = (IOpportunitySelector) mocks.mock(IOpportunitySelector.class);

        // Test Data
        Id userId = fflib_IDGenerator.generate(User.SObjectType);
        List<Id> ownerIds = new List<Id>{ userId };
        List<String> marketNames = new List<String>{ 'US' };
        List<String> forecastingStatuses = new List<String>{ 'In' };
        List<String> accountSegments = new List<String>{ 'Unspecified' };

        // Define mock return values
        Decimal totalCriticalErrorsNum = 10;
        Decimal openOpportunitiesNum = 30;
        Decimal endingWOExtensionNum = 20;

        // Setup mocks behavior
        mocks.startStubbing();
        mocks.when(opportunitySelector.getSumCriticalErrorNumsByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(totalCriticalErrorsNum);
        mocks.when(opportunitySelector.getSumOpenOpportunitiesByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(openOpportunitiesNum);
        mocks.when(opportunitySelector.getSumEndingWOExtensionByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(endingWOExtensionNum);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(opportunitySelector, null, null);
        Decimal result = service.getHygieneQualityRating(ownerIds, marketNames, forecastingStatuses, accountSegments);

        // Assert
        // Expected: 1 - (10 / (30 + 20)) = 1 - 0.2 = 0.8
        System.assertEquals(0.8, result, 'Hygiene quality rating should be calculated correctly');

        // Verify selector methods were called with correct parameters
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1)).getSumCriticalErrorNumsByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1)).getSumOpenOpportunitiesByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1)).getSumEndingWOExtensionByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
    }

    @IsTest
    static void givenZeroDenominator_whenGetHygieneQualityRating_thenReturnNull() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        IOpportunitySelector opportunitySelector = (IOpportunitySelector) mocks.mock(IOpportunitySelector.class);

        // Test Data
        Id userId = fflib_IDGenerator.generate(User.SObjectType);
        List<Id> ownerIds = new List<Id>{ userId };
        List<String> marketNames = new List<String>{ 'US' };
        List<String> forecastingStatuses = new List<String>{ 'In' };
        List<String> accountSegments = new List<String>{ 'Unspecified' };

        // Define mock return values - edge case with zero denominator
        Decimal totalCriticalErrorsNum = 5;
        Decimal openOpportunitiesNum = 0;
        Decimal endingWOExtensionNum = 0;

        // Setup mocks behavior
        mocks.startStubbing();
        mocks.when(opportunitySelector.getSumCriticalErrorNumsByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(totalCriticalErrorsNum);
        mocks.when(opportunitySelector.getSumOpenOpportunitiesByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(openOpportunitiesNum);
        mocks.when(opportunitySelector.getSumEndingWOExtensionByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(endingWOExtensionNum);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(opportunitySelector, null, null);

        // Assert

        Decimal result = service.getHygieneQualityRating(ownerIds, marketNames, forecastingStatuses, accountSegments);
        System.assertEquals(null, result);
    }

    @IsTest
    static void givenZeroCriticalErrors_whenGetHygieneQualityRating_thenReturnPerfectRating() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        IOpportunitySelector opportunitySelector = (IOpportunitySelector) mocks.mock(IOpportunitySelector.class);

        // Test Data
        Id userId = fflib_IDGenerator.generate(User.SObjectType);
        List<Id> ownerIds = new List<Id>{ userId };
        List<String> marketNames = new List<String>{ 'US' };
        List<String> forecastingStatuses = new List<String>{ 'In' };
        List<String> accountSegments = new List<String>{ 'Unspecified' };

        // Define mock return values - perfect scenario with zero critical errors
        Decimal totalCriticalErrorsNum = 0;
        Decimal openOpportunitiesNum = 25;
        Decimal endingWOExtensionNum = 15;

        // Setup mocks behavior
        mocks.startStubbing();
        mocks.when(opportunitySelector.getSumCriticalErrorNumsByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(totalCriticalErrorsNum);
        mocks.when(opportunitySelector.getSumOpenOpportunitiesByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(openOpportunitiesNum);
        mocks.when(opportunitySelector.getSumEndingWOExtensionByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments)).thenReturn(endingWOExtensionNum);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(opportunitySelector, null, null);
        Decimal result = service.getHygieneQualityRating(ownerIds, marketNames, forecastingStatuses, accountSegments);

        // Assert
        // Expected: 1 - (0 / (25 + 15)) = 1 - 0 = 1.0 (perfect rating)
        System.assertEquals(1.0, result, 'With zero critical errors, hygiene quality rating should be 1.0');

        // Verify selector methods were called with correct parameters
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1)).getSumCriticalErrorNumsByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1)).getSumOpenOpportunitiesByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1)).getSumEndingWOExtensionByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
    }

    @IsTest
    static void givenOwnerIdsAndMarketNames_whenGetCriticalErrorsOpportunityNumbers_thenReturnCountOfCriticalErrors() {
        // Arrange
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        // Mock Selectors
        IOpportunitySelector opportunitySelector = (IOpportunitySelector) mocks.mock(IOpportunitySelector.class);

        // Ids
        Id oppId = fflib_IDGenerator.generate(Opportunity.SObjectType);
        Id userId = fflib_IDGenerator.generate(User.SObjectType);
        List<String> accountSegments = new List<String>{ 'Unspecified' };

        // Test Data
        Opportunity opp = new Opportunity(Id = oppId);
        List<Opportunity> oppList = new List<Opportunity>{ opp, opp }; // 2 opportunities with critical errors

        // Stub Selectors
        mocks.startStubbing();
        mocks.when(opportunitySelector.getSumOpportunityCriticalErrorByOwnerIdsAndMarketNames(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'In' }, accountSegments)).thenReturn(oppList);
        mocks.stopStubbing();

        // Act
        RiskAlarmingService service = new RiskAlarmingService(opportunitySelector, null, null);
        Decimal result = service.getCriticalErrorsOpportunityNumbers(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'In' }, accountSegments);

        // Assert
        System.assertEquals(2, result, 'Should return the count of opportunities with critical errors');

        // Verify method was called with correct parameters
        ((IOpportunitySelector) mocks.verify(opportunitySelector, 1))
            .getSumOpportunityCriticalErrorByOwnerIdsAndMarketNames(new List<Id>{ userId }, new List<String>{ 'US' }, new List<String>{ 'In' }, accountSegments);
    }
}
