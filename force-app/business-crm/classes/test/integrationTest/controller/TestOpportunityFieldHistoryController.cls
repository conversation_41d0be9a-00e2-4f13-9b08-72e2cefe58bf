@IsTest
public class TestOpportunityFieldHistoryController {
    @IsTest
    static void shouldGetFieldHistoryLastUpdateWhenDataAvaiable() {
        Opportunity opp = TestFactory.createOpportunity();
        List<OpportunityFieldHistory> oppFieldHistories = new List<OpportunityFieldHistory>();
        OpportunityFieldHistory stageNameFieldHistory = new OpportunityFieldHistory();

        stageNameFieldHistory.Field = 'StageName';
        stageNameFieldHistory.OpportunityId = opp.Id;

        OpportunityFieldHistory nextStepFieldHistory = new OpportunityFieldHistory();

        nextStepFieldHistory.Field = 'NextStep';
        nextStepFieldHistory.OpportunityId = opp.Id;

        oppFieldHistories.add(stageNameFieldHistory);

        oppFieldHistories.add(nextStepFieldHistory);

        insert oppFieldHistories;

        List<String> queryFieldList = new List<String>();
        queryFieldList.add('StageName');
        queryFieldList.add('Probability');
        queryFieldList.add('NextStep');

        Map<String, Datetime> fieldUpateResult = OpportunityFieldHistoryController.getLastestFieldsUpdateTime(opp.Id, queryFieldList);
        System.assertEquals(Date.today(), fieldUpateResult.get('StageName').date());
        System.assertEquals(null, fieldUpateResult.get('Probability'));
        System.assertEquals(Date.today(), fieldUpateResult.get('NextStep').date());
    }
}
