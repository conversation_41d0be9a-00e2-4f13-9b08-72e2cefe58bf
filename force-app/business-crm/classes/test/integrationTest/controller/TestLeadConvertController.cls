@IsTest
private class TestLeadConvertController {
    @IsTest
    static void shouldPrepareCorrectEmailParameters() {
        TriggerToggle.turnOff();
        Id recordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId();
        Lead testLead = TestFactory.createLead(1, recordTypeId, new Map<String, Object>());
        testLead.Status = 'Sales Accepted';
        testLead.Handover_Notes__c = 'Test Handover Notes';
        testLead.Recent_Qualification_Path__c = 'Outbound';
        testLead.Email = '<EMAIL>';
        insert testLead;
        TriggerToggle.turnOn();
        Test.startTest();
        LeadConvertController.EmailDetails emailDetails = LeadConvertController.getEmailDetails('Lead', testLead.Id);

        String bodyHTML = LeadConvertController.prepareMailBody(emailDetails);
        System.Test.stopTest();
        System.assertEquals(true, bodyHTML.contains('Lead'));
        System.assertEquals(true, bodyHTML.contains('Test Handover Notes'));
        System.assertEquals(true, bodyHTML.contains('<EMAIL>'));
        System.assertEquals(true, bodyHTML.contains('Outbound'));
    }

    @IsTest
    static void shouldPrepareCorrectEmailParametersForContact() {
        TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
        TriggerToggle.turnOff();

        Contact testContact = testFixture.prepareContact(false);
        testContact.Handover_Notes__c = 'Test Handover Notes';
        testContact.Recent_Qualification_Path__c = 'Outbound';
        testContact.Email = '<EMAIL>';
        insert testContact;
        TriggerToggle.turnOn();
        Test.startTest();
        LeadConvertController.EmailDetails emailDetails = LeadConvertController.getEmailDetails('Contact', testContact.Id);

        String bodyHTML = LeadConvertController.prepareMailBody(emailDetails);
        System.Test.stopTest();
        System.assertEquals(true, bodyHTML.contains('Contact'));
        System.assertEquals(true, bodyHTML.contains('Test Handover Notes'));
        System.assertEquals(true, bodyHTML.contains('<EMAIL>'));
        System.assertEquals(true, bodyHTML.contains('Outbound'));
    }
}
