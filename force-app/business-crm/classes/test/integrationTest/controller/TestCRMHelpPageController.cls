@IsTest
private class TestCRMHelpPageController {
    @IsTest
    static void shouldGetRightHelpDoc() {
        List<CRM_Help_Page_Tabs__c> tabs = new List<CRM_Help_Page_Tabs__c>();
        CRM_Help_Page_Tabs__c lead = new CRM_Help_Page_Tabs__c();
        lead.Name = 'Lead Management';
        lead.Order__c = 1;
        lead.Profile_List__c = 'ALL';
        tabs.add(lead);
        CRM_Help_Page_Tabs__c opp = new CRM_Help_Page_Tabs__c();
        opp.Name = 'Opportunity Management';
        opp.Order__c = 2;
        opp.Profile_List__c = 'ALL';
        tabs.add(opp);
        insert tabs;

        System.Test.startTest();
        List<HelpDoc__c> helpDocs = prepareHelpDocs();
        Database.insert(helpDocs);
        List<CRMHelpPageController.HelpPageTabData> result = CRMHelpPageController.getHelpPageTabsData();
        System.Test.stopTest();

        System.assertEquals('Lead Management', result[0].TabName);
        System.assertEquals(1, result[0].HelpDocs.size());
        System.assertEquals('Opportunity Management', result[1].TabName);
        System.assertEquals(1, result[1].HelpDocs.size());
    }

    private static List<HelpDoc__c> prepareHelpDocs() {
        List<HelpDoc__c> helpDocs = new List<HelpDoc__c>();
        String helpDocJson = '{"HelpDocTitle__c":"null","HelpDocURL__c":"null","CurrencyIsoCode":"USD","HelpDocType__c":';
        String typeLead = '"Lead Management"}';
        String typeOpp = '"Opportunity Management"}';
        helpDocs.add((HelpDoc__c) JSON.deserialize(helpDocJson + typeLead, HelpDoc__c.class));
        helpDocs.add((HelpDoc__c) JSON.deserialize(helpDocJson + typeOpp, HelpDoc__c.class));
        return helpDocs;
    }
}
