@isTest
public with sharing class TestConvertLeadAndMergeContactController {
    private static final Id PSContactRecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId();
    private static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    private static final Datetime timeNow = Datetime.now();

    private static fflib_ApexMocks mocks = new fflib_ApexMocks();
    private static IFieldDefinitionSelector mockFieldDefinitionSelector = (IFieldDefinitionSelector) mocks.mock(IFieldDefinitionSelector.class);
    private static TWPSA_TestDataFactory dataFactory = new TWPSA_TestDataFactory();
    private static User salesUser = dataFactory.createUser('CRM - Sales', 'salesUser', new Set<String>{ Constants.CLT_PM_PERMISSION_SET_NAME });

    static {
        List<Holistic_Engagement_Model__c> model = new List<Holistic_Engagement_Model__c>{
            new Holistic_Engagement_Model__c(Channel_Type__c = 'Content - Blog', Status__c = 'Test Responded', Is_Engaged__c = true)
        };
        insert model;
    }

    private static List<FieldDefinition> getMockContactFieldDefinitionList() {
        List<FieldDefinition> mockContactFieldDefinitionList = new List<FieldDefinition>();
        List<String> mockContactFieldDefinitionStringList = new List<String>{
            '{"QualifiedApiName": "AccountId", "Label": "Account Name", "DataType": "Lookup(Account)"}',
            '{"QualifiedApiName": "Email", "Label": "Email", "DataType": "Email"}',
            '{"QualifiedApiName": "Support__c", "Label": "Support", "DataType": "Picklist"}',
            '{"QualifiedApiName": "AssistantName__c", "Label": "Assistant Name", "DataType": "Text(40)"}',
            '{"QualifiedApiName": "Title", "Label": "Job Title", "DataType": "Text(128)"}',
            '{"QualifiedApiName": "OwnerId", "Label": "Contact Owner", "DataType": "Lookup(User)"}',
            '{"QualifiedApiName": "Salutation_Formality__c", "Label": "Salutation Formality", "DataType": "Picklist"}',
            '{"QualifiedApiName": "Previous_Owner__c", "Label": "Previous Owner", "DataType": "Lookup(User)"}'
        };
        for (String fieldDefinitionString : mockContactFieldDefinitionStringList) {
            mockContactFieldDefinitionList.add((FieldDefinition) JSON.deserialize(fieldDefinitionString, FieldDefinition.class));
        }
        return mockContactFieldDefinitionList;
    }

    private static void mockDuplicateContactFieldCustomSettingData() {
        Map<String, String> DuplicateContactFieldMap = new Map<String, String>{
            //  apiName, customizedLabelName(If not set,input null)
            'AccountId' => 'Account',
            'OwnerId' => null,
            'Email' => null,
            'Phone' => null,
            'Title' => null,
            'job_title__c' => null,
            'Address' => 'Location',
            'Function__c' => null,
            'Business_Unit__c' => null,
            'Salutation_Formality__c' => null,
            'Preferred_Language__c' => null,
            'Company_Relationship_Type__c' => null,
            'ReportsToId' => null,
            'Strategy__c' => null,
            'Support__c' => null,
            'Influence__c' => null,
            'Contact_Frequency__c' => null,
            'AssistantName__c' => null,
            'Asst_Email_Address__c' => null,
            'Asst_Mobile__c' => null,
            'AssistantPhone' => null,
            // add for test if support custom lookup Field
            'Previous_Owner__c' => null
        };
        List<DuplicateContactField__c> DuplicateContactFieldListForInsert = new List<DuplicateContactField__c>();
        Integer orderNum = 1;
        for (String apiName : DuplicateContactFieldMap.keySet()) {
            DuplicateContactField__c dcf = new DuplicateContactField__c(name = apiName, CustomizedLabel__c = DuplicateContactFieldMap.get(apiName), DuplicateContactFieldOrder__c = orderNum);
            DuplicateContactFieldListForInsert.add(dcf);
            orderNum++;
        }
        insert DuplicateContactFieldListForInsert;
    }

    @isTest
    public static void shouldGetDuplicateContactWhichIsNotPersonalEmail() {
        Personal_Email_Provider_Domains__c domainList = new Personal_Email_Provider_Domains__c();
        domainList.Name = 'gmail.com';
        insert domainList;

        Account account = testFixture.prepareAccount(true);
        List<Contact> listContact = new List<Contact>();
        Contact con1 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            MailingCountry = 'China'
        );
        Contact con2 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            MailingCountry = 'China'
        );
        Contact con3 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>', //Personal_Email__c = true
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            MailingCountry = 'China'
        );
        listContact.add(con1);
        listContact.add(con2);
        listContact.add(con3);
        insert listContact;

        System.runAs(salesUser) {
            System.Test.startTest();
            Map<String, List<Id>> duplicateRecordsMap = ConvertLeadAndMergeContactController.getDuplicateLeadsAndContacts(con1.Id);
            System.Test.stopTest();
            System.assertEquals(1, duplicateRecordsMap.get('Contact').size());
            System.assertEquals(con2.Id, duplicateRecordsMap.get('Contact')[0]);
        }
    }

    @isTest
    public static void shouldNotGetDuplicateContactWhenIsPersonalEmail() {
        Personal_Email_Provider_Domains__c domainList = new Personal_Email_Provider_Domains__c();
        domainList.Name = 'gmail.com';
        insert domainList;

        Account account = testFixture.prepareAccount(true);
        List<Contact> listContact = new List<Contact>();
        Contact con1 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>', //Personal_Email__c = true
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            MailingCountry = 'China'
        );
        Contact con2 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            MailingCountry = 'China'
        );
        listContact.add(con1);
        listContact.add(con2);
        insert listContact;
        System.runAs(salesUser) {
            System.Test.startTest();
            Map<String, List<Id>> duplicateRecordsMap = ConvertLeadAndMergeContactController.getDuplicateLeadsAndContacts(con1.Id);
            System.Test.stopTest();
            System.assertEquals(null, duplicateRecordsMap);
        }
    }

    //    @isTest
    //    public static void shouldContainTheFieldWithRuleToMerge() {
    //        System.runAs(salesUser) {
    //            mockDuplicateContactFieldCustomSettingData();
    //            List<FieldDefinition> contactFieldDefinitionList = getMockContactFieldDefinitionList();
    //            mocks.startStubbing();
    //            mocks.when(mockFieldDefinitionSelector.getEntityFieldDefinitionList('Contact')).thenReturn(contactFieldDefinitionList);
    //            mocks.stopStubbing();
    //
    //            List<String> fieldWithRuleToMerge = ConvertLeadAndMergeContactController.getFieldListWithRuleToMerge();
    //            List<String> fieldListToMerge = ConvertLeadAndMergeContactController.getFieldListToMerge();
    //
    //            System.assertEquals(true, checkIfListContainAllValueInAnotherList(fieldListToMerge, fieldWithRuleToMerge));
    //
    //            fieldWithRuleToMerge.add('field not writable for us to manage merge');
    //            System.assertEquals(false, checkIfListContainAllValueInAnotherList(fieldListToMerge, fieldWithRuleToMerge));
    //        }
    //    }
    //
    //    private static Boolean checkIfListContainAllValueInAnotherList(List<String> bigList, List<String> smallList) {
    //        for (String field : smallList) {
    //            if (!bigList.contains(field)) {
    //                return false;
    //            }
    //        }
    //        return true;
    //    }

    @isTest
    public static void shouldSuccessfulMergeContacts() {
        Account account = testFixture.prepareAccount(false, 'CRMACC1');
        Account account2 = testFixture.prepareAccount(false, 'CRMACC2');
        insert new List<Account>{ account, account2 };
        List<Contact> listContact = new List<Contact>();
        User newPreviousOwner = testFixture.prepareUser('CRM - Sales', 'testNewOwner');
        User previousOwner = testFixture.prepareUser('CRM - Sales', 'testOwner');
        Contact con1 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2010, 1, 1),
            TW_Alumni__c = false,
            Not_at_Account__c = false,
            MailingCountry = 'China',
            Handover_Notes__c = 'con1 handovernotes',
            Interest_CXPD_Date__c = Date.newInstance(2000, 1, 1),
            EmailBouncedDate = Date.newInstance(2022, 1, 1),
            EmailBouncedReason = 'contact 1 email bounced reason',
            Subscription_Technology_Radar__c = 'true',
            Interest_in_CXPD__c = false,
            mkto71_Subscription_Access_Thoughtworks__c = true,
            Stop_Prevent_Marketing__c = false,
            Email_Invalid__c = true,
            Date_of_First_Attempt__c = Date.newInstance(2022, 1, 2),
            Response_Date__c = Date.newInstance(2023, 1, 2),
            Marketing_Influence_Expired_Date__c = Date.newInstance(2024, 1, 2)
        );
        Contact con2 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Blog',
            Partnership_Involved__c = true,
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2020, 1, 1),
            TW_Alumni__c = false,
            Not_at_Account__c = false,
            MailingCountry = 'China',
            MobilePhone = '********',
            Interest_CXPD_Date__c = Date.newInstance(2021, 1, 1),
            Interest_in_CXPD__c = true,
            Previous_Owner__c = newPreviousOwner.Id,
            Status__c = 'Won',
            EmailBouncedDate = Date.newInstance(2021, 1, 1),
            EmailBouncedReason = 'contact 2 email bounced reason',
            Subscription_Technology_Radar__c = 'false',
            mkto71_Subscription_Access_Thoughtworks__c = false,
            HasOptedOutOfEmail = false,
            Consent_General_Marketing__c = true,
            Email_Invalid__c = false,
            Date_of_First_Attempt__c = Date.newInstance(2022, 1, 1),
            Response_Date__c = Date.newInstance(2023, 1, 1),
            Marketing_Influence_Expired_Date__c = Date.newInstance(2024, 1, 1)
        );
        Contact con3 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account2.Id,
            Title = 'Sales Head',
            LeadSource = 'Contact Us Form',
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2000, 1, 1),
            TW_Alumni__c = true,
            Not_at_Account__c = true,
            LID__No_longer_at_Company__c = 'Not at Company',
            Date_not_at_account_detected__c = Date.newInstance(2022, 1, 1),
            MailingCountry = 'China',
            Handover_Notes__c = 'con3 handovernotes',
            Interest_CXPD_Date__c = Date.newInstance(2010, 1, 1),
            Interest_in_CXPD__c = false,
            Previous_Owner__c = previousOwner.Id,
            EmailBouncedDate = Date.newInstance(2020, 1, 1),
            EmailBouncedReason = 'contact 3 email bounced reason',
            Stop_Prevent_Marketing__c = true,
            mkto71_Subscription_Perspectives__c = true,
            HasOptedOutOfEmail = true,
            Consent_General_Marketing__c = true,
            Email_Invalid__c = false,
            Date_of_First_Attempt__c = Date.newInstance(2022, 1, 3),
            Response_Date__c = Date.newInstance(2023, 1, 3),
            Marketing_Influence_Expired_Date__c = Date.newInstance(2024, 1, 3)
        );
        listContact.add(con1);
        listContact.add(con2);
        listContact.add(con3);
        insert listContact;
        List<Id> conIds = new List<Id>{ con1.Id, con2.Id, con3.Id };

        List<Contact> cons = [
            SELECT
                Id,
                Email,
                Title,
                MobilePhone,
                Handover_Notes__c,
                Account_Name__c,
                MailingCountry,
                OwnerId,
                AccountId,
                Owner.Name,
                Country__c,
                Function__c,
                Salutation_Formality__c,
                Preferred_Language__c,
                Company_Relationship_Type__c,
                TW_Alumni__c,
                ReportsTo.Account_Name__c,
                Strategy__c,
                Support__c,
                Influence__c,
                Contact_Frequency__c,
                AssistantName__c,
                Asst_Mobile__c,
                Asst_Email_Address__c,
                AssistantPhone,
                CreatedDate,
                MailingCity,
                MailingPostalCode,
                MailingState,
                MailingStreet,
                Previous_Owner__c,
                LeadSource,
                First_Marketing_Influenced_CampaignDate__c,
                Not_at_Account__c,
                Contact_Original_Date__c
            FROM Contact
            WHERE Id IN :conIds
            ORDER BY CreatedDate ASC
        ];
        System.debug(cons[0].AccountId);
        Id newContactId = cons[2].Id;
        String newContactEmail = cons[0].Email;
        String newContactTitle = cons[0].Title;
        String newLeadSource = cons[0].LeadSource;
        Date newFirstMarketingInfluencedCampaignDate = cons[0].First_Marketing_Influenced_CampaignDate__c;
        DateTime oldestCreatedDate = cons[0].Contact_Original_Date__c;
        List<Id> contactIds = new List<Id>{ newContactId, cons[0].Id, cons[1].Id };
        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'OwnerId' => cons[2].OwnerId,
            'AccountId' => cons[2].AccountId,
            'Title' => newContactTitle,
            'Email' => newContactEmail,
            'MailingCountry' => cons[2].MailingCountry,
            'Previous_Owner__c' => newPreviousOwner.Id
        };

        mockDuplicateContactFieldCustomSettingData();
        List<FieldDefinition> contactFieldDefinitionList = getMockContactFieldDefinitionList();
        mocks.startStubbing();
        mocks.when(mockFieldDefinitionSelector.getEntityFieldDefinitionList('Contact')).thenReturn(contactFieldDefinitionList);
        mocks.stopStubbing();

        System.runAs(salesUser) {
            System.Test.startTest();
            String callbackContactStringId = ConvertLeadAndMergeContactController.mergeContacts(contactIds, userMergeContactFields);
            System.Test.stopTest();

            List<Contact> duplicateContacts = [
                SELECT
                    Id,
                    AccountId,
                    Email,
                    Title,
                    MobilePhone,
                    Handover_Notes__c,
                    Interest_CXPD_Date__c,
                    Interest_in_CXPD__c,
                    Previous_Owner__c,
                    LeadSource,
                    First_Marketing_Influenced_CampaignDate__c,
                    TW_Alumni__c,
                    Not_at_Account__c,
                    EmailBouncedDate,
                    EmailBouncedReason,
                    IsEmailBounced,
                    Bypass_Validation_Rule__c,
                    Is_Merge_Stage__c,
                    Subscription_Technology_Radar__c,
                    mkto71_Subscription_Access_Thoughtworks__c,
                    Stop_Prevent_Marketing__c,
                    mkto71_Subscription_Perspectives__c,
                    HasOptedOutOfEmail,
                    Consent_General_Marketing__c,
                    Contact_Original_Date__c,
                    Partnership_Involved__c,
                    LID__No_longer_at_Company__c,
                    Date_not_at_account_detected__c,
                    Email_Invalid__c,
                    Date_of_First_Attempt__c,
                    Response_Date__c,
                    Marketing_Influence_Expired_Date__c
                FROM Contact
                WHERE Id IN :conIds
                ORDER BY CreatedDate ASC
            ];
            Contact masterContact = duplicateContacts[0];

            System.assertEquals(cons[1].Id, masterContact.Id);
            System.assertEquals(true, masterContact.Partnership_Involved__c);
            System.assertEquals(false, masterContact.Bypass_Validation_Rule__c);
            System.assertEquals(false, masterContact.Is_Merge_Stage__c);
            System.assertEquals(1, duplicateContacts.size());
            System.assertEquals(Date.newInstance(2022, 1, 1), masterContact.EmailBouncedDate);
            System.assertEquals('contact 1 email bounced reason', masterContact.EmailBouncedReason);
            System.assertEquals(true, masterContact.IsEmailBounced);
            System.assertEquals(newLeadSource, masterContact.LeadSource); // test get from old
            System.assertEquals(newFirstMarketingInfluencedCampaignDate, masterContact.First_Marketing_Influenced_CampaignDate__c);
            System.assertEquals(true, masterContact.TW_Alumni__c); // test get true if exist true
            System.assertEquals(cons[2].AccountId, masterContact.AccountId);
            System.assertEquals('Not at Company', masterContact.LID__No_longer_at_Company__c); // test follow account retained in deduplication
            System.assertEquals(Date.newInstance(2022, 1, 1), masterContact.Date_not_at_account_detected__c); // test follow account retained in deduplication
            System.assertEquals(true, masterContact.Not_at_Account__c); // test follow account retained in deduplication
            System.assertEquals(String.ValueOf(masterContact.Id), callbackContactStringId);
            System.assertEquals(newContactEmail, masterContact.Email); //test get from new
            System.assertEquals(newContactTitle, masterContact.Title); //test get from user selector
            System.assertEquals(true, masterContact.Handover_Notes__c.contains('con1 handovernotes')); //test concatenate
            System.assertEquals(true, masterContact.Handover_Notes__c.contains('con3 handovernotes'));
            System.assertEquals(true, masterContact.Interest_in_CXPD__c);
            System.assertEquals('True', masterContact.Subscription_Technology_Radar__c);
            System.assertEquals(true, masterContact.mkto71_Subscription_Access_Thoughtworks__c);
            System.assertEquals(true, masterContact.Stop_Prevent_Marketing__c);
            System.assertEquals(true, masterContact.mkto71_Subscription_Perspectives__c);
            System.assertEquals(true, masterContact.HasOptedOutOfEmail);
            System.assertEquals(true, masterContact.Consent_General_Marketing__c);
            System.assertEquals(Date.newInstance(2021, 1, 1), masterContact.Interest_CXPD_Date__c);
            System.assertEquals(String.ValueOf(newPreviousOwner.Id), masterContact.Previous_Owner__c); //test custom lookup field merge
            System.assertEquals(oldestCreatedDate, masterContact.Contact_Original_Date__c);
            System.assertEquals(true, masterContact.Email_Invalid__c);
            System.assertEquals(Date.newInstance(2022, 1, 1), masterContact.Date_of_First_Attempt__c);
            System.assertEquals(Date.newInstance(2023, 1, 1), masterContact.Response_Date__c);
            System.assertEquals(Date.newInstance(2024, 1, 1), masterContact.Marketing_Influence_Expired_Date__c);
        }
    }

    @isTest
    public static void shouldSuccessfulMergeContactsWithNotAtAccount() {
        Account account = testFixture.prepareAccount(false, 'CRMACC1');
        Account account2 = testFixture.prepareAccount(false, 'CRMACC2');
        insert new List<Account>{ account, account2 };
        List<Contact> listContact = new List<Contact>();
        User newPreviousOwner = testFixture.prepareUser('CRM - Sales', 'testNewOwner');
        User previousOwner = testFixture.prepareUser('CRM - Sales', 'testOwner');
        Contact con1 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2010, 1, 1),
            TW_Alumni__c = false,
            Not_at_Account__c = false,
            MailingCountry = 'China',
            Handover_Notes__c = 'con1 handovernotes',
            Interest_CXPD_Date__c = Date.newInstance(2000, 1, 1),
            EmailBouncedDate = Date.newInstance(2022, 1, 1),
            EmailBouncedReason = 'contact 1 email bounced reason',
            Subscription_Technology_Radar__c = 'true',
            Interest_in_CXPD__c = false,
            mkto71_Subscription_Access_Thoughtworks__c = true,
            Stop_Prevent_Marketing__c = false
        );
        Contact con2 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Blog',
            Partnership_Involved__c = true,
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2020, 1, 1),
            TW_Alumni__c = false,
            Not_at_Account__c = false,
            MailingCountry = 'China',
            MobilePhone = '********',
            Interest_CXPD_Date__c = Date.newInstance(2021, 1, 1),
            Interest_in_CXPD__c = true,
            Previous_Owner__c = newPreviousOwner.Id,
            Status__c = 'Won',
            EmailBouncedDate = Date.newInstance(2021, 1, 1),
            EmailBouncedReason = 'contact 2 email bounced reason',
            Subscription_Technology_Radar__c = 'false',
            mkto71_Subscription_Access_Thoughtworks__c = false,
            HasOptedOutOfEmail = false,
            Consent_General_Marketing__c = true
        );
        Contact con3 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account2.Id,
            Title = 'Sales Head',
            LeadSource = 'Contact Us Form',
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2000, 1, 1),
            TW_Alumni__c = true,
            Not_at_Account__c = true,
            LID__No_longer_at_Company__c = 'Not at Company',
            Date_not_at_account_detected__c = Date.newInstance(2022, 1, 1),
            MailingCountry = 'China',
            Handover_Notes__c = 'con3 handovernotes',
            Interest_CXPD_Date__c = Date.newInstance(2010, 1, 1),
            Interest_in_CXPD__c = false,
            Previous_Owner__c = previousOwner.Id,
            EmailBouncedDate = Date.newInstance(2020, 1, 1),
            EmailBouncedReason = 'contact 3 email bounced reason',
            Stop_Prevent_Marketing__c = true,
            mkto71_Subscription_Perspectives__c = true,
            HasOptedOutOfEmail = true,
            Consent_General_Marketing__c = true
        );
        listContact.add(con1);
        listContact.add(con2);
        listContact.add(con3);
        insert listContact;
        List<Id> conIds = new List<Id>{ con1.Id, con2.Id, con3.Id };

        List<Contact> cons = [
            SELECT
                Id,
                Email,
                Title,
                MobilePhone,
                Handover_Notes__c,
                Account_Name__c,
                MailingCountry,
                OwnerId,
                AccountId,
                Owner.Name,
                Country__c,
                Function__c,
                Salutation_Formality__c,
                Preferred_Language__c,
                Company_Relationship_Type__c,
                TW_Alumni__c,
                ReportsTo.Account_Name__c,
                Strategy__c,
                Support__c,
                Influence__c,
                Contact_Frequency__c,
                AssistantName__c,
                Asst_Mobile__c,
                Asst_Email_Address__c,
                AssistantPhone,
                CreatedDate,
                MailingCity,
                MailingPostalCode,
                MailingState,
                MailingStreet,
                Previous_Owner__c,
                LeadSource,
                First_Marketing_Influenced_CampaignDate__c,
                Not_at_Account__c,
                Contact_Original_Date__c,
                Date_not_at_account_detected__c
            FROM Contact
            WHERE Id IN :conIds
            ORDER BY CreatedDate ASC
        ];
        Id newContactId = cons[2].Id;
        String newContactEmail = cons[0].Email;
        String newContactTitle = cons[0].Title;
        List<Id> contactIds = new List<Id>{ newContactId, cons[0].Id, cons[1].Id };
        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'OwnerId' => cons[2].OwnerId,
            'AccountId' => cons[0].AccountId,
            'Title' => newContactTitle,
            'Email' => newContactEmail,
            'MailingCountry' => cons[2].MailingCountry
        };

        mockDuplicateContactFieldCustomSettingData();
        List<FieldDefinition> contactFieldDefinitionList = getMockContactFieldDefinitionList();
        mocks.startStubbing();
        mocks.when(mockFieldDefinitionSelector.getEntityFieldDefinitionList('Contact')).thenReturn(contactFieldDefinitionList);
        mocks.stopStubbing();

        System.runAs(salesUser) {
            System.Test.startTest();
            ConvertLeadAndMergeContactController.mergeContacts(contactIds, userMergeContactFields);
            System.Test.stopTest();
        }
        List<Contact> duplicateContacts = [
            SELECT Id, AccountId, Not_at_Account__c, LID__No_longer_at_Company__c, Date_not_at_account_detected__c
            FROM Contact
            WHERE Id IN :conIds
            ORDER BY CreatedDate ASC
        ];
        Contact masterContact = duplicateContacts[0];
        System.assertEquals(cons[1].Id, masterContact.Id);
        System.assertEquals(cons[0].AccountId, masterContact.AccountId);
        System.assertEquals(null, masterContact.LID__No_longer_at_Company__c);
        System.assertEquals(null, masterContact.Date_not_at_account_detected__c);
        System.assertEquals(false, masterContact.Not_at_Account__c);
    }

    @isTest
    public static void shouldNotGetDuplicateLeadsForCLTUsers() {
        List<User> userList = new List<User>();
        User salesUser = dataFactory.createUser(Constants.TW_SALES_USER_PROFILE_NAME, 'saleUser');
        salesUser.FirstName = 'CRM';
        salesUser.LastName = 'Test user 1';
        salesUser.Email = '<EMAIL>';

        User cltUser = dataFactory.createUser(Constants.TW_BUSINESS_MANAGEMENT_PROFILE_NAME, 'cltUser');
        cltUser.FirstName = 'CRM';
        cltUser.LastName = 'Test user 2';
        cltUser.Email = '<EMAIL>';

        userList.add(salesUser);
        userList.add(cltUser);
        insert userList;

        List<Account> accountList = new List<Account>();
        Account account = testFixture.prepareAccount(false);
        account.Name = 'test account';
        insert account;

        List<Contact> listContact = new List<Contact>();
        Contact con1 = testFixture.preparePSContact(false, account);
        con1.FirstName = 'Beck';
        con1.LastName = 'David';
        con1.MailingCountry = 'India';

        Contact con2 = testFixture.preparePSContact(false, account);
        con2.FirstName = 'Beck';
        con2.LastName = 'David';
        con2.MailingCountry = 'India';

        listContact.add(con1);
        listContact.add(con2);
        insert listContact;

        Lead lead = testFixture.prepareLead(false);
        lead.FirstName = 'Beck';
        lead.LastName = 'David';
        lead.Country = 'India';
        insert lead;

        Map<String, List<Id>> duplicateRecordsMap1;
        Map<String, List<Id>> duplicateRecordsMap2;
        System.Test.startTest();
        System.runAs(cltUser) {
            duplicateRecordsMap1 = ConvertLeadAndMergeContactController.getDuplicateLeadsAndContacts(con1.Id);
        }
        System.runAs(salesUser) {
            duplicateRecordsMap2 = ConvertLeadAndMergeContactController.getDuplicateLeadsAndContacts(con1.Id);
        }
        System.Test.stopTest();
        System.assertEquals(con2.Id, duplicateRecordsMap1.get('Contact')[0]);
        System.assertEquals(0, duplicateRecordsMap1.get('Lead').size());
        System.assertEquals(con2.Id, duplicateRecordsMap2.get('Contact')[0]);
        System.assertEquals(lead.Id, duplicateRecordsMap2.get('Lead')[0]);
    }

    //    @isTest
    //    public static void shouldSuccessfulMergeContactsWithSameEmail() {
    //        Account account = testFixture.prepareAccount(false, 'CRMACC3');
    //        Account account1 = testFixture.prepareAccount(false, 'CRMACC4');
    //        insert new List<Account>{ account, account1 };
    //
    //        Contact contact1 = new Contact(
    //            RecordTypeId = PSContactRecordTypeId,
    //            Salutation = 'Mr.',
    //            LastName = 'merge1',
    //            FirstName = 'test12',
    //            Email = '<EMAIL>',
    //            AccountId = account.Id,
    //            MailingCountry = 'China',
    //            Title = 'Crm marketing',
    //            LeadSource = 'Alumni Referral',
    //            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2010, 1, 1),
    //            TW_Alumni__c = false,
    //            Not_at_Account__c = false,
    //            Handover_Notes__c = 'con1 handovernotes',
    //            Interest_CXPD_Date__c = Date.newInstance(2000, 1, 1),
    //            EmailBouncedDate = Date.newInstance(2022, 1, 1),
    //            EmailBouncedReason = 'contact 1 email bounced reason',
    //            Subscription_Technology_Radar__c = 'true',
    //            Interest_in_CXPD__c = false,
    //            mkto71_Subscription_Access_Thoughtworks__c = true,
    //            Stop_Prevent_Marketing__c = false,
    //            Email_Invalid__c = true
    //        );
    //        insert contact1;
    //
    //        Contact contact2 = new Contact(
    //            RecordTypeId = PSContactRecordTypeId,
    //            Salutation = 'Mr.',
    //            LastName = 'merge2',
    //            FirstName = 'test12',
    //            Email = '<EMAIL>',
    //            AccountId = account1.Id,
    //            MailingCountry = 'Australia',
    //            Title = 'Sales manager',
    //            LeadSource = 'Blog1',
    //            Partnership_Involved__c = true,
    //            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2020, 1, 1),
    //            TW_Alumni__c = false,
    //            Not_at_Account__c = true,
    //            MobilePhone = '********23',
    //            Interest_CXPD_Date__c = Date.newInstance(2021, 1, 2),
    //            Interest_in_CXPD__c = true,
    //            Status__c = 'Won',
    //            EmailBouncedDate = Date.newInstance(2021, 1, 2),
    //            EmailBouncedReason = 'contact 1 email bounced reason',
    //            Subscription_Technology_Radar__c = 'false',
    //            mkto71_Subscription_Access_Thoughtworks__c = false,
    //            HasOptedOutOfEmail = false,
    //            Consent_General_Marketing__c = true,
    //            Email_Invalid__c = false
    //        );
    //        insert contact2;
    //
    //        contact2.Email = '<EMAIL>';
    //        update contact2;
    //
    //        List<Id> contacts = new List<Id>{ contact1.Id, contact2.Id };
    //        List<Contact> cons = [
    //            SELECT Id, Email, OwnerId, AccountId, MailingCountry
    //            FROM Contact
    //            WHERE Id IN :contacts
    //            ORDER BY CreatedDate ASC
    //        ];
    //        List<Id> contactIds = new List<Id>{ cons[0].Id, cons[1].Id };
    //        Map<String, Object> userMergeContactFields = new Map<String, Object>{
    //            'OwnerId' => cons[1].OwnerId,
    //            'AccountId' => cons[1].AccountId,
    //            'Email' => cons[1].Email,
    //            'MailingCountry' => cons[1].MailingCountry
    //        };
    //
    //        System.runAs(salesUser) {
    //            System.Test.startTest();
    //            String callbackContactStringId = ConvertLeadAndMergeContactController.mergeContacts(contactIds, userMergeContactFields);
    //            System.Test.stopTest();
    //
    //            List<Contact> duplicateContacts = [
    //                SELECT Id, FirstName, LastName, Email, MailingCountry, Email_Invalid__c
    //                FROM Contact
    //                WHERE Id IN :contactIds
    //                ORDER BY CreatedDate ASC
    //            ];
    //            System.assertEquals(cons[1].Id, callbackContactStringId);
    //            System.assertEquals(1, duplicateContacts.size());
    //            System.assertEquals('<EMAIL>', duplicateContacts[0].Email);
    //            System.assertEquals('Australia', duplicateContacts[0].MailingCountry);
    //            System.assertEquals(true, duplicateContacts[0].Email_Invalid__c);
    //        }
    //    }

    @IsTest
    public static void shouldSuccessfulMergeContactsWithBothInvalidEmail() {
        Account account = testFixture.prepareAccount(false, 'CRMACC1');
        Account account2 = testFixture.prepareAccount(false, 'CRMACC2');
        insert new List<Account>{ account, account2 };
        List<Contact> listContact = new List<Contact>();
        Contact con1 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Alumni Referral',
            MailingCountry = 'China',
            Email_Invalid__c = true
        );
        Contact con2 = new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'David',
            FirstName = 'Beck',
            Email = '<EMAIL>',
            AccountId = account.Id,
            Title = 'Sales manager',
            LeadSource = 'Blog',
            Partnership_Involved__c = true,
            MailingCountry = 'China',
            MobilePhone = '********',
            Status__c = 'Won',
            Email_Invalid__c = true
        );
        listContact.add(con1);
        listContact.add(con2);
        insert listContact;
        List<Id> conIds = new List<Id>{ con1.Id, con2.Id };

        List<Contact> cons = [
            SELECT Id, Email, Title, MailingCountry, AccountId, OwnerId, Email_Invalid__c
            FROM Contact
            WHERE Id IN :conIds
            ORDER BY CreatedDate ASC
        ];
        Id newContactId = cons[1].Id;
        String newContactEmail = cons[0].Email;
        List<Id> contactIds = new List<Id>{ newContactId, cons[0].Id, cons[1].Id };
        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'OwnerId' => cons[1].OwnerId,
            'AccountId' => cons[0].AccountId,
            'Email' => newContactEmail,
            'MailingCountry' => cons[1].MailingCountry
        };

        mockDuplicateContactFieldCustomSettingData();
        List<FieldDefinition> contactFieldDefinitionList = getMockContactFieldDefinitionList();
        mocks.startStubbing();
        mocks.when(mockFieldDefinitionSelector.getEntityFieldDefinitionList('Contact')).thenReturn(contactFieldDefinitionList);
        mocks.stopStubbing();

        System.runAs(salesUser) {
            System.Test.startTest();
            ConvertLeadAndMergeContactController.mergeContacts(contactIds, userMergeContactFields);
            System.Test.stopTest();
        }
        List<Contact> duplicateContacts = [
            SELECT Id, AccountId, Email, Email_Invalid__c
            FROM Contact
            WHERE Id IN :conIds
            ORDER BY CreatedDate ASC
        ];
        Contact masterContact = duplicateContacts[0];
        System.assertEquals(cons[1].Id, masterContact.Id);
        System.assertEquals(cons[0].AccountId, masterContact.AccountId);
        System.assertEquals(newContactEmail, masterContact.Email);
        System.assertEquals(true, masterContact.Email_Invalid__c);
    }

    @IsTest
    public static void testCampaignMemberRelatedLogicWhenMergeLeadAndContact() {
        TriggerToggle.turnOff();
        Account account = testFixture.prepareAccount(false, 'CRMACC3');
        Account account1 = testFixture.prepareAccount(false, 'CRMACC4');
        account1.Name = 'selectAccount';
        insert new List<Account>{ account, account1 };

        Campaign campaign1 = testFixture.prepareCampaign(false);
        campaign1.Name = 'Test campaign 11';
        Campaign campaign2 = testFixture.prepareCampaign(false);
        campaign2.Name = 'Test campaign 22';
        insert new List<Campaign>{ campaign1, campaign2 };
        insert TestFactory.createRelatedCampaignMemberStatuses(new List<Campaign>{ campaign1, campaign2 });
        TriggerToggle.turnOn();
        Contact contact1 = preparePSContact(account, '', 'Won');
        Contact contact2 = preparePSContact(account, '', 'Sales Accepted');
        contact2.Email = '<EMAIL>';
        insert new List<Contact>{ contact1, contact2 };

        Lead lead1 = new Lead(
            RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
            FirstName = 'test12',
            LastName = 'merge',
            Company = 'BigCampany',
            Country = 'China',
            LeadSource = 'Customer Referral'
        );
        insert lead1;

        CampaignMember campaign1MemberLead1 = new CampaignMember(CampaignId = campaign1.Id, Status = 'Test Responded', LeadId = lead1.Id);
        CampaignMember campaign2MemberContact1 = new CampaignMember(CampaignId = campaign2.Id, Status = 'Test Responded', ContactId = contact1.Id);
        CampaignMember campaign2MemberContact2 = new CampaignMember(CampaignId = campaign2.Id, Status = 'Test Responded', ContactId = contact2.Id);
        List<CampaignMember> campaignMemberList = new List<CampaignMember>{ campaign1MemberLead1, campaign2MemberContact1, campaign2MemberContact2 };
        insert campaignMemberList;

        List<Id> mergeIds = new List<Id>{ contact1.Id, contact2.Id, lead1.Id };

        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'AccountId' => account1.Id,
            'OwnerId' => contact1.OwnerId,
            'Email' => contact1.Email,
            'MailingCountry' => contact1.MailingCountry
        };

        System.runAs(salesUser) {
            System.Test.startTest();
            ConvertLeadAndMergeContactController.mergeContacts(mergeIds, userMergeContactFields);
            System.Test.stopTest();
        }
        //update Company_Account__c on campaign member
        List<CampaignMember> campaignMembersAfterMerge = [SELECT Id, Company_Account__c, Person__c FROM CampaignMember WHERE ContactId = :contact1.Id ORDER BY Campaign.Name ASC];
        System.assertEquals(2, campaignMembersAfterMerge.size());
        System.assertEquals('selectAccount', campaignMembersAfterMerge.get(0).Company_Account__c);

        System.assertNotEquals(null, campaignMembersAfterMerge.get(0).Person__c);
        System.assertEquals(campaignMembersAfterMerge.get(0).Person__c, campaignMembersAfterMerge.get(1).Person__c);

        //merge different contacts, contacts were added to the same campaign, campaign members would be deleted, only one campaign member were left
        Set<Id> deletedCampaignMemberIdSet = CollectionUtils.getIdSet(campaignMemberList);
        deletedCampaignMemberIdSet.removeAll(CollectionUtils.getIdSet(campaignMembersAfterMerge));
        System.assertEquals(1, deletedCampaignMemberIdSet.size());

        String deletedCampaignMemberId = (String) new List<Id>(deletedCampaignMemberIdSet).get(0);

        List<Marketing_Activated_Person__c> maPersonList = [
            SELECT Id, Contact__c, Campaign_Member_Id__c, Person__c
            FROM Marketing_Activated_Person__c
            WHERE Contact__c = :contact1.Id
            ORDER BY Campaign__r.Name ASC
        ];
        System.assertEquals(2, maPersonList.size());
        System.assertEquals(campaignMembersAfterMerge.get(0).Id, maPersonList.get(0).Campaign_Member_Id__c);
        System.assertEquals(campaignMembersAfterMerge.get(0).Person__c, maPersonList.get(0).Person__c);
        System.assertEquals(campaignMembersAfterMerge.get(1).Id, maPersonList.get(1).Campaign_Member_Id__c);
        System.assertEquals(campaignMembersAfterMerge.get(1).Person__c, maPersonList.get(1).Person__c);

        List<Custom_Recycle_Bin__c> campaignMembersInRecycle = [SELECT Id, Record_Id__c FROM Custom_Recycle_Bin__c WHERE Type__c = 'CampaignMember'];
        System.assertEquals(1, deletedCampaignMemberIdSet.size());
        System.assertEquals(1, campaignMembersInRecycle.size());
        System.assertEquals(deletedCampaignMemberId, campaignMembersInRecycle.get(0).Record_Id__c);

        List<Campaign_Member_Field_History__c> campaignMemberFieldHistories = [SELECT Id, Campaign_Member_Id__c FROM Campaign_Member_Field_History__c ORDER BY Campaign__r.Name ASC];
        System.assertEquals(3, campaignMemberFieldHistories.size());
        System.assertEquals(campaignMembersAfterMerge.get(0).Id, campaignMemberFieldHistories.get(0).Campaign_Member_Id__c);
        System.assertEquals(campaignMembersAfterMerge.get(1).Id, campaignMemberFieldHistories.get(1).Campaign_Member_Id__c);
        System.assertEquals(campaignMembersAfterMerge.get(1).Id, campaignMemberFieldHistories.get(2).Campaign_Member_Id__c);

        List<Holistic_Engagement__c> engagements = [SELECT Id, Campaign_Member_Id__c FROM Holistic_Engagement__c ORDER BY Campaign__r.Name ASC];
        System.assertEquals(3, engagements.size());
        System.assertEquals(campaignMembersAfterMerge.get(0).Id, engagements.get(0).Campaign_Member_Id__c);
        System.assertEquals(campaignMembersAfterMerge.get(1).Id, engagements.get(1).Campaign_Member_Id__c);
        System.assertEquals(campaignMembersAfterMerge.get(1).Id, engagements.get(2).Campaign_Member_Id__c);
    }

    @IsTest
    private static void shouldReplaceMergedContactIdWithMasterContactIdAndMemberId() {
        MocksFactory.mockLatestMonthlyExchangeRate();
        Id psContactRecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId();

        TriggerToggle.turnOff();
        Account account = testFixture.prepareAccount(true);
        Contact masterContact = TestFactory.buildTestPSContact(0, account, psContactRecordTypeId);
        Contact mergedContact001 = TestFactory.buildTestPSContact(1, account, psContactRecordTypeId);
        insert new List<Contact>{ masterContact, mergedContact001 };

        Person__c masterPerson = new Person__c();
        masterPerson.Contact__c = masterContact.Id;
        insert masterPerson;

        Datetime timeNow = Datetime.now();

        Campaign campaign = testFixture.prepareCampaign(true);
        insert TestFactory.createRelatedCampaignMemberStatuses(new List<Campaign>{ campaign });
        CampaignMember cm1 = new CampaignMember(ContactId = masterContact.Id, CampaignId = campaign.Id, Status = 'Test Responded', FirstRespondedDateTime__c = timeNow.addDays(-1));
        CampaignMember cm2 = new CampaignMember(ContactId = mergedContact001.Id, CampaignId = campaign.Id, Status = 'Test Responded', FirstRespondedDateTime__c = timeNow.addDays(-2));
        insert new List<CampaignMember>{ cm1, cm2 };

        Opportunity oppoA = testFixture.prepareOpportunity(false);
        oppoA.Opportunity_Contract_Value_in_USD__c = ********;
        Opportunity oppoB = testFixture.prepareOpportunity(false);
        oppoB.Opportunity_Contract_Value_in_USD__c = ********;
        insert new List<Opportunity>{ oppoA, oppoB };

        OpportunityContactRole masterContactInOppoA = new OpportunityContactRole(ContactId = masterContact.Id, OpportunityId = oppoA.Id, Role = 'Team member', IsPrimary = false);
        OpportunityContactRole mergedContact001InOppoB = new OpportunityContactRole(ContactId = mergedContact001.Id, OpportunityId = oppoB.Id, Role = 'merged Team member', IsPrimary = false);
        insert new List<OpportunityContactRole>{ masterContactInOppoA, mergedContact001InOppoB };

        Campaign_Influence__c ci1 = new Campaign_Influence__c(
            Campaign__c = campaign.Id,
            Contact__c = masterContact.Id,
            Opportunity__c = oppoA.Id,
            Opportunity_Contact_Role_Id__c = masterContactInOppoA.Id,
            Campaign_Member_Id__c = cm1.Id,
            Influence_Type__c = 'Influence to create opportunity',
            Contact_Added_On_Opportunity_Datetime__c = timeNow,
            First_Responded_Datetime__c = timeNow.addDays(-1)
        );
        Campaign_Influence__c ci2 = new Campaign_Influence__c(
            Campaign__c = campaign.Id,
            Contact__c = mergedContact001.Id,
            Opportunity__c = oppoB.Id,
            Opportunity_Contact_Role_Id__c = mergedContact001InOppoB.Id,
            Campaign_Member_Id__c = cm2.Id,
            Influence_Type__c = 'Influence to create opportunity',
            Contact_Added_On_Opportunity_Datetime__c = timeNow,
            First_Responded_Datetime__c = timeNow.addDays(-2)
        );
        insert new List<Campaign_Influence__c>{ ci1, ci2 };

        Campaign_Influence_Log__c influenceLog1 = new Campaign_Influence_Log__c(
            Campaign_Influence_Id__c = ci1.Id,
            Campaign_Id__c = campaign.Id,
            Contact_Id__c = masterContact.Id,
            Opportunity_Id__c = oppoA.Id
        );
        Campaign_Influence_Log__c influenceLog2 = new Campaign_Influence_Log__c(
            Campaign_Influence_Id__c = ci2.Id,
            Campaign_Id__c = campaign.Id,
            Contact_Id__c = mergedContact001.Id,
            Opportunity_Id__c = oppoB.Id
        );
        insert new List<Campaign_Influence_Log__c>{influenceLog1, influenceLog2};

        TriggerToggle.turnOn();

        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'OwnerId' => masterContact.OwnerId,
            'AccountId' => masterContact.AccountId,
            'Email' => masterContact.Email,
            'MailingCountry' => masterContact.MailingCountry
        };
        System.runAs(salesUser) {
            System.Test.startTest();
            ConvertLeadAndMergeContactController.mergeContacts(new List<Id>{ masterContact.Id, mergedContact001.Id }, userMergeContactFields);
            System.Test.stopTest();
        }

        List<CampaignMember> campaignMembersAfterMerge = [SELECT Id, Company_Account__c FROM CampaignMember WHERE ContactId = :masterContact.Id ORDER BY Campaign.Name ASC];
        System.assertEquals(1, campaignMembersAfterMerge.size());

        List<Campaign_Influence__c> campaignInfluences = [SELECT Id, Contact__c, Campaign_Member_Id__c, Touch_Point__c, Marketing_Pipeline__c FROM Campaign_Influence__c WHERE Contact__c = :masterContact.Id ORDER BY Id ASC];
        System.assertEquals(2, campaignInfluences.size());
        System.assertEquals(ci1.Id, campaignInfluences[0].Id);
        System.assertEquals(masterContact.Id, campaignInfluences[0].Contact__c);
        System.assertEquals(null, campaignInfluences[0].Touch_Point__c);
        System.assertEquals(null, campaignInfluences[0].Marketing_Pipeline__c);
        System.assertEquals(campaignMembersAfterMerge.get(0).Id, campaignInfluences[0].Campaign_Member_Id__c);
        System.assertEquals(ci2.Id, campaignInfluences[1].Id);
        System.assertEquals(masterContact.Id, campaignInfluences[1].Contact__c);
        System.assertEquals(null, campaignInfluences[1].Touch_Point__c);
        System.assertEquals(null, campaignInfluences[1].Marketing_Pipeline__c);
        System.assertEquals(campaignMembersAfterMerge.get(0).Id, campaignInfluences[1].Campaign_Member_Id__c);

        List<Campaign_Influence_Log__c> influenceLogs = [SELECT Id, Campaign_Influence_Id__c, Opportunity_Id__c, Campaign_Id__c, Operation_Path__c, Operation_influence__c, Operation_Datetime__c, Operator__c FROM Campaign_Influence_Log__c WHERE Contact_Id__c = :masterContact.Id ORDER BY Campaign_Influence_Id__c ASC, CreatedDate ASC, Id ASC];
        String expectedPath = 'Merge Contact(Master Contact is ' + masterContact.Id +  ')';
        System.assertEquals(3, influenceLogs.size());
        System.assertEquals(ci1.Id, influenceLogs.get(0).Campaign_Influence_Id__c);
        System.assertEquals(ci1.Opportunity__c, influenceLogs.get(0).Opportunity_Id__c);
        System.assertEquals(ci1.Campaign__c, influenceLogs.get(0).Campaign_Id__c);
        System.assertEquals(ci2.Id, influenceLogs.get(1).Campaign_Influence_Id__c);
        System.assertEquals(ci2.Opportunity__c, influenceLogs.get(1).Opportunity_Id__c);
        System.assertEquals(ci2.Campaign__c, influenceLogs.get(1).Campaign_Id__c);
        System.assertEquals(ci2.Id, influenceLogs.get(2).Campaign_Influence_Id__c);
        System.assertEquals(ci2.Opportunity__c, influenceLogs.get(2).Opportunity_Id__c);
        System.assertEquals(ci2.Campaign__c, influenceLogs.get(2).Campaign_Id__c);
        System.assertEquals(expectedPath, influenceLogs.get(2).Operation_Path__c);
        System.assertEquals(true, influenceLogs.get(2).Operation_influence__c.containsIgnoreCase('Update Campaign Influence, detail: Change First Responded Datetime from'));
        System.assertEquals(salesUser.Id, influenceLogs.get(2).Operator__c);
    }

    @IsTest
    public static void shouldHolisticEngagementActivityWhenMergeLeadAndContact() {
        TriggerToggle.turnOff();
        Account account1 = testFixture.prepareAccount(true, 'CRMACC4');

        TriggerToggle.turnOn();
        Contact contact1 = preparePSContact(account1, '113344', 'Won');
        Contact contact2 = preparePSContact(account1, '113345', 'Sales Accepted');
        contact2.Email = '<EMAIL>';
        insert new List<Contact>{ contact1, contact2 };

        Lead lead1 = new Lead(
            RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
            FirstName = 'test12',
            LastName = 'merge',
            Company = 'BigCampany',
            Country = 'China',
            LeadSource = 'Customer Referral',
            MarketoContactId__c = '113346'
        );
        insert lead1;

        WebActivityObject__c webActivity1 = prepareWebActivity(timeNow.addDays(-20), 112394, '113344');
        WebActivityObject__c webActivity2 = prepareWebActivity(timeNow.addDays(-15), 112395, '113345');
        WebActivityObject__c webActivity3 = prepareWebActivity(timeNow.addDays(-10), 112396, '113346');
        insert new List<WebActivityObject__c>{ webActivity1, webActivity2, webActivity3 };

        List<Id> mergeIds = new List<Id>{ contact1.Id, contact2.Id, lead1.Id };

        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'AccountId' => account1.Id,
            'OwnerId' => contact1.OwnerId,
            'Email' => contact1.Email,
            'MailingCountry' => contact1.MailingCountry
        };

        System.runAs(salesUser) {
            System.Test.startTest();
            ConvertLeadAndMergeContactController.mergeContacts(mergeIds, userMergeContactFields);
            System.Test.stopTest();
        }

        Person__c person1 = [SELECT Id FROM Person__c WHERE Contact__c = :contact1.Id][0];
        List<Holistic_Engagement__c> engagements = [
            SELECT Id, Person__c, Web_Activity__c, Web_Activity__r.ContactId__c, Customer_Lifecycle_Stage__c
            FROM Holistic_Engagement__c
            ORDER BY Web_Activity__r.ActivityId__c ASC
        ];
        System.assertEquals(3, engagements.size());
        System.assertEquals(person1.Id, engagements.get(0).Person__c);
        System.assertEquals(contact1.MarketoContactId__c, engagements.get(0).Web_Activity__r.ContactId__c);
        System.assertEquals(person1.Id, engagements.get(1).Person__c);
        System.assertEquals(contact1.MarketoContactId__c, engagements.get(1).Web_Activity__r.ContactId__c);
        System.assertEquals(person1.Id, engagements.get(2).Person__c);
        System.assertEquals(contact1.MarketoContactId__c, engagements.get(2).Web_Activity__r.ContactId__c);
    }

    @isTest
    private static void shouldDeleteDurationRecordWhenMergeContacts() {
        TriggerToggle.turnOff();
        Account account1 = testFixture.prepareAccount(true, 'CRMACC4');

        TriggerToggle.turnOn();
        Contact contact1 = preparePSContact(account1, '113344', 'Won');
        Contact contact2 = preparePSContact(account1, '113345', 'Sales Accepted');
        contact2.Email = '<EMAIL>';
        insert new List<Contact>{ contact1, contact2 };

        Lead lead1 = new Lead(
            RecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId(),
            FirstName = 'test12',
            LastName = 'merge',
            Company = 'BigCampany',
            Country = 'China',
            LeadSource = 'Customer Referral',
            MarketoContactId__c = '113346'
        );
        insert lead1;

        List<Lead_Contact_Status__c> contactStatusBeforeDelete = [SELECT Id, Lead__c, Contact__c, Type__c FROM Lead_Contact_Status__c ORDER BY Type__c ASC, Status__c ASC];
        System.assertEquals(3, contactStatusBeforeDelete.size());
        System.assertEquals(contact2.Id, contactStatusBeforeDelete.get(0).Contact__c);
        System.assertEquals(contact1.Id, contactStatusBeforeDelete.get(1).Contact__c);
        System.assertEquals(lead1.Id, contactStatusBeforeDelete.get(2).Lead__c);

        Map<String, Object> userMergeContactFields = new Map<String, Object>{
            'OwnerId' => contact1.OwnerId,
            'AccountId' => contact1.AccountId,
            'Title' => contact1.Title,
            'Email' => contact1.Email,
            'MailingCountry' => contact1.MailingCountry
        };

        System.Test.startTest();
        ConvertLeadAndMergeContactController.mergeContacts(new List<Id>{ contact1.Id, contact2.Id, lead1.Id }, userMergeContactFields);
        System.Test.stopTest();

        List<Lead_Contact_Status__c> contactStatusAfterDelete = [SELECT Id, Lead__c, Contact__c, Type__c FROM Lead_Contact_Status__c ORDER BY Type__c ASC, Status__c ASC];
        System.assertEquals(1, contactStatusAfterDelete.size());
        System.assertEquals(contact1.Id, contactStatusAfterDelete.get(0).Contact__c);
    }

    private static Contact preparePSContact(Account acc, String marketoId, String status) {
        return new Contact(
            RecordTypeId = PSContactRecordTypeId,
            Salutation = 'Mr.',
            LastName = 'merge',
            FirstName = 'test12',
            Email = '<EMAIL>',
            AccountId = acc.Id,
            MailingCountry = 'China',
            Title = 'Crm marketing',
            LeadSource = 'Alumni Referral',
            First_Marketing_Influenced_CampaignDate__c = Date.newInstance(2010, 1, 1),
            TW_Alumni__c = false,
            Status__c = status,
            Not_at_Account__c = false,
            Handover_Notes__c = 'con1 handovernotes',
            Interest_CXPD_Date__c = Date.newInstance(2000, 1, 1),
            EmailBouncedDate = Date.newInstance(2022, 1, 1),
            EmailBouncedReason = 'contact 1 email bounced reason',
            Subscription_Technology_Radar__c = 'true',
            Interest_in_CXPD__c = false,
            mkto71_Subscription_Access_Thoughtworks__c = true,
            Stop_Prevent_Marketing__c = false,
            MarketoContactId__c = marketoId
        );
    }
    private static WebActivityObject__c prepareWebActivity(Datetime activityDate, Integer activityId, String contactId) {
        return new WebActivityObject__c(
            ActivityDate__c = activityDate,
            ActivityId__c = activityId,
            ContactId__c = contactId,
            PageLink__c = 'TestWebpageLink',
            SearchEngine__c = 'TestSearchEngine',
            SearchEngineLink__c = 'TestRefererURL',
            IsNewest__c = true
        );
    }
}
