@isTest
private class TestActivityAfterDeleteTriggerHandler {
    private static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldChangeLeadStageAndDateWhenDeleteActivity() {
        TriggerToggle.turnOff();
        Lead lead1 = testFixture.prepareLead(false);
        lead1.FirstName = 'lead1';
        lead1.Email = '<EMAIL>';
        lead1.SAL_Substage__c = CRMConstants.SUBSTAGE_ATTEMPTED;
        lead1.Date_of_First_Attempt__c = Date.today();
        lead1.Date_of_Attempted__c = Date.today();
        Lead lead2 = testFixture.prepareLead(false);
        lead2.FirstName = 'lead2';
        lead2.Email = '<EMAIL>';
        lead2.SAL_Substage__c = CRMConstants.SUBSTAGE_ATTEMPTED;
        lead2.Date_of_First_Attempt__c = Date.today();
        lead2.Date_of_Attempted__c = Date.today();
        insert new List<Lead>{ lead1, lead2 };

        Task task1 = new Task(Subject = 'test', TaskSubtype = 'Call', Subject_Text__c = 'note', WhoId = lead1.Id);
        Task task2 = new Task(Subject = 'test', TaskSubtype = 'Call', Subject_Text__c = 'note', WhoId = lead2.Id);
        Event event2 = new Event(Subject = 'test', Subject_Text__c = 'note', DurationInMinutes = 60, ActivityDateTime = Date.today(), WhoId = lead2.Id);
        insert new List<Task>{ task1, task2 };
        insert event2;

        TriggerToggle.turnOn();

        System.Test.startTest();
        delete new List<Task>{ task1, task2 };
        Lead newLead1 = [SELECT SAL_Substage__c, Date_of_First_Attempt__c, Date_of_Attempted__c FROM Lead WHERE Id = :lead1.Id LIMIT 1];
        System.assertEquals(CRMConstants.SUBSTAGE_NEW_ASSIGNED, newLead1.SAL_Substage__c);
        System.assertEquals(null, newLead1.Date_of_First_Attempt__c);
        System.assertEquals(null, newLead1.Date_of_Attempted__c);

        Lead newLead2 = [SELECT SAL_Substage__c, Date_of_First_Attempt__c, Date_of_Attempted__c FROM Lead WHERE Id = :lead2.Id LIMIT 1];
        System.assertEquals(CRMConstants.SUBSTAGE_ATTEMPTED, newLead2.SAL_Substage__c);
        System.assertEquals(true, newLead2.Date_of_Attempted__c != null);
        System.assertEquals(true, newLead2.Date_of_First_Attempt__c != null);
        System.Test.stopTest();
    }

    @IsTest
    static void shouldChangeContactStageAndDateWhenDeleteActivity() {
        TriggerToggle.turnOff();
        Account acc = testFixture.prepareAccount(true);
        Contact contact1 = testFixture.preparePSContact(false, acc);
        contact1.FirstName = 'contact1';
        contact1.Email = '<EMAIL>';
        contact1.SAL_Substage__c = CRMConstants.SUBSTAGE_ATTEMPTED;
        contact1.Date_of_First_Attempt__c = Date.today();
        Contact contact2 = testFixture.preparePSContact(false, acc);
        contact2.FirstName = 'contact2';
        contact2.Email = '<EMAIL>';
        contact2.SAL_Substage__c = CRMConstants.SUBSTAGE_ATTEMPTED;
        contact2.Date_of_First_Attempt__c = Date.today();
        insert new List<Contact>{ contact1, contact2 };

        Task task1 = new Task(Subject = 'test', TaskSubtype = 'Call', Subject_Text__c = 'note', WhoId = contact1.Id);
        Event event2 = new Event(Subject = 'test', Subject_Text__c = 'note', DurationInMinutes = 60, ActivityDateTime = Date.today(), WhoId = contact2.Id);
        Task task2 = new Task(Subject = 'test', TaskSubtype = 'Call', Subject_Text__c = 'note', WhoId = contact2.Id);
        insert new List<Task>{ task1, task2 };
        insert event2;

        TriggerToggle.turnOn();

        System.Test.startTest();
        delete new List<Task>{ task1, task2 };
        Contact newContact1 = [SELECT SAL_Substage__c, Date_of_First_Attempt__c FROM Contact WHERE Id = :contact1.Id LIMIT 1];
        System.assertEquals(CRMConstants.SUBSTAGE_NEW_ASSIGNED, newContact1.SAL_Substage__c);
        System.assertEquals(null, newContact1.Date_of_First_Attempt__c);

        Contact newContact2 = [SELECT SAL_Substage__c, Date_of_First_Attempt__c FROM Contact WHERE Id = :contact2.Id LIMIT 1];
        System.assertEquals(CRMConstants.SUBSTAGE_ATTEMPTED, newContact2.SAL_Substage__c);
        System.assertEquals(true, newContact2.Date_of_First_Attempt__c != null);
        System.Test.stopTest();
    }
}
