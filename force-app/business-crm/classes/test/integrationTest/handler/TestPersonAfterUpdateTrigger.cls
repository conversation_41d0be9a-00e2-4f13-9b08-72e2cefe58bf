@IsTest
public class TestPersonAfterUpdateTrigger {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldSyncLeadOrContactWhenPersonQualifiedOrMQL() {
        TriggerToggle.turnOff();
        Account acc = testFixture.prepareAccount(true);
        TriggerToggle.turnOn();

        Contact contact = testFixture.getDataFactory().createPSContact(acc);
        contact.Status__c = 'Sales Accepted';
        insert contact;

        Person__c contactPerson = [SELECT Id FROM Person__c WHERE Contact__c = :contact.Id];
        contactPerson.Person_Disqualified_Reason__c = 'Client not aligned';
        contactPerson.Person_Disqualification_Notes__c = 'test contact note';

        Lead lead = testFixture.getDataFactory().createPSLead();
        lead.Status = 'Sales Accepted';
        insert lead;

        Person__c leadPerson = [SELECT Id FROM Person__c WHERE Lead__c = :lead.Id];
        leadPerson.Person_Sales_Follow_up_Notes__c = 'test sales follow up lead note';
        leadPerson.Person_Customer_Lifecycle_Stage__c = 'Marketing Qualified';

        update new List<Person__c>{ contactPerson, leadPerson };

        List<Person__c> resultPerson = [
            SELECT Id, Disqualification_Notes__c, Disqualified_Reason__c, Sales_Follow_up_Notes__c, Lead__c, Contact__c, Customer_Lifecycle_Stage__c
            FROM Person__c
            WHERE Contact__c = :contact.Id OR Lead__c = :lead.Id
            ORDER BY CreatedDate ASC
        ];
        Assert.areEqual(contactPerson.Person_Disqualified_Reason__c, resultPerson[0].Disqualified_Reason__c);
        Assert.areEqual(contactPerson.Person_Disqualification_Notes__c, resultPerson[0].Disqualification_Notes__c);
        Assert.areEqual(leadPerson.Person_Sales_Follow_up_Notes__c, resultPerson[1].Sales_Follow_up_Notes__c);
        Assert.areEqual(leadPerson.Person_Customer_Lifecycle_Stage__c, resultPerson[1].Customer_Lifecycle_Stage__c);
    }
}
