@IsTest
public class TestPersonBeforeDeleteTrigger {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldDeleteCampaignMemberFieldHistoryWhenDeletePerson() {
        TriggerToggle.turnOff();
        Account acc = testFixture.prepareAccount(true);
        Holistic_Engagement_Model__c model = new Holistic_Engagement_Model__c(Channel_Type__c = 'Content - Blog', Status__c = 'Exposed to Content', Is_Engaged__c = false);
        insert model;
        TriggerToggle.turnOn();

        Contact contact = testFixture.getDataFactory().createPSContact(acc);
        insert contact;

        Person__c person = [SELECT Id FROM Person__c WHERE Contact__c = :contact.Id];
        CampaignMember campaignMember = testFixture.prepareCampaignMember(false, null, null, contact);
        campaignMember.Person__c = person.Id;
        insert campaignMember;

        List<Campaign_Member_Field_History__c> fieldHistories = [SELECT Id, Person__c, Person__r.Contact__c FROM Campaign_Member_Field_History__c WHERE Campaign_Member_Id__c = :campaignMember.Id];
        Assert.areEqual(1, fieldHistories.size());
        Assert.areEqual(contact.Id, fieldHistories.get(0).Person__r.Contact__c);
        Assert.areEqual(campaignMember.Person__c, fieldHistories.get(0).Person__c);

        delete contact;
        List<Campaign_Member_Field_History__c> fieldHistoriesAfterDeletePerson = [SELECT Id FROM Campaign_Member_Field_History__c WHERE Campaign_Member_Id__c = :campaignMember.Id];
        Assert.areEqual(0, fieldHistoriesAfterDeletePerson.size());
    }
}
