@IsTest
private class TestMKTContactBeforeDeleteTriggerHandler {
    private static TWPSA_TestFixture_Default testFixture;
    private static Id resourceContactRecordTypeId;
    private static Profile marketoProfile;
    private static User marKetoUser;
    private static User psaAutoUser;
    static {
        testFixture = new TWPSA_TestFixture_Default();
        resourceContactRecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        marketoProfile = [SELECT Id, Name FROM Profile WHERE Name = 'Marketo Sync'];
        marKetoUser = testFixture.prepareUser();
        psaAutoUser = [SELECT Id FROM User WHERE Username LIKE 'psaauto%'];
    }

    @IsTest
    static void marketoUserCannotDeleteResourceContact() {
        System.runAs(psaAutoUser) {
            marKetoUser.ProfileId = marketoProfile.Id;
            update marKetoUser;
        }

        Contact resourceContact = testFixture.prepareContact(true);
        resourceContact.RecordTypeId = resourceContactRecordTypeId;
        update resourceContact;

        System.Test.startTest();
        System.runAs(marKetoUser) {
            try {
                delete resourceContact;
                System.Assert(false);
            } catch (Exception e) {
                system.assertEquals(true, e.getMessage().contains('Error. You don’t have permission to delete resource.'));
            }
        }
        System.Test.stopTest();

        List<Contact> contactList = [SELECT Id FROM Contact];
        System.assertEquals(1, contactList.size());
    }

    @IsTest
    static void adminCanDeleteResourceContact() {
        Contact resourceContact = testFixture.prepareContact(true);

        System.Test.startTest();
        System.runAs(psaAutoUser) {
            delete resourceContact;
        }
        System.Test.stopTest();

        List<Contact> contactList = [SELECT Id FROM Contact];
        System.assertEquals(0, contactList.size());
    }
}
