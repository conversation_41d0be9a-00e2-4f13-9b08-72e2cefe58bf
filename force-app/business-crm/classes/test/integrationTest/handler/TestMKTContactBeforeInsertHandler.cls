@IsTest
private class TestMKTContactBeforeInsertHandler {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldAssignedToSalesWhenCheckedDuringContactCreation() {
        Contact con = testFixture.prepareContact(false);
        con.Assign_to_Sales__c = 'Yes';
        con.Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ENGAGED;

        insert con;

        Id contactId = con.Id;
        List<Id> contactIds = new List<Id>();
        contactIds.add(contactId);
        Map<Id, Contact> contacts = getContactsById(contactIds);
        Contact contact = contacts.get(contactId);

        System.assertEquals('Sales Accepted', contact.Status__c);
        System.assertEquals(28, contact.Days_Left_Before_Recycled__c);
    }
    @IsTest
    static void shouldNotSyncToMarketoIfEmailIsNull() {
        Contact con = testFixture.prepareContact(false);
        con.FirstName = '99999';
        con.Email = null;

        insert con;
        Contact queryContact = [SELECT Id, Sync_to_Marketo__c FROM Contact WHERE Id = :con.Id];
        System.assertEquals(false, queryContact.Sync_to_Marketo__c);
    }

    @IsTest
    static void shouldNotSyncToMarketoIfFirstNameAnonymised() {
        Contact con = testFixture.prepareContact(false);
        con.FirstName = CRMConstants.FIRST_NAME_TO_TAG_ANONYMOUS;
        con.Email = '<EMAIL>';

        insert con;
        Contact queryContact = [SELECT Id, Sync_to_Marketo__c FROM Contact WHERE Id = :con.Id];
        System.assertEquals(false, queryContact.Sync_to_Marketo__c);
    }

    @IsTest
    static void shouldSyncToMarketoIfInsert() {
        Contact con = testFixture.prepareContact(false);
        con.FirstName = '99999';
        con.Email = '<EMAIL>';

        insert con;
        Contact queryContact = [SELECT Id, Sync_to_Marketo__c FROM Contact WHERE Id = :con.Id];
        System.assertEquals(true, queryContact.Sync_to_Marketo__c);
    }

    private static Map<Id, Contact> getContactsById(List<Id> contactIdList) {
        return new Map<Id, Contact>([SELECT Status__c, Days_Left_Before_Recycled__c FROM Contact WHERE Id IN :contactIdList]);
    }
}
