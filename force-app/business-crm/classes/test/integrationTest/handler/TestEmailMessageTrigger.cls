@IsTest
public with sharing class TestEmailMessageTrigger {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldHandlePersonSubstageWhenEmailActivityAfterDeleteAfterDelete() {
        TriggerToggle.turnOff();
        Account account = testFixture.prepareAccount(true);
        Contact contact = testFixture.preparePSContact(false, account);
        insert contact;

        TriggerToggle.turnOn();

        EmailMessage email = new EmailMessage();
        email.FromAddress = '<EMAIL>';
        email.Incoming = true;
        email.Status = '3';
        email.ToAddress = '<EMAIL>';
        email.Subject = 'Test email';
        email.HtmlBody = 'Test email body';
        insert email;

        EmailMessageRelation emr = new EmailMessageRelation();
        emr.emailMessageId = email.Id;
        emr.relationId = contact.Id;
        emr.relationType = 'ToAddress';
        insert emr;

        contact.Status__c = 'Sales Accepted';
        update contact;

        List<Task> task = [SELECT Id FROM Task WHERE WhoId = :contact.Id];
        System.assertEquals(1, task.size());

        // when
        delete email;

        // then
        Contact updatedContact = [SELECT SAL_Substage__c FROM Contact WHERE Id = :contact.Id];
        System.assertEquals('New Assigned', updatedContact.SAL_Substage__c);
    }
}
