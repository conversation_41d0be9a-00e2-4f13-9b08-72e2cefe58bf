@IsTest
public with sharing class TestSurveyRespAfterUpdateTrigger {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldUpdateContactNPSInfoOnSurveyResponseUpdate() {
        // given
        TriggerToggle.turnOff();
        Contact testContact = testFixture.prepareContact(false);
        insert testContact;

        Survey_Response__c response = new Survey_Response__c(Contact__c = testContact.Id, NPS_Score__c = '8', NPS_Comment__c = 'Very good', Response_Datetime__c = Datetime.now().addDays(-1));
        insert response;
        TriggerToggle.turnOn();

        //when
        Survey_Response__c updatedResponse = [SELECT Id, NPS_Score__c, NPS_Comment__c, Response_Datetime__c FROM Survey_Response__c WHERE Contact__c = :testContact.Id];
        updatedResponse.NPS_Score__c = '10';
        updatedResponse.NPS_Comment__c = 'Excellent';
        updatedResponse.Response_Datetime__c = Datetime.now();
        update updatedResponse;

        //then
        Contact updatedContact = [SELECT Latest_NPS_Comment__c, Latest_NPS_Score__c, Latest_NPS_Response_Date__c FROM Contact WHERE Id = :testContact.Id];
        System.assertEquals('Excellent', updatedContact.Latest_NPS_Comment__c, 'The latest NPS comment should be updated to Excellent.');
        System.assertEquals('10', updatedContact.Latest_NPS_Score__c, 'The latest NPS score should be updated to 10.');
        System.assertEquals(Date.today(), updatedContact.Latest_NPS_Response_Date__c, 'The latest NPS response date should be updated to today.');
    }
}
