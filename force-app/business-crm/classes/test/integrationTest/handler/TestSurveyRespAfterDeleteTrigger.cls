@IsTest
public with sharing class TestSurveyRespAfterDeleteTrigger {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldUpdateContactWithLatestNPSInfoAfterDelete() {
        // given
        TriggerToggle.turnOff();
        Contact testContact = testFixture.prepareContact(false);
        insert testContact;

        Survey_Response__c oldResponse = new Survey_Response__c(Contact__c = testContact.Id, NPS_Score__c = '7', NPS_Comment__c = 'Good service', Response_Datetime__c = Datetime.now().addDays(-1));
        Survey_Response__c latestResponse = new Survey_Response__c(Contact__c = testContact.Id, NPS_Score__c = '9', NPS_Comment__c = 'Excellent service', Response_Datetime__c = Datetime.now());

        insert oldResponse;
        insert latestResponse;
        TriggerToggle.turnOn();

        // when
        delete oldResponse;

        // then
        Contact updatedContact = [SELECT Latest_NPS_Comment__c, Latest_NPS_Score__c, Latest_NPS_Response_Date__c FROM Contact WHERE Id = :testContact.Id];
        System.assertEquals('Excellent service', updatedContact.Latest_NPS_Comment__c, 'The latest NPS comment should be updated.');
        System.assertEquals('9', updatedContact.Latest_NPS_Score__c, 'The latest NPS score should be updated.');
        System.assertEquals(Date.today(), updatedContact.Latest_NPS_Response_Date__c, 'The latest NPS response date should be updated.');
    }
}
