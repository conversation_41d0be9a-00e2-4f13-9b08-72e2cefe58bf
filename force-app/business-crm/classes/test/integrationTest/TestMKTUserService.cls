@IsTest
private class TestMKTUserService {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    @IsTest
    static void shouldChangeUserAndContactFieldsWhenUserDeprovision() {
        User user1 = testFixture.prepareUser();
        user1.UserPermissionsMarketingUser = true;
        User user2 = testFixture.prepareUser('System Administrator', 'user2');

        System.runAs(user2) {
            System.Test.startTest();
            user1.IsActive = false;
            update user1;
            System.Test.stopTest();
        }

        user1 = [SELECT Id, ProfileId, UserPermissionsMarketingUser FROM User WHERE Id = :user1.Id];
        Profile baseProfile = [SELECT Id FROM Profile WHERE Name = :Constants.BASE_PROFILE_NAME];
        System.assertEquals(baseProfile.Id, user1.ProfileId);
        System.assertEquals(false, user1.UserPermissionsMarketingUser);
    }
}
