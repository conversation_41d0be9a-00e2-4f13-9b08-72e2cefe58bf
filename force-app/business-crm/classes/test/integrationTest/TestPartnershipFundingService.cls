@IsTest
private class TestPartnershipFundingService {
    private static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    private static Opportunity_Partnership__c partnership;

    private static void preparePartnership() {
        testFixture.prepareAccount(true);
        Opportunity opportunity = testFixture.prepareOpportunity(true);

        partnership = new Opportunity_Partnership__c(
            Partnership_Name__c = 'AWS',
            Engagement_Type__c = 'Sell With',
            Support_Type__c = 'Funding',
            Status__c = 'To Be Submitted',
            Opportunity_Id__c = opportunity.Id
        );
        insert partnership;
    }

    private static Partnership_Funding__c prepareFunding(String phase) {
        Partnership_Funding__c funding = new Partnership_Funding__c(
            Partnership_Id__c = partnership.Id,
            Partner_Funding_Program__c = 'AWS - Proof of Concept - POC',
            Funding_Amount__c = 1000,
            Funding_Start_Date__c = Date.today(),
            Phase__c = phase,
            Name__c = 'testFunding'
        );
        return funding;
    }

    @IsTest
    static void shouldSetPartnershipSuccessWhenFundingPhaseUpgradeGreater5() {
        TriggerToggle.turnOff();
        preparePartnership();
        String phase0 = '0 - Possible opportunities that can be explored Funding (0%)';
        Partnership_Funding__c funding = prepareFunding(phase0);
        insert funding;

        TriggerToggle.turnOn();

        Test.startTest();
        funding.Phase__c = '5 - Funding Request Approved - Project starts (100%)';
        funding.Funding_Approval_Date__c = Date.today();
        funding.Funding_Expiry_Date__c = Date.today();
        funding.Funding_Request_ID__c = '1111';
        funding.Invoice_Number__c = '1111';
        funding.PO_Number__c = '1111';
        update funding;
        Test.stopTest();

        Opportunity_Partnership__c partnership = [SELECT Id, Status__c FROM Opportunity_Partnership__c WHERE Id = :partnership.Id];
        System.assertEquals('Success', partnership.Status__c);
    }

    @IsTest
    static void shouldSetPartnershipSuccessWhenInsertFundingPhase5() {
        TriggerToggle.turnOff();
        preparePartnership();
        String phase5 = '5 - Funding Request Approved - Project starts (100%)';
        Partnership_Funding__c funding = prepareFunding(phase5);
        funding.Funding_Approval_Date__c = Date.today();
        funding.Funding_Expiry_Date__c = Date.today();
        funding.Funding_Request_ID__c = '1111';
        funding.Invoice_Number__c = '1111';
        funding.PO_Number__c = '1111';

        TriggerToggle.turnOn();

        Test.startTest();
        insert funding;
        Test.stopTest();

        Opportunity_Partnership__c partnership = [SELECT Id, Status__c FROM Opportunity_Partnership__c WHERE Id = :partnership.Id];
        System.assertEquals('Success', partnership.Status__c);
    }

    @IsTest
    static void shouldNotResetPartnershipStatusWhenFundingDegrade() {
        TriggerToggle.turnOff();
        preparePartnership();
        String phase7 = '7 - Claim submission for Approval';
        Partnership_Funding__c funding = prepareFunding(phase7);
        funding.Funding_Approval_Date__c = Date.today();
        funding.Funding_Expiry_Date__c = Date.today();
        funding.Funding_Request_ID__c = '1111';
        funding.Invoice_Number__c = '1111';
        funding.PO_Number__c = '1111';
        partnership.Status__c = 'Success';

        update partnership;
        insert funding;

        TriggerToggle.turnOn();

        Test.startTest();
        funding.phase__c = 'Lost Funding Opportunity';
        update funding;
        Test.stopTest();

        Opportunity_Partnership__c partnership = [SELECT Id, Status__c FROM Opportunity_Partnership__c WHERE Id = :partnership.Id];
        System.assertEquals('Success', partnership.Status__c);
    }
}
