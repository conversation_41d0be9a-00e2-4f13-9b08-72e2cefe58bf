@IsTest
public with sharing class TestsZoomInfoService {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    class MockHttpResponse implements HttpCalloutMock {
        public MockHttpResponse() {
        }
        public HttpResponse respond(HttpRequest request) {
            HttpResponse resp = new HttpResponse();
            resp.setStatusCode(200);
            resp.setHeader('Content-type', 'application/json');
            if (request.getEndpoint() == 'https://api.zoominfo.com/authenticate') {
                resp.setBody('{"success": true, "jwt": "abcd"}');
                return resp;
            }
            resp.setBody(
                    '{"success": true, "data": {\n' +
                            '  "result": [\n' +
                            '    {\n' +
                            '      "matchStatus": "FULL_MATCH",\n' +
                            '      "input": {\n' +
                            '        "lastName": "lastName1",\n' +
                            '        "firstName": "firstName1",\n' +
                            '        "emailAddress": "<EMAIL>",\n' +
                            '        "companyName": "companyName1",\n' +
                            '        "phone": "12345",\n' +
                            '        "jobTitle": "jobTitle1"\n' +
                            '      },\n' +
                            '      "data": [\n' +
                            '        {\n' +
                            '          "phone": "(*************",\n' +
                            '          "personHasMoved": "No",\n' +
                            '          "jobTitle": "Chief Technology Officer",\n' +
                            '          "jobFunction": [\n' +
                            '            {\n' +
                            '              "name": "Engineering & Technical Executive",\n' +
                            '              "department": "C-Suite"\n' +
                            '            },\n' +
                            '            {\n' +
                            '              "name": "Engineering & Technical",\n' +
                            '              "department": "Engineering & Technical"\n' +
                            '            },\n' +
                            '            {\n' +
                            '              "name": "Technology Operations",\n' +
                            '              "department": "Engineering & Technical"\n' +
                            '            }\n' +
                            '          ],\n' +
                            '          "firstName": "Blythe",\n' +
                            '          "externalUrls": [\n' +
                            '            {\n' +
                            '              "url": "https://www.linkedin.com/in/blythewalker",\n' +
                            '              "type": "linkedin.com"\n' +
                            '            }\n' +
                            '          ],\n' +
                            '          "email": "<EMAIL>",\n' +
                            '          "directPhoneDoNotCall": false,\n' +
                            '          "lastUpdatedDate": "07/11/2024 10:29 AM",\n' +
                            '          "country": "Bermuda",\n' +
                            '          "company": {\n' +
                            '            "website": "www.renre.com",\n' +
                            '            "revenueNumeric": 10451261000,\n' +
                            '            "primaryIndustry": [\n' +
                            '              "Insurance"\n' +
                            '            ],\n' +
                            '            "name": "RenaissanceRe Holdings",\n' +
                            '            "industries": [\n' +
                            '              "Insurance"\n' +
                            '            ],\n' +
                            '            "employeeCount": 925\n' +
                            '          },\n' +
                            '          "city": "Hamilton"\n' +
                            '        }\n' +
                            '      ]\n' +
                            '    },\n' +
                            '    {\n' +
                            '      "matchStatus": "COMPANY_ONLY_MATCH",\n' +
                            '      "input": {\n' +
                            '        "lastName": "lastName2",\n' +
                            '        "firstName": "firstName2",\n' +
                            '        "emailAddress": "<EMAIL>",\n' +
                            '        "companyName": "companyName2",\n' +
                            '        "phone": "12345",\n' +
                            '        "jobTitle": "jobTitle2"\n' +
                            '      },\n' +
                            '      "data": [\n' +
                            '        {\n' +
                            '          "phone": "",\n' +
                            '          "personHasMoved": "",\n' +
                            '          "jobTitle": "",\n' +
                            '          "jobFunction": [],\n' +
                            '          "firstName": "",\n' +
                            '          "externalUrls": [],\n' +
                            '          "email": "",\n' +
                            '          "directPhoneDoNotCall": false,\n' +
                            '          "country": "",\n' +
                            '          "company": {\n' +
                            '            "website": "www.qmetric.invalid.co.uk",\n' +
                            '            "revenueNumeric": 16249000,\n' +
                            '            "primaryIndustry": [\n' +
                            '              "Insurance"\n' +
                            '            ],\n' +
                            '            "name": "QMetric Group",\n' +
                            '            "industries": [\n' +
                            '              "Insurance"\n' +
                            '            ],\n' +
                            '            "employeeCount": 316\n' +
                            '          },\n' +
                            '          "city": ""\n' +
                            '        }\n' +
                            '      ]\n' +
                            '    }\n' +
                            '  ],\n' +
                            '  "requiredFields": [],\n' +
                            '  "outputFields": [\n' +
                            '    [\n' +
                            '      "firstName",\n' +
                            '      "email",\n' +
                            '      "phone",\n' +
                            '      "directPhoneDoNotCall",\n' +
                            '      "companyPrimaryIndustry",\n' +
                            '      "companyIndustries",\n' +
                            '      "city",\n' +
                            '      "country",\n' +
                            '      "companyName",\n' +
                            '      "companyWebsite",\n' +
                            '      "jobFunction",\n' +
                            '      "externalUrls",\n' +
                            '      "jobTitle",\n' +
                            '      "personHasMoved",\n' +
                            '      "companyRevenueNumeric",\n' +
                            '      "companyEmployeeCount",\n' +
                            '      "id",\n' +
                            '      "companyId"\n' +
                            '    ]\n' +
                            '  ]\n' +
                            '} }'
            );
            System.debug('resp ' + resp.getBody());
            return resp;
        }
    }

    @IsTest
    static void shouldEnrichLeadAndContactsSuccessfully() {
        TriggerToggle.turnOff();
        Lead notEnrichLead1 = testFixture.getDataFactory().createPSLead();
        notEnrichLead1.Email = '<EMAIL>';
        notEnrichLead1.LastName = 'lastName1';
        notEnrichLead1.FirstName = 'firstName1';
        notEnrichLead1.Phone = '12345';
        notEnrichLead1.Company = 'companyName1';
        notEnrichLead1.Title = 'jobTitle1';
        notEnrichLead1.Is_ZoomInfo_Enrich__c = true;

        Lead notEnrichLead2 = testFixture.getDataFactory().createPSLead();
        notEnrichLead2.Is_ZoomInfo_Enrich__c = false;

        insert new List<Lead> { notEnrichLead1, notEnrichLead2 };

        Account testAccount = testFixture.prepareAccount(false);
        testAccount.Name = 'CompanyName2';
        insert testAccount;
        Contact testContact = testFixture.getDataFactory().createPSContact(testAccount);
        testContact.Email = '<EMAIL>';
        testContact.LastName = 'lastName2';
        testContact.FirstName = 'firstName2';
        testContact.Phone = '12345';
        testContact.Account = testAccount;
        testContact.Title = 'jobTitle2';
        testContact.Is_ZoomInfo_Enrich__c = false;
        insert testContact;

        Campaign campaign = testFixture.prepareCampaign(true);

        CampaignMember campaignMemberLead = new CampaignMember(CampaignId = campaign.Id, Status = 'Test Responded', LeadId = notEnrichLead1.Id);
        CampaignMember campaignMemberContact = new CampaignMember(CampaignId = campaign.Id, Status = 'Test Responded', ContactId = testContact.Id);
        List<CampaignMember> campaignMemberList = new List<CampaignMember>{ campaignMemberLead, campaignMemberContact };
        insert campaignMemberList;
        TriggerToggle.turnOn();
        List<Id> campaignMemberId = new List<Id> { campaignMemberLead.Id, campaignMemberContact.Id};
        Test.setMock(HttpCalloutMock.class, new MockHttpResponse());
        Test.startTest();
        ZoomInfoService zoomInfoService = new ZoomInfoService();
        zoomInfoService.enrichCampaignMembers(campaignMemberId, campaign.Name, campaign.Id, null);
        Test.stopTest();

        List<Lead> updatedLeads = [SELECT Id, Is_ZoomInfo_Enrich__c FROM Lead WHERE Id = :notEnrichLead1.Id OR Id = :notEnrichLead2.Id];
        Contact updatedContact = [SELECT Id, Is_ZoomInfo_Enrich__c FROM Contact WHERE Id = :testContact.Id];

        System.assertEquals(true, updatedLeads.get(0).Is_ZoomInfo_Enrich__c);
        System.assertEquals(false, updatedLeads.get(1).Is_ZoomInfo_Enrich__c);
        System.assertEquals(true, updatedContact.Is_ZoomInfo_Enrich__c);
    }
}