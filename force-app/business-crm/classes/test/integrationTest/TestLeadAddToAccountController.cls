@isTest
private class TestLeadAddToAccountController {
    private static IMKTContactSelector contactSelector = new MKTContactSelector();
    private static IAccountSelector accountSelector = new AccountSelector();
    private static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    private static User salesUser = testFixture.prepareUser('CRM - Sales', 'salesUser');

    static {
        List<Holistic_Engagement_Model__c> model = new List<Holistic_Engagement_Model__c>{ new Holistic_Engagement_Model__c(Channel_Type__c = 'Content - Blog', Status__c = 'Test Responded') };
        insert model;
    }

    @IsTest
    static void shouldConvertLeadWithExistingAccount() {
        TriggerToggle.turnOff();
        Id recordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId();
        Lead leadToConvert = TestFactory.createLead(1, recordTypeId, new Map<String, Object>());
        insert leadToConvert;
        String leadId = (String) leadToConvert.Id;

        Account existingAccount = TestFactory.buildTestAccount(1024);
        insert existingAccount;

        String existingAccountId = (String) existingAccount.Id;

        Campaign campaign = testFixture.prepareCampaign(true);
        insert TestFactory.createRelatedCampaignMemberStatuses(new List<Campaign>{ campaign });

        TriggerToggle.turnOn();
        CampaignMember campaignMember = new CampaignMember(CampaignId = campaign.Id, Status = 'Test Responded', LeadId = leadId);
        insert campaignMember;

        LeadStatus defaultStatus = [SELECT MasterLabel FROM LeadStatus WHERE isDefault = TRUE LIMIT 1];
        String status = defaultStatus.MasterLabel;

        System.Test.startTest();
        Map<Id, Id> leadIdToContactIdMap;
        System.runAs(salesUser) {
            leadIdToContactIdMap = LeadAddToAccountController.addToExistingAccount(new Map<Id, String>{ leadId => status }, existingAccountId, false);
        }
        System.Test.stopTest();

        Id contactId = leadIdToContactIdMap.get(leadId);
        Contact contactConverted = contactSelector.getContactListByContactId(leadIdToContactIdMap.values())[0];

        System.assertEquals(existingAccountId, (String) contactConverted.AccountId);
        System.assertEquals(status, contactConverted.Status__c);

        List<Marketing_Activated_Person__c> marketingActivatedPersonList = [
            SELECT Id, Contact__c, Contact__r.Status__c, Contact__r.leadConvertFlag__c, Lead__r.Status, Campaign_Member_Id__c, Campaign__c
            FROM Marketing_Activated_Person__c
        ];
        System.assertEquals(1, marketingActivatedPersonList.size());
        System.assertEquals(leadIdToContactIdMap.values().get(0), (String) marketingActivatedPersonList[0].Contact__c);
        System.assertEquals(leadId, (String) marketingActivatedPersonList[0].Lead__c);
        System.assertEquals('Known', marketingActivatedPersonList[0].Contact__r.Status__c);
        System.assertEquals(false, marketingActivatedPersonList[0].Contact__r.leadConvertFlag__c);
        System.assertEquals('Converting', marketingActivatedPersonList[0].Lead__r.Status);

        List<CampaignMember> convertCampaignMembers = [SELECT Id, Company_Account__c FROM CampaignMember WHERE CampaignId = :campaign.Id AND ContactId = :contactId];
        System.assertEquals(1, convertCampaignMembers.size());
        System.assertEquals('Test Account1024', convertCampaignMembers.get(0).Company_Account__c);
    }

    @IsTest
    static void shouldConvertLeadWithNewAccount() {
        TriggerToggle.turnOff();
        Id recordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId();
        Lead leadToConvert = TestFactory.createLead(1, recordTypeId, new Map<String, Object>());
        insert leadToConvert;
        String leadId = (String) leadToConvert.Id;

        Campaign campaign = testFixture.prepareCampaign(true);
        insert TestFactory.createRelatedCampaignMemberStatuses(new List<Campaign>{ campaign });

        TriggerToggle.turnOn();
        CampaignMember campaignMember = new CampaignMember(CampaignId = campaign.Id, Status = 'Test Responded', LeadId = leadId);
        insert campaignMember;

        String newAccountName = 'new account name';
        String industry = 'Education';
        String salesMarketUnit = 'Australia Market';
        String market = 'China - Automotive Market';

        LeadStatus defaultStatus = [SELECT MasterLabel FROM LeadStatus WHERE isDefault = TRUE LIMIT 1];
        String status = defaultStatus.MasterLabel;

        System.Test.startTest();
        String contactId = '';
        System.runAs(salesUser) {
            contactId = LeadAddToAccountController.addToNewAccount(leadId, newAccountName, industry, salesMarketUnit, status, market);
        }
        System.Test.stopTest();

        Contact contactConverted = contactSelector.getContactListByContactId(new List<Id>{ contactId })[0];
        Id accountCreatedId = contactConverted.AccountId;
        Account accountCreated = accountSelector.getAccountsNameIndustrySalesMarketUnitByIds(new List<Id>{ accountCreatedId }).get(accountCreatedId);

        System.assertEquals(newAccountName, accountCreated.Name);
        System.assertEquals(industry, accountCreated.Industry);
        System.assertEquals(salesMarketUnit, (String) accountCreated.Sales_Market_Unit_Picklist__c);
        System.assertEquals(market, (String) accountCreated.Market_Picklist__c);
        System.assertEquals(status, contactConverted.Status__c);

        List<Marketing_Activated_Person__c> marketingActivatedPersonList = [SELECT Id, Contact__c, Lead__c, Campaign_Member_Id__c, Campaign__c FROM Marketing_Activated_Person__c];
        System.assertEquals(1, marketingActivatedPersonList.size());
        System.assertEquals(contactId, (String) marketingActivatedPersonList[0].Contact__c);
        System.assertEquals(leadId, (String) marketingActivatedPersonList[0].Lead__c);

        List<CampaignMember> convertCampaignMembers = [SELECT Id, Company_Account__c FROM CampaignMember WHERE CampaignId = :campaign.Id AND ContactId = :contactId];
        System.assertEquals(1, convertCampaignMembers.size());
        System.assertEquals('new account name', convertCampaignMembers.get(0).Company_Account__c);
    }

    @IsTest
    static void shouldAddToAccountWithFieldsUpdateAndSetReminder() {
        TriggerToggle.turnOff();
        Id recordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId();
        Lead leadToConvert = TestFactory.createLead(1, recordTypeId, new Map<String, Object>());
        insert leadToConvert;

        Account existingAccount = TestFactory.buildTestAccount(1024);
        insert existingAccount;

        TriggerToggle.turnOn();

        Task task = new Task(ActivityDate = Date.today(), OwnerId = salesUser.Id);
        leadToConvert.SAL_Substage__c = CRMConstants.SUBSTAGE_QUALIFIED_WITHOUT_OPPORTUNITY;
        leadToConvert.Disqualified_Reason__c = 'TEST REASON';

        System.Test.startTest();
        String contactId = '';
        System.runAs(salesUser) {
            contactId = LeadAddToAccountController.addToAccountWithFieldsUpdateAndSetReminder(leadToConvert, (String) existingAccount.Id, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY, task);
        }
        System.Test.stopTest();

        Contact contactConverted = [SELECT Id, AccountId, SAL_Substage__c, Status__c, Disqualified_Reason__c FROM Contact WHERE Id = :contactId LIMIT 1];
        Task newTask = [SELECT Id, Subject, Status FROM Task WHERE WhoId = :contactConverted.Id LIMIT 1];

        System.assertEquals(existingAccount.Id, contactConverted.AccountId);
        System.assertEquals(CRMConstants.SUBSTAGE_QUALIFIED_WITHOUT_OPPORTUNITY, contactConverted.SAL_Substage__c);
        System.assertEquals('TEST REASON', contactConverted.Disqualified_Reason__c);
        System.assertEquals(CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY, contactConverted.Status__c);
        System.assertEquals('Follow-up reminder', newTask.Subject);
        System.assertEquals('Not Started', newTask.Status);
    }

    @IsTest
    static void shouldMismatchAccount() {
        TriggerToggle.turnOff();
        Lead lead = testFixture.prepareLead(true);
        TriggerToggle.turnOn();

        System.Test.startTest();
        LeadAddToAccountController.mismatchAccount(new List<Id>{ lead.Id });
        System.Test.stopTest();

        Lead leadAfter = [SELECT Id, Is_Mismatched__c, Mismatched_by__c FROM Lead WHERE Id = :lead.Id LIMIT 1];
        Assert.areEqual(true, leadAfter.Is_Mismatched__c);
        Assert.areEqual(UserInfo.getUserId(), leadAfter.Mismatched_by__c);
    }

    @IsTest
    static void shouldSyncMismatchedByAndQuickConvertByToPerson() {
        Id currentUserId = UserInfo.getUserId();
        TriggerToggle.turnOff();
        User marKetoUser = testFixture.prepareUser('Marketo Sync', 'testMarketo');
        Account account1 = testFixture.prepareAccount(true);

        TriggerToggle.turnOn();
        Datetime now = Datetime.now();
        Lead lead1 = testFixture.prepareLead(false);
        lead1.Status = 'Engaged';
        lead1.LeanData__Reporting_Matched_Account__c = account1.Id;
        lead1.LeanData__Reporting_Timestamp__c = now;

        System.runAs(marKetoUser) {
            insert lead1;
        }

        LeadAddToAccountController.mismatchAccount(new List<Id>{ lead1.Id });

        Person__c personResult1 = [SELECT Id, Mismatched_by__c, Lead__r.Is_Mismatched__c, Lead__r.Mismatched_by__c, Account_Matched_Datetime__c, Matched_Account__c FROM Person__c WHERE Lead__c = :lead1.Id LIMIT 1];
        Assert.areEqual(true, personResult1.Lead__r.Is_Mismatched__c);
        Assert.areEqual(currentUserId, personResult1.Mismatched_by__c);
        Assert.areEqual(currentUserId, personResult1.Lead__r.Mismatched_by__c);
        Assert.areEqual(now, personResult1.Account_Matched_Datetime__c);
        Assert.areEqual(account1.Id, personResult1.Matched_Account__c);

        System.Test.startTest();
        LeadAddToAccountController.addToExistingAccount(new Map<Id, String>{ lead1.Id => 'Engaged' }, account1.Id, true);
        System.Test.stopTest();

        Person__c personResult2 = [
            SELECT Id, Mismatched_by__c, Quick_Converted_by__c, Lead__r.Mismatched_by__c, Contact__c, Contact__r.Quick_Converted_by__c, Contact__r.Account_Matched_Datetime__c, Contact__r.Matched_Account__c, Account_Matched_Datetime__c, Matched_Account__c
            FROM Person__c
            WHERE Lead__c = :lead1.Id
            LIMIT 1
        ];
        Assert.areEqual(currentUserId, personResult2.Mismatched_by__c);
        Assert.areEqual(currentUserId, personResult2.Lead__r.Mismatched_by__c);
        Assert.isNotNull(personResult2.Contact__c);
        Assert.areEqual(currentUserId, personResult2.Quick_Converted_by__c);
        Assert.areEqual(currentUserId, personResult2.Contact__r.Quick_Converted_by__c);
        Assert.areEqual(now, personResult2.Contact__r.Account_Matched_Datetime__c);
        Assert.areEqual(account1.Id, personResult2.Contact__r.Matched_Account__c);
        Assert.areEqual(now, personResult2.Account_Matched_Datetime__c);
        Assert.areEqual(account1.Id, personResult2.Matched_Account__c);
    }
}
