@IsTest
public with sharing class TestUserHierarchyService {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    static User_Reporting_Hierarchy__c buildHierarchy(String a, String p, String c, Integer d) {
        User_Reporting_Hierarchy__c h1 = new User_Reporting_Hierarchy__c();
        h1.Depth__c = d;
        h1.Parent__c = p;
        h1.Child__c = c;
        h1.Ancestor__c = a;
        return h1;
    }

    @IsTest
    static void shouldGenerateUserHierarchyDTOSuccessfully() {
        Id resourceType = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        TriggerToggle.turnOff();
        Account acc = testFixture.prepareAccount(true);
        Org_Unit__c org1 = new Org_Unit__c(Name = '1111', Name__c = 'Global Markets');
        insert org1;

        User u0 = testFixture.prepareUser('CRM - Sales', 'u0');
        User u1 = testFixture.prepareUser('CRM - Sales', 'u1');
        User u2 = testFixture.prepareUser('CRM - Sales', 'u2');
        User u3 = testFixture.prepareUser('CRM - Sales', 'u3');
        u0.EmployeeNumber = CRMConstants.ROOT_SALES_EMPLOYEE_ID;
        u1.EmployeeNumber = '00001';
        u2.EmployeeNumber = '00002';
        u3.EmployeeNumber = '00003';
        update new List<User>{ u0, u1, u2, u3 };

        Contact contact0 = new Contact(LastName = 'c0', RecordTypeId = resourceType, Employee_ID__c = u0.EmployeeNumber, pse__Salesforce_User__c = u0.Id, POH__c = org1.Id);
        Contact contact1 = new Contact(LastName = 'c1', RecordTypeId = resourceType, Employee_ID__c = u1.EmployeeNumber, pse__Salesforce_User__c = u1.Id, POH__c = org1.Id);
        Contact contact2 = new Contact(LastName = 'c2', RecordTypeId = resourceType, Employee_ID__c = u2.EmployeeNumber, pse__Salesforce_User__c = u2.Id, POH__c = org1.Id);
        Contact contact3 = new Contact(LastName = 'c3', RecordTypeId = resourceType, Employee_ID__c = u3.EmployeeNumber, pse__Salesforce_User__c = u3.Id, POH__c = org1.Id);
        insert new List<Contact>{ contact0, contact1, contact2, contact3 };

        insert new List<User_Reporting_Hierarchy__c>{
            buildHierarchy(CRMConstants.ROOT_SALES_EMPLOYEE_ID, CRMConstants.ROOT_SALES_EMPLOYEE_ID, '00001', 1),
            buildHierarchy(CRMConstants.ROOT_SALES_EMPLOYEE_ID, '00001', '00002', 2),
            buildHierarchy(CRMConstants.ROOT_SALES_EMPLOYEE_ID, '00002', '00003', 3),
            buildHierarchy('00001', '00001', '00002', 1),
            buildHierarchy('00001', '00002', '00003', 2)
        };

        Opportunity opp = TestFactory.buildTestPSOpp(0, acc, Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Professional Services Opportunity').getRecordTypeId());
        opp.OwnerId = u1.Id;
        opp.CloseDate = Date.today().addMonths(6);
        insert opp;
        Opportunity opp1 = TestFactory.buildTestPSOpp(1, acc, Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Internal Opportunity').getRecordTypeId());
        opp1.OwnerId = u2.Id;
        opp1.CloseDate = Date.today().addMonths(6);
        insert opp1;
        TriggerToggle.turnOn();

        Test.startTest();
        List<UserHierarchyWithPOHDTO> results = MKTContactController.getPOHAndHierarchiesByUserId(u1.Id);
        Test.stopTest();

        System.debug(JSON.serialize(results));
        Assert.areEqual(results.size(), 3);
        Assert.areEqual(results.get(0).hasOpportunity, true);
        Assert.areEqual(results.get(1).hasOpportunity, false);
        Assert.areEqual(results.get(2).hasOpportunity, false);
        Assert.areEqual(results.get(0).parentId, null);
        Assert.areEqual(results.get(1).parentId, '00001');
        Assert.areEqual(results.get(2).parentId, '00002');
        Assert.areEqual(results.get(0).employeeId, '00001');
        Assert.areEqual(results.get(1).employeeId, '00002');
        Assert.areEqual(results.get(2).employeeId, '00003');
        Assert.areEqual(results.get(0).childId, '00001');
        Assert.areEqual(results.get(1).childId, '00002');
        Assert.areEqual(results.get(2).childId, '00003');
        Assert.areEqual(results.get(0).depth, 0);
        Assert.areEqual(results.get(1).depth, 2);
        Assert.areEqual(results.get(2).depth, 3);
        Assert.areEqual(results.get(0).pohName, 'Global Markets');
        Assert.areEqual(results.get(1).pohName, 'Global Markets');
        Assert.areEqual(results.get(2).pohName, 'Global Markets');
    }

    @IsTest
    static void shouldReturnAllHierarchyData() {
        Id resourceType = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        TriggerToggle.turnOff();
        Org_Unit__c org1 = new Org_Unit__c(Name = '1111', Name__c = 'Global Markets');
        insert org1;

        User u0 = testFixture.prepareUser('CRM - Sales', 'u0');
        User u1 = testFixture.prepareUser('CRM - Sales', 'u1');
        User u2 = testFixture.prepareUser('CRM - Sales', 'u2');
        u0.EmployeeNumber = '00001';
        u1.EmployeeNumber = '00002';
        u2.EmployeeNumber = '00011';
        update new List<User>{ u0, u1, u2 };

        Contact contact0 = new Contact(LastName = 'c0', RecordTypeId = resourceType, Employee_ID__c = u0.EmployeeNumber, pse__Salesforce_User__c = u0.Id, POH__c = org1.Id);
        Contact contact1 = new Contact(LastName = 'c1', RecordTypeId = resourceType, Employee_ID__c = u1.EmployeeNumber, pse__Salesforce_User__c = u1.Id, POH__c = org1.Id);
        Contact contact2 = new Contact(LastName = 'c2', RecordTypeId = resourceType, Employee_ID__c = u2.EmployeeNumber, pse__Salesforce_User__c = u2.Id, POH__c = org1.Id);
        insert new List<Contact>{ contact0, contact1, contact2 };

        insert new List<User_Reporting_Hierarchy__c>{ buildHierarchy(null, null, '00001', 0), buildHierarchy('00001', '00001', '00002', 1), buildHierarchy(null, null, '00011', 0) };

        TriggerToggle.turnOn();

        Test.startTest();
        List<UserHierarchyWithPOHDTO> results = MKTContactController.getAllPOHAndHierarchies();
        Test.stopTest();

        System.debug(JSON.serialize(results));
        Assert.areEqual(results.size(), 3);
    }

    @IsTest
    static void shouldReturnUserHierarchyTree() {
        Id resourceType = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        TriggerToggle.turnOff();
        Org_Unit__c org1 = new Org_Unit__c(Name = '1111', Name__c = 'Global Markets');
        insert org1;

        User u0 = testFixture.prepareUser('CRM - Sales Manager', 'u0');
        User u1 = testFixture.prepareUser('CRM - Sales Manager', 'u1');
        User u2 = testFixture.prepareUser('CRM - Sales', 'u2');
        User u3 = testFixture.prepareUser('CRM - Sales', 'u3');
        u0.EmployeeNumber = '00001';
        u1.EmployeeNumber = '00002';
        u2.EmployeeNumber = '00021';
        u3.EmployeeNumber = '00022';
        update new List<User>{ u0, u1, u2, u3 };

        Contact contact0 = new Contact(LastName = 'c0', RecordTypeId = resourceType, Employee_ID__c = u0.EmployeeNumber, pse__Salesforce_User__c = u0.Id, POH__c = org1.Id);
        Contact contact1 = new Contact(LastName = 'c1', RecordTypeId = resourceType, Employee_ID__c = u1.EmployeeNumber, pse__Salesforce_User__c = u1.Id, POH__c = org1.Id);
        Contact contact2 = new Contact(LastName = 'c2', RecordTypeId = resourceType, Employee_ID__c = u2.EmployeeNumber, pse__Salesforce_User__c = u2.Id, POH__c = org1.Id);
        insert new List<Contact>{ contact0, contact1, contact2 };

        insert new List<User_Reporting_Hierarchy__c>{
                buildHierarchy('00001', '00001', '00002', 1),
                buildHierarchy('00001', '00002', '00021', 2),
                buildHierarchy('00001', '00002', '00022', 2)
        };

        Opportunity opp = testFixture.prepareOpportunity(false);
        opp.RecordType = [SELECT Id FROM RecordType WHERE Name = :Constants.OPPO_PS_RECORD_TYPE_NAME LIMIT 1];
        opp.Owner = u3;
        opp.OwnerId = u3.Id;
        insert opp;
        Contact contact = testFixture.getContact();
        contact.Employee_ID__c = u3.EmployeeNumber;
        update contact;

        TriggerToggle.turnOn();

        Test.startTest();
        UserHierarchyDTO result = (new UserHierarchyService()).getUserHierarchy(u0.Id, u0.EmployeeNumber);
        Test.stopTest();

        Assert.areEqual(result.depth, 0);
        Assert.areEqual(result.employeeId, '00001');
        Assert.areEqual(result.children.size(), 1);
        Assert.areEqual(result.children.get(0).depth, 1);
        Assert.areEqual(result.children.get(0).employeeId, '00002');
        Assert.areEqual(result.children.get(0).children.size(), 1);
        Assert.areEqual(result.children.get(0).children.get(0).depth, 2);
        Assert.areEqual(result.children.get(0).children.get(0).employeeId, '00022');
        Assert.areEqual(result.children.get(0).children.get(0).hasOpportunity, true);
        Assert.areEqual(result.children.get(0).children.get(0).children.size(), 0);
    }
}
