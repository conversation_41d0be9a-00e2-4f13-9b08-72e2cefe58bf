@isTest
private class TestCalculateAttributionValueBatchJob {
    private final static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    private final static TWPSA_TestDataFactory dataFactory = new TWPSA_TestDataFactory();
    private final static Datetime DATETIME_NOW = Datetime.now();

    @isTest
    static void shouldRecalculateAllInfluenceAttributionDetail() {
        TriggerToggle.turnOff();

        Account acc = testFixture.prepareAccount(true);
        Campaign campaign = testFixture.prepareCampaign(true);

        Contact con1 = testFixture.preparePSContact(false, acc);
        con1.Email = '<EMAIL>';
        Contact con2 = testFixture.preparePSContact(false, acc);
        con2.Email = '<EMAIL>';
        insert new List<Contact>{con1, con2};

        Opportunity opp = dataFactory.createOpportunity(acc, null, null);
        opp.Opportunity_Contract_Value_in_USD__c = ********;
        insert opp;

        Opportunity_Offering_Service__c offeringService1 = new Opportunity_Offering_Service__c(
            Opportunity__c = opp.Id,
            Offering__c = 'AI Readiness',
            Services__c = 'AI Readiness Strategy',
            Proportion__c = 40
        );
        Opportunity_Offering_Service__c offeringService2 = new Opportunity_Offering_Service__c(
            Opportunity__c = opp.Id,
            Offering__c = 'Digital Application Management and Ops (DAMO)',
            Services__c = 'Application Development;Application Support',
            Proportion__c = 60
        );
        insert new List<Opportunity_Offering_Service__c>{offeringService1, offeringService2};

        Campaign_Influence__c campaignInfluence1 = buildCampaignInfluence(opp.Id, con1.Id, campaign.Id);
        Campaign_Influence__c campaignInfluence2 = buildCampaignInfluence(opp.Id, con2.Id, campaign.Id);
        insert new List<Campaign_Influence__c>{campaignInfluence1, campaignInfluence2};

        Test.startTest();
        Database.executeBatch(new CalculateAttributionValueBatchJob(null, null));
        Test.stopTest();

        List<Campaign_Influence_Attribution_Detail__c> attributionDetails = [SELECT Id, Campaign_Influence__c, Opportunity_Offering__c, Proportion__c, Allocated_OCV_in_USD__c, Attribution_Value_in_USD__c FROM Campaign_Influence_Attribution_Detail__c ORDER BY Campaign_Influence__r.Contact__r.Email ASC, Opportunity_Offering__c ASC];
        Assert.areEqual(4, attributionDetails.size());
        Assert.areEqual(campaignInfluence1.Id, attributionDetails.get(0).Campaign_Influence__c);
        Assert.areEqual('AI Readiness', attributionDetails.get(0).Opportunity_Offering__c);
        Assert.areEqual(40, attributionDetails.get(0).Proportion__c);
        Assert.areEqual(4938271.2, attributionDetails.get(0).Allocated_OCV_in_USD__c);
        Assert.areEqual(2469135.6, attributionDetails.get(0).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence1.Id, attributionDetails.get(1).Campaign_Influence__c);
        Assert.areEqual('Digital Application Management and Ops (DAMO)', attributionDetails.get(1).Opportunity_Offering__c);
        Assert.areEqual(60, attributionDetails.get(1).Proportion__c);
        Assert.areEqual(7407406.8, attributionDetails.get(1).Allocated_OCV_in_USD__c);
        Assert.areEqual(3703703.4, attributionDetails.get(1).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence2.Id, attributionDetails.get(2).Campaign_Influence__c);
        Assert.areEqual('AI Readiness', attributionDetails.get(2).Opportunity_Offering__c);
        Assert.areEqual(40, attributionDetails.get(2).Proportion__c);
        Assert.areEqual(4938271.2, attributionDetails.get(2).Allocated_OCV_in_USD__c);
        Assert.areEqual(2469135.6, attributionDetails.get(2).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence2.Id, attributionDetails.get(3).Campaign_Influence__c);
        Assert.areEqual('Digital Application Management and Ops (DAMO)', attributionDetails.get(3).Opportunity_Offering__c);
        Assert.areEqual(60, attributionDetails.get(3).Proportion__c);
        Assert.areEqual(7407406.8, attributionDetails.get(3).Allocated_OCV_in_USD__c);
        Assert.areEqual(3703703.4, attributionDetails.get(3).Attribution_Value_in_USD__c);
    }

    @isTest
    static void shouldRecalculateInfluenceAttributionDetailWithoutOffering() {
        TriggerToggle.turnOff();

        Account acc = testFixture.prepareAccount(true);
        Campaign campaign = testFixture.prepareCampaign(true);

        Contact con1 = testFixture.preparePSContact(false, acc);
        con1.Email = '<EMAIL>';
        Contact con2 = testFixture.preparePSContact(false, acc);
        con2.Email = '<EMAIL>';
        insert new List<Contact>{con1, con2};

        Opportunity opp = dataFactory.createOpportunity(acc, null, null);
        opp.Opportunity_Contract_Value_in_USD__c = ********;
        insert opp;

        Campaign_Influence__c campaignInfluence1MA = buildCampaignInfluence(opp.Id, con1.Id, campaign.Id);
        Campaign_Influence__c campaignInfluence1MI = buildCampaignInfluence(opp.Id, con1.Id, campaign.Id);
        campaignInfluence1MI.Marketing_Pipeline__c = 'Marketing Influenced';
        Campaign_Influence__c campaignInfluence2 = buildCampaignInfluence(opp.Id, con2.Id, campaign.Id);
        insert new List<Campaign_Influence__c>{campaignInfluence1MA, campaignInfluence1MI, campaignInfluence2};

        Test.startTest();
        Database.executeBatch(new CalculateAttributionValueBatchJob(null, null));
        Test.stopTest();

        List<Campaign_Influence_Attribution_Detail__c> attributionDetails = [SELECT Id, Campaign_Influence__c, Opportunity_Offering__c, Proportion__c, Allocated_OCV_in_USD__c, Attribution_Value_in_USD__c FROM Campaign_Influence_Attribution_Detail__c ORDER BY Campaign_Influence__r.Contact__r.Email ASC, Campaign_Influence__r.Marketing_Pipeline__c ASC];
        Assert.areEqual(3, attributionDetails.size());
        Assert.areEqual(campaignInfluence1MA.Id, attributionDetails.get(0).Campaign_Influence__c);
        Assert.areEqual(null, attributionDetails.get(0).Opportunity_Offering__c);
        Assert.areEqual(null, attributionDetails.get(0).Proportion__c);
        Assert.areEqual(null, attributionDetails.get(0).Allocated_OCV_in_USD__c);
        Assert.areEqual(6172839, attributionDetails.get(0).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence1MI.Id, attributionDetails.get(1).Campaign_Influence__c);
        Assert.areEqual(null, attributionDetails.get(1).Opportunity_Offering__c);
        Assert.areEqual(null, attributionDetails.get(1).Proportion__c);
        Assert.areEqual(null, attributionDetails.get(1).Allocated_OCV_in_USD__c);
        Assert.areEqual(********, attributionDetails.get(1).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence2.Id, attributionDetails.get(2).Campaign_Influence__c);
        Assert.areEqual(null, attributionDetails.get(2).Opportunity_Offering__c);
        Assert.areEqual(null, attributionDetails.get(2).Proportion__c);
        Assert.areEqual(null, attributionDetails.get(2).Allocated_OCV_in_USD__c);
        Assert.areEqual(6172839, attributionDetails.get(2).Attribution_Value_in_USD__c);
    }

    @isTest
    static void shouldRecalculateInfluenceAttributionDetailWhenInfluenceDeleted() {
        TriggerToggle.turnOff();

        Account acc = testFixture.prepareAccount(true);
        Campaign campaign = testFixture.prepareCampaign(true);

        Contact con1 = testFixture.preparePSContact(false, acc);
        con1.Email = '<EMAIL>';
        Contact con2 = testFixture.preparePSContact(false, acc);
        con2.Email = '<EMAIL>';
        insert new List<Contact>{con1, con2};

        Opportunity opp = dataFactory.createOpportunity(acc, null, null);
        opp.Opportunity_Contract_Value_in_USD__c = ********;
        insert opp;

        Opportunity_Offering_Service__c offeringService1 = new Opportunity_Offering_Service__c(
                Opportunity__c = opp.Id,
                Offering__c = 'AI Readiness',
                Services__c = 'AI Readiness Strategy',
                Proportion__c = 40
        );
        Opportunity_Offering_Service__c offeringService2 = new Opportunity_Offering_Service__c(
                Opportunity__c = opp.Id,
                Offering__c = 'Digital Application Management and Ops (DAMO)',
                Services__c = 'Application Development;Application Support',
                Proportion__c = 60
        );
        insert new List<Opportunity_Offering_Service__c>{offeringService1, offeringService2};

        Campaign_Influence__c campaignInfluence1 = buildCampaignInfluence(opp.Id, con1.Id, campaign.Id);
        Campaign_Influence__c campaignInfluence2 = buildCampaignInfluence(opp.Id, con2.Id, campaign.Id);
        insert new List<Campaign_Influence__c>{campaignInfluence1, campaignInfluence2};

        Campaign_Influence_Attribution_Detail__c influence1AttributionValue1 = buildInfluenceAttributionDetail(campaignInfluence1, offeringService1, 4938271.2, 2469135.6);
        Campaign_Influence_Attribution_Detail__c influence1AttributionValue2 = buildInfluenceAttributionDetail(campaignInfluence1, offeringService2, 7407406.8, 3703703.4);
        Campaign_Influence_Attribution_Detail__c influence2AttributionValue1 = buildInfluenceAttributionDetail(campaignInfluence2, offeringService1, 4938271.2, 2469135.6);
        Campaign_Influence_Attribution_Detail__c influence2AttributionValue2 = buildInfluenceAttributionDetail(campaignInfluence2, offeringService2, 7407406.8, 3703703.4);
        insert new List<Campaign_Influence_Attribution_Detail__c>{influence1AttributionValue1, influence1AttributionValue2, influence2AttributionValue1, influence2AttributionValue2};

        delete campaignInfluence1;
        List<Campaign_Influence_Attribution_Detail__c> attributionDetailsAfterDeleteInfluence = [SELECT Id, Campaign_Influence__c, Opportunity_Offering__c, Proportion__c, Allocated_OCV_in_USD__c, Attribution_Value_in_USD__c FROM Campaign_Influence_Attribution_Detail__c ORDER BY Campaign_Influence__r.Contact__r.Email ASC, Opportunity_Offering__c ASC];
        Assert.areEqual(2, attributionDetailsAfterDeleteInfluence.size());
        Assert.areEqual(campaignInfluence2.Id, attributionDetailsAfterDeleteInfluence.get(0).Campaign_Influence__c);
        Assert.areEqual('AI Readiness', attributionDetailsAfterDeleteInfluence.get(0).Opportunity_Offering__c);
        Assert.areEqual(40, attributionDetailsAfterDeleteInfluence.get(0).Proportion__c);
        Assert.areEqual(4938271.2, attributionDetailsAfterDeleteInfluence.get(0).Allocated_OCV_in_USD__c);
        Assert.areEqual(2469135.6, attributionDetailsAfterDeleteInfluence.get(0).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence2.Id, attributionDetailsAfterDeleteInfluence.get(1).Campaign_Influence__c);
        Assert.areEqual('Digital Application Management and Ops (DAMO)', attributionDetailsAfterDeleteInfluence.get(1).Opportunity_Offering__c);
        Assert.areEqual(60, attributionDetailsAfterDeleteInfluence.get(1).Proportion__c);
        Assert.areEqual(7407406.8, attributionDetailsAfterDeleteInfluence.get(1).Allocated_OCV_in_USD__c);
        Assert.areEqual(3703703.4, attributionDetailsAfterDeleteInfluence.get(1).Attribution_Value_in_USD__c);

        Test.startTest();
        Database.executeBatch(new CalculateAttributionValueBatchJob(DATETIME_NOW.addHours(-1), DATETIME_NOW.addHours(1)));
        Test.stopTest();

        List<Campaign_Influence_Attribution_Detail__c> attributionDetails = [SELECT Id, Campaign_Influence__c, Opportunity_Offering__c, Proportion__c, Allocated_OCV_in_USD__c, Attribution_Value_in_USD__c FROM Campaign_Influence_Attribution_Detail__c ORDER BY Campaign_Influence__r.Contact__r.Email ASC, Opportunity_Offering__c ASC];
        Assert.areEqual(2, attributionDetails.size());
        Assert.areEqual(campaignInfluence2.Id, attributionDetails.get(0).Campaign_Influence__c);
        Assert.areEqual('AI Readiness', attributionDetails.get(0).Opportunity_Offering__c);
        Assert.areEqual(40, attributionDetails.get(0).Proportion__c);
        Assert.areEqual(4938271.2, attributionDetails.get(0).Allocated_OCV_in_USD__c);
        Assert.areEqual(4938271.2, attributionDetails.get(0).Attribution_Value_in_USD__c);
        Assert.areEqual(campaignInfluence2.Id, attributionDetails.get(1).Campaign_Influence__c);
        Assert.areEqual('Digital Application Management and Ops (DAMO)', attributionDetails.get(1).Opportunity_Offering__c);
        Assert.areEqual(60, attributionDetails.get(1).Proportion__c);
        Assert.areEqual(7407406.8, attributionDetails.get(1).Allocated_OCV_in_USD__c);
        Assert.areEqual(7407406.8, attributionDetails.get(1).Attribution_Value_in_USD__c);
    }

    private static Campaign_Influence_Attribution_Detail__c buildInfluenceAttributionDetail(Campaign_Influence__c influence, Opportunity_Offering_Service__c offeringService, Decimal allocatedOCV,  Decimal attributionValue) {
        return new Campaign_Influence_Attribution_Detail__c(
            Campaign_Influence__c = influence.Id,
            Proportion__c = offeringService.Proportion__c,
            Allocated_OCV_in_USD__c = allocatedOCV,
            Opportunity_Offering__c = offeringService.Offering__c,
            Attribution_Value_in_USD__c = attributionValue
        );
    }

    private static Campaign_Influence__c buildCampaignInfluence(Id oppId, Id conId, Id campaignId) {
        return new Campaign_Influence__c(
            Opportunity__c = oppId,
            Contact__c = conId,
            Campaign__c = campaignId,
            Influence_Type__c = 'Influence to create opportunity',
            Marketing_Pipeline__c = 'Marketing Activated',
            Contact_Added_On_Opportunity_Datetime__c = DATETIME_NOW,
            First_Responded_Datetime__c = DATETIME_NOW.addDays(-2)
        );
    }
}