@isTest
private with sharing class TestMKTAccountController {
    private static TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();

    static {
        List<Holistic_Engagement_Model__c> model = new List<Holistic_Engagement_Model__c>{ new Holistic_Engagement_Model__c(Channel_Type__c = 'Content - Blog', Status__c = 'Test Responded') };
        insert model;
    }


    @IsTest
    static void shouldRetrieveLeadWithEngagementsCorrectly() {
        Account account = testFixture.prepareAccount(true);
        Lead lead = testFixture.prepareLead(false);
        lead.LeanData__Reporting_Matched_Account__c = account.Id;
        insert lead;
        TriggerToggle.turnOff();
        Campaign campaign = testFixture.prepareCampaign(true);
        CampaignMember campaignMember = testFixture.prepareCampaignMember(true, campaign, lead, null);
        TriggerToggle.turnOn();
        Person__c person = [SELECT Id, Status__c FROM Person__c WHERE Lead__c = :lead.Id];
        Datetime now = Datetime.now();
        Holistic_Engagement__c engagement = new Holistic_Engagement__c(
                Person__c = person.Id,
                Campaign__c = campaign.Id,
                Campaign_Member_Id__c = campaignMember.Id,
                Engagement_Type__c = 'Campaign',
                Engagement_Behavior__c = campaignMember.Status,
                Engagement_DateTime__c = now.addDays(-10),
                Customer_Lifecycle_Stage__c = person.Status__c
        );
        insert engagement;

        List<LeadWithEngagementsDTO> leadWithEngagementsDTOS = MKTAccountController.getLeadsAndEngagementsWithMatchedAccount(account.Id, 0);

        Assert.areEqual(leadWithEngagementsDTOS.size(), 1);
        Assert.areEqual(leadWithEngagementsDTOS.get(0).lead.Id, lead.Id);
        Assert.areEqual(leadWithEngagementsDTOS.get(0).engagements.size(), 1);
    }
}
