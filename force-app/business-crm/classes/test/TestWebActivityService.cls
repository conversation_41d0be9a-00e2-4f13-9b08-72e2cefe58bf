@isTest
private class TestWebActivityService {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    private static final Datetime timeNow = Datetime.now();

    @isTest
    static void testGenerateAndDeleteHolisticEngagementsByWebActivities() {
        Lead psLead1 = testFixture.prepareLead(false);
        Lead psLead2 = testFixture.prepareLead(false);
        psLead1.MarketoContactId__c = '113344';
        insert new List<Lead>{ psLead1, psLead2 };

        WebActivityObject__c webActivity1 = new WebActivityObject__c(
            ActivityDate__c = timeNow.addDays(-20),
            ActivityId__c = 112394,
            ContactId__c = '113344',
            PageLink__c = 'TestWebpageLink',
            SearchEngine__c = 'TestSearchEngine',
            SearchEngineLink__c = 'TestRefererURL',
            IsNewest__c = true
        );
        WebActivityObject__c webActivity2 = new WebActivityObject__c(
            ActivityDate__c = timeNow.addDays(-10),
            ActivityId__c = 112395,
            ContactId__c = '113345',
            PageLink__c = 'TestWebpageLink',
            SearchEngine__c = 'TestSearchEngine',
            SearchEngineLink__c = 'TestRefererURL',
            IsNewest__c = true
        );
        insert new List<WebActivityObject__c>{ webActivity1, webActivity2 };

        List<Holistic_Engagement__c> engagements = [
            SELECT Id, Person__c, Person__r.Lead__c, Web_Activity__c, Engagement_Type__c, Engagement_Behavior__c, Engagement_Source__c, Engagement_DateTime__c, Customer_Lifecycle_Stage__c
            FROM Holistic_Engagement__c
        ];
        Assert.areEqual(1, engagements.size());
        Assert.areEqual(psLead1.Id, engagements.get(0).Person__r.Lead__c);
        Assert.areEqual(webActivity1.Id, engagements.get(0).Web_Activity__c);
        Assert.areEqual('Web Activity', engagements.get(0).Engagement_Type__c);
        Assert.areEqual('<a href="TestWebpageLink" target="_blank">TestWebpageLink</a>', engagements.get(0).Engagement_Source__c);
        Assert.areEqual(webActivity1.ActivityDate__c, engagements.get(0).Engagement_DateTime__c);
        Assert.areEqual('Known', engagements.get(0).Customer_Lifecycle_Stage__c);

        delete new List<WebActivityObject__c>{ webActivity1, webActivity2 };

        List<Holistic_Engagement__c> engagementsAfterDelete = [SELECT Id FROM Holistic_Engagement__c];
        Assert.areEqual(0, engagementsAfterDelete.size());
    }

    @isTest
    static void testUpdateLeadContactLatestWebActivities() {
        TriggerToggle.turnOff();
        Lead psLead = testFixture.prepareLead(false);
        psLead.Latest_Web_Activity__c = 'TestWebpageLink1';
        psLead.Latest_Web_Activity_Date__c = timeNow.addDays(-30);
        psLead.MarketoContactId__c = '3420686';

        Account account = testFixture.prepareAccount(false);
        account.Name = 'test account';
        insert account;

        Contact psContact = testFixture.preparePSContact(false, account);
        psContact.Latest_Web_Activity__c = 'TestWebpageLink2';
        psContact.Latest_Web_Activity_Date__c = timeNow.addDays(-10);
        psContact.MarketoContactId__c = '3420687';

        insert psLead;
        insert psContact;

        WebActivityObject__c webActivity1 = new WebActivityObject__c(
                ActivityDate__c = timeNow.addDays(-3),
                ActivityId__c = 112394,
                ContactId__c = psLead.MarketoContactId__c,
                PageLink__c = 'TestWebpageLink3',
                SearchEngine__c = 'TestSearchEngine3',
                SearchEngineLink__c = 'TestRefererURL3',
                IsNewest__c = true
        );
        WebActivityObject__c webActivity2 = new WebActivityObject__c(
                ActivityDate__c = timeNow.addDays(-2),
                ActivityId__c = 112396,
                ContactId__c = psContact.MarketoContactId__c,
                PageLink__c = 'TestWebpageLink4',
                SearchEngine__c = 'TestSearchEngine4',
                SearchEngineLink__c = 'TestRefererURL4',
                IsNewest__c = true
        );
        WebActivityObject__c webActivity3 = new WebActivityObject__c(
                ActivityDate__c = timeNow.addDays(-1),
                ActivityId__c = 112397,
                ContactId__c = psContact.MarketoContactId__c,
                PageLink__c = 'TestWebpageLink4',
                SearchEngine__c = 'TestSearchEngine5',
                SearchEngineLink__c = 'TestRefererURL5',
                IsNewest__c = true
        );
        TriggerToggle.turnOn();
        insert new List<WebActivityObject__c>{ webActivity1, webActivity2, webActivity3 };

        List<Lead> leadResult = [SELECT Id, Latest_Web_Activity__c, Latest_Web_Activity_Date__c FROM Lead WHERE Id =:psLead.Id];
        List<Contact> contactResult = [SELECT Id, Latest_Web_Activity__c, Latest_Web_Activity_Date__c FROM Contact WHERE Id =:psContact.Id];

        Assert.areEqual(1, leadResult.size());
        Assert.areEqual(1, contactResult.size());

        Assert.areEqual(webActivity1.ActivityDate__c, leadResult.get(0).Latest_Web_Activity_Date__c);
        Assert.areEqual(webActivity1.PageLink__c, leadResult.get(0).Latest_Web_Activity__c);

        Assert.areEqual(webActivity3.ActivityDate__c, contactResult.get(0).Latest_Web_Activity_Date__c);
        Assert.areEqual(webActivity3.PageLink__c, contactResult.get(0).Latest_Web_Activity__c);
    }
}
