@IsTest(IsParallel=true)
public without sharing class MKTContactServiceTest {
    private final static ID PS_CONTACT_ID = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId();
    private final static ID RESOURCE_CONTACT_ID = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();

    private static MKTContactService marketingContactService;
    private static IProfileSelector mockProfileSelector;
    private static IMKTContactSelector mockContactSelector;
    private static SurveyResponseService mockSurveyResponseService;
    private static fflib_ApexMocks mocks = new fflib_ApexMocks();

    private static Account acc;

    static {
        mockContactSelector = (IMKTContactSelector) mocks.mock(IMKTContactSelector.class);
        mockProfileSelector = (IProfileSelector) mocks.mock(IProfileSelector.class);
        mockSurveyResponseService = (SurveyResponseService) mocks.mock(SurveyResponseService.class);
        marketingContactService = new MKTContactService(mockProfileSelector, mockContactSelector, mockSurveyResponseService);

        acc = new Account(Id = fflib_IDGenerator.generate(Account.sObjectType));
    }

    @IsTest
    static void shouldCheckIfShouldSyncToMarketo() {
        Contact con1 = new Contact(FirstName = 'testName', Email = null, Sync_to_Marketo__c = false);
        Contact con2 = new Contact(FirstName = CRMConstants.FIRST_NAME_TO_TAG_ANONYMOUS, Email = '<EMAIL>', Sync_to_Marketo__c = false);
        Contact con3 = new Contact(FirstName = 'testName', Email = '<EMAIL>', Sync_to_Marketo__c = false);

        Contact con4 = new Contact(FirstName = 'testName', Email = null, Sync_to_Marketo__c = true);
        Contact con5 = new Contact(FirstName = 'testName', Email = '<EMAIL>', Sync_to_Marketo__c = true);

        System.Test.startTest();
        marketingContactService.checkIfShouldSyncToMarketoWhenUpdate(new List<Contact>{ con1, con2, con3, con4, con5 });
        System.Test.stopTest();

        System.assertEquals(false, con1.Sync_to_Marketo__c);
        System.assertEquals(false, con2.Sync_to_Marketo__c);
        System.assertEquals(true, con3.Sync_to_Marketo__c);
        System.assertEquals(true, con4.Sync_to_Marketo__c);
        System.assertEquals(true, con5.Sync_to_Marketo__c);
    }

    @IsTest
    static void notAtAccountshouldChangeToTrue() {
        Map<Id, Contact> oldContactMap = new Map<Id, Contact>();
        Contact oldCon = (Contact) JSON.deserialize('{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","LID__No_longer_at_Company__c":"Ignore","Not_at_Account__c":false}', Contact.class);
        oldContactMap.put(Id.valueOf('0030500000DSO7hAAH'), oldCon);
        Contact newCon = (Contact) JSON.deserialize(
            '{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","LID__No_longer_at_Company__c":"Not at Company","Not_at_Account__c":false}',
            Contact.class
        );

        marketingContactService.updateNotAtAccountWhenNotAtCompanyFlagChange(oldContactMap, new List<Contact>{ newCon });
        System.assertEquals(true, newCon.Not_at_Account__c);
    }

    @IsTest
    static void notAtAccountshouldChangeToFalse() {
        Map<Id, Contact> oldContactMap = new Map<Id, Contact>();
        Contact oldCon = (Contact) JSON.deserialize(
            '{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","LID__No_longer_at_Company__c":"Not at Company","Not_at_Account__c":true}',
            Contact.class
        );
        oldContactMap.put(Id.valueOf('0030500000DSO7hAAH'), oldCon);
        Contact newCon = (Contact) JSON.deserialize('{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","Not_at_Account__c":true}', Contact.class);

        marketingContactService.updateNotAtAccountWhenNotAtCompanyFlagChange(oldContactMap, new List<Contact>{ newCon });
        System.assertEquals(false, newCon.Not_at_Account__c);
    }

    @IsTest
    static void GivenNotAtAccountFlagChangeToTrueShouldDateShouldPopulatedWithToday() {
        Map<Id, Contact> oldContactMap = new Map<Id, Contact>();
        Contact oldCon = (Contact) JSON.deserialize('{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","Not_at_Account__c":false}', Contact.class);
        oldContactMap.put(Id.valueOf('0030500000DSO7hAAH'), oldCon);
        Contact newCon = (Contact) JSON.deserialize('{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","Not_at_Account__c":true}', Contact.class);

        marketingContactService.checkNotAtAccountFlagChange(oldContactMap, new List<Contact>{ newCon });
        System.assertEquals(Date.today(), newCon.Date_not_at_account_detected__c);
    }

    @IsTest
    static void GivenNotAtAccountFlagChangetoFalseShouldNotAtAccountDateShouldPopulatedWithNull() {
        Map<Id, Contact> oldContactMap = new Map<Id, Contact>();
        Contact oldCon = (Contact) JSON.deserialize(
            '{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","Not_at_Account__c":true, "Date_not_at_account_detected__c": "2021-08-22"}',
            Contact.class
        );
        oldContactMap.put(Id.valueOf('0030500000DSO7hAAH'), oldCon);
        Contact newCon = (Contact) JSON.deserialize('{"Id":"0030500000DSO7hAAH","RecordTypeId":"012500000005IiNAAU","Not_at_Account__c":false}', Contact.class);

        marketingContactService.checkNotAtAccountFlagChange(oldContactMap, new List<Contact>{ newCon });
        System.assertEquals(null, newCon.Date_not_at_account_detected__c);
    }

    @IsTest
    static void shouldUpdateContactsWithLatestNPSInfo() {
        // given
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Survey_Response__c oldResponse = new Survey_Response__c(
            Contact__c = contactId,
            NPS_Score__c = '5',
            NPS_Comment__c = 'Old comment',
            Response_Datetime__c = Datetime.newInstanceGmt(2023, 1, 1, 12, 0, 0)
        );
        Survey_Response__c latestResponse = new Survey_Response__c(
            Contact__c = contactId,
            NPS_Score__c = '8',
            NPS_Comment__c = 'Latest comment',
            Response_Datetime__c = Datetime.newInstanceGmt(2024, 1, 2, 12, 0, 0)
        );
        List<Survey_Response__c> responses = new List<Survey_Response__c>{ oldResponse, latestResponse };
        Map<Id, List<Survey_Response__c>> mockResponseMap = new Map<Id, List<Survey_Response__c>>{ contactId => responses };
        fflib_ArgumentCaptor contactCaptor = fflib_ArgumentCaptor.forClass(List<Contact>.class);

        mocks.startStubbing();
        mocks.when(mockSurveyResponseService.getContactIdToSurveyResponsesMap(new Set<Id>{ contactId })).thenReturn(mockResponseMap);
        mocks.stopStubbing();

        // when
        marketingContactService.updateNPSInfo(responses);

        // then
        ((IMKTContactSelector) mocks.verify(mockContactSelector, 1)).updateContacts((List<Contact>) contactCaptor.capture());
        List<Contact> capturedContacts = (List<Contact>) contactCaptor.getValue();
        System.assertEquals(1, capturedContacts.size());
        System.assertEquals('Latest comment', capturedContacts[0].Latest_NPS_Comment__c);
        System.assertEquals('8', capturedContacts[0].Latest_NPS_Score__c);
        System.assertEquals(Date.newInstance(2024, 1, 2), capturedContacts[0].Latest_NPS_Response_Date__c);
    }

    @IsTest
    public static void shouldNotifyAccountOwnerWhenPSContactMoveFromAccount() {
        Contact contact = new Contact(
            Id = fflib_IDGenerator.generate(Contact.sObjectType),
            LastName = 'test',
            job_title__c = 'Manager',
            accountId = acc.id,
            CurrencyIsoCode = 'CNY',
            RecordTypeId = PS_CONTACT_ID
        );
        Contact contactMoveFromAccount = contact.clone(true, true);
        contactMoveFromAccount.LID__No_longer_at_Company__c = 'Not at Company';

        Set<Id> needGenerateTaskContactIds = marketingContactService.getNeedGenerateTaskContactIds(new Map<Id, Contact>{ contact.Id => contact }, new List<Contact>{ contactMoveFromAccount });

        System.assertEquals(1, needGenerateTaskContactIds.size());
        System.assertEquals(contact.id, (new List<Id>(needGenerateTaskContactIds))[0]);
    }

    @IsTest
    public static void shouldNotNotifyAccountOwnerWhenResourceContactMoveFromAccount() {
        Contact contact = new Contact(
            Id = fflib_IDGenerator.generate(Contact.sObjectType),
            LastName = 'test',
            job_title__c = 'Manager',
            accountId = acc.id,
            CurrencyIsoCode = 'CNY',
            RecordTypeId = RESOURCE_CONTACT_ID
        );
        Contact contactMoveFromAccount = contact.clone(true, true);
        contactMoveFromAccount.LID__No_longer_at_Company__c = 'Not at Company';

        Set<Id> needGenerateTaskContactIds = marketingContactService.getNeedGenerateTaskContactIds(new Map<Id, Contact>{ contact.Id => contact }, new List<Contact>{ contactMoveFromAccount });

        System.assertEquals(0, needGenerateTaskContactIds.size());
    }

    @IsTest
    public static void shouldNotifyAccountOwnerWhenPSContactIgnoreMoveFromAccount() {
        Contact contact = new Contact(
            Id = fflib_IDGenerator.generate(Contact.sObjectType),
            LastName = 'test',
            job_title__c = 'Manager',
            accountId = acc.id,
            CurrencyIsoCode = 'CNY',
            RecordTypeId = PS_CONTACT_ID,
            LID__No_longer_at_Company__c = 'Ignore'
        );
        Contact contactMoveFromAccount = contact.clone(true, true);
        contactMoveFromAccount.LID__No_longer_at_Company__c = 'Not at Company';

        Set<Id> needGenerateTaskContactIds = marketingContactService.getNeedGenerateTaskContactIds(new Map<Id, Contact>{ contact.Id => contact }, new List<Contact>{ contactMoveFromAccount });

        System.assertEquals(0, needGenerateTaskContactIds.size());
    }

    @IsTest
    public static void shouldNotNotifyAccountOwnerWhenPSContactHasNoAccount() {
        Contact contact = new Contact(Id = fflib_IDGenerator.generate(Contact.sObjectType), LastName = 'test', job_title__c = 'Manager', CurrencyIsoCode = 'CNY', RecordTypeId = PS_CONTACT_ID);
        Contact contactMoveFromAccount = contact.clone(true, true);
        contactMoveFromAccount.LID__No_longer_at_Company__c = 'Not at Company';

        Set<Id> needGenerateTaskContactIds = marketingContactService.getNeedGenerateTaskContactIds(new Map<Id, Contact>{ contact.Id => contact }, new List<Contact>{ contactMoveFromAccount });

        System.assertEquals(0, needGenerateTaskContactIds.size());
    }

    @IsTest
    public static void shouldReturnOnlyInternalContactWhenInternalContactCountOverLimit() {
        // given
        Id accountId1 = fflib_IDGenerator.generate(Account.sObjectType);
        List<Id> accountIds = new List<Id>{ accountId1 };

        List<Contact> internalContactList = new List<Contact>();

        Integer limitNum = 100;
        for (Integer i = 0; i < limitNum; i++) {
            internalContactList.add(new Contact(Id = fflib_IDGenerator.generate(Contact.sObjectType), LastName = 'test' + i, accountId = accountId1));
        }

        mocks.startStubbing();
        mocks.when(mockContactSelector.searchContactByNameAndAccountIdsInOrOutWithLimit('test', accountIds, true, limitNum)).thenReturn(internalContactList);
        mocks.stopStubbing();
        // when
        System.Test.startTest();
        List<Contact> contactList = marketingContactService.searchContactByNameAndAccountIdsWithInternalPriorityAndLimit('test', accountIds, limitNum);
        System.Test.stopTest();
        // then
        for (Integer i = 0; i < limitNum; i++) {
            System.assertEquals(accountId1, contactList[i].accountId);
        }
    }

    @IsTest
    public static void shouldReturnInternalContactFirstWhenInternalContactCountBelowLimit() {
        // given
        Id accountId1 = fflib_IDGenerator.generate(Account.sObjectType);
        List<Id> accountIds = new List<Id>{ accountId1 };
        Id accountId2 = fflib_IDGenerator.generate(Account.sObjectType);
        List<Contact> internalContactList = new List<Contact>();
        List<Contact> externalContactList = new List<Contact>();
        for (Integer i = 0; i < 30; i++) {
            internalContactList.add(new Contact(Id = fflib_IDGenerator.generate(Contact.sObjectType), LastName = 'internalTest' + i, accountId = accountId1));
        }
        for (Integer i = 0; i < 20; i++) {
            externalContactList.add(new Contact(Id = fflib_IDGenerator.generate(Contact.sObjectType), LastName = 'externalTest' + i, accountId = accountId2));
        }

        mocks.startStubbing();
        Integer limitNum = 100;
        mocks.when(mockContactSelector.searchContactByNameAndAccountIdsInOrOutWithLimit('test', accountIds, true, limitNum)).thenReturn(internalContactList);
        mocks.when(mockContactSelector.searchContactByNameAndAccountIdsInOrOutWithLimit('test', accountIds, false, limitNum - internalContactList.size())).thenReturn(externalContactList);
        mocks.stopStubbing();
        // when
        System.Test.startTest();
        List<Contact> contactList = marketingContactService.searchContactByNameAndAccountIdsWithInternalPriorityAndLimit('test', accountIds, limitNum);
        System.Test.stopTest();
        // then
        for (Integer i = 0; i < 30; i++) {
            System.assertEquals(accountId1, contactList[i].accountId);
        }
        for (Integer i = 30; i < 50; i++) {
            System.assertEquals(accountId2, contactList[i].accountId);
        }
    }

    @IsTest
    static void shouldUpdateStatusWhenQualifyOut() {
        Id contactId1 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id contactId2 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id contactId3 = fflib_IDGenerator.generate(Contact.SObjectType);
        Contact newContact1 = new Contact(Id = contactId1, RecordTypeId = PS_CONTACT_ID, Disqualified_Reason__c = 'No buying intent');
        Contact newContact2 = new Contact(Id = contactId2, RecordTypeId = PS_CONTACT_ID, Disqualified_Reason__c = null);
        Contact newContact3 = new Contact(Id = contactId3, RecordTypeId = PS_CONTACT_ID, Disqualified_Reason__c = 'Company size too small');
        List<Contact> newContactList = new List<Contact>{ newContact1, newContact2, newContact3 };

        Contact oldContact1 = new Contact(Id = contactId1, RecordTypeId = PS_CONTACT_ID, Disqualified_Reason__c = null);
        Contact oldContact2 = new Contact(Id = contactId2, RecordTypeId = PS_CONTACT_ID, Disqualified_Reason__c = null);
        Contact oldContact3 = new Contact(Id = contactId3, RecordTypeId = PS_CONTACT_ID, Disqualified_Reason__c = null);
        Map<Id, Contact> oldContactMap = new Map<Id, Contact>{ contactId1 => oldContact1, contactId2 => oldContact2, contactId3 => oldContact3 };

        marketingContactService.updateStatusWhenQualifyOut(newContactList, oldContactMap);

        System.assertEquals(newContactList.get(0).Status__c, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY);
        System.assertEquals(newContactList.get(1).Status__c, null);
        System.assertEquals(newContactList.get(2).Status__c, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_WILL_NEVER_BUY);
    }

    @IsTest
    static void shouldUpdateMQLEndDateWhenStatusChangeFromMQL() {
        Datetime mockDatetimeNow = Datetime.newInstanceGmt(2023, 1, 1, 1, 1, 1);
        TimeUtils.now = mockDatetimeNow;
        // Given

        Id contactId1 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id contactId2 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id contactId3 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id contactId4 = fflib_IDGenerator.generate(Contact.SObjectType);

        Contact oldContact1 = new Contact(Id = contactId1, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);
        Contact newContact1 = new Contact(Id = contactId1, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);

        Contact oldContact2 = new Contact(Id = contactId2, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);
        Contact newContact2 = new Contact(Id = contactId2, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY);

        Contact oldContact3 = new Contact(Id = contactId3, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY, MQL_End_Date__c = Datetime.newInstanceGmt(2023, 12, 31, 0, 0, 0));
        Contact newContact3 = new Contact(Id = contactId3, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);

        Contact oldContact4 = new Contact(Id = contactId4, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_CONVERT);
        Contact newContact4 = new Contact(Id = contactId4, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);

        List<Contact> newContacts = new List<Contact>{ newContact1, newContact2, newContact3, newContact4 };
        Map<Id, Contact> oldContactMap = new Map<Id, Contact>{ contactId1 => oldContact1, contactId2 => oldContact2, contactId3 => oldContact3, contactId4 => oldContact4 };

        //When
        marketingContactService.updateMQLEndDateWhenStatusChangeFromMQL(newContacts, oldContactMap);

        //Then
        System.assertEquals(null, newContact1.MQL_End_Date__c);
        System.assertEquals(mockDatetimeNow, newContact2.MQL_End_Date__c);
        System.assertEquals(null, newContact3.MQL_End_Date__c);
        System.assertEquals(null, newContact4.MQL_End_Date__c);
    }

    @IsTest
    static void shouldUpdateQualificationInfoWhenChangeContactStatusToSAL() {
        Contact oldContact = new Contact(Id = fflib_IDGenerator.generate(Contact.SObjectType), Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ENGAGED);

        Contact newContact = new Contact(Id = oldContact.Id, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL);

        marketingContactService.handleQualificationInfoWhenStageIsSAL(new Map<Id, Contact>{ oldContact.Id => oldContact }, new List<SObject>{ newContact });

        System.assertEquals(newContact.Recent_Qualification_Path__c, 'Sales Generated');
    }

    @IsTest
    static void shouldNotUpdateQualificationInfoWhenChangeContactStatusToSALButPreviousStatusIsMQL() {
        Contact oldContact = new Contact(Id = fflib_IDGenerator.generate(Contact.SObjectType), Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);

        Contact newContact = new Contact(Id = oldContact.Id, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL);

        marketingContactService.handleQualificationInfoWhenStageIsSAL(new Map<Id, Contact>{ oldContact.Id => oldContact }, new List<SObject>{ newContact });

        System.assertEquals(newContact.Recent_Qualification_Path__c, null);
    }

    @IsTest
    static void shouldSetQuickConvertedByWhenQuickConvertLeadToContact() {
        MKTFrontEndContext.isQuickConvertLeadToContact = true;

        Contact newContact = new Contact(ContactConverted__c = true, RecordTypeId = PS_CONTACT_ID);

        marketingContactService.setQuickConvertedByOnContact(new List<SObject>{ newContact });

        System.assertEquals(newContact.Quick_Converted_by__c, UserInfo.getUserId());
    }

    @IsTest
    static void shouldSyncEngageB2B() {
        Contact contact = new Contact(RecordTypeId = PS_CONTACT_ID, Email = null);

        marketingContactService.syncEngageB2B(new List<Contact>{ contact });

        Assert.areEqual(false, contact.Enable_ima_sync__c);

        contact.Email = '<EMAIL>';
        marketingContactService.syncEngageB2B(new List<Contact>{ contact });

        Assert.areEqual(true, contact.Enable_ima_sync__c);
    }
}
