@IsTest(IsParallel=true)
public class SurveyResponseServiceTest {
    private static fflib_ApexMocks mocks = new fflib_ApexMocks();
    private static ILeadSelector mockLeadSelector = (ILeadSelector) mocks.mock(ILeadSelector.class);
    private static IMKTContactSelector mockContactSelector = (IMKTContactSelector) mocks.mock(IMKTContactSelector.class);
    private static IAccountSelector mockAccountSelector = (IAccountSelector) mocks.mock(IAccountSelector.class);
    private static ISurveyResponseSelector mockSurveyResponseSelector = (ISurveyResponseSelector) mocks.mock(ISurveyResponseSelector.class);
    private static ICampaignMemberSelector mockCampaignMemberSelector = (ICampaignMemberSelector) mocks.mock(ICampaignMemberSelector.class);

    @IsTest
    public static void shouldLinkToContactAndLeadGivenContactIdOrLeadId() {
        // given
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id contactId2 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id leadId = fflib_IDGenerator.generate(Lead.SObjectType);
        Id leadId2 = fflib_IDGenerator.generate(Lead.SObjectType);

        Survey_Response__c resp1 = new Survey_Response__c(Contact_Id__c = contactId);
        Survey_Response__c resp2 = new Survey_Response__c(Lead_Id__c = leadId);
        Survey_Response__c resp3 = new Survey_Response__c(Contact_Id__c = contactId2, Lead_Id__c = leadId2);

        mocks.startStubbing();
        mocks.when(mockContactSelector.getContactListByContactId(new List<Id>{ contactId, contactId2 }))
            .thenReturn(new List<Contact>{ new Contact(Id = contactId, AccountId = accountId), new Contact(Id = contactId2, AccountId = accountId) });
        mocks.when(mockLeadSelector.getLeadsById(new Set<Id>{ leadId })).thenReturn(new List<Lead>{ new Lead(Id = leadId), new Lead(Id = leadId2) });
        mocks.stopStubbing();

        SurveyResponseService surveyResponseService = new SurveyResponseService(mockContactSelector, mockLeadSelector, null, null, null);

        // when
        surveyResponseService.link2ContactOrLead(new List<Survey_Response__c>{ resp1, resp2, resp3 });

        // then
        System.assertEquals(contactId, resp1.Contact__c);
        System.assertEquals(null, resp1.Lead__c);
        System.assertEquals(accountId, resp1.Account__c);

        System.assertEquals(null, resp2.Contact__c);
        System.assertEquals(leadId, resp2.Lead__c);
        System.assertEquals(null, resp2.Account__c);

        System.assertEquals(contactId2, resp3.Contact__c);
        System.assertEquals(null, resp3.Lead__c);
        System.assertEquals(accountId, resp3.Account__c);
    }

    @IsTest
    public static void shouldNotCreateEmailWhenAccountOwnerIsInactive() {
        Id NPSRecordTypeId = Schema.SObjectType.Survey_Response__c.getRecordTypeInfosByName().get('NPS').getRecordTypeId();
        SurveyResponseService surveyResponseService = new SurveyResponseService(mockContactSelector, mockLeadSelector, mockAccountSelector, mockSurveyResponseSelector, null);
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id accountOwnerId = fflib_IDGenerator.generate(User.SObjectType);
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id respId = fflib_IDGenerator.generate(Survey_Response__c.SObjectType);
        Survey_Response__c resp1 = new Survey_Response__c(Id = respId, Type__c = 'Contact', RecordTypeId = NPSRecordTypeId);
        Survey_Response__c dbResp = (Survey_Response__c) JSON.deserialize(
            '{"attributes":{"type":"Survey_Response__c"},"Id":"' +
                respId +
                '","Type__c":"Contact", "NPS_Score__c":"6", "NPS_Comment__c":"not like it", "CreatedDate":"2022-06-17T23:57:32", "RecordTypeId": "' +
                NPSRecordTypeId +
                '","Contact__c":"' +
                contactId +
                '","Contact__r":{"attributes":{"type":"Contact"},"Id":"' +
                contactId +
                '","Name":"Test Client Name","AccountId":"' +
                accountId +
                '"}}',
            Survey_Response__c.class
        );

        mocks.startStubbing();
        mocks.when(mockAccountSelector.getAccountWithActiveOwner(new Set<Id>{ accountId })).thenReturn(new Map<Id, Account>());
        mocks.when(mockSurveyResponseSelector.getNPSSurveyResponseByIds(new Set<Id>{ respId })).thenReturn(new List<Survey_Response__c>{ dbResp });
        mocks.stopStubbing();

        List<Messaging.SingleEmailMessage> emails = surveyResponseService.createNPSSurveyResultNotificationEmails(new List<Survey_Response__c>{ resp1 });
        System.assertEquals(0, emails.size());
    }

    @IsTest
    public static void shouldCreateDistractorEmailWhenReceiveSurveyResult() {
        Id NPSRecordTypeId = Schema.SObjectType.Survey_Response__c.getRecordTypeInfosByName().get('NPS').getRecordTypeId();
        SurveyResponseService surveyResponseService = new SurveyResponseService(mockContactSelector, mockLeadSelector, mockAccountSelector, mockSurveyResponseSelector, null);
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id accountOwnerId = fflib_IDGenerator.generate(User.SObjectType);
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id respId = fflib_IDGenerator.generate(Survey_Response__c.SObjectType);
        Account acc = new Account(Id = accountId, Name = 'Test Account', OwnerId = accountOwnerId);
        User accountOwner = (User) JSON.deserialize('{"attributes":{"type":"User"}, "Id":"' + accountOwnerId + '", "Name":"Test Account Owner", "Email":"<EMAIL>", "IsActive": true}', User.class);
        acc.Owner = accountOwner;
        Survey_Response__c resp1 = new Survey_Response__c(Id = respId, Type__c = 'Contact', RecordTypeId = NPSRecordTypeId);
        Survey_Response__c dbResp = (Survey_Response__c) JSON.deserialize(
            '{"attributes":{"type":"Survey_Response__c"},"Id":"' +
                respId +
                '","Type__c":"Contact", "NPS_Score__c":"6", "NPS_Comment__c":"not like it", "CreatedDate":"2022-06-17T23:57:32", "RecordTypeId": "' +
                NPSRecordTypeId +
                '","Contact__c":"' +
                contactId +
                '","Contact__r":{"attributes":{"type":"Contact"},"Id":"' +
                contactId +
                '","Name":"Test Client Name","AccountId":"' +
                accountId +
                '"}}',
            Survey_Response__c.class
        );

        Boolean isSandbox = OrganizationInfo.isSandbox();
        String domainPrefix = isSandbox ? '--' + OrganizationInfo.sandBoxName() + '.sandbox' : '';

        mocks.startStubbing();
        mocks.when(mockAccountSelector.getAccountWithActiveOwner(new Set<Id>{ accountId })).thenReturn(new Map<Id, Account>{ accountId => acc });
        mocks.when(mockSurveyResponseSelector.getNPSSurveyResponseByIds(new Set<Id>{ respId })).thenReturn(new List<Survey_Response__c>{ dbResp });
        mocks.stopStubbing();

        List<Messaging.SingleEmailMessage> emails = surveyResponseService.createNPSSurveyResultNotificationEmails(new List<Survey_Response__c>{ resp1 });
        System.assertEquals(1, emails.size());
        Messaging.SingleEmailMessage distractorEmail = emails.get(0);
        System.assertEquals('<EMAIL>', distractorEmail.getToAddresses().get(0));
        System.assertEquals('<EMAIL>', distractorEmail.getReplyTo());
        System.assertEquals('CRM Team', distractorEmail.getSenderDisplayName());
        System.assertEquals('NPS Detractor Alert: Test Account received an NPS detractor score from Test Client Name', distractorEmail.getSubject());
        String body =
            '<p>Hi Test Account Owner,</p>' +
            '<br/>' +
            '<p>We just received a detractor score on the NPS survey from Test Client Name, Test Account. Please review the survey result below and follow up with your client to address their feedback.</p>' +
            '<br/>' +
            '<p>Contact Name: <b><a href="https://thoughtworks' +
            domainPrefix +
            '.lightning.force.com/lightning/r/Contact/' +
            contactId +
            '/view">Test Client Name</a></b></p>' +
            '<p>Account Name: <b><a href="https://thoughtworks' +
            domainPrefix +
            '.lightning.force.com/lightning/r/Account/' +
            acc.Id +
            '/view">Test Account</a></b></p>' +
            '<p>On a scale of 0-10, how likely are you to recommend Thoughtworks to a friend or colleague?: <b>6</b></p>' +
            '<p>What is the most important reason for your score?: <b>not like it</b></p>' +
            '<p>Time completed: <b>2022-06-17 23:57:32</b></p>' +
            '<br/>' +
            '<i>As a reminder, scores of 0-6 are classified as “detractors” per the Net Promoter Score method.</i>' +
            '<br/>' +
            '<br/>' +
            '<p>Sent by the CRM Team</p>';
        System.assertEquals(body, distractorEmail.getHtmlBody());
    }
    @IsTest
    public static void shouldCreateNormalEmailWhenReceiveSurveyResult() {
        Id NPSRecordTypeId = Schema.SObjectType.Survey_Response__c.getRecordTypeInfosByName().get('NPS').getRecordTypeId();
        SurveyResponseService surveyResponseService = new SurveyResponseService(mockContactSelector, mockLeadSelector, mockAccountSelector, mockSurveyResponseSelector, null);
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id accountOwnerId = fflib_IDGenerator.generate(User.SObjectType);
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id respId = fflib_IDGenerator.generate(Survey_Response__c.SObjectType);
        Account acc = new Account(Id = accountId, Name = 'Test Account', OwnerId = accountOwnerId);
        User accountOwner = (User) JSON.deserialize('{"attributes":{"type":"User"}, "Id":"' + accountOwnerId + '", "Name":"Test Account Owner", "Email":"<EMAIL>", "IsActive": true}', User.class);
        acc.Owner = accountOwner;
        Survey_Response__c resp1 = new Survey_Response__c(Id = respId, Type__c = 'Contact', RecordTypeId = NPSRecordTypeId);
        Survey_Response__c dbResp = (Survey_Response__c) JSON.deserialize(
            '{"attributes":{"type":"Survey_Response__c"},"Id":"' +
                respId +
                '","Type__c":"Contact", "NPS_Score__c":"7", "NPS_Comment__c":"like it", "CreatedDate":"2022-06-17T23:57:32", "RecordTypeId": "' +
                NPSRecordTypeId +
                '","Contact__c":"' +
                contactId +
                '","Contact__r":{"attributes":{"type":"Contact"},"Id":"' +
                contactId +
                '","Name":"Test Client Name","AccountId":"' +
                accountId +
                '"}}',
            Survey_Response__c.class
        );

        Boolean isSandbox = OrganizationInfo.isSandbox();
        String domainPrefix = isSandbox ? '--' + OrganizationInfo.sandBoxName() + '.sandbox' : '';

        mocks.startStubbing();
        mocks.when(mockAccountSelector.getAccountWithActiveOwner(new Set<Id>{ accountId })).thenReturn(new Map<Id, Account>{ accountId => acc });
        mocks.when(mockSurveyResponseSelector.getNPSSurveyResponseByIds(new Set<Id>{ respId })).thenReturn(new List<Survey_Response__c>{ dbResp });
        mocks.stopStubbing();

        List<Messaging.SingleEmailMessage> emails = surveyResponseService.createNPSSurveyResultNotificationEmails(new List<Survey_Response__c>{ resp1 });
        System.assertEquals(1, emails.size());
        Messaging.SingleEmailMessage normalEmail = emails.get(0);
        System.assertEquals('<EMAIL>', normalEmail.getToAddresses().get(0));
        System.assertEquals('<EMAIL>', normalEmail.getReplyTo());
        System.assertEquals('CRM Team', normalEmail.getSenderDisplayName());
        System.assertEquals('New NPS survey response from Test Client Name, Test Account', normalEmail.getSubject());
        String body =
            '<p>Hi Test Account Owner,</p>' +
            '<br/>' +
            '<p>We just received a new NPS survey response from Test Client Name, Test Account. Please review the survey result below and follow up with your client to address their feedback, as needed.</p>' +
            '<br/>' +
            '<p>Contact Name: <b><a href="https://thoughtworks' +
            domainPrefix +
            '.lightning.force.com/lightning/r/Contact/' +
            contactId +
            '/view">Test Client Name</a></b></p>' +
            '<p>Account Name: <b><a href="https://thoughtworks' +
            domainPrefix +
            '.lightning.force.com/lightning/r/Account/' +
            acc.Id +
            '/view">Test Account</a></b></p>' +
            '<p>On a scale of 0-10, how likely are you to recommend Thoughtworks to a friend or colleague?: <b>7</b></p>' +
            '<p>What is the most important reason for your score?: <b>like it</b></p>' +
            '<p>Time completed: <b>2022-06-17 23:57:32</b></p>' +
            '<br/>' +
            '<i>As a reminder, the Net Promoter Score method classifies scores of 0-6 as “detractors”, scores of 7-8 as “passive”, and scores of 9-10 as “promoters”.</i>' +
            '<br/>' +
            '<br/>' +
            '<p>Sent by the CRM Team</p>';
        System.assertEquals(body, normalEmail.getHtmlBody());
    }

    @IsTest
    public static void noContactJoinNPSCampaign() {
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Account acc = new Account(Id = accountId, Name = 'Test Account');
        Contact contact1 = new Contact(Id = contactId);
        mocks.startStubbing();
        mocks.when(mockContactSelector.getContactsByAccount(accountId)).thenReturn(new Map<Id, Contact>{ contactId => contact1 });
        mocks.when(mockSurveyResponseSelector.getNewestCampaignIdAndCreateDateByContactIds(new Set<Id>{ contactId })).thenReturn(new List<Survey_Response__c>());
        mocks.stopStubbing();

        SurveyResponseService surveyResponseService = new SurveyResponseService(mockContactSelector, null, mockAccountSelector, mockSurveyResponseSelector, null);
        List<SurveyResponseDetail> res = surveyResponseService.getNewestNPSSurveyResponseByAccountId(accountId);
        System.assertEquals(0, res.size());
    }

    @IsTest
    public static void contactJoinSomeNPSCampaignAndNormalCampaign() {
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id campaignId = fflib_IDGenerator.generate(Campaign.SObjectType);
        Account acc = new Account(Id = accountId, Name = 'Test Account');
        Contact contact1 = new Contact(Id = contactId);
        CampaignMember cm = (CampaignMember) JSON.deserialize(
            '{"attributes":{"type":"CampaignMember"}, "Type":"Contact", "Contact":{"Id":"' + contactId + '"}, "Campaign": {"Id": "' + campaignId + '", "Status": "Completed"}}',
            CampaignMember.class
        );

        mocks.startStubbing();
        mocks.when(mockContactSelector.getContactsByAccount(accountId)).thenReturn(new Map<Id, Contact>{ contactId => contact1 });
        mocks.when(mockSurveyResponseSelector.getNewestCampaignIdAndCreateDateByContactIds(new Set<Id>{ contactId }))
            .thenReturn(new List<Survey_Response__c>{ new Survey_Response__c(Campaign__c = campaignId) });
        mocks.when(mockSurveyResponseSelector.getNPSSurveyResponsesByContactIdsAndCampaignIds(new Set<Id>{ contactId }, new Set<Id>{ campaignId }))
            .thenReturn(new List<Survey_Response__c>{ new Survey_Response__c(Type__c = 'Contact', Contact__c = contactId, NPS_Score__c = '10', NPS_Comment__c = 'I like it.') });
        mocks.when(mockCampaignMemberSelector.getJoinNPSSurveyCampaignMember(campaignId, accountId, new Set<Id>{ contactId })).thenReturn(new List<CampaignMember>{ cm });
        mocks.stopStubbing();

        SurveyResponseService surveyResponseService = new SurveyResponseService(mockContactSelector, null, mockAccountSelector, mockSurveyResponseSelector, mockCampaignMemberSelector);
        List<SurveyResponseDetail> res = surveyResponseService.getNewestNPSSurveyResponseByAccountId(accountId);
        System.assertEquals(1, res.size());
        System.assertEquals(contactId, res.get(0).contactId);
        System.assertEquals('Completed', res.get(0).campaignStatus);
        System.assertEquals('10', res.get(0).response.NPS_Score__c);
        System.assertEquals('I like it.', res.get(0).response.NPS_Comment__c);
    }

    @IsTest
    public static void computerCategoriesTest() {
        SurveyResponseCategories categories = new SurveyResponseCategories('2021', 0, 0, 0);
        SurveyResponseService surveyResponseService = new SurveyResponseService();
        SurveyResponseCategories categoriesScore = surveyResponseService.computerCategories('9', categories);
        System.assertEquals(1, categoriesScore.promoter);
    }

    @IsTest
    public static void getSurveyResponseCategoriesTest() {
        Id contactId = fflib_IDGenerator.generate(Contact.SObjectType);
        Id campaignId = fflib_IDGenerator.generate(Campaign.SObjectType);
        Id respId = fflib_IDGenerator.generate(Survey_Response__c.SObjectType);
        List<Survey_Response__c> sr = new List<Survey_Response__c>();
        Id NPSRecordTypeId = Schema.SObjectType.Survey_Response__c.getRecordTypeInfosByName().get('NPS').getRecordTypeId();
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Survey_Response__c dbResp = (Survey_Response__c) JSON.deserialize(
            '{"attributes":{"type":"Survey_Response__c"},"Id":"' +
                respId +
                '","Type__c":"Contact", "NPS_Score__c":"10", "NPS_Comment__c":"like it", "CreatedDate":"2022-06-17T23:57:32", "RecordTypeId": "' +
                NPSRecordTypeId +
                '","Campaign__r":{"attributes":{"type":"Campaign"},"Id":"' +
                campaignId +
                '","StartDate":"2021-09-08"},"Contact__c":"' +
                contactId +
                '","Contact__r":{"attributes":{"type":"Contact"},"Id":"' +
                contactId +
                '","Name":"Test Client Name","AccountId":"' +
                accountId +
                '"}}',
            Survey_Response__c.class
        );
        sr.add(dbResp);
        SurveyResponseService surveyResponseService = new SurveyResponseService();
        Map<String, SurveyResponseCategories> categoriesSurvey = surveyResponseService.getSurveyResponseCategories(sr);
        System.assertEquals(1, categoriesSurvey.get('2021').promoter);
    }

    @IsTest
    public static void getNetPromoterScoreTest() {
        Id accountId = fflib_IDGenerator.generate(Account.SObjectType);
        Id contactId1 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id campaignId1 = fflib_IDGenerator.generate(Campaign.SObjectType);
        Id contactId2 = fflib_IDGenerator.generate(Contact.SObjectType);
        Id campaignId2 = fflib_IDGenerator.generate(Campaign.SObjectType);
        Id respId1 = fflib_IDGenerator.generate(Survey_Response__c.SObjectType);
        Id respId2 = fflib_IDGenerator.generate(Survey_Response__c.SObjectType);
        Id NPSRecordTypeId = Schema.SObjectType.Survey_Response__c.getRecordTypeInfosByName().get('NPS').getRecordTypeId();

        List<Survey_Response__c> twSurveyResponses = new List<Survey_Response__c>();
        List<Survey_Response__c> accountSurveyResponses = new List<Survey_Response__c>();
        Survey_Response__c dbResp1 = (Survey_Response__c) JSON.deserialize(
            '{"attributes":{"type":"Survey_Response__c"},"Id":"' +
                respId1 +
                '","Type__c":"Contact", "NPS_Score__c":"10", "NPS_Comment__c":"like it", "CreatedDate":"2022-06-17T23:57:32", "RecordTypeId": "' +
                NPSRecordTypeId +
                '","Campaign__r":{"attributes":{"type":"Campaign"},"Id":"' +
                campaignId1 +
                '","StartDate":"2021-09-08"},"Contact__c":"' +
                contactId1 +
                '","Contact__r":{"attributes":{"type":"Contact"},"Id":"' +
                contactId1 +
                '","Name":"Test Client Name","AccountId":"' +
                accountId +
                '"}}',
            Survey_Response__c.class
        );

        Survey_Response__c dbResp2 = (Survey_Response__c) JSON.deserialize(
            '{"attributes":{"type":"Survey_Response__c"},"Id":"' +
                respId2 +
                '","Type__c":"Contact", "NPS_Score__c":"8", "NPS_Comment__c":"like it", "CreatedDate":"2022-06-17T23:57:32", "RecordTypeId": "' +
                NPSRecordTypeId +
                '","Campaign__r":{"attributes":{"type":"Campaign"},"Id":"' +
                campaignId2 +
                '","StartDate":"2022-09-08"},"Contact__c":"' +
                contactId2 +
                '","Contact__r":{"attributes":{"type":"Contact"},"Id":"' +
                contactId2 +
                '","Name":"Test Client Name","AccountId":"' +
                accountId +
                '"}}',
            Survey_Response__c.class
        );
        twSurveyResponses.add(dbResp1);
        twSurveyResponses.add(dbResp2);
        accountSurveyResponses.add(dbResp1);

        SurveyResponseService surveyResponseService = new SurveyResponseService();
        Map<String, NetPromoterScore> netPromoterScore = surveyResponseService.getNetPromoterScore(twSurveyResponses, accountSurveyResponses);
        System.assertEquals(100, netPromoterScore.get('2021').currentAccount);
        System.assertEquals(100, netPromoterScore.get('2021').twAccount);
        System.assertEquals(0, netPromoterScore.get('2022').twAccount);
        System.assertEquals(true, netPromoterScore.get('2022').noResponse);
    }
}
