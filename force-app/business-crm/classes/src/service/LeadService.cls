public without sharing class LeadService {
    private final static Logger LOGGER = new Logger(LeadService.class.getName(), TeamName.CRM);
    public static Map<String, List<String>> statusFields;
    public static Map<String, String> apiLabelMap;
    static Id psLeadId;
    static Id RESOURCE_CONTACT_ID;
    private static final List<String> LEAD_FIELDS_CHANGE_NEED_TO_PUBLISH_EVENT = new List<String>{ 'Company', 'Recent_Marketing_Influenced_Campaign__c' };
    private static final String SUBMITTED_INQUIRY_TO_THOUGHT_WORK = 'Submitted Inquiry to Thoughtworker';
    private static final Map<String, String> COUNTRY_MAP = new Map<String, String>{
        'AU' => 'Australia',
        'CA' => 'Canada',
        'CN' => 'China',
        'DE' => 'Germany',
        'GB' => 'United Kingdom',
        'IN' => 'India',
        'JP' => 'Japan',
        'MX' => 'Mexico',
        'US' => 'United States',
        'USA' => 'United States',
        'FR' => 'France',
        'BR' => 'Brazil'
    };

    private IMKTContactSelector contactSelector = new MKTContactSelector();
    private CalculateHVOService calculateHVOService = new CalculateHVOService();
    private PersonFieldHistorySelector personFieldHistorySelector = new PersonFieldHistorySelector();
    private ICampaignMemberSelector campaignMemberSelector = new CampaignMemberSelector();
    private CustomSettingsSelector customSettingsSelector = new CustomSettingsSelector();
    private ProfileSelector profileSelector = new ProfileSelector();
    private CurrencySelector currencySelector = new CurrencySelector();
    private ActivityService activityService = new ActivityService();
    private CampaignSelector campaignSelector = new CampaignSelector();
    private CommonUserService commonUserService = new CommonUserService();
    private LeadSelector leadSelector = new LeadSelector();
    private MKTUserSelector mktUserSelector = new MKTUserSelector();

    static {
        psLeadId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId();
        RESOURCE_CONTACT_ID = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        statusFields = new Map<String, List<String>>();
        statusFields.put('On Hold', new List<String>{ 'Next_Action_Date__c' });
        statusFields.put('Qualified Out', new List<String>{ 'Disqualified_Reason__c' });
        apiLabelMap = new Map<String, String>();
        apiLabelMap.put('Next_Action_Date__c', 'Next Action Date');
        apiLabelMap.put('Disqualified_Reason__c', 'Disqualification Reason');
    }

    public LeadService() {
    }

    public LeadService(
        IMKTContactSelector contactSelector,
        ICampaignMemberSelector campaignMemberSelector,
        PersonFieldHistorySelector personFieldHistorySelector,
        CustomSettingsSelector customSettingsSelector,
        ProfileSelector profileSelector,
        CurrencySelector currencySelector,
        ActivityService activityService,
        CampaignSelector campaignSelector,
        CalculateHVOService calculateHVOService,
        LeadSelector leadSelector,
        CommonUserService commonUserService,
        MKTUserSelector mktUserSelector
    ) {
        this.contactSelector = contactSelector;
        this.campaignMemberSelector = campaignMemberSelector;
        this.personFieldHistorySelector = personFieldHistorySelector;
        this.customSettingsSelector = customSettingsSelector;
        this.profileSelector = profileSelector;
        this.currencySelector = currencySelector;
        this.activityService = activityService;
        this.campaignSelector = campaignSelector;
        this.calculateHVOService = calculateHVOService;
        this.leadSelector = leadSelector;
        this.commonUserService = commonUserService;
        this.mktUserSelector = mktUserSelector;
    }

    public void mapLeadCountry(List<Lead> leads) {
        for (Lead lead : leads) {
            if (lead.RecordTypeId != psLeadId) {
                continue;
            }
            String mappedCountry = COUNTRY_MAP.get(lead.Country);
            if (mappedCountry != null) {
                lead.Country = mappedCountry;
            }
        }
    }

    public void checkIfShouldSyncToMarketoWhenInsert(List<Lead> newLeads) {
        for (Lead lead : newLeads) {
            if (lead.RecordTypeId != psLeadId) {
                continue;
            }
            if (lead.Email == null) {
                lead.Sync_to_Marketo__c = false;
            }
        }
    }

    public void checkIfShouldSyncToMarketoWhenUpdate(List<Lead> updateLeads) {
        for (Lead lead : updateLeads) {
            if (lead.RecordTypeId != psLeadId) {
                continue;
            }
            if (lead.Email != null && lead.FirstName != CRMConstants.FIRST_NAME_TO_TAG_ANONYMOUS) {
                lead.Sync_to_Marketo__c = true;
            }
        }
    }

    public void checkLeadStatusFiledLeaveValidation(List<Lead> leadList) {
        for (Lead lead : leadList) {
            if (!statusFields.containsKey(lead.Status) || lead.RecordTypeId != psLeadId) {
                continue;
            }
            for (String field : statusFields.get(lead.Status)) {
                Boolean errorField = false;
                if (lead.get(field) == null) {
                    errorField = true;
                } else {
                    if (!field.contains('Date')) {
                        if (((String) lead.get(field)).trim() == '') {
                            errorField = true;
                        }
                    }
                }
                if (errorField) {
                    switch on field {
                        when 'Next_Action_Date__c' {
                            lead.Next_Action_Date__c.addError('You must enter the ' + apiLabelMap.get(field) + '.');
                        }
                        when 'Disqualified_Reason__c' {
                            lead.Disqualified_Reason__c.addError('You must enter the ' + apiLabelMap.get(field) + '.');
                        }
                    }
                }
            }
        }
    }

    public void setDefaultCurrency(List<Lead> leads) {
        for (Lead l : leads) {
            l.CurrencyIsoCode = 'USD';
        }
    }

    public void mapLeadCountryWithSupervisingRegion(List<Lead> leadList) {
        for (Lead lead : leadList) {
            if (lead.Country != null) {
                try {
                    MapCountryAndSupervisingRegion__c SupervisingRegionMapping = MapCountryAndSupervisingRegion__c.getValues(lead.Country);
                    if (SupervisingRegionMapping != null) {
                        lead.Region__c = SupervisingRegionMapping.SupervisingRegion__c;
                    } else {
                        lead.Region__c = 'Other';
                    }
                } catch (System.InvalidParameterValueException e) {
                    lead.Region__c = 'Other';
                    LOGGER.error('The Lead ' + lead.LastName + ' ' + lead.FirstName + ' country exists InvalidParameterValueException when mapping country with supervising region: ' + e.getMessage());
                }
            }
        }
    }

    public void mapCountryWithSupervisingRegionAndDivision(Map<Id, SObject> oldLeadOrContactMap, List<SObject> newLeadOrContacts) {
        if (newLeadOrContacts.isEmpty()) {
            return;
        }
        String contactOrLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String countryFieldName = contactOrLead == 'Lead' ? 'Country' : 'MailingCountry';

        Map<String, SObject> countryAndDivisionSupervisingRegionMap = new Map<String, SObject>();

        for (SObject mapping : customSettingsSelector.getCountryAndDivisionSupervisingRegionMap()) {
            String countryName = (String) mapping.get('CountryName__c');
            String normalizedCountryName = countryName.replace(' ', '').toLowerCase();
            countryAndDivisionSupervisingRegionMap.put(normalizedCountryName, mapping);
        }

        for (SObject leadOrContact : newLeadOrContacts) {
            String newCountry = (String) leadOrContact.get(countryFieldName);

            if (String.isBlank(newCountry)) {
                leadOrContact.put('Supervising_Region__c', null);
                leadOrContact.put('Division__c', null);
            } else if (oldLeadOrContactMap == null || !newCountry.equals(oldLeadOrContactMap.get(leadOrContact.Id).get(countryFieldName))) {
                String newNormalizedCountryName = newCountry.replace(' ', '').toLowerCase();
                if (countryAndDivisionSupervisingRegionMap.containsKey(newNormalizedCountryName)) {
                    SObject divisionSupervisingRegionMap = countryAndDivisionSupervisingRegionMap.get(newNormalizedCountryName);
                    leadOrContact.put('Supervising_Region__c', (String) divisionSupervisingRegionMap.get('SupervisingRegion__c'));
                    leadOrContact.put('Division__c', (String) divisionSupervisingRegionMap.get('Division__c'));
                } else {
                    leadOrContact.put('Supervising_Region__c', 'Others');
                    leadOrContact.put('Division__c', 'Others');
                }
            }
        }
    }

    public void setInboundCallOrEmailSourceLeadContactAsMQL(List<SObject> newLeadOrContactList) {
        String contactOrLead = newLeadOrContactList[0].getSObjectType().getDescribe().getName();
        String statusFieldName = contactOrLead == CRMConstants.OBJECT_LEAD ? 'Status' : 'Status__c';
        for (SObject leadOrContact : newLeadOrContactList) {
            String source = (String) leadOrContact.get('LeadSource');
            if (!isInboundSource(source) || (contactOrLead == CRMConstants.OBJECT_CONTACT && (Boolean) leadOrContact.get('leadConvertFlag__c'))) {
                continue;
            }
            String qualificationTrigger = (source == CRMConstants.INBOUND_SOCIAL_MEDIA) ? 'Inbound - Social Media' : 'Inbound Call';
            leadOrContact.put('Qualification_Trigger__c', qualificationTrigger);
            leadOrContact.put(statusFieldName, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);
            leadOrContact.put('Qualification_Date__c', Date.today());
            leadOrContact.put('Explicit_Intent_Detected__c', true);
        }
    }

    public void addInboundCallOrEmailLeadContactToCampaign(List<SObject> newLeadOrContactList) {
        Id campaignId;
        for (SObject leadOrContact : newLeadOrContactList) {
            String source = (String) leadOrContact.get('LeadSource');
            if (isInboundSource(source) && !LeadConvertContext.isContactConvertedFromLead((Id) leadOrContact.get('Id'))) {
                List<Campaign> campaign = campaignSelector.getCampaignByName('Contact Us');
                if (campaign.isEmpty()) {
                    LOGGER.error('can not find campaign named Contact Us When create new lead or contact :' + (Id) leadOrContact.get('Id'));
                } else {
                    campaignId = campaign[0].Id;
                }
                break;
            }
        }

        List<CampaignMember> campaignMembers = new List<CampaignMember>();
        String contactOrLead = newLeadOrContactList[0].getSObjectType().getDescribe().getName();
        for (SObject leadOrContact : newLeadOrContactList) {
            String source = (String) leadOrContact.get('LeadSource');
            if (!isInboundSource(source) || campaignId == null || LeadConvertContext.isContactConvertedFromLead((Id) leadOrContact.get('Id'))) {
                continue;
            }
            if (contactOrLead == CRMConstants.OBJECT_LEAD) {
                campaignMembers.add(new CampaignMember(CampaignId = campaignId, LeadId = (Id) leadOrContact.get('Id'), Status = SUBMITTED_INQUIRY_TO_THOUGHT_WORK));
            } else {
                campaignMembers.add(new CampaignMember(CampaignId = campaignId, ContactId = (Id) leadOrContact.get('Id'), Status = SUBMITTED_INQUIRY_TO_THOUGHT_WORK));
            }
        }
        campaignMemberSelector.insertCampaignMembers(campaignMembers);
    }

    private Boolean isInboundSource(String source) {
        return source == CRMConstants.INBOUND_CALL_OR_EMAIL || source == CRMConstants.INBOUND_SOCIAL_MEDIA;
    }

    public void setQualifyInfoWhenConvertToMQL(Map<Id, SObject> oldLeadOrContactMap, List<SObject> newLeadOrContacts) {
        if (newLeadOrContacts.isEmpty()) {
            return;
        }
        String contactOrLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String statusFieldName = contactOrLead == 'Lead' ? 'Status' : 'Status__c';

        for (SObject leadOrContact : newLeadOrContacts) {
            String newStatus = (String) leadOrContact.get(statusFieldName);
            if (newStatus != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                continue;
            }

            if (oldLeadOrContactMap == null) {
                leadOrContact.put('Who_qualify_the_MQL__c', UserInfo.getUserId());
                leadOrContact.put('MQL_Qualification_Date__c', Date.today());
            } else {
                String oldStatus = (String) oldLeadOrContactMap.get(leadOrContact.Id).get(statusFieldName);
                if (oldStatus != newStatus) {
                    leadOrContact.put('Who_qualify_the_MQL__c', UserInfo.getUserId());
                    leadOrContact.put('MQL_Qualification_Date__c', Date.today());
                    leadOrContact = updateQualificationTriggerAndDetailWhenMQLWithoutProfileFitment(leadOrContact, oldStatus);
                }
            }
        }
    }

    public void updateLeadToMQLWhenAQLIsTrue(Map<Id, SObject> oldLeadOrContactMap, List<SObject> newLeadOrContacts) {
        if (newLeadOrContacts.isEmpty()) {
            return;
        }
        String contactOrLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String statusFieldName = contactOrLead == 'Lead' ? 'Status' : 'Status__c';

        for (SObject leadOrContact : newLeadOrContacts) {
            SObject oldLeadOrContact = oldLeadOrContactMap.get(leadOrContact.Id);
            if (!leadOrContact.get('Auto_Qualified_Lead__c').equals(oldLeadOrContact.get('Auto_Qualified_Lead__c')) && leadOrContact.get('Auto_Qualified_Lead__c') == true) {
                leadOrContact.put(statusFieldName, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);
            }
        }
    }

    public void updateLeadToMQLWhenHVOIsTrue(Map<Id, SObject> oldLeadOrContactMap, List<SObject> newLeadOrContacts) {
        if (newLeadOrContacts.isEmpty()) {
            return;
        }
        String contactOrLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String statusFieldName = contactOrLead == 'Lead' ? 'Status' : 'Status__c';

        for (SObject leadOrContact : newLeadOrContacts) {
            SObject oldLeadOrContact = oldLeadOrContactMap.get(leadOrContact.Id);
            if (
                !leadOrContact.get('Is_Incomplete_Profile__c').equals(oldLeadOrContact.get('Is_Incomplete_Profile__c')) ||
                !leadOrContact.get('Active_HVO_Respondent__c').equals(oldLeadOrContact.get('Active_HVO_Respondent__c'))
            ) {
                if (leadOrContact.get('Is_Incomplete_Profile__c') == false && leadOrContact.get('Active_HVO_Respondent__c') == true) {
                    leadOrContact.put(statusFieldName, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL);
                }
            }
        }
    }

    private SObject updateQualificationTriggerAndDetailWhenMQLWithoutProfileFitment(SObject leadOrContact, String oldStatus) {
        String engagementLevel = (String) leadOrContact.get('Engagement_Level__c');
        Boolean explicitIntentDetected = (Boolean) leadOrContact.get('Explicit_Intent_Detected__c');
        Boolean ifManuallyMQLWithoutProfileFitment = oldStatus == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ENGAGED && leadOrContact.get('Auto_Profile_Fitment__c') == false;
        if (ifManuallyMQLWithoutProfileFitment) {
            if (engagementLevel == 'High' && explicitIntentDetected == true) {
                leadOrContact.put('Qualification_Trigger__c', CRMConstants.QUALIFICATION_TRIGGER_OUTBOUND);
                leadOrContact.put('Qualification_Details__c', CRMConstants.QUALIFICATION_DETAIL_HIGH_ENGAGEMENT_AND_EXPLICIT_INTENT);
            } else if (engagementLevel == 'High') {
                leadOrContact.put('Qualification_Trigger__c', CRMConstants.QUALIFICATION_TRIGGER_OUTBOUND);
                leadOrContact.put('Qualification_Details__c', CRMConstants.QUALIFICATION_DETAIL_HIGH_ENGAGEMENT);
            } else if (explicitIntentDetected == true) {
                leadOrContact.put('Qualification_Trigger__c', CRMConstants.QUALIFICATION_TRIGGER_OUTBOUND);
                leadOrContact.put('Qualification_Details__c', CRMConstants.QUALIFICATION_DETAIL_EXPLICIT_INTENT);
            }
        }
        return leadOrContact;
    }

    public void markAsPersonalEmail(Map<Id, Lead> leadMapOld, List<Lead> leadList) {
        for (Lead lead : leadList) {
            if (leadMapOld == null || leadMapOld.get(lead.Id).Email != lead.Email) {
                if (lead.Email != null && Utility.isPersonalEmail(lead.Email, lead.Id)) {
                    lead.Personal_Email__c = true;
                } else {
                    lead.Personal_Email__c = false;
                }
            }
        }
    }

    public void publishLeadEvent(TriggerParameters tp) {
        List<Lead_Operation__e> eventsToPublish = generateLeadEvent(tp);
        if (eventsToPublish.size() > 0) {
            EventPublisher.publishLeadOperationEvent(eventsToPublish);
        }
    }

    @TestVisible
    private List<Lead_Operation__e> generateLeadEvent(TriggerParameters tp) {
        List<Lead_Operation__e> eventsToGenerate = new List<Lead_Operation__e>();

        String operationType = tp.operationType;

        List<Lead> leadListNeedPublishEvent = (List<Lead>) tp.newList;

        for (Lead lead : leadListNeedPublishEvent) {
            Lead_Operation__e event = new Lead_Operation__e();
            event.Publisher__c = 'CRM';
            event.Record_Id__c = lead.Id;
            event.Object__c = 'Lead';
            event.Operation__c = operationType;

            if (operationType == 'Update') {
                Lead oldLead = (Lead) tp.oldMap.get(lead.Id);
                String changedFields = getChangedFields(oldLead, lead);
                if (String.isEmpty(changedFields)) {
                    continue;
                } else {
                    event.Change_Fields__c = changedFields;
                }
            }
            eventsToGenerate.add(event);
        }

        return eventsToGenerate;
    }

    private String getChangedFields(Lead oldLead, Lead newLead) {
        List<String> changedFields = new List<String>();
        List<String> fields = LEAD_FIELDS_CHANGE_NEED_TO_PUBLISH_EVENT;

        for (String fieldName : fields) {
            if (oldLead.get(fieldName) != newLead.get(fieldName)) {
                changedFields.add(fieldName);
            }
        }

        return String.join(changedFields, ',');
    }

    public void giveEmptyLeadSourceUnknownValue(List<lead> newLeadList) {
        Id currentUserProfileId = System.UserInfo.getProfileId();
        List<Profile> profileDetail = profileSelector.getProfiles(new Set<Id>{ currentUserProfileId });
        if (profileDetail.size() != 1) {
            return;
        }
        String profileName = profileDetail[0].Name;
        for (Lead lead : newLeadList) {
            if (lead.LeadSource == null && lead.RecordTypeId == psLeadId && profileName == 'Marketo Sync') {
                lead.LeadSource = 'Unknown';
            }
        }
    }
    public void updatePartnershipInvolvedFlag(List<Lead> newLeadList) {
        List<Lead> leadsToUpdate = new List<Lead>();
        for (Lead lead : newLeadList) {
            if (CRMConstants.partnershipLeadSource.contains(lead.LeadSource)) {
                leadsToUpdate.add(new Lead(Id = lead.Id, Partnership_Involved__c = true));
            }
        }
        FieldUpdateEventService.buildAndSendFieldUpdateEvents(leadsToUpdate, new Set<String>{ 'Partnership_Involved__c' });
    }

    public void resetEmailInvalid(Map<Id, Lead> leadMapOld, List<Lead> leadListNew) {
        Id userProfileId = (Id) UserInfo.getProfileId();
        Id marketoSyncProfileId = profileSelector.getProfileByName('Marketo Sync').Id;
        if (userProfileId == marketoSyncProfileId)
            return;
        for (Lead lead : leadListNew) {
            Lead oldLead = leadMapOld.get(lead.Id);
            if (oldLead.Email != lead.Email && oldLead.Email_Invalid__c) {
                lead.Email_Invalid__c = false;
            }
        }
    }

    public void updateAQLDurationWhenLeaveAQL(Map<Id, Lead> leadMapOld, List<Lead> leadListNew) {
        for (Lead newLead : leadListNew) {
            Lead oldLead = leadMapOld.get(newLead.Id);
            if (oldLead.Auto_Qualified_Lead__c && !newLead.Auto_Qualified_Lead__c) {
                newLead.AQL_Duration__c = oldLead.Days_as_AQL__c;
            }
        }
    }

    public void clearAQLWhenStageIsNotEngaged(List<SObject> leadOrContacts) {
        if (leadOrContacts == null || leadOrContacts.isEmpty()) {
            return;
        }
        String sobjectType = leadOrContacts.getSObjectType().getDescribe().getName();
        String statusFieldName = sobjectType == 'Lead' ? 'Status' : 'Status__c';
        for (SObject leadOrContact : leadOrContacts) {
            String status = (String) leadOrContact.get(statusFieldName);
            if (status != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ENGAGED && status != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_CONVERT && status != null) {
                leadOrContact.put('Auto_Qualified_Lead__c', false);
            }
        }
    }

    public void updateStatusWhenQualifyOut(List<Lead> newLeadList, Map<Id, Lead> oldLeadMap) {
        for (Lead newLead : newLeadList) {
            if (newLead.Disqualified_Reason__c != null && oldLeadMap.get(newLead.Id).Disqualified_Reason__c != newLead.Disqualified_Reason__c && newLead.RecordTypeId == psLeadId) {
                if (CRMConstants.WILL_NEVER_BUY_OPTIONS.contains(newLead.Disqualified_Reason__c)) {
                    newLead.Status = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_WILL_NEVER_BUY;
                }
                if (CRMConstants.NOT_SALES_READY_OPTIONS.contains(newLead.Disqualified_Reason__c)) {
                    newLead.Status = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY;
                }
                newLead.Disqualification_Date__c = Date.today();
            }
        }
    }

    public void updateMQLEndDateWhenStatusChangeFromMQL(List<Lead> newLeads, Map<Id, Lead> oldLeadsMap) {
        for (Lead newLead : newLeads) {
            if (oldLeadsMap.get(newLead.Id).Status != newLead.Status) {
                if (oldLeadsMap.get(newLead.Id).Status == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL && newLead.Status != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_CONVERT) {
                    newLead.MQL_End_Date__c = TimeUtils.now();
                }
                if (newLead.Status == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    newLead.MQL_End_Date__c = null;
                }
            }
        }
    }

    public void enhanceQualifyInfoWhenConvertToAQLOrMQL(Map<Id, SObject> oldLeadOrContactMap, List<SObject> newLeadOrContacts) {
        String contactOrLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String statusFieldName = contactOrLead.equals('Lead') ? 'Status' : 'Status__c';

        for (SObject newObject : newLeadOrContacts) {
            if (contactOrLead.equals('Contact') && newObject.get('Record_Type_Name__c').equals('Resource')) {
                continue;
            }
            SObject oldObject = oldLeadOrContactMap.get(newObject.Id);
            String oldStatus = (String) oldObject.get(statusFieldName);
            Boolean oldIsAQL = (Boolean) oldObject.get('Auto_Qualified_Lead__c');

            updateQualificationPath(newObject, statusFieldName, false, oldIsAQL, oldStatus);
        }
    }

    public void enhanceQualifyInfoWhenCreateWithAQLOrMQL(List<SObject> newLeadOrContacts) {
        String contactOrLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String statusFieldName = contactOrLead.equals('Lead') ? 'Status' : 'Status__c';

        for (SObject newObject : newLeadOrContacts) {
            if (contactOrLead.equals('Contact') && newObject.get('Record_Type_Name__c').equals('Resource')) {
                continue;
            }

            updateQualificationPath(newObject, statusFieldName, true, null, null);
        }
    }

    private static void updateQualificationPath(SObject newObject, String statusFieldName, Boolean isNewRecord, Boolean oldIsAQL, String oldStatus) {
        String newStatus = (String) newObject.get(statusFieldName);
        Boolean newIsAQL = (Boolean) newObject.get('Auto_Qualified_Lead__c');

        if (newIsAQL && (isNewRecord || !newIsAQL.equals(oldIsAQL))) {
            newObject.put('Recent_Qualification_Path__c', CRMConstants.QUALIFICATION_PATH_OUTBOUND);
            return;
        }

        if (CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL.equals(newStatus) && (isNewRecord || !newStatus.equals(oldStatus))) {
            String recentQualificationPath = (String) newObject.get('Recent_Qualification_Path__c');
            if (CRMConstants.QUALIFICATION_PATH_FAST_TRACK.equals(recentQualificationPath)) {
                return;
            }

            String qualificationTrigger = (String) newObject.get('Qualification_Trigger__c');
            if (CRMConstants.QUALIFICATION_TRIGGER_OUTBOUND.equals(qualificationTrigger)) {
                newObject.put('Recent_Qualification_Path__c', CRMConstants.QUALIFICATION_PATH_OUTBOUND);
                return;
            }

            if (
                qualificationTrigger != null &&
                (qualificationTrigger.contains(CRMConstants.QUALIFICATION_TRIGGER_INBOUND) || qualificationTrigger.contains(CRMConstants.QUALIFICATION_TRIGGER_REFERRAL))
            ) {
                newObject.put('Recent_Qualification_Path__c', CRMConstants.QUALIFICATION_PATH_INBOUND);
                return;
            }

            newObject.put('Recent_Qualification_Path__c', CRMConstants.QUALIFICATION_PATH_OTHERS);
        }
    }

    public void handleContactQualificationInfoWhenConvertLead(Map<Id, Lead> oldLeadMap, Map<Id, Lead> newLeadMap) {
        List<Contact> contactsToUpdate = new List<Contact>();
        List<Lead> leadsToUpdate = new List<Lead>();
        for (Id leadId : newLeadMap.keySet()) {
            Lead newLead = newLeadMap.get(leadId);
            Lead oldLead = oldLeadMap.get(leadId);
            if (newLead.Status == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL && newLead.Status != oldLead.Status && oldLead.Status != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                Lead lead = new Lead(Id = newLead.Id, Recent_Qualification_Path__c = CRMConstants.QUALIFICATION_PATH_SG);
                leadsToUpdate.add(lead);
            }
        }
        if (!leadsToUpdate.isEmpty()) {
            update leadsToUpdate;
        }
    }

    public void setRecycledRelatedFields(Map<Id, SObject> oldLeadOrContactMap, List<SObject> newLeadOrContacts, String objectName) {
        String statusFieldName = objectName == CRMConstants.OBJECT_LEAD ? CRMConstants.LEAD_STATUS_FIELD : CRMConstants.CONTACT_STATUS_FIELD;

        for (SObject leadOrContact : newLeadOrContacts) {
            String newStatus = (String) leadOrContact.get(statusFieldName);
            String oldStatus = (String) oldLeadOrContactMap.get(leadOrContact.Id).get(statusFieldName);
            if (newStatus != oldStatus && newStatus == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_RECYCLED) {
                leadOrContact.put('Date_of_Recycled__c', Date.today());
                leadOrContact.put('Days_Left_Before_Recycled__c', null);
                if (leadOrContact.get('Date_of_Assigned__c') != null && !CRMConstants.COMPLETED_SUBSTAGE_SET.contains((String) leadOrContact.get('SAL_Substage__c'))) {
                    leadOrContact.put('SAL_Substage__c', CRMConstants.SUBSTAGE_RECYCLED);
                }
            }

            String newSubstage = (String) leadOrContact.get('SAL_Substage__c');
            String OldSubstage = (String) oldLeadOrContactMap.get(leadOrContact.Id).get('SAL_Substage__c');
            if (newSubstage != OldSubstage && newSubstage != CRMConstants.SUBSTAGE_NEW_ASSIGNED) {
                leadOrContact.put('Days_Left_Before_Recycled__c', null);
            }
        }
    }

    public void moveToSALWhenSALFlagIsTrue(List<SObject> newPersons, String sObjectType) {
        Boolean isLead = sObjectType == CRMConstants.OBJECT_LEAD;
        for (SObject person : newPersons) {
            if (!isLead && (Boolean) person.get('ContactConverted__c')) {
                continue;
            }
            String statusFieldName = isLead ? 'Status' : 'Status__c';
            if ('Yes'.equalsIgnoreCase((String) person.get('Assign_to_Sales__c')) && !CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL.equals(person.get(statusFieldName))) {
                person.put(statusFieldName, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL);
                person.put('Date_of_Assigned__c', Date.today());
                person.put('SAL_Substage__c', CRMConstants.SUBSTAGE_NEW_ASSIGNED);
                person.put('Recent_Qualification_Path__c', CRMConstants.QUALIFICATION_PATH_SG);
                person.put('Days_Left_Before_Recycled__c', CRMConstants.CYCLE_OF_NEW_ASSIGNED_TO_RECYCLED);
                person.put('SAL_Assigner__c', UserInfo.getUserId());
            }
        }
    }

    public void setFieldsWhenUpdateStatusToSAL(List<SObject> newLeadOrContacts, Map<Id, SObject> oldLeadOrContactsMap) {
        Boolean isLead = newLeadOrContacts[0].getSObjectType().getDescribe().getName() == CRMConstants.OBJECT_LEAD ? true : false;
        String statusFieldName = isLead ? 'Status' : 'Status__c';

        Set<Id> leadOrContactIds = new Set<Id>();
        Boolean isEventUser = commonUserService.isPlatformEventUser();
        User user = mktUserSelector.getCRMUser();

        for (SObject leadOrContact : newLeadOrContacts) {
            String newStatus = (String) leadOrContact.get(statusFieldName);
            SObject oldRecord = oldLeadOrContactsMap.get(leadOrContact.Id);
            String oldStatus = (String) oldRecord.get(statusFieldName);
            Boolean oldLeadConvertFlag = (Boolean) oldRecord.get('leadConvertFlag__c');
            Boolean newLeadConvertFlag = (Boolean) leadOrContact.get('leadConvertFlag__c');
            Id leadOrContactId = (Id) leadOrContact.get('Id');

            if (CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL.equals(newStatus) && !newStatus.equals(oldStatus)) {
                if (!isLead && LeadConvertContext.isContactConvertedFromLead(leadOrContactId)) {
                    continue;
                }
                leadOrContact.put('Date_of_Assigned__c', Date.today());
                if (isEventUser || user.Id.toString().equals(UserInfo.getUserId())) {
                    leadOrContact.put('SAL_Assigner__c', leadOrContact.get('Who_qualify_the_MQL__c'));
                } else {
                    leadOrContact.put('SAL_Assigner__c', UserInfo.getUserId());
                }
                leadOrContactIds.add(leadOrContactId);
            }
        }
        if (leadOrContactIds.isEmpty()) {
            return;
        }
        Map<Id, List<ActivityUnifiedDTO>> whoIdToActivityMap = activityService.getActivitiesByPersonIds(leadOrContactIds);
        setLeadOrContactSubStageAndDate(newLeadOrContacts, whoIdToActivityMap);
    }

    public void setLifeCycleDateWhenStatusChanged(List<SObject> newLeadOrContacts, Map<Id, SObject> oldLeadOrContactsMap) {
        String objectName = newLeadOrContacts[0].getSObjectType().getDescribe().getName();
        String statusFieldName = objectName == CRMConstants.OBJECT_LEAD ? CRMConstants.LEAD_STATUS_FIELD : CRMConstants.CONTACT_STATUS_FIELD;

        for (SObject leadOrContact : newLeadOrContacts) {
            if (objectName == CRMConstants.OBJECT_CONTACT && leadOrContact.get('Is_Merge_Stage__c') != null && (Boolean) leadOrContact.get('Is_Merge_Stage__c')) {
                continue;
            }

            String newStatus = (String) leadOrContact.get(statusFieldName);
            SObject oldRecord = oldLeadOrContactsMap == null ? null : oldLeadOrContactsMap.get(leadOrContact.Id);
            String oldStatus = oldRecord == null ? null : (String) oldRecord.get(statusFieldName);

            if (objectName == CRMConstants.OBJECT_CONTACT && leadOrContact.get('ContactConverted__c') != null && (Boolean) leadOrContact.get('ContactConverted__c') && oldStatus == null) {
                continue;
            }

            if (CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL.equals(newStatus) && !newStatus.equals(oldStatus)) {
                leadOrContact.put('Sales_Accepted_Lifecycle_Date__c', Datetime.now());
            }
        }
    }

    private void setLeadOrContactSubStageAndDate(List<SObject> records, Map<Id, List<ActivityUnifiedDTO>> whoIdToActivityMap) {
        for (SObject record : records) {
            List<ActivityUnifiedDTO> activities = whoIdToActivityMap.get((Id) record.get('Id'));
            if (activities != null && !activities.isEmpty()) {
                record.put('SAL_Substage__c', CRMConstants.SUBSTAGE_ATTEMPTED);
                record.put('Date_of_First_Attempt__c', activityService.getEarliestActivityDate(activities));
            } else {
                record.put('SAL_Substage__c', CRMConstants.SUBSTAGE_NEW_ASSIGNED);
            }
        }
    }

    public void updateLeadOrContactSubStage(List<SObject> newLeadOrContacts, Map<Id, SObject> oldLeadOrContactsMap) {
        for (SObject leadOrContact : newLeadOrContacts) {
            SObject oldLeadOrContact = oldLeadOrContactsMap.get(leadOrContact.Id);
            String newStage = (String) leadOrContact.get('SAL_Substage__c');
            String oldStage = (String) oldLeadOrContact.get('SAL_Substage__c');
            Date today = Date.today();

            if (oldStage == newStage) {
                continue;
            }

            if (newStage == CRMConstants.SUBSTAGE_NEW_ASSIGNED) {
                leadOrContact.put('Date_of_Assigned__c', today);
                leadOrContact.put('Days_Left_Before_Recycled__c', CRMConstants.CYCLE_OF_NEW_ASSIGNED_TO_RECYCLED);
                continue;
            }

            if (newStage == CRMConstants.SUBSTAGE_ATTEMPTED) {
                leadOrContact.put('Date_of_Attempted__c', today);
                continue;
            }

            if (newStage == CRMConstants.SUBSTAGE_CONVERSATION) {
                leadOrContact.put('Date_of_In_Conversation__c', today);
                continue;
            }

            if (CRMConstants.COMPLETED_SUBSTAGE_SET.contains(newStage)) {
                if (oldStage == CRMConstants.SUBSTAGE_NEW_ASSIGNED) {
                    leadOrContact.put('Date_of_Attempted__c', today);
                    leadOrContact.put('Date_of_In_Conversation__c', today);
                }
                if (oldStage == CRMConstants.SUBSTAGE_ATTEMPTED) {
                    leadOrContact.put('Date_of_In_Conversation__c', today);
                }
                leadOrContact.put('Date_of_Completed__c', today);
            }
        }
    }

    public void convertCurrencyFieldFromRingLead(List<SObject> newRecords, Map<Id, SObject> oldRecordsMap, String objectName) {
        if (!UserInfo.getUserName().startsWith(CRMConstants.RINGLEAD_INTEGRATION_USER) && !ZoomInfoClient.inCall) {
            return;
        }

        List<SObject> needConvertRecords = new List<SObject>();
        for (SObject record : newRecords) {
            if (record.get('CurrencyIsoCode') != 'USD') {
                needConvertRecords.add(record);
            }
        }
        if (needConvertRecords.isEmpty()) {
            return;
        }

        List<CurrencyType> currencyTypes = currencySelector.getCurrencyType();
        Map<Object, Object> isoCodeToConversionRate = CollectionUtils.convertListToMap(currencyTypes, CurrencyType.IsoCode, CurrencyType.ConversionRate);
        for (SObject record : needConvertRecords) {
            SObject oldRecord = oldRecordsMap.get(record.Id);
            Decimal conversionRate = (Decimal) isoCodeToConversionRate.get(record.get('CurrencyIsoCode'));
            // In RingLead's Web Submission, Map Fields have `OverWrite` and `UpdateIfBlank` options.
            // In order to convert currency correctly, should handle with different logic
            if (objectName == CRMConstants.OBJECT_LEAD) {
                handleUpdateIfBlankCurrencyFiled(record, oldRecord, Lead.AnnualRevenue, conversionRate);
                handleOverWriteCurrencyField(record, Lead.ZoomInfo_Company_Revenue__c, conversionRate);
            }
            if (objectName == CRMConstants.OBJECT_CONTACT) {
                handleOverWriteCurrencyField(record, Contact.ZoomInfo_Company_Revenue__c, conversionRate);
            }
        }
    }

    private void handleUpdateIfBlankCurrencyFiled(SObject record, SObject oldRecord, Schema.SObjectField fieldName, Decimal conversionRate) {
        Decimal currencyValue = (Decimal) record.get(fieldName);
        Decimal oldCurrencyValue = (Decimal) oldRecord.get(fieldName);
        if (oldCurrencyValue == null && currencyValue != null) {
            record.put(fieldName, conversionRate * currencyValue);
        }
    }

    private void handleOverWriteCurrencyField(SObject record, Schema.SObjectField fieldName, Decimal conversionRate) {
        Decimal currencyValue = (Decimal) record.get(fieldName);
        if (currencyValue != null) {
            record.put(fieldName, conversionRate * currencyValue);
        }
    }

    public void setIsZoomInfoEnrichField(List<SObject> newPersons, Map<Id, SObject> oldPersonMap) {
        if (!UserInfo.getUserName().startsWith(CRMConstants.RINGLEAD_INTEGRATION_USER) && !ZoomInfoClient.inCall) {
            return;
        }
        List<String> enrichFields = new List<String>();
        enrichFields.addAll(CRMConstants.ENRICH_ROW_FIELD_API_NAME);
        enrichFields.addAll(CRMConstants.ZOOM_INFO_FIELD_API_NAME);
        Map<String, Schema.SObjectField> objectFields = newPersons.get(0).getSObjectType().getDescribe().fields.getMap();

        for (SObject newPerson : newPersons) {
            Id newPersonId = (Id) newPerson.get('Id');
            Boolean isEnriched = (Boolean) oldPersonMap.get(newPersonId).get('Is_ZoomInfo_Enrich__c');
            if (isEnriched) {
                continue;
            }
            for (String fieldApiName : enrichFields) {
                if (!objectFields.containsKey(fieldApiName)) {
                    continue;
                }
                Object newValue = newPerson.get(fieldApiName);
                Object oldValue = oldPersonMap.get(newPersonId).get(fieldApiName);
                if (
                    newValue == oldValue ||
                    (newValue == null && ((oldValue instanceof Integer && (Integer) oldValue == 0) || (oldValue instanceof Decimal && ((Decimal) oldValue) == 0))) ||
                    (oldValue == null && ((newValue instanceof Integer && (Integer) newValue == 0) || (newValue instanceof Decimal && ((Decimal) newValue) == 0)))
                ) {
                    continue;
                }
                if (!String.isBlank(String.valueOf(newValue))) {
                    newPerson.put('Is_ZoomInfo_Enrich__c', true);
                    break;
                }
            }
        }
    }

    public void updateLeadContactHVOFlagAfterCampaignUpdated(List<Campaign> newCampaigns, Map<Id, Campaign> oldCampaignMap) {
        Set<Id> campaignIds = new Set<Id>();
        Set<Id> hvoCampaignIds = new Set<Id>();
        for (Campaign newCampaign : newCampaigns) {
            String oldType = oldCampaignMap.get(newCampaign.Id).Type;
            if (oldType == newCampaign.Type) {
                continue;
            }
            if (newCampaign.Type == CRMConstants.CAMPAIGN_HVO_TYPE || oldType == CRMConstants.CAMPAIGN_HVO_TYPE) {
                campaignIds.add(newCampaign.Id);
                if (newCampaign.Type == CRMConstants.CAMPAIGN_HVO_TYPE) {
                    hvoCampaignIds.add(newCampaign.Id);
                }
            }
        }
        if (campaignIds.isEmpty()) {
            return;
        }
        List<CampaignMember> allHVOCampaignMembers = campaignMemberSelector.getAllHVOFlagCampaignMembersByCampaignIds(campaignIds);
        if (allHVOCampaignMembers.isEmpty()) {
            return;
        }
        Set<Id> hvoLeadContactIds = new Set<Id>();
        Set<Id> needReCalculateHVOLeadContactIds = new Set<Id>();
        Set<Id> needUpdateHVOLeadContactIds = new Set<Id>();
        for (CampaignMember member : allHVOCampaignMembers) {
            Id leadOrContactId = member.ContactId != null ? member.ContactId : (member.LeadId != null ? member.LeadId : null);
            if (leadOrContactId == null) {
                continue;
            }
            needReCalculateHVOLeadContactIds.add(leadOrContactId);

            if (hvoCampaignIds.contains(member.CampaignId)) {
                hvoLeadContactIds.add(leadOrContactId);

                if (
                    (member.ContactId != null && (!member.Contact.Active_HVO_Respondent__c || !member.Contact.HVO_Respondent__c)) ||
                    (member.LeadId != null && (!member.Lead.Active_HVO_Respondent__c || !member.Lead.HVO_Respondent__c))
                ) {
                    needUpdateHVOLeadContactIds.add(leadOrContactId);
                }
            }
        }

        List<Contact> toBeUpdateContact = new List<Contact>();
        List<Lead> toBeUpdateLead = new List<Lead>();
        addActiveHvoAsTrueToList(needUpdateHVOLeadContactIds, toBeUpdateContact, toBeUpdateLead);

        needReCalculateHVOLeadContactIds.removeAll(hvoLeadContactIds);
        if (needReCalculateHVOLeadContactIds.isEmpty()) {
            leadSelector.updateLeads(toBeUpdateLead);
            contactSelector.updateContacts(toBeUpdateContact);
            return;
        }
        calculateHVOService.updateHVOForLeadContact(needReCalculateHVOLeadContactIds, null, toBeUpdateLead, toBeUpdateContact);
    }

    public void updateLeadContactHVOFlagBeforeCampaignDeleted(Map<Id, Campaign> campaignMap) {
        Set<Id> deletedCampaignIds = new Set<Id>();
        for (Campaign campaign : campaignMap.values()) {
            if (campaign.Type == CRMConstants.CAMPAIGN_HVO_TYPE) {
                deletedCampaignIds.add(campaign.Id);
            }
        }
        if (deletedCampaignIds.isEmpty()) {
            return;
        }
        List<CampaignMember> deletedCampaignMembers = campaignMemberSelector.getHVOCampaignMembersByCampaignIds(deletedCampaignIds);
        if (deletedCampaignMembers.isEmpty()) {
            return;
        }
        Set<Id> leadContactIds = getLeadContactId(deletedCampaignMembers);
        calculateHVOService.updateHVOForLeadContact(leadContactIds, deletedCampaignIds, null, null);
    }

    public void updateLeadContactHVOFlagAfterMemberStatusUpdated(List<CampaignMember> newMembers, Map<Id, CampaignMember> oldMemberMap) {
        Set<Id> memberIds = getMemberIdsToRecalculateHVO(newMembers, oldMemberMap);
        if (memberIds.isEmpty()) {
            return;
        }

        Map<Id, CampaignMember> campaignMemberMap = new Map<Id, CampaignMember>(campaignMemberSelector.getMemberInfoAndHVOInfoByMemberIds(memberIds));

        Set<Id> hvoLeadContactIds = new Set<Id>();
        Set<Id> needReCalculateHVOLeadContactIds = new Set<Id>();
        Set<Id> needUpdateHVOLeadContactIds = new Set<Id>();
        for (CampaignMember member : campaignMemberMap.values()) {
            Id leadOrContactId = member.ContactId != null ? member.ContactId : (member.LeadId != null ? member.LeadId : null);
            if (leadOrContactId == null) {
                continue;
            }

            if (member.Status == CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS && oldMemberMap.get(member.Id).Status != CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS) {
                hvoLeadContactIds.add(leadOrContactId);
                if (
                    (member.ContactId != null && (!member.Contact.Active_HVO_Respondent__c || !member.Contact.HVO_Respondent__c)) ||
                    (member.LeadId != null && (!member.Lead.Active_HVO_Respondent__c || !member.Lead.HVO_Respondent__c))
                ) {
                    needUpdateHVOLeadContactIds.add(leadOrContactId);
                }
            }

            if (member.Status != CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS && oldMemberMap.get(member.Id).Status == CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS) {
                needReCalculateHVOLeadContactIds.add(leadOrContactId);
            }
        }

        List<Contact> toBeUpdateContact = new List<Contact>();
        List<Lead> toBeUpdateLead = new List<Lead>();
        addActiveHvoAsTrueToList(needUpdateHVOLeadContactIds, toBeUpdateContact, toBeUpdateLead);

        needReCalculateHVOLeadContactIds.removeAll(hvoLeadContactIds);
        if (needReCalculateHVOLeadContactIds.isEmpty()) {
            leadSelector.updateLeads(toBeUpdateLead);
            contactSelector.updateContacts(toBeUpdateContact);
            return;
        }
        calculateHVOService.updateHVOForLeadContact(needReCalculateHVOLeadContactIds, null, toBeUpdateLead, toBeUpdateContact);
    }

    public void updateLeadContactHVOFlagAfterMemberDelete(List<CampaignMember> newMembers) {
        List<CampaignMember> filterMembers = new List<CampaignMember>();
        Set<Id> needReCalculateHVOLeadContactIds = new Set<Id>();
        for (CampaignMember member : newMembers) {
            Id leadOrContactId = member.ContactId != null ? member.ContactId : (member.LeadId != null ? member.LeadId : null);
            if (leadOrContactId != null && member.Status == CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS) {
                needReCalculateHVOLeadContactIds.add(leadOrContactId);
            }
        }
        if (needReCalculateHVOLeadContactIds.isEmpty()) {
            return;
        }
        calculateHVOService.updateHVOForLeadContact(needReCalculateHVOLeadContactIds, null, null, null);
    }

    public void updateLeadContactHVOFlagAfterMemberInsert(List<CampaignMember> newMembers) {
        Set<Id> memberIds = getMemberIdsToRecalculateHVO(newMembers, null);
        if (memberIds.isEmpty()) {
            return;
        }

        List<CampaignMember> campaignMembers = campaignMemberSelector.getHighValueTypeAndInActiveHVOMemberByMemberIds(memberIds);
        List<Lead> updatedLeads = new List<SObject>();
        List<Contact> updatedContacts = new List<SObject>();
        for (CampaignMember campaignMember : campaignMembers) {
            Id contactId = campaignMember.ContactId;
            Id leadId = campaignMember.LeadId;
            if (contactId != null) {
                updatedContacts.add(new Contact(Id = contactId, HVO_Respondent__c = true, Active_HVO_Respondent__c = true));
            } else if (leadId != null) {
                updatedLeads.add(new Lead(Id = leadId, HVO_Respondent__c = true, Active_HVO_Respondent__c = true));
            }
        }
        leadSelector.updateLeads(updatedLeads);
        contactSelector.updateContacts(updatedContacts);
    }

    public void setLeadHVOFlagWhenCLSChanged(List<Lead> newLeads, Map<Id, Lead> oldLeadMap) {
        for (Lead newLead : newLeads) {
            if (newLead.Status != oldLeadMap.get(newLead.Id).Status && (!CRMConstants.ACTIVE_HVO_FLAG_CLS.contains(newLead.Status)) && newLead.Active_HVO_Respondent__c) {
                newLead.Active_HVO_Respondent__c = false;
            }
        }
    }

    private Set<Id> getLeadContactId(List<CampaignMember> campaignMembers) {
        Set<Id> leadContactId = new Set<Id>();
        for (CampaignMember campaignMember : campaignMembers) {
            Id contactId = campaignMember.ContactId;
            Id leadId = campaignMember.LeadId;
            if (contactId != null) {
                leadContactId.add(contactId);
            } else if (leadId != null) {
                leadContactId.add(leadId);
            }
        }
        return leadContactId;
    }

    private Set<Id> getMemberIdsToRecalculateHVO(List<CampaignMember> newMembers, Map<Id, CampaignMember> oldMemberMap) {
        Set<Id> memberIds = new Set<Id>();
        for (CampaignMember member : newMembers) {
            if (
                (member.ContactId != null || member.LeadId != null) &&
                ((oldMemberMap == null && member.Status == CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS) ||
                (oldMemberMap != null &&
                oldMemberMap.get(member.Id).Status != member.Status &&
                (member.Status == CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS ||
                oldMemberMap.get(member.Id).Status == CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS)))
            ) {
                memberIds.add(member.Id);
            }
        }
        return memberIds;
    }

    private void addActiveHvoAsTrueToList(Set<Id> hvoAsTrueLeadContactIds, List<Contact> toBeUpdateContact, List<Lead> toBeUpdateLead) {
        for (Id leadOrContactId : hvoAsTrueLeadContactIds) {
            Boolean isContact = leadOrContactId.getSobjectType().getDescribe().getName() == CRMConstants.OBJECT_CONTACT;
            if (isContact) {
                toBeUpdateContact.add(new Contact(Id = leadOrContactId, HVO_Respondent__c = true, Active_HVO_Respondent__c = true));
            } else {
                toBeUpdateLead.add(new Lead(Id = leadOrContactId, HVO_Respondent__c = true, Active_HVO_Respondent__c = true));
            }
        }
    }

    public void setMismatchRelatedFieldsByOnLead(List<Lead> newLeadList, Map<Id, Lead> oldLeadMap) {
        Id currentUserId = UserInfo.getUserId();
        for (Lead psLead : newLeadList) {
            if (psLead.RecordTypeId == psLeadId) {
                if (psLead.LeanData__Reporting_Matched_Account__c != oldLeadMap.get(psLead.Id).LeanData__Reporting_Matched_Account__c) {
                    psLead.Is_Mismatched__c = false;
                }
                if (psLead.Is_Mismatched__c && !oldLeadMap.get(psLead.Id).Is_Mismatched__c) {
                    psLead.Mismatched_by__c = currentUserId;
                }
                if (!psLead.Is_Mismatched__c) {
                    psLead.Mismatched_by__c = null;
                }
            }
        }
    }

    public void updateLeadToSalWhenLeadChangedFromOtherToMQL(Map<Id, Lead> oldLeadMap, List<Lead> newLeadList) {
        List<Lead> leadsToUpdate = new List<Lead>();
        for (Lead lead : newLeadList) {
            if (oldLeadMap == null) {
                if (lead.Status == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    leadsToUpdate.add(new Lead(Id = lead.Id, Status = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL));
                }
            } else {
                Lead oldLead = oldLeadMap.get(lead.Id);
                if (oldLead.Status != lead.Status && lead.Status == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    leadsToUpdate.add(new Lead(Id = lead.Id, Status = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL));
                }
            }
        }
        if (!leadsToUpdate.isEmpty()) {
            FieldUpdateEventService.buildAndSendFieldUpdateEvents(leadsToUpdate, new Set<String>{ 'Status' });
        }
    }
}
