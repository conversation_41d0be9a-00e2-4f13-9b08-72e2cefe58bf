public interface ITaskService {
    void createTask(Task task);

    void createReminderTask(Task task);

    void updateOriginalSujectField(List<Task> newTasks);

    Integer getOpenTaskCount(Id userId);

    Integer getDelegatedTaskCount(Id userId);

    List<Task> getRecentDueOpenTaskList(Id userId, Integer openTaskAmountLimit);

    List<Task> getRecentDueDelegatedTaskList(Id userId, Integer delagatedTaskAmountLimit);

    void changeTaskStatusToComplete(Id taskId);

    void changeTaskStatusToOrigin(Id taskId, String originStatus);
}
