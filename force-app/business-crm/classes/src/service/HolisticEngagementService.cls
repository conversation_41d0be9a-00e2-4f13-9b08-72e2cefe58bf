public without sharing class HolisticEngagementService {
    private ProfileSelector profileSelector = new ProfileSelector();
    private HolisticEngagementSelector holisticEngagementSelector = new HolisticEngagementSelector();

    public HolisticEngagementService() {
    }

    public HolisticEngagementService(ProfileSelector profileSelector, HolisticEngagementSelector holisticEngagementSelector) {
        this.profileSelector = profileSelector;
        this.holisticEngagementSelector = holisticEngagementSelector;
    }

    public void insertCampaignHolisticEngagement(List<CampaignMember> newCampaignMembers, Map<Id, CampaignMember> oldCampaignMemberMap) {
        List<Holistic_Engagement__c> holisticEngagements = new List<Holistic_Engagement__c>();

        for (CampaignMember newMember : newCampaignMembers) {
            CampaignMember oldMember = oldCampaignMemberMap != null ? oldCampaignMemberMap.get(newMember.Id) : null;
            if (
                (oldMember == null ||
                oldMember.Status != newMember.Status) &&
                newMember.Is_Marketing_Campaign__c &&
                !isMemberOutOfEngagementModel(CampaignMemberContext.channelTypeToEngagedStatusSetMap, newMember.Campaign_Type__c, newMember.Status)
            ) {
                holisticEngagements.add(
                    new Holistic_Engagement__c(
                        Campaign__c = newMember.CampaignId,
                        Campaign_Member_Id__c = newMember.Id,
                        Person__c = newMember.Person__c,
                        Customer_Lifecycle_Stage__c = newMember.Customer_Lifecycle_Stage__c,
                        Engagement_Behavior__c = newMember.Status,
                        Engagement_DateTime__c = newMember.LastModifiedDate,
                        Engagement_Type__c = 'Campaign'
                    )
                );
            }
        }
        holisticEngagementSelector.insertAll(holisticEngagements);
    }

    public void deleteCampaignHolisticEngagementByCampaignMembers(List<CampaignMember> campaignMembers) {
        Set<Id> campaignMemberIds = new Set<Id>();

        for (CampaignMember campaignMember : campaignMembers) {
            campaignMemberIds.add(campaignMember.Id);
        }

        List<Holistic_Engagement__c> holisticEngagements = holisticEngagementSelector.findAllByCampaignMemberIds(campaignMemberIds);
        holisticEngagementSelector.deleteAll(holisticEngagements);
    }

    public void blockUpsertMembersOutOfEngagementModelAndSetMemberEngagedFlag(List<CampaignMember> newCampaignMembers) {
        for (CampaignMember member : newCampaignMembers) {
            String actualStatus = CampaignMemberContext.getActualStatusOfCampaignMember(member);
            String campaignType = member.Campaign_Type__c;
            if (member.Is_Marketing_Campaign__c && isMemberOutOfEngagementModel(CampaignMemberContext.channelTypeToStatusSetMap, campaignType, actualStatus)) {
                member.addError('The ' + actualStatus + ' of ' + campaignType + ' doesn\'t maintain in Engagement Model, Please Contact Campaign Infrastructure.');
            }
            member.Is_Engaged__c = member.Is_Marketing_Campaign__c && !isMemberOutOfEngagementModel(CampaignMemberContext.channelTypeToEngagedStatusSetMap, campaignType, actualStatus);
        }
    }

    private Boolean isMemberOutOfEngagementModel(Map<String, Set<String>> channelTypeToStatus, String campaignType, String actualStatus) {
        return !channelTypeToStatus.containsKey(campaignType) || channelTypeToStatus.get(campaignType) == null || !channelTypeToStatus.get(campaignType).contains(actualStatus);
    }

    public void updateCampaignMemberEngagedByBatchJob(List<Holistic_Engagement_Model__c> holisticEngagementModels, Map<Id, Holistic_Engagement_Model__c> oldHolisticEngagementModelMap) {
        Set<String> channelTypes = new Set<String>();
        Set<String> statuses = new Set<String>();

        for (Holistic_Engagement_Model__c holisticEngagementModel : holisticEngagementModels) {
            if (holisticEngagementModel.Is_Engaged__c != oldHolisticEngagementModelMap.get(holisticEngagementModel.Id).Is_Engaged__c) {
                channelTypes.add(holisticEngagementModel.Channel_Type__c);
                statuses.add(holisticEngagementModel.Status__c);
            }
        }
        if (!channelTypes.isEmpty()) {
            Database.executeBatch(new UpdateCampaignMemberEngagedJob(channelTypes, statuses));
        }
    }

    public void blockDeleteHolisticEngagementModel(List<Holistic_Engagement_Model__c> newHolisticEngagementModels) {
        Profile currentUserProfile = profileSelector.getProfile(UserInfo.getProfileId());
        if (!Constants.TW_ADMIN_USER_PROFILE_NAME.equals(currentUserProfile.Name) && !Constants.TW_POWER_USER_PROFILE_NAME.equals(currentUserProfile.Name)) {
            for (Holistic_Engagement_Model__c newHolisticEngagementModel : newHolisticEngagementModels) {
                newHolisticEngagementModel.addError('Holistic Engagement model' + newHolisticEngagementModel.Name + '(' + newHolisticEngagementModel.Id + ')' + 'should not be deleted');
            }
        }
    }
    public void deleteHolisticEngagementsWhenDeletePerson(List<Person__c> personList) {
        Set<Id> personIds = new Set<Id>();
        for (Person__c person : personList) {
            if (
                person.Type__c.equalsIgnoreCase(CRMConstants.OBJECT_LEAD) || (person.Type__c.equalsIgnoreCase(CRMConstants.OBJECT_CONTACT) && !ContactMergeContext.isMergedContact(person.Contact__c))
            ) {
                personIds.add(person.Id);
            }
        }
        if (personIds.isEmpty()) {
            return;
        }
        List<Holistic_Engagement__c> toBeDeletedHolisticEngagements = holisticEngagementSelector.getHolisticEngagementsByPersonIds(personIds);
        holisticEngagementSelector.deleteHolisticEngagements(toBeDeletedHolisticEngagements);
    }

    public void deleteHolisticEngagementsWhenDeleteCampaign(Map<Id, Campaign> campaignMap) {
        List<Holistic_Engagement__c> toBeDeletedHolisticEngagements = holisticEngagementSelector.getHolisticEngagementsByCampaignIds(campaignMap.keySet());
        holisticEngagementSelector.deleteHolisticEngagements(toBeDeletedHolisticEngagements);
    }

    public void mergeCampaignHolisticEngagementsAfterContactMerge(Set<Id> campaignMemberIdSetBeforeMerge, Map<Id, CampaignMember> campaignMemberMapAfterMerge, Id masterPersonId) {
        List<Id> existCampaignMemberIds = new List<Id>(campaignMemberMapAfterMerge.keySet());
        Map<Object, Object> campaignIdToExistCampaignMemberIdMap = CollectionUtils.convertListToMap((List<SObject>) campaignMemberMapAfterMerge.values(), CampaignMember.CampaignId, CampaignMember.Id);

        List<Holistic_Engagement__c> holisticEngagementList = holisticEngagementSelector.findAllByCampaignMemberIds(campaignMemberIdSetBeforeMerge);
        if (holisticEngagementList.isEmpty()) {
            return;
        }
        for (Holistic_Engagement__c engagementRecord : holisticEngagementList) {
            if (!existCampaignMemberIds.contains(engagementRecord.Campaign_Member_Id__c)) {
                engagementRecord.Campaign_Member_Id__c = (Id) campaignIdToExistCampaignMemberIdMap.get(engagementRecord.Campaign__c);
            }
            engagementRecord.Person__c = masterPersonId;
        }
        holisticEngagementSelector.updateHolisticEngagements(holisticEngagementList);
    }

    public String getCLSForEngagementByEngageTimeAndStatusHistory(Person__c person, Datetime engagementDatetime, Map<Id, List<Person_Field_History__c>> leadOrContactIdToSortedDESCHistoriesMap) {
        String clsForEngagement = null;
        if (engagementDatetime == null || person == null) {
            return clsForEngagement;
        }

        Id leadOrContactId = person.Type__c.equalsIgnoreCase(CRMConstants.OBJECT_CONTACT) ? person.Contact__c : person.Lead__c;
        List<Person_Field_History__c> sortedDESCHistories = leadOrContactIdToSortedDESCHistoriesMap.containsKey(leadOrContactId)
            ? leadOrContactIdToSortedDESCHistoriesMap.get(leadOrContactId)
            : new List<Person_Field_History__c>();
        for (Person_Field_History__c history : sortedDESCHistories) {
            if (engagementDatetime >= history.Created_Date__c) {
                clsForEngagement = history.New_Value__c;
                break;
            }
        }

        if (clsForEngagement == null) {
            if (sortedDESCHistories.isEmpty()) {
                clsForEngagement = person.Customer_Lifecycle_Stage__c;
            } else {
                String oldestOldValue = sortedDESCHistories.get(sortedDESCHistories.size() - 1).Old_Value__c;
                String oldestNewValue = sortedDESCHistories.get(sortedDESCHistories.size() - 1).New_Value__c;
                clsForEngagement = oldestOldValue == null || oldestOldValue == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_CONVERT ? oldestNewValue : oldestOldValue;
            }
        }
        return clsForEngagement;
    }
}
