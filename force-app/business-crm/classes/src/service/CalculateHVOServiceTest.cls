@IsTest
public without sharing class CalculateHVOServiceTest {
    private static final TWPSA_TestFixture_Default testFixture = new TWPSA_TestFixture_Default();
    private static final fflib_ApexMocks mocks = new fflib_ApexMocks();
    private static final ICampaignMemberSelector mockCampaignMemberSelector;
    private static final PersonFieldHistorySelector mockPersonFieldHistorySelector;
    private static final CalculateHVOService calculateHVOService;
    private static final LeadSelector mockLeadSelector;
    private static final IMKTContactSelector mockContactSelector;
    private static final CampaignMemberStatusHistorySelector mockCampaignMemberStatusHistorySelector;

    static {
        mockCampaignMemberSelector = (ICampaignMemberSelector) mocks.mock(ICampaignMemberSelector.class);
        mockPersonFieldHistorySelector = (PersonFieldHistorySelector) mocks.mock(PersonFieldHistorySelector.class);
        mockLeadSelector = (LeadSelector) mocks.mock(LeadSelector.class);
        mockContactSelector = (IMKTContactSelector) mocks.mock(IMKTContactSelector.class);
        mockCampaignMemberStatusHistorySelector = (CampaignMemberStatusHistorySelector) mocks.mock(CampaignMemberStatusHistorySelector.class);
        calculateHVOService = new CalculateHVOService(mockCampaignMemberSelector, mockPersonFieldHistorySelector, mockLeadSelector, mockContactSelector, mockCampaignMemberStatusHistorySelector);
    }
    @IsTest
    static void shouldUpdateHVOAsTrueAndActiveHVOAsFalseForLeadContactWhenGiveAllCampaignMember() {
        Lead lead = new Lead(Id = fflib_IDGenerator.generate(Schema.Lead.SObjectType), HVO_Respondent__c = false, Active_HVO_Respondent__c = false);
        Contact contact = new Contact(Id = fflib_IDGenerator.generate(Schema.Contact.SObjectType), HVO_Respondent__c = false, Active_HVO_Respondent__c = false);
        Set<Id> leadContactIds = new Set<Id>{ lead.Id, contact.Id };

        List<CampaignMember> allCampaignMember = new List<CampaignMember>();
        CampaignMember campaignMemberA = new CampaignMember(
            Id = fflib_IDGenerator.generate(Schema.CampaignMember.SObjectType),
            LeadId = lead.Id,
            ContactId = null,
            Status = CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS
        );
        CampaignMember campaignMemberB = new CampaignMember(
            Id = fflib_IDGenerator.generate(Schema.CampaignMember.SObjectType),
            LeadId = null,
            ContactId = contact.Id,
            Status = CRMConstants.CAMPAIGN_MEMBER_HVO_STATUS
        );
        allCampaignMember.add(campaignMemberA);
        allCampaignMember.add(campaignMemberB);

        Person__c person1 = new Person__c(Id = fflib_IDGenerator.generate(Person__c.SObjectType), Contact__c = contact.Id);
        Person__c person2 = new Person__c(Id = fflib_IDGenerator.generate(Person__c.SObjectType), Lead__c = lead.Id);

        Campaign_Member_Field_History__c CampaignMemberHistory1 = new Campaign_Member_Field_History__c(Campaign_Member_Id__c = campaignMemberA.Id, Person__c = person1.Id);
        Campaign_Member_Field_History__c CampaignMemberHistory2 = new Campaign_Member_Field_History__c(Campaign_Member_Id__c = campaignMemberB.Id, Person__c = person2.Id);
        List<Campaign_Member_Field_History__c> allExpressIntentMemberHistories = new List<Campaign_Member_Field_History__c>();
        allExpressIntentMemberHistories.add(CampaignMemberHistory1);
        allExpressIntentMemberHistories.add(CampaignMemberHistory2);

        Person_Field_History__c leadHistory = new Person_Field_History__c();
        leadHistory.Contact__c = lead.Id;
        leadHistory.Field_Name__c = 'Status';
        leadHistory.Old_Value__c = 'Sales Accepted';
        leadHistory.New_Value__c = 'Recycled';
        leadHistory.Created_Date__c = Datetime.now().addHours(1);

        Person_Field_History__c contactHistory = new Person_Field_History__c();
        contactHistory.Contact__c = lead.Id;
        contactHistory.Field_Name__c = 'Status';
        contactHistory.Old_Value__c = 'Sales Accepted';
        contactHistory.New_Value__c = 'Recycled';
        contactHistory.Created_Date__c = Datetime.now().addHours(-1);

        mocks.startStubbing();
        mocks.when(mockCampaignMemberSelector.getAllHVOCampaignMemberByLeadContactId(leadContactIds)).thenReturn(allCampaignMember);
        mocks.when(mockCampaignMemberStatusHistorySelector.getHVOStatusHistoriesByLeadOrContactIds(leadContactIds)).thenReturn(allExpressIntentMemberHistories);
        mocks.when(mockPersonFieldHistorySelector.getHistoriesByLeadOrConIds(leadContactIds)).thenReturn(new List<Person_Field_History__c>{ leadHistory, contactHistory });
        mocks.stopStubbing();

        calculateHVOService.updateHVOForLeadContact(leadContactIds, null, new List<Lead>(), new List<Contact>());

        fflib_ArgumentCaptor argument1 = fflib_ArgumentCaptor.forClass(List<Lead>.class);
        fflib_ArgumentCaptor argument2 = fflib_ArgumentCaptor.forClass(List<Contact>.class);
        ((LeadSelector) mocks.verify(mockLeadSelector, 1)).updateLeads((List<Lead>) argument1.capture());
        ((IMKTContactSelector) mocks.verify(mockContactSelector, 1)).updateContacts((List<Contact>) argument2.capture());
        List<Lead> updatedLeads = (List<Lead>) argument1.getValue();
        List<Contact> updatedContacts = (List<Contact>) argument2.getValue();

        System.assertEquals(1, updatedLeads.size());
        System.assertEquals(1, updatedContacts.size());
        System.assertEquals(true, updatedLeads[0].HVO_Respondent__c);
        System.assertEquals(true, updatedContacts[0].HVO_Respondent__c);

        System.assertEquals(true, updatedLeads[0].Active_HVO_Respondent__c);
        System.assertEquals(true, updatedContacts[0].Active_HVO_Respondent__c);
    }

    @IsTest
    static void shouldUpdateHVOAsFalseForLeadContactWhenGiveAllCampaignMember() {
        Lead lead = new Lead(Id = fflib_IDGenerator.generate(Schema.Lead.SObjectType), HVO_Respondent__c = true, Active_HVO_Respondent__c = true);
        Contact contact = new Contact(Id = fflib_IDGenerator.generate(Schema.Contact.SObjectType), HVO_Respondent__c = true, Active_HVO_Respondent__c = true);
        Set<Id> leadContactIds = new Set<Id>{ lead.Id, contact.Id };

        mocks.startStubbing();
        mocks.when(mockCampaignMemberSelector.getAllHVOCampaignMemberByLeadContactId(leadContactIds)).thenReturn(new List<CampaignMember>());
        mocks.stopStubbing();

        calculateHVOService.updateHVOForLeadContact(leadContactIds, null, new List<Lead>(), new List<Contact>());

        fflib_ArgumentCaptor argument1 = fflib_ArgumentCaptor.forClass(List<Lead>.class);
        fflib_ArgumentCaptor argument2 = fflib_ArgumentCaptor.forClass(List<Contact>.class);
        ((LeadSelector) mocks.verify(mockLeadSelector, 1)).updateLeads((List<Lead>) argument1.capture());
        ((IMKTContactSelector) mocks.verify(mockContactSelector, 1)).updateContacts((List<Contact>) argument2.capture());
        List<Lead> updatedLeads = (List<Lead>) argument1.getValue();
        List<Contact> updatedContacts = (List<Contact>) argument2.getValue();

        System.assertEquals(1, updatedLeads.size());
        System.assertEquals(1, updatedContacts.size());
        System.assertEquals(false, updatedLeads[0].HVO_Respondent__c);
        System.assertEquals(false, updatedContacts[0].HVO_Respondent__c);

        System.assertEquals(false, updatedLeads[0].Active_HVO_Respondent__c);
        System.assertEquals(false, updatedContacts[0].Active_HVO_Respondent__c);
    }
}
