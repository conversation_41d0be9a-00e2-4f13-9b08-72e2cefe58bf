public without sharing class CRMTriggerContext {
    private static final Map<String, List<Id>> triggerInfoMap = new Map<String, List<Id>>();

    public static void set(String key, List<Id> infoList) {
        triggerInfoMap.put(key, infoList);
    }

    public static List<Id> get(String key) {
        if (!triggerInfoMap.containsKey(key)) {
            return new List<Id>();
        }
        return triggerInfoMap.get(key);
    }
}
