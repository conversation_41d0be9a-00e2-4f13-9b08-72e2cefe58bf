public with sharing class RiskAlarmingService {
    private static final Logger LOGGER = new Logger(RiskAlarmingService.class.getName(), TeamName.CRM);
    private static final String FIELD_AMOUNT_UNPAID = 'AmountUnPaid';
    private static final String FIELD_FINAL_TOTAL = 'FinalTotal';
    private static final String START_DATE_IN_THE_PAST = 'Start date in the past';
    private static final String CLOSE_DATE_IN_PAST = 'Close Date in Past';
    private static final String ENDING_W_O_EXTENSION = 'Ending w/o extension';

    // Warning sign constants
    private static final String TWO_WEEKS_NOT_CONTRACT = 'TWO_WEEKS_NOT_CONTRACT';
    private static final String ONE_MONTH_NOT_PROPOSAL = 'ONE_MONTH_NOT_PROPOSAL';
    private static final String ONE_MONTH_DIFF_OCV_AMOUNT = 'ONE_MONTH_DIFF_OCV_AMOUNT';
    private static final Set<String> EARLY_STAGES = new Set<String>{ 'Qualification', 'Solution Development', 'Proposal', '' };
    private static final Set<String> VERY_EARLY_STAGES = new Set<String>{ 'Qualification', 'Solution Development', '' };
    private final IOpportunitySelector opportunitySelector;
    private final ITimecardSelector timeCardSelector;
    private final ICurrencySelector currencySelector;
    private final ISalesInvoiceSelector invoiceSelector;

    public RiskAlarmingService() {
        this(new OpportunitySelector(), new TimecardSelector(), new CurrencySelector(), new SalesInvoiceSelector());
    }

    public RiskAlarmingService(ISalesInvoiceSelector invoiceSelector, ICurrencySelector currencySelector) {
        this(new OpportunitySelector(), new TimecardSelector(), currencySelector, invoiceSelector);
    }

    public RiskAlarmingService(IOpportunitySelector opportunitySelector, ITimecardSelector timeCardSelector, ICurrencySelector currencySelector) {
        this(opportunitySelector, timeCardSelector, currencySelector, new SalesInvoiceSelector());
    }

    public RiskAlarmingService(IOpportunitySelector opportunitySelector, ITimecardSelector timeCardSelector, ICurrencySelector currencySelector, ISalesInvoiceSelector invoiceSelector) {
        this.opportunitySelector = opportunitySelector;
        this.timeCardSelector = timeCardSelector;
        this.currencySelector = currencySelector;
        this.invoiceSelector = invoiceSelector;
    }

    private Map<Id, Decimal> calculateTotalBillableAmount(List<Opportunity> opportunities) {
        Set<Id> projectIds = CollectionUtils.getIdSet(opportunities, 'pse__Primary_Project__c');
        List<pse__Timecard_Header__c> timeCardHeaders = timeCardSelector.getTimeCardHeaderByProjectIdAndFirstBillableDate(projectIds);
        List<CurrencyType> currencyTypes = currencySelector.getCurrencyType();
        Map<Object, Object> codeRateMap = CollectionUtils.convertListToMap(currencyTypes, CurrencyType.IsoCode, CurrencyType.ConversionRate);

        Map<Id, Decimal> projectToTotalAmountMap = new Map<Id, Decimal>();
        for (pse__Timecard_Header__c timeCardHeader : timeCardHeaders) {
            if (String.isBlank(timeCardHeader.CurrencyIsoCode) || codeRateMap.get(timeCardHeader.CurrencyIsoCode) == null) {
                continue;
            }
            Decimal conversionRate = Decimal.valueOf(codeRateMap.get(timeCardHeader.CurrencyIsoCode).toString());
            if (conversionRate == 0) {
                continue;
            }
            Decimal billableAmount = timeCardHeader.pse__Total_Billable_Amount__c.divide(conversionRate, 10, RoundingMode.HALF_UP);
            if (!projectToTotalAmountMap.containsKey(timeCardHeader.pse__Project__c)) {
                projectToTotalAmountMap.put(timeCardHeader.pse__Project__c, 0);
            }
            Decimal currentAmount = projectToTotalAmountMap.get(timeCardHeader.pse__Project__c);
            projectToTotalAmountMap.put(timeCardHeader.pse__Project__c, currentAmount + billableAmount);
        }
        return projectToTotalAmountMap;
    }

    public Decimal getWorkAtRiskAmount(List<Id> ownerIds, List<String> markerNames, List<String> accountSegments) {
        if (ownerIds == null && markerNames == null || accountSegments == null || accountSegments.isEmpty()) {
            return 0;
        }
        List<Opportunity> opportunities = opportunitySelector.getOpenedPsOpportunitiesAndOcvGreaterThanZeroByOwnerIdsAndMarketNames(ownerIds, markerNames, accountSegments);
        Map<Id, Decimal> projectToTotalAmountMap = calculateTotalBillableAmount(opportunities);
        Decimal totalAmount = 0;
        for (Decimal amount : projectToTotalAmountMap.values()) {
            totalAmount += amount;
        }
        return totalAmount.setScale(2, RoundingMode.HALF_UP);
    }

    public Decimal getCriticalErrorsOpportunityNumbers(List<Id> ownerIds, List<String> marketNames, List<String> forecastingStatuses, List<String> accountSegments) {
        if (ownerIds == null && marketNames == null && forecastingStatuses == null || accountSegments == null || accountSegments.isEmpty()) {
            return 0;
        }
        return opportunitySelector.getSumOpportunityCriticalErrorByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments).size();
    }

    public List<RiskAlarmingCriticalErrorsDTO> getCriticalErrorsOpportunity(List<Id> ownerIds, List<String> marketNames, List<String> forecastingStatuses, List<String> accountSegments) {
        if (ownerIds == null && marketNames == null && forecastingStatuses == null || accountSegments == null || accountSegments.isEmpty()) {
            return new List<RiskAlarmingCriticalErrorsDTO>();
        }
        List<Opportunity> opportunities = opportunitySelector.getSumOpportunityCriticalErrorByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        List<RiskAlarmingCriticalErrorsDTO> criticalErrorsDTOS = new List<RiskAlarmingCriticalErrorsDTO>();
        for (Opportunity opportunity : opportunities) {
            criticalErrorsDTOS.add(new RiskAlarmingCriticalErrorsDTO(getCriticalErrorsStr(opportunity), opportunity));
        }
        return criticalErrorsDTOS;
    }

    private String getCriticalErrorsStr(Opportunity opportunity) {
        List<String> criticalErrorsList = new List<String>();
        if (opportunity.CloseDateinPast__c) {
            criticalErrorsList.add(CLOSE_DATE_IN_PAST);
        }
        if (opportunity.StartDateinPast__c) {
            criticalErrorsList.add(START_DATE_IN_THE_PAST);
        }
        if (opportunity.EndingWOExtension__c) {
            criticalErrorsList.add(ENDING_W_O_EXTENSION);
        }
        return String.join(criticalErrorsList, ', ');
    }

    private List<String> checkWarningTypes(Opportunity opportunity, Decimal convertedAmount) {
        List<String> warnings = new List<String>();
        Date today = Date.today();
        Date twoWeeksFromNow = today.addDays(14);
        Date thirtyDaysFromNow = today.addDays(30);

        Boolean hasContractValue = opportunity.Opportunity_Contract_Value_in_USD__c > 0;
        Boolean closeDateWithinTwoWeeks = opportunity.CloseDate <= twoWeeksFromNow;
        Boolean startDateWithinTwoWeeks = opportunity.Start_Date__c <= twoWeeksFromNow;
        Boolean closeDateWithin30Days = opportunity.CloseDate <= thirtyDaysFromNow;
        Boolean startDateWithin30Days = opportunity.Start_Date__c <= thirtyDaysFromNow;
        Boolean isEarlyStage = EARLY_STAGES.contains(opportunity.StageName);
        Boolean isVeryEarlyStage = VERY_EARLY_STAGES.contains(opportunity.StageName);
        Boolean isOpenOpportunity = !new List<String>{ 'Closed Won', 'Closed Lost' }.contains(opportunity.StageName);

        // ⚠️ Two weeks no contract
        if ((closeDateWithinTwoWeeks || startDateWithinTwoWeeks) && hasContractValue && isEarlyStage && isOpenOpportunity) {
            warnings.add(TWO_WEEKS_NOT_CONTRACT);
        }

        // ⚠️ One month no proposal
        if ((closeDateWithin30Days || startDateWithin30Days) && hasContractValue && isVeryEarlyStage && isOpenOpportunity) {
            warnings.add(ONE_MONTH_NOT_PROPOSAL);
        }

        // ⚠️ One month amount diff
        if (closeDateWithin30Days && isOpenOpportunity) {
            if (convertedAmount != null && convertedAmount != 0) {
                Decimal percentageDiff = ((opportunity.Opportunity_Contract_Value_in_USD__c - convertedAmount) / convertedAmount) * 100;
                if (Math.abs(percentageDiff.round()) >= 30) {
                    warnings.add(ONE_MONTH_DIFF_OCV_AMOUNT);
                }
            } else if (Math.abs(opportunity.Opportunity_Contract_Value_in_USD__c) > 100) {
                warnings.add(ONE_MONTH_DIFF_OCV_AMOUNT);
            }
        }

        return warnings;
    }

    public Decimal getHygieneQualityRating(List<Id> ownerIds, List<String> marketNames, List<String> forecastingStatuses, List<String> accountSegments) {
        if (ownerIds == null && marketNames == null && forecastingStatuses == null || accountSegments == null || accountSegments.isEmpty()) {
            return 0;
        }
        Decimal totalCriticalErrorsNum = opportunitySelector.getSumCriticalErrorNumsByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        if (totalCriticalErrorsNum == null) {
            totalCriticalErrorsNum = 0;
        }
        Decimal openOpportunitiesNum = opportunitySelector.getSumOpenOpportunitiesByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        Decimal endingWOExtensionNum = opportunitySelector.getSumEndingWOExtensionByOwnerIdsAndMarketNames(ownerIds, marketNames, forecastingStatuses, accountSegments);
        if (openOpportunitiesNum + endingWOExtensionNum == 0) {
            return null;
        }
        return 1 - (totalCriticalErrorsNum / (openOpportunitiesNum + endingWOExtensionNum));
    }

    public List<WorkAsRiskDTO> getWorkAsRiskList(List<Id> ownerIds, List<String> marketNames, List<String> accountSegments) {
        if (ownerIds == null && marketNames == null || accountSegments == null || accountSegments.isEmpty()) {
            return new List<WorkAsRiskDTO>();
        }
        List<Opportunity> opportunities = opportunitySelector.getOpenedPsOpportunitiesAndOcvGreaterThanZeroByOwnerIdsAndMarketNames(ownerIds, marketNames, accountSegments);
        Map<Id, Decimal> projectToTotalAmountMap = calculateTotalBillableAmount(opportunities);

        List<WorkAsRiskDTO> result = new List<WorkAsRiskDTO>();
        for (Opportunity opp : opportunities) {
            if (!projectToTotalAmountMap.containsKey(opp.pse__Primary_Project__c)) {
                continue;
            }
            Decimal totalBillableAmountInUSD = projectToTotalAmountMap.get(opp.pse__Primary_Project__c);
            result.add(new WorkAsRiskDTO(opp, totalBillableAmountInUSD.setScale(2, RoundingMode.HALF_UP)));
        }
        return result;
    }

    private Map<Id, Map<String, Decimal>> calculateTotalUnpaidAmount(List<Sales_Invoice__c> invoices) {
        List<CurrencyType> currencyTypes = currencySelector.getCurrencyType();
        Map<Object, Object> codeRateMap = CollectionUtils.convertListToMap(currencyTypes, CurrencyType.IsoCode, CurrencyType.ConversionRate);
        Map<Id, Map<String, Decimal>> invoiceAmountMap = new Map<Id, Map<String, Decimal>>();
        for (Sales_Invoice__c invoice : invoices) {
            if (String.isBlank(invoice.CurrencyIsoCode) || codeRateMap.get(invoice.CurrencyIsoCode) == null) {
                continue;
            }
            Decimal conversionRate = Decimal.valueOf(codeRateMap.get(invoice.CurrencyIsoCode).toString());
            if (conversionRate == 0) {
                continue;
            }
            Map<String, Decimal> amountInfo = new Map<String, Decimal>{
                FIELD_AMOUNT_UNPAID => invoice.Amount_Unpaid__c != null ? invoice.Amount_Unpaid__c.divide(conversionRate, 10, RoundingMode.HALF_UP) : 0,
                FIELD_FINAL_TOTAL => invoice.Final_Total__c != null ? invoice.Final_Total__c.divide(conversionRate, 10, RoundingMode.HALF_UP) : 0
            };
            invoiceAmountMap.put(invoice.Id, amountInfo);
        }
        return invoiceAmountMap;
    }

    public Decimal getOverdueInvoiceAmount(Set<Id> ownerIds, Set<String> marketNames, List<String> accountSegments) {
        if (ownerIds == null && marketNames == null || accountSegments == null || accountSegments.isEmpty()) {
            return 0;
        }
        List<Sales_Invoice__c> invoices = invoiceSelector.getOverdueSalesInvoiceByAccountOwnerOrAccountMarketWithStatus(ownerIds, marketNames, accountSegments);
        Map<Id, Map<String, Decimal>> invoiceAmountMap = calculateTotalUnpaidAmount(invoices);
        Decimal totalAmount = 0;
        for (Map<String, Decimal> amountInfo : invoiceAmountMap.values()) {
            totalAmount += amountInfo.get(FIELD_AMOUNT_UNPAID);
        }
        return totalAmount.setScale(2, RoundingMode.HALF_UP);
    }

    public List<InvoiceOverDueDTO> getOverdueInvoiceList(Set<Id> ownerIds, Set<String> marketNames, List<String> accountSegments) {
        if (ownerIds == null && marketNames == null || accountSegments == null || accountSegments.isEmpty()) {
            return new List<InvoiceOverDueDTO>();
        }
        List<Sales_Invoice__c> invoices = invoiceSelector.getOverdueSalesInvoiceByAccountOwnerOrAccountMarketWithStatus(ownerIds, marketNames, accountSegments);
        Map<Id, Map<String, Decimal>> invoiceAmountMap = calculateTotalUnpaidAmount(invoices);
        List<InvoiceOverDueDTO> result = new List<InvoiceOverDueDTO>();
        for (Sales_Invoice__c invoice : invoices) {
            if (!invoiceAmountMap.containsKey(invoice.Id)) {
                continue;
            }
            Map<String, Decimal> amountInfo = invoiceAmountMap.get(invoice.Id);
            result.add(new InvoiceOverDueDTO(invoice, amountInfo.get(FIELD_AMOUNT_UNPAID).setScale(2, RoundingMode.HALF_UP), amountInfo.get(FIELD_FINAL_TOTAL).setScale(2, RoundingMode.HALF_UP)));
        }
        return result;
    }

    public OpportunityWarningSignBatchResult getPSOpportunitiesWithWarningSignsBatch(
        List<Id> ownerIds,
        List<String> marketNames,
        List<String> forecastingStatuses,
        Id lastId,
        Integer batchSize,
        List<String> accountSegments
    ) {
        if (ownerIds == null && marketNames == null && forecastingStatuses == null || accountSegments == null || accountSegments.isEmpty()) {
            return new OpportunityWarningSignBatchResult(new List<OpportunityWarningSignDTO>(), false, null);
        }

        // Prepare parameters
        List<Id> safeOwnerIds = ownerIds == null ? new List<Id>() : ownerIds;
        List<String> safeMarketNames = marketNames == null ? new List<String>() : marketNames;
        List<String> safeForecastingStatuses = forecastingStatuses == null ? new List<String>() : forecastingStatuses;
        Integer safeBatchSize = (batchSize == null || batchSize <= 0 || batchSize > 30000) ? 30000 : batchSize;

        // Get currency conversion rates once
        List<CurrencyType> currencyTypes = currencySelector.getCurrencyType();
        Map<Object, Object> codeRateMap = CollectionUtils.convertListToMap(currencyTypes, CurrencyType.IsoCode, CurrencyType.ConversionRate);

        // Get a batch of opportunities
        List<Opportunity> batch = opportunitySelector.getPSOpportunitiesWithWarningSignsBatch(safeOwnerIds, safeMarketNames, safeForecastingStatuses, lastId, safeBatchSize, accountSegments);

        // Process this batch
        List<OpportunityWarningSignDTO> results = new List<OpportunityWarningSignDTO>();
        Id newLastId = null;

        for (Opportunity opp : batch) {
            Decimal conversionRate = Decimal.valueOf(codeRateMap.get(opp.CurrencyIsoCode).toString());
            Decimal convertedAmount = opp.Amount.divide(conversionRate, 2, RoundingMode.HALF_UP);

            List<String> warningTypes = checkWarningTypes(opp, convertedAmount);

            if (!warningTypes.isEmpty()) {
                results.add(OpportunityWarningSignDTO.convertFrom(opp, convertedAmount, warningTypes));
            }

            // Keep track of the last Id
            newLastId = opp.Id;
        }

        // Determine if there are more records
        Boolean hasMoreRecords = batch.size() <= safeBatchSize;

        return new OpportunityWarningSignBatchResult(results, hasMoreRecords, newLastId);
    }
}
