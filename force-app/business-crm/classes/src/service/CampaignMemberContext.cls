public with sharing class CampaignMemberContext {

    @TestVisible
    private static HolisticEngagementModelSelector holisticEngagementModelSelector = new HolisticEngagementModelSelector();
    @TestVisible
    private static CampaignMemberStatusSelector campaignMemberStatusSelector = new CampaignMemberStatusSelector();

    public static Map<String, Set<String>> channelTypeToStatusSetMap = new Map<String, Set<String>>();
    public static Map<String, Set<String>> channelTypeToEngagedStatusSetMap = new Map<String, Set<String>>();

    public static Map<Id, Map<String, CampaignMemberStatus>> campaignIdToLabelToMemberStatusMap = new Map<Id, Map<String, CampaignMemberStatus>>();
    public static Map<Id, CampaignMemberStatus> campaignIdToDefaultMemberStatusMap = new Map<Id, CampaignMemberStatus>();

    public static void addEngagementModelAndMemberStatusInfoInContext(List<CampaignMember> newCampaignMemberList, Map<Id, CampaignMember> oldCampaignMemberMap) {

        if (channelTypeToStatusSetMap.isEmpty()) {
            List<Holistic_Engagement_Model__c> models = holisticEngagementModelSelector.findAllModel();
            for (Holistic_Engagement_Model__c model : models) {
                Set<String> statusSet = channelTypeToStatusSetMap.get(model.Channel_Type__c) ?? new Set<String>();
                statusSet.add(model.Status__c);
                channelTypeToStatusSetMap.put(model.Channel_Type__c, statusSet);

                if (model.Is_Engaged__c) {
                    Set<String> engagedStatusSet = channelTypeToEngagedStatusSetMap.get(model.Channel_Type__c) ?? new Set<String>();
                    engagedStatusSet.add(model.Status__c);
                    channelTypeToEngagedStatusSetMap.put(model.Channel_Type__c, engagedStatusSet);
                }
            }
        }

        Set<Id> newCampaignIds = new Set<Id>();
        for (CampaignMember campaignMember : newCampaignMemberList) {
            if (oldCampaignMemberMap.isEmpty() || campaignMember.Status != oldCampaignMemberMap.get(campaignMember.Id).Status || String.isBlank(campaignMember.Status)) {
                newCampaignIds.add(campaignMember.CampaignId);
            }
        }

        if (!campaignIdToLabelToMemberStatusMap.isEmpty()) {
            Set<Id> campaignIdsInContext = campaignIdToLabelToMemberStatusMap.keySet();
            newCampaignIds.removeAll(campaignIdsInContext);
        }

        if (!newCampaignIds.isEmpty()) {
            List<CampaignMemberStatus> allCampaignMemberStatuses = campaignMemberStatusSelector.getCampaignMemberStatusesByCampaignIds(newCampaignIds);
            for (CampaignMemberStatus memberStatus : allCampaignMemberStatuses) {
                if (memberStatus.IsDefault) {
                    campaignIdToDefaultMemberStatusMap.put(memberStatus.CampaignId, memberStatus);
                }
                Map<String, CampaignMemberStatus> labelToMemberStatusMap = campaignIdToLabelToMemberStatusMap.get(memberStatus.CampaignId) ?? new Map<String, CampaignMemberStatus>();
                labelToMemberStatusMap.put(memberStatus.Label, memberStatus);
                campaignIdToLabelToMemberStatusMap.put(memberStatus.CampaignId, labelToMemberStatusMap);
            }
        }
    }

    public static String getActualStatusOfCampaignMember(CampaignMember member) {
        String actualStatus = member.Status;
        if (String.isBlank(member.Status)) {
            CampaignMemberStatus defaultStatus = campaignIdToDefaultMemberStatusMap.get(member.CampaignId);
            if (defaultStatus != null) {
                actualStatus = defaultStatus.Label;
            }
        }
        return actualStatus;
    }
}