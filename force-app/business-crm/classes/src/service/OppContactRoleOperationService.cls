public without sharing class OppContactRoleOperationService {
    private AccountService accountService = new AccountService();
    private OpportunityContactRoleService opportunityContactRoleService = new OpportunityContactRoleService();
    private IOpportunityContactRoleSelector opportunityContactRoleSelector = new OpportunityContactRoleSelector();
    private final CampaignInfluenceService campaignInfluenceService = new CampaignInfluenceService();

    public OppContactRoleOperationService() {
    }

    public OppContactRoleOperationService(AccountService accountService, OpportunityContactRoleService opportunityContactRoleService) {
        this.accountService = accountService;
        this.opportunityContactRoleService = opportunityContactRoleService;
    }

    public void publishOppContactRoleEvent(TriggerParameters tp) {
        List<Opportunity_Contact_Role_Operation__e> eventsToPublish = generateOppContactRoleEvent(tp);
        EventPublisher.publishOppContactRoleOperationEvent(eventsToPublish);
    }

    private List<Opportunity_Contact_Role_Operation__e> generateOppContactRoleEvent(TriggerParameters tp) {
        List<Opportunity_Contact_Role_Operation__e> eventsToGenerate = new List<Opportunity_Contact_Role_Operation__e>();

        String operationType = tp.operationType;
        Map<Id, OpportunityContactRole> oldMap = (Map<Id, OpportunityContactRole>) tp.oldMap;
        Map<Id, OpportunityContactRole> newMap = (Map<Id, OpportunityContactRole>) tp.newMap;

        List<OpportunityContactRole> oppContactRoleList = (oldMap == null) ? newMap.values() : oldMap.values();
        for (Id oppContactRoleId : CollectionUtils.getIdSet(oppContactRoleList)) {
            OpportunityContactRole oldOppContactRole = new OpportunityContactRole();
            if (oldMap != null) {
                oldOppContactRole = oldMap.get(oppContactRoleId);
            }
            OpportunityContactRole newOppContactRole = new OpportunityContactRole();
            if (newMap != null) {
                newOppContactRole = newMap.get(oppContactRoleId);
            }
            Opportunity_Contact_Role_Operation__e event = new Opportunity_Contact_Role_Operation__e();
            event.Operation__c = operationType;
            event.Change_Fields__c = EventUtils.getChangedFields(OpportunityContactRole.SObjectType, oldOppContactRole, newOppContactRole);
            event.Extra__c = EventUtils.constructChangedFieldsValueJson(
                new List<Schema.SObjectField>{ OpportunityContactRole.OpportunityId, OpportunityContactRole.ContactId, OpportunityContactRole.Id },
                oldOppContactRole,
                newOppContactRole
            );
            eventsToGenerate.add(event);
        }
        return eventsToGenerate;
    }

    public void updateNumberOfBuyersWhenAccountIdChange(List<Opportunity_Contact_Role_Operation__e> events) {
        // handle opp delete trigger oppContactRole delete situation
        Set<Id> contactIdSet = new Set<Id>();
        // handle contact delete trigger oppContactRole delete situation
        Set<Id> oppIdSet = new Set<Id>();
        for (Opportunity_Contact_Role_Operation__e event : events) {
            if (event.Operation__c == 'Insert' || event.Operation__c == 'Delete') {
                Map<String, Object> jsonMap = (Map<String, Object>) JSON.deserializeUntyped(event.Extra__c);
                contactIdSet.add((Id) ((Map<String, Object>) jsonMap.get('oldValue')).get('ContactId'));
                contactIdSet.add((Id) ((Map<String, Object>) jsonMap.get('newValue')).get('ContactId'));
                oppIdSet.add((Id) ((Map<String, Object>) jsonMap.get('oldValue')).get('OpportunityId'));
                oppIdSet.add((Id) ((Map<String, Object>) jsonMap.get('newValue')).get('OpportunityId'));
            }
        }
        if (!contactIdSet.isEmpty()) {
            accountService.syncNumberOfBuyersByContactIdsAndOppIds(contactIdSet, oppIdSet);
        }
    }

    public void updateContactRollupOnOpportunity(List<Opportunity_Contact_Role_Operation__e> events) {
        Set<Id> oppIdSet = new Set<Id>();
        for (Opportunity_Contact_Role_Operation__e event : events) {
            if (event.Operation__c == 'Insert' || event.Operation__c == 'Delete') {
                Map<String, Object> jsonMap = (Map<String, Object>) JSON.deserializeUntyped(event.Extra__c);
                oppIdSet.add((Id) ((Map<String, Object>) jsonMap.get('oldValue')).get('OpportunityId'));
                oppIdSet.add((Id) ((Map<String, Object>) jsonMap.get('newValue')).get('OpportunityId'));
            }
        }
        if (!oppIdSet.isEmpty()) {
            opportunityContactRoleService.updateContactRollupInOpportunityFieldForOpportunity(oppIdSet);
        }
    }
}
