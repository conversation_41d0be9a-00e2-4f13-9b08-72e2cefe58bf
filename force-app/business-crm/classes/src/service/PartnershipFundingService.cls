public with sharing class PartnershipFundingService {
    private static CommonUserService userService = new CommonUserService();
    private static IPermissionSetAssignmentSelector permissionSetAssignmentSelector = new PermissionSetAssignmentSelector();
    private static IPartnershipFundingSelector partnershipFundingSelector = new PartnershipFundingSelector();
    private static IPartnershipsSelector partnershipsSelector = new PartnershipsSelector();

    private final static String PARTNERSHIP_PEEMISSION = 'TW_Partnership_Management';
    private final static Map<String, Integer> PHASE_NUMBER_MAP = new Map<String, Integer>{
        '0 - Possible opportunities that can be explored Funding (0%)' => 0,
        '1 - Qualification - Alignment conversations with Sales Team (20%)' => 1,
        '2 - Connection with the Cloud Partner (40%)' => 2,
        '3 - Create Funding SOW and ARR Estimation (60%)' => 3,
        '4 - Funding Request Submitted - Waiting for Approval (80%)' => 4,
        '5 - Funding Request Approved - Project starts (100%)' => 5,
        '6 - Proof of Execution - Project is delivered to the client' => 6,
        '7 - Claim submission for Approval' => 7,
        '8 - Invoice submission' => 8,
        '9 - Payment Received' => 9,
        'Lost Funding Opportunity' => -1
    };

    public Boolean getPartnershipsFundingEditAccess(Id userId) {
        if (userService.isAdminOrPowerUser()) {
            return true;
        }
        if (userIsPartnershipsTeamMember(userId)) {
            return true;
        }

        return false;
    }

    private static Boolean userIsPartnershipsTeamMember(Id userId) {
        List<PermissionSetAssignment> userAssignPartnershipsPermission = permissionSetAssignmentSelector.getByUsersIdAndPermissionSetName(new Set<Id>{ userId }, PARTNERSHIP_PEEMISSION);
        return userAssignPartnershipsPermission.size() > 0;
    }

    public List<Partnership_Funding__c> getPartnershipsFundings(Id partnershipId) {
        return partnershipFundingSelector.getPartnershipsFundings(partnershipId);
    }

    public void syncPartnershipStatusWhenPhaseUpgrade(Map<Id, Partnership_Funding__c> oldFundingMap, List<Partnership_Funding__c> newFundingList) {
        Set<Id> partnershipIdsToUpdate = new Set<Id>();

        for (Partnership_Funding__c newFunding : newFundingList) {
            Integer oldPhase = oldFundingMap != null ? getPhaseNumber(oldFundingMap.get(newFunding.Id).Phase__c) : -1;
            Integer newPhase = getPhaseNumber(newFunding.Phase__c);
            if (newPhase >= 5 && oldPhase < 5) {
                partnershipIdsToUpdate.add(newFunding.Partnership_Id__c);
            }
        }

        if (partnershipIdsToUpdate.isEmpty()) {
            return;
        }

        List<Opportunity_Partnership__c> partnershipsToUpdate = partnershipsSelector.getNotSuccessPartnershipsByIds(partnershipIdsToUpdate);
        if (!partnershipsToUpdate.isEmpty()) {
            for (Opportunity_Partnership__c partnership : partnershipsToUpdate) {
                partnership.Status__c = 'Success';
            }
            update partnershipsToUpdate;
        }
    }

    private Integer getPhaseNumber(String phase) {
        return PHASE_NUMBER_MAP.get(phase) != null ? PHASE_NUMBER_MAP.get(phase) : -1;
    }
}
