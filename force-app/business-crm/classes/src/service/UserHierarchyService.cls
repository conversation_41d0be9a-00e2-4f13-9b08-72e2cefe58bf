public with sharing class UserHierarchyService {
    private UserHierarchySelector userHierarchySelector = new UserHierarchySelector();
    private UserSelector userSelector = new UserSelector();
    private TerritorySelector territorySelector = new TerritorySelector();

    private MKTContactSelector contactSelector = new MKTContactSelector();

    private UserTerritory2AssociationSelector associationSelector = new UserTerritory2AssociationSelector();

    public UserHierarchyDTO getUserHierarchyWithWhitelist(String userId) {
        List<UserTerritory2Association> associations = associationSelector.getAssociationsByManageUser(userId);
        Set<Id> whiteListTerritoryIds = new Set<Id>();
        Set<String> whiteListTerritoryNames = new Set<String>();
        for (UserTerritory2Association association : associations) {
            whiteListTerritoryIds.add(association.Territory2.Id);
            whiteListTerritoryNames.add(association.Territory2.Market_Name__c);
        }
        Boolean isGSTGDO = !whiteListTerritoryIds.isEmpty() && whiteListTerritoryNames.contains('Global Markets');
        Territory2 rootUser = this.territorySelector.getRootUser();
        Id rootUserId = rootUser.ForecastUser.Id;
        String rootEmployeeNumber = rootUser.ForecastUser.EmployeeNumber;

        if (isGSTGDO) {
            return getUserHierarchy(rootUserId, rootEmployeeNumber);
        } else {
            return getUserHierarchy(userId, rootEmployeeNumber);
        }
    }

    public UserHierarchyDTO getUserHierarchy(String userId, String rootEmployeeNumber) {
        User user = userSelector.getById(userId);
        if (user == null) {
            return null;
        }
        String employeeId = user.EmployeeNumber;
        if (employeeId == null) {
            return null;
        }
        Integer depthDistance = 0;
        if (employeeId != rootEmployeeNumber) {
            List<User_Reporting_Hierarchy__c> currentHierarchyWithFullView = [SELECT Depth__c FROM User_Reporting_Hierarchy__c WHERE Ancestor__c = :rootEmployeeNumber AND Child__c = :employeeId];
            if (currentHierarchyWithFullView.isEmpty()) {
                return null;
            }
            depthDistance = currentHierarchyWithFullView.get(0).Depth__c.intValue();
        }

        List<User_Reporting_Hierarchy__c> allHierarchies = userHierarchySelector.getChildrenHierarchies(employeeId);
        Map<String, List<User_Reporting_Hierarchy__c>> parentId2DirectChildNodes = new Map<String, List<User_Reporting_Hierarchy__c>>();
        for (User_Reporting_Hierarchy__c hierarchy : allHierarchies) {
            if (!parentId2DirectChildNodes.containsKey(hierarchy.Parent__c)) {
                parentId2DirectChildNodes.put(hierarchy.Parent__c, new List<User_Reporting_Hierarchy__c>());
            }
            parentId2DirectChildNodes.get(hierarchy.Parent__c).add(hierarchy);
        }

        Set<String> allEmployeeIds = new Set<String>{ employeeId };
        for (User_Reporting_Hierarchy__c hierarchy : allHierarchies) {
            allEmployeeIds.add(hierarchy.Child__c);
        }

        Map<Object, SObject> employeesMap = CollectionUtils.convertListToMap(contactSelector.getContactWithPOHByEmployeeIds(allEmployeeIds), Contact.Employee_ID__c);

        List<Territory2> territories = territorySelector.getTerritoryByEmployeeIds(allEmployeeIds);

        Map<String, Territory2> territoriesMap = new Map<String, Territory2>();
        for (Territory2 territory : territories) {
            territoriesMap.put(territory.ForecastUser.EmployeeNumber, territory);
        }

        List<AggregateResult> employeeOwnOpportunity = [
            SELECT Owner.EmployeeNumber
            FROM Opportunity
            WHERE
                Owner.EmployeeNumber IN :allEmployeeIds
                AND RecordType.Name = :Constants.OPPO_PS_RECORD_TYPE_NAME
                AND (CloseDate = LAST_YEAR
                OR CloseDate = THIS_YEAR
                OR CloseDate = NEXT_YEAR)
                AND Dead_because__c NOT IN :Constants.DEAD_BECAUSE
            GROUP BY Owner.EmployeeNumber
        ];

        Set<String> employeeOwnOpportunitySet = new Set<String>();
        for (AggregateResult res : employeeOwnOpportunity) {
            employeeOwnOpportunitySet.add((String) res.get('EmployeeNumber'));
        }

        List<String> userRoleTargetType = new List<String>{ Constants.GMD_ROLE_BASED_FORECAST, Constants.OCV_ROLE_BASED_FORECAST };
        List<ForecastingType> forecastingTypes = [SELECT Id FROM ForecastingType WHERE MasterLabel IN :userRoleTargetType];
        Set<Id> forecastingTypeIds = CollectionUtils.getIdSet(forecastingTypes);

        //1.is user role target, not territory manager target 2.is GMD_ROLE_BASED_FORECAST and OCV_ROLE_BASED_FORECAST type target
        List<AggregateResult> employeeOwnTarget = [
            SELECT QuotaOwner.EmployeeNumber
            FROM ForecastingQuota
            WHERE
                QuotaOwner.EmployeeNumber IN :allEmployeeIds
                AND (StartDate = LAST_YEAR
                OR StartDate = THIS_YEAR
                OR StartDate = NEXT_YEAR)
                AND ForecastingTypeId IN :forecastingTypeIds
                AND Territory2Id = NULL
            GROUP BY QuotaOwner.EmployeeNumber
        ];

        Set<String> employeeOwnTargetSet = new Set<String>();
        for (AggregateResult res : employeeOwnTarget) {
            employeeOwnTargetSet.add((String) res.get('EmployeeNumber'));
        }

        return buildUserHierarchyTree(employeeId, depthDistance, parentId2DirectChildNodes, employeesMap, territoriesMap, employeeOwnOpportunitySet, employeeOwnTargetSet);
    }

    private UserHierarchyDTO buildUserHierarchyTree(
        String currentEmployeeId,
        Integer depth,
        Map<String, List<User_Reporting_Hierarchy__c>> parentId2DirectChildNodes,
        Map<Object, SObject> employeesMap,
        Map<String, Territory2> territoriesMap,
        Set<String> employeeOwnOpportunitySet,
        Set<String> employeeOwnTargetSet
    ) {
        Contact employee = (Contact) employeesMap.get(currentEmployeeId);
        if (employee == null || employee.pse__Salesforce_User__c == null) {
            return null;
        }
        UserHierarchyDTO currentNode = new UserHierarchyDTO();
        currentNode.employeeId = currentEmployeeId;
        currentNode.depth = depth;
        currentNode.id = employee.pse__Salesforce_User__c;
        currentNode.name = employee.Name;
        currentNode.pohName = employee.POH__r.Name__c;
        currentNode.hasOpportunity = employeeOwnOpportunitySet.contains(currentEmployeeId);
        currentNode.hasTarget = employeeOwnTargetSet.contains(currentEmployeeId);
        List<UserHierarchyDTO> children = new List<UserHierarchyDTO>();
        if (parentId2DirectChildNodes.containsKey(currentEmployeeId)) {
            for (User_Reporting_Hierarchy__c childNode : parentId2DirectChildNodes.get(currentEmployeeId)) {
                UserHierarchyDTO child = buildUserHierarchyTree(
                    childNode.Child__c,
                    depth + 1,
                    parentId2DirectChildNodes,
                    employeesMap,
                    territoriesMap,
                    employeeOwnOpportunitySet,
                    employeeOwnTargetSet
                );
                if (child != null) {
                    children.add(child);
                }
            }
        }
        currentNode.children = children;

        if (currentNode.children.isEmpty()) {
            if (!(currentNode.hasOpportunity || currentNode.hasTarget)) {
                return null;
            }
        }

        Territory2 territory = territoriesMap.get(currentEmployeeId);

        currentNode.type = territory != null
            ? territory.Territory2Type.DeveloperName
            : (currentNode.children.isEmpty() ? Constants.USER_HIERARCHY_TYPE_SALES : Constants.USER_HIERARCHY_TYPE_SALES_MANAGER);

        return currentNode;
    }

    public List<UserHierarchyWithPOHDTO> getAndBuildPohWithHierarchies(String currentEmployeeId) {
        List<User_Reporting_Hierarchy__c> rootHierarchies = userHierarchySelector.getChildrenHierarchies(CRMConstants.ROOT_SALES_EMPLOYEE_ID);
        Map<Object, SObject> rootHierarchiesMap = CollectionUtils.convertListToMap(rootHierarchies, User_Reporting_Hierarchy__c.Child__c);
        List<User_Reporting_Hierarchy__c> hierarchies = userHierarchySelector.getChildrenHierarchies(currentEmployeeId);
        Map<Object, SObject> hierarchiesMap = CollectionUtils.convertListToMap(hierarchies, User_Reporting_Hierarchy__c.Child__c);
        Set<String> allEmployeeIds = new Set<String>{ currentEmployeeId };
        for (User_Reporting_Hierarchy__c hierarchy : hierarchies) {
            allEmployeeIds.add(hierarchy.Child__c);
        }

        List<Contact> employees = contactSelector.getContactWithPOHByEmployeeIds(allEmployeeIds);
        Map<Object, SObject> employeesMap = CollectionUtils.convertListToMap(employees, Contact.Employee_ID__c);

        List<AggregateResult> hasOpportunitiesOwnerMap = [
            SELECT Owner.EmployeeNumber
            FROM Opportunity
            WHERE
                Owner.EmployeeNumber IN :allEmployeeIds
                AND RecordType.Name = :Constants.OPPO_PS_RECORD_TYPE_NAME
                AND (CloseDate = THIS_YEAR
                OR (CloseDate = LAST_YEAR
                AND CALENDAR_QUARTER(CloseDate) IN (3, 4))
                OR (CloseDate = NEXT_YEAR
                AND CALENDAR_QUARTER(CloseDate) IN (1, 2)))
            GROUP BY Owner.EmployeeNumber, Owner.Name
        ];
        Set<String> hasOpportunityEmployeeIdSet = new Set<String>();
        for (AggregateResult res : hasOpportunitiesOwnerMap) {
            hasOpportunityEmployeeIdSet.add((String) res.get('EmployeeNumber'));
        }

        List<UserHierarchyWithPOHDTO> result = new List<UserHierarchyWithPOHDTO>();
        for (Object employeeId : employeesMap.keySet()) {
            User_Reporting_Hierarchy__c hierarchy = (User_Reporting_Hierarchy__c) hierarchiesMap.get(employeeId);
            User_Reporting_Hierarchy__c rootHierarchy = (User_Reporting_Hierarchy__c) rootHierarchiesMap.get(employeeId);
            if (rootHierarchy == null && employeeId != currentEmployeeId) {
                continue;
            }
            Contact employee = (Contact) employeesMap.get(employeeId);
            UserHierarchyWithPOHDTO hierarchyDTO = new UserHierarchyWithPOHDTO();
            hierarchyDTO.id = employee.pse__Salesforce_User__c;
            hierarchyDTO.name = employee.Name;
            hierarchyDTO.employeeId = employee.Employee_ID__c;
            hierarchyDTO.pohName = employee.POH__r.Name__c;
            hierarchyDTO.hasOpportunity = hasOpportunityEmployeeIdSet.contains(employee.Employee_ID__c);
            if (hierarchy != null && rootHierarchy != null) {
                hierarchyDTO.childId = hierarchy.Child__c;
                hierarchyDTO.parentId = hierarchy.Parent__c;
                hierarchyDTO.depth = (Integer) rootHierarchy.Depth__c;
            } else {
                hierarchyDTO.childId = currentEmployeeId;
                hierarchyDTO.parentId = null;
                hierarchyDTO.depth = 0;
            }
            result.add(hierarchyDTO);
        }

        return result;
    }

    public List<UserHierarchyWithPOHDTO> getAndBuildPohWithHierarchies(Id userId) {
        User user = userSelector.getById(userId);
        return getAndBuildPohWithHierarchies(user.EmployeeNumber);
    }

    public List<UserHierarchyWithPOHDTO> getAllPohWithHierarchies() {
        List<User_Reporting_Hierarchy__c> rootHierarchies = userHierarchySelector.getChildrenHierarchies(null);
        Set<String> rootEmployeeIds = new Set<String>();
        for (User_Reporting_Hierarchy__c rootHierarchy : rootHierarchies) {
            rootEmployeeIds.add(rootHierarchy.Child__c);
        }

        List<User_Reporting_Hierarchy__c> allHierarchies = userHierarchySelector.getChildrenHierarchiesByEmployees(rootEmployeeIds);
        Map<String, List<SObject>> allHierarchiesMap = CollectionUtils.groupByStringField(allHierarchies, 'Ancestor__c');

        Set<String> allEmployeeIds = new Set<String>(rootEmployeeIds);
        for (User_Reporting_Hierarchy__c hierarchy : allHierarchies) {
            allEmployeeIds.add(hierarchy.Child__c);
        }

        List<Contact> employees = contactSelector.getContactWithPOHByEmployeeIds(allEmployeeIds);
        Map<Object, SObject> employeesMap = CollectionUtils.convertListToMap(employees, Contact.Employee_ID__c);

        List<UserHierarchyWithPOHDTO> result = new List<UserHierarchyWithPOHDTO>();
        for (String rootEmployeeId : rootEmployeeIds) {
            List<User_Reporting_Hierarchy__c> hierarchies = allHierarchiesMap.get(rootEmployeeId);
            Contact employee = (Contact) employeesMap.get(rootEmployeeId);
            if (employee == null) {
                continue;
            }
            UserHierarchyWithPOHDTO hierarchyDTO = new UserHierarchyWithPOHDTO();
            hierarchyDTO.id = employee.pse__Salesforce_User__c;
            hierarchyDTO.name = employee.Name;
            hierarchyDTO.employeeId = employee.Employee_ID__c;
            hierarchyDTO.pohName = employee.POH__r.Name__c;
            hierarchyDTO.hasOpportunity = false;
            hierarchyDTO.childId = (String) rootEmployeeId;
            hierarchyDTO.parentId = null;
            hierarchyDTO.depth = 0;
            result.add(hierarchyDTO);
            if (hierarchies != null) {
                for (User_Reporting_Hierarchy__c hierarchy : hierarchies) {
                    employee = (Contact) employeesMap.get(hierarchy.Child__c);
                    if (employee == null) {
                        continue;
                    }
                    hierarchyDTO = new UserHierarchyWithPOHDTO();
                    hierarchyDTO.id = employee.pse__Salesforce_User__c;
                    hierarchyDTO.name = employee.Name;
                    hierarchyDTO.employeeId = employee.Employee_ID__c;
                    hierarchyDTO.pohName = employee.POH__r.Name__c;
                    hierarchyDTO.hasOpportunity = false;
                    hierarchyDTO.childId = hierarchy.Child__c;
                    hierarchyDTO.parentId = hierarchy.Parent__c;
                    hierarchyDTO.depth = (Integer) hierarchy.Depth__c;
                    result.add(hierarchyDTO);
                }
            }
        }

        return result;
    }
}
