public with sharing class VoiceOfCustomerService {
    final VoiceOfCustomerSelector voiceOfCustomerSelector = new VoiceOfCustomerSelector();

    public void deleteByContactIds(List<Id> contactIds) {
        Set<Id> needDeleteContactId = new Set<Id>();
        for (Id contactId : contactIds) {
            if (!ContactMergeContext.isMergedContact(contactId)) {
                needDeleteContactId.add(contactId);
            }
        }
        if (needDeleteContactId.isEmpty()) {
            return;
        }
        voiceOfCustomerSelector.deleteByContactIds(needDeleteContactId);
    }

    public void cleanInvalidVoC() {
        List<Voice_of_Customer__c> voiceOfCustomers = [SELECT Id FROM Voice_of_Customer__c WHERE Contact__c = NULL];
        delete voiceOfCustomers;
    }
}
