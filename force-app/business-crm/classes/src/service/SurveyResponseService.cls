public with sharing class SurveyResponseService {
    public static final String SURVEY_DEFAULT_REPLY_TO_EMAIL_ADDRESS = '<EMAIL>';
    public static final String SURVEY_DEFAULT_SENDER_DISPLAY_NAME = 'CRM Team';
    private static final String DEFAULT_NPS_SURVEY_COMMENT = '(none)';
    private static final Logger LOGGER = new Logger(SurveyResponseService.class.getName(), TeamName.CRM);

    private IMKTContactSelector contactSelector = new MKTContactSelector();
    private ILeadSelector leadSelector = new LeadSelector();
    private IAccountSelector accountSelector = new AccountSelector();
    private ISurveyResponseSelector surveyResponseSelector = new SurveyResponseSelector();
    private ICampaignMemberSelector campaignMemberSelector = new CampaignMemberSelector();
    private ICampaignSelector campaignSelector = new CampaignSelector();
    private Id NPSRecordTypeId = Schema.SObjectType.Survey_Response__c.getRecordTypeInfosByName().get('NPS').getRecordTypeId();

    public SurveyResponseService() {
    }

    @TestVisible
    private SurveyResponseService(
        IMKTContactSelector mockContactSelector,
        ILeadSelector mockLeadSelector,
        IAccountSelector mockAccountSelector,
        ISurveyResponseSelector mockSurveyResponseSelector,
        ICampaignMemberSelector mockCampaignMemberSelector
    ) {
        contactSelector = mockContactSelector;
        leadSelector = mockLeadSelector;
        accountSelector = mockAccountSelector;
        surveyResponseSelector = mockSurveyResponseSelector;
        campaignMemberSelector = mockCampaignMemberSelector;
    }

    public void chooseRecordType(List<Survey_Response__c> surveyResps) {
        for (Survey_Response__c resp : surveyResps) {
            if (resp.NPS_Score__c != null || resp.NPS_Comment__c != '') {
                resp.RecordTypeId = NPSRecordTypeId;
            }
        }
    }

    public void link2ContactOrLead(List<Survey_Response__c> surveyResps) {
        Map<Id, List<Survey_Response__c>> contactIds2SurveyResp = new Map<Id, List<Survey_Response__c>>();
        Map<Id, List<Survey_Response__c>> leadId2SurveyResp = new Map<Id, List<Survey_Response__c>>();

        for (Survey_Response__c resp : surveyResps) {
            if (resp.Contact_Id__c != null) {
                List<Survey_Response__c> contactSurveyResps = contactIds2SurveyResp.get(resp.Contact_Id__c);
                if (contactSurveyResps == null) {
                    contactSurveyResps = new List<Survey_Response__c>();
                    contactIds2SurveyResp.put(resp.Contact_Id__c, contactSurveyResps);
                }
                contactSurveyResps.add(resp);
                continue;
            }

            if (resp.Lead_Id__c != null) {
                List<Survey_Response__c> leadSurveyResps = leadId2SurveyResp.get(resp.Lead_Id__c);
                if (leadSurveyResps == null) {
                    leadSurveyResps = new List<Survey_Response__c>();
                    leadId2SurveyResp.put(resp.Lead_Id__c, leadSurveyResps);
                }
                leadSurveyResps.add(resp);
            }
        }

        if (!contactIds2SurveyResp.isEmpty()) {
            List<Contact> contacts = contactSelector.getContactListByContactId(new List<Id>(contactIds2SurveyResp.keySet()));
            for (Contact c : contacts) {
                List<Survey_Response__c> contactSurveyResps = contactIds2SurveyResp.get(c.Id);
                if (contactSurveyResps == null) {
                    continue;
                }
                for (Survey_Response__c resp : contactSurveyResps) {
                    resp.Contact__c = c.Id;
                    resp.Account__c = c.AccountId;
                }
            }
        }

        if (!leadId2SurveyResp.isEmpty()) {
            List<Lead> leads = leadSelector.getLeadsById(leadId2SurveyResp.keySet());
            for (Lead l : leads) {
                List<Survey_Response__c> leadSurveyResps = leadId2SurveyResp.get(l.Id);
                if (leadSurveyResps == null) {
                    continue;
                }
                for (Survey_Response__c resp : leadSurveyResps) {
                    resp.Lead__c = l.Id;
                }
            }
        }
    }

    public List<Messaging.SingleEmailMessage> createNPSSurveyResultNotificationEmails(List<Survey_Response__c> surveyResps) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        Set<Id> surveyIdsNeedSendEmail = new Set<Id>();
        for (Survey_Response__c resp : surveyResps) {
            if (resp.RecordTypeId != NPSRecordTypeId) {
                continue;
            }

            if (resp.Type__c == 'Lead') {
                continue;
            }
            surveyIdsNeedSendEmail.add(resp.Id);
        }

        if (surveyIdsNeedSendEmail.isEmpty()) {
            return emails;
        }
        List<Survey_Response__c> surveiesNeedSendEmail = surveyResponseSelector.getNPSSurveyResponseByIds(surveyIdsNeedSendEmail);
        Set<Id> accountIds = new Set<Id>();
        for (Survey_Response__c resp : surveiesNeedSendEmail) {
            accountIds.add(resp.Contact__r.AccountId);
        }
        Map<Id, Account> accounts = accountSelector.getAccountWithActiveOwner(accountIds);

        for (Survey_Response__c resp : surveiesNeedSendEmail) {
            Id accountId = resp.Contact__r.AccountId;
            Account account = accounts.get(accountId);
            if (account == null) {
                // because account owner is inactive, so skip
                continue;
            }
            String accountOwnerName = account.Owner.Name;
            String accountOwnerEmail = account.Owner.Email;
            if (isDetractorScore(Integer.valueof(resp.NPS_Score__c))) {
                emails.add(createDetractorEmail(resp, account, accountOwnerName, accountOwnerEmail));
                continue;
            }
            emails.add(createNormalNotinicationEmail(resp, account, accountOwnerName, accountOwnerEmail));
        }
        return emails;
    }

    public void fillEmptyResponseDatetimeWithCreatedDate(List<Survey_Response__c> newResponses) {
        for (Survey_Response__c response : newResponses) {
            if (response.Response_Datetime__c == null) {
                response.Response_Datetime__c = Datetime.now();
            }
        }
    }

    private Boolean isDetractorScore(Integer npsScore) {
        return npsScore < 7;
    }

    private Messaging.SingleEmailMessage createDetractorEmail(Survey_Response__c resp, Account account, String accountOwnerName, String accountOwnerEmail) {
        String clientName = resp.Contact__r.Name;
        String accountName = account.Name;
        Boolean isSandbox = getIsSandbox();
        String contactLink = getContactRecordLink(resp.Contact__c, clientName, isSandbox);
        String accountLink = getAccountRecordLink(account, isSandbox);

        Messaging.SingleEmailMessage mail = getDefaultNotificationEmail(accountOwnerEmail);
        mail.setSubject(String.format('NPS Detractor Alert: {0} received an NPS detractor score from {1}', new List<String>{ accountName, clientName }));

        String bodyTemplate =
            '<p>Hi {0},</p>' +
            '<br/>' +
            '<p>We just received a detractor score on the NPS survey from {1}, {2}. Please review the survey result below and follow up with your client to address their feedback.</p>' +
            '<br/>' +
            '<p>Contact Name: <b>{3}</b></p>' +
            '<p>Account Name: <b>{4}</b></p>' +
            '<p>On a scale of 0-10, how likely are you to recommend Thoughtworks to a friend or colleague?: <b>{5}</b></p>' +
            '<p>What is the most important reason for your score?: <b>{6}</b></p>' +
            '<p>Time completed: <b>{7}</b></p>' +
            '<br/>' +
            '<i>As a reminder, scores of 0-6 are classified as “detractors” per the Net Promoter Score method.</i>' +
            '<br/>' +
            '<br/>' +
            '<p>Sent by the CRM Team</p>';
        String body = String.format(
            bodyTemplate,
            new List<Object>{ accountOwnerName, clientName, accountName, contactLink, accountLink, resp.NPS_Score__c, getNPSComment(resp.NPS_Comment__c), resp.CreatedDate }
        );
        mail.setHtmlBody(body);
        return mail;
    }

    private String getNPSComment(String rawComment) {
        if (rawComment == null || rawComment == '') {
            return DEFAULT_NPS_SURVEY_COMMENT;
        }
        return rawComment;
    }

    private Messaging.SingleEmailMessage createNormalNotinicationEmail(Survey_Response__c resp, Account account, String accountOwnerName, String accountOwnerEmail) {
        String clientName = resp.Contact__r.Name;
        String accountName = account.Name;
        Boolean isSanbdox = getIsSandbox();
        String contactLink = getContactRecordLink(resp.Contact__c, clientName, isSanbdox);
        String accountLink = getAccountRecordLink(account, isSanbdox);

        Messaging.SingleEmailMessage mail = getDefaultNotificationEmail(accountOwnerEmail);
        mail.setSubject(String.format('New NPS survey response from {0}, {1}', new List<String>{ clientName, accountName }));

        String bodyTemplate =
            '<p>Hi {0},</p>' +
            '<br/>' +
            '<p>We just received a new NPS survey response from {1}, {2}. Please review the survey result below and follow up with your client to address their feedback, as needed.</p>' +
            '<br/>' +
            '<p>Contact Name: <b>{3}</b></p>' +
            '<p>Account Name: <b>{4}</b></p>' +
            '<p>On a scale of 0-10, how likely are you to recommend Thoughtworks to a friend or colleague?: <b>{5}</b></p>' +
            '<p>What is the most important reason for your score?: <b>{6}</b></p>' +
            '<p>Time completed: <b>{7}</b></p>' +
            '<br/>' +
            '<i>As a reminder, the Net Promoter Score method classifies scores of 0-6 as “detractors”, scores of 7-8 as “passive”, and scores of 9-10 as “promoters”.</i>' +
            '<br/>' +
            '<br/>' +
            '<p>Sent by the CRM Team</p>';
        String body = String.format(
            bodyTemplate,
            new List<Object>{ accountOwnerName, clientName, accountName, contactLink, accountLink, resp.NPS_Score__c, getNPSComment(resp.NPS_Comment__c), resp.CreatedDate }
        );
        mail.setHtmlBody(body);
        return mail;
    }

    private Messaging.SingleEmailMessage getDefaultNotificationEmail(String accountOwnerEmail) {
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();

        mail.setToAddresses(new List<String>{ accountOwnerEmail });

        mail.setReplyTo(SURVEY_DEFAULT_REPLY_TO_EMAIL_ADDRESS);

        mail.setSenderDisplayName(SURVEY_DEFAULT_SENDER_DISPLAY_NAME);

        mail.setBccSender(false);

        mail.setUseSignature(false);

        return mail;
    }

    private String getContactRecordLink(Id contactId, String contactName, Boolean isSandbox) {
        if (isSandbox) {
            String sandboxName = OrganizationInfo.sandBoxName();
            return '<a href="https://thoughtworks--' + sandboxName + '.sandbox.lightning.force.com/lightning/r/Contact/' + contactId + '/view">' + contactName + '</a>';
        } else {
            return '<a href="https://thoughtworks.lightning.force.com/lightning/r/Contact/' + contactId + '/view">' + contactName + '</a>';
        }
    }

    private String getAccountRecordLink(Account acc, Boolean isSandbox) {
        if (isSandbox) {
            String sandboxName = OrganizationInfo.sandBoxName();
            return '<a href="https://thoughtworks--' + sandboxName + '.sandbox.lightning.force.com/lightning/r/Account/' + acc.Id + '/view">' + acc.Name + '</a>';
        } else {
            return '<a href="https://thoughtworks.lightning.force.com/lightning/r/Account/' + acc.Id + '/view">' + acc.Name + '</a>';
        }
    }

    private Boolean getIsSandbox() {
        return OrganizationInfo.isSandbox();
    }

    public void updateCampaignMemberSurveyResponse(List<Survey_Response__c> surveyResps) {
        Set<Id> campaignIds = new Set<Id>();
        Map<Id, Map<Id, Id>> leadContactId2CampaignIdSurveyResponseIdMap = new Map<Id, Map<Id, Id>>();
        for (Survey_Response__c surveyResponse : surveyResps) {
            campaignIds.add(surveyResponse.Campaign__c);
            Id id = surveyResponse.Type__c == 'Lead' ? surveyResponse.Lead__c : surveyResponse.Contact__c;
            if (leadContactId2CampaignIdSurveyResponseIdMap.containsKey(id)) {
                leadContactId2CampaignIdSurveyResponseIdMap.get(id).put(surveyResponse.Campaign__c, surveyResponse.Id);
            } else {
                Map<Id, Id> mapCampaignIdToSurveyResponseId = new Map<Id, Id>{ surveyResponse.Campaign__c => surveyResponse.Id };
                leadContactId2CampaignIdSurveyResponseIdMap.put(id, mapCampaignIdToSurveyResponseId);
            }
        }

        List<CampaignMember> campaignMembers = campaignMemberSelector.getCampaignMembersByCampaignId(campaignIds);
        for (CampaignMember campaignMember : campaignMembers) {
            Id id = campaignMember.Type == 'Lead' ? campaignMember.LeadId : campaignMember.ContactId;
            if (leadContactId2CampaignIdSurveyResponseIdMap.containsKey(id)) {
                campaignMember.Survey_Response__c = leadContactId2CampaignIdSurveyResponseIdMap.get(id).get(campaignMember.CampaignId);
            }
        }

        if (!campaignMembers.isEmpty()) {
            update campaignMembers;
        }
    }

    public Map<String, SurveyResponseCategories> getNPSSurveyCategories(Set<Id> accountIds) {
        List<Survey_Response__c> surveyResponses = surveyResponseSelector.getNPSSurveyResponsesByAccountIds(accountIds);
        return getSurveyResponseCategories(surveyResponses);
    }

    public Map<String, NetPromoterScore> getNPSSurveyNetPromoterScore(Set<Id> accountIds) {
        List<Survey_Response__c> twSurveyResponses = surveyResponseSelector.getNPSSurveyResponses();
        List<Survey_Response__c> accountSurveyResponses = surveyResponseSelector.getNPSSurveyResponsesByAccountIds(accountIds);
        return getNetPromoterScore(twSurveyResponses, accountSurveyResponses);
    }

    public Map<String, NetPromoterScore> getNetPromoterScore(List<Survey_Response__c> twSurveyResponses, List<Survey_Response__c> accountSurveyResponses) {
        Map<String, SurveyResponseCategories> year2twSurveyResponsesMap = getSurveyResponseCategories(twSurveyResponses);
        Map<String, SurveyResponseCategories> year2AccountSurveyResponsesMap = getSurveyResponseCategories(accountSurveyResponses);
        Map<String, NetPromoterScore> yearToNetPromoterScoreMap = new Map<String, NetPromoterScore>();
        for (String year : year2twSurveyResponsesMap.keySet()) {
            NetPromoterScore netPromoterScore = new NetPromoterScore();
            netPromoterScore.year = year;
            if (year2AccountSurveyResponsesMap.containsKey(year)) {
                SurveyResponseCategories accountCategories = year2AccountSurveyResponsesMap.get(year);
                Decimal accountSum = accountCategories.promoter + accountCategories.passive + accountCategories.detractor;
                netPromoterScore.currentAccount = ((accountCategories.promoter / accountSum - accountCategories.detractor / accountSum) * 100).setScale(0);
            } else {
                netPromoterScore.currentAccount = 0;
                netPromoterScore.noResponse = true;
            }
            SurveyResponseCategories twCategories = year2twSurveyResponsesMap.get(year);
            Decimal twSum = twCategories.promoter + twCategories.passive + twCategories.detractor;
            netPromoterScore.twAccount = ((twCategories.promoter / twSum - twCategories.detractor / twSum) * 100).setScale(0);
            yearToNetPromoterScoreMap.put(year, netPromoterScore);
        }
        return yearToNetPromoterScoreMap;
    }

    public Map<String, SurveyResponseCategories> getSurveyResponseCategories(List<Survey_Response__c> surveyResponse) {
        Map<String, SurveyResponseCategories> yearToCategoriesMap = new Map<String, SurveyResponseCategories>();
        for (Survey_Response__c surveyResponseItem : surveyResponse) {
            String year = String.valueOf(surveyResponseItem.Campaign__r.StartDate).substring(0, 4);
            SurveyResponseCategories categories = new SurveyResponseCategories(year, 0, 0, 0);
            if (yearToCategoriesMap.containsKey(year)) {
                categories = yearToCategoriesMap.get(year);
            }
            yearToCategoriesMap.put(year, computerCategories(surveyResponseItem.NPS_Score__c, categories));
        }
        return yearToCategoriesMap;
    }

    public SurveyResponseCategories computerCategories(String score, SurveyResponseCategories categories) {
        SurveyResponseCategories categoriesScore = categories;
        if (Decimal.valueOf(score) > 8) {
            categoriesScore.promoter++;
        } else if (Decimal.valueOf(score) > 6) {
            categoriesScore.passive++;
        } else {
            categoriesScore.detractor++;
        }
        return categoriesScore;
    }

    public List<Survey_Response__c> getNPSSurveyResponseByAccountIds(Set<Id> accountIds) {
        List<Survey_Response__c> surveyResponses = surveyResponseSelector.getNPSSurveyResponsesByAccountIds(accountIds);
        return surveyResponses;
    }

    public List<SurveyResponseDetail> getNewestNPSSurveyResponseByAccountId(String accountId) {
        Map<Id, Contact> id2Contact = contactSelector.getContactsByAccount(accountId);
        Set<Id> contactIds = id2Contact.keySet();
        List<SurveyResponseDetail> results = new List<SurveyResponseDetail>();
        // 1. query out campaigns
        List<Survey_Response__c> srs = surveyResponseSelector.getNewestCampaignIdAndCreateDateByContactIds(contactIds);
        if (srs.size() < 1) {
            return results;
        }
        Id lastCampaignId = srs[0].Campaign__c;
        // 2. query out response results
        List<Survey_Response__c> surveyResponses = surveyResponseSelector.getNPSSurveyResponsesByContactIdsAndCampaignIds(contactIds, new Set<Id>{ lastCampaignId });
        Map<Id, Survey_Response__c> contactId2Resp = new Map<Id, Survey_Response__c>();
        for (Survey_Response__c resp : surveyResponses) {
            contactId2Resp.put(resp.Contact__c, resp);
        }

        // 3. query out all survey people
        List<CampaignMember> cms = campaignMemberSelector.getJoinNPSSurveyCampaignMember(lastCampaignId, accountId, contactId2Resp.keySet());

        for (CampaignMember cm : cms) {
            SurveyResponseDetail srd = new SurveyResponseDetail(
                cm.Contact.Name,
                cm.Contact.Id,
                contactId2Resp.get(cm.Contact.Id),
                cm.Campaign.Status,
                cm.campaignId,
                cm.Campaign.StartDate,
                cm.Contact.AccountId,
                cm.Contact.Account.Name
            );
            results.add(srd);
        }
        return results;
    }

    public List<SurveyResponseDetail> getNPSSurveyResponseByAccountIdAndYear(String accountId, Integer year) {
        // 1. Query The NPS campaign according to the year
        Date startDate = Date.newInstance(year, 1, 1) - 1;
        Date endDate = Date.newInstance(year, 12, 31) + 1;
        List<Campaign> campaigns = campaignSelector.getCampaignByYear(startDate, endDate);
        List<SurveyResponseDetail> results = new List<SurveyResponseDetail>();
        if (campaigns.size() > 1) {
            return results;
        }
        if (campaigns.size() == 0) {
            return results;
        }
        // 2. Query out all survey responses in this campaign
        List<Survey_Response__c> npsResponses = surveyResponseSelector.getNPSSurveyResponsesByCampaignId(campaigns[0].Id);
        Map<String, Id> contactId2SurveryRespAccountId = new Map<String, Id>();
        Map<Id, Survey_Response__c> contactId2Resp = new Map<Id, Survey_Response__c>();
        for (Survey_Response__c resp : npsResponses) {
            contactId2SurveryRespAccountId.put(resp.Contact__c + ',' + resp.Campaign__c, resp.Account__c);
            contactId2Resp.put(resp.Contact__c, resp);
        }

        // 3. Query CampaignMember
        List<CampaignMember> campaignMembers = campaignMemberSelector.getJoinNPSSurveyCampaignMember(campaigns[0].Id, accountId, contactId2Resp.keySet());
        // 4.
        for (CampaignMember cm : campaignMembers) {
            if (contactId2SurveryRespAccountId.containsKey(cm.Contact.Id + ',' + cm.CampaignId) && contactId2SurveryRespAccountId.get(cm.Contact.Id + ',' + cm.CampaignId) != accountId) {
                continue;
            }
            SurveyResponseDetail srd = new SurveyResponseDetail(
                cm.Contact.Name,
                cm.Contact.Id,
                contactId2Resp.get(cm.Contact.Id),
                cm.Campaign.Status,
                cm.campaignId,
                cm.Campaign.StartDate,
                cm.Contact.AccountId,
                cm.Contact.Account.Name
            );
            results.add(srd);
        }
        return results;
    }

    public Map<Id, List<Survey_Response__c>> getContactIdToSurveyResponsesMap(Set<Id> contactIds) {
        List<Survey_Response__c> surveyResponses = surveyResponseSelector.getNPSSurveyResponsesByContactIds(contactIds);
        return (Map<Id, List<Survey_Response__c>>) CollectionUtils.groupByIdField(surveyResponses, Survey_Response__c.Contact__c);
    }

    public List<Survey_Response__c> getSurveyResponsesList(Id contactId) {
        return surveyResponseSelector.getNPSSurveyResponsesByContactIds(new Set<Id>{ contactId });
    }

    // get all survey response years for frontend picklist
    public List<Integer> getNPSSurveyResponseYears(String accountId) {
        // 1. Query out all survey response in this account
        List<CampaignMember> campaignMembers = campaignMemberSelector.getAllNPSCampaignMember();
        // 2. Select all years
        Set<Id> cmContactIds = new Set<Id>();
        for (CampaignMember cm : campaignMembers) {
            cmContactIds.add(cm.Contact.Id);
        }
        Map<String, Id> existSurveyContactId2AccountId = new Map<String, Id>();
        List<Survey_Response__c> existSurveyResps = surveyResponseSelector.getNPSSurveyResponsesByContactIds(cmContactIds);
        for (Survey_Response__c resp : existSurveyResps) {
            existSurveyContactId2AccountId.put(resp.Contact__c + ',' + resp.Campaign__c, resp.Account__c);
        }
        Set<Integer> yearFilter = new Set<Integer>();
        Map<String, Id> contactId2SurveryRespAccountId = new Map<String, Id>();
        for (CampaignMember cm : campaignMembers) {
            if (existSurveyContactId2AccountId.containsKey(cm.Contact.Id + ',' + cm.CampaignId)) {
                if (existSurveyContactId2AccountId.get(cm.Contact.Id + ',' + cm.CampaignId) != accountId) {
                    continue;
                }
                yearFilter.add(cm.Campaign.StartDate.year());
            } else {
                if (cm.Contact.AccountId != accountId) {
                    continue;
                }
                yearFilter.add(cm.Campaign.StartDate.year());
            }
        }
        // 3. Sort desc
        List<Integer> years = new List<Integer>(yearFilter);
        years.sort();
        for (Integer i = 0; i < years.size() / 2; i++) {
            Integer temp = years[i];
            years[i] = years[years.size() - 1 - i];
            years[years.size() - 1 - i] = temp;
        }
        return years;
    }

    public Map<String, Survey_Response__c> getLastSurveyResponseByAccountId(String accountId, Integer year) {
        // 1. Query out all survey response except the year
        List<Survey_Response__c> npsResponses = surveyResponseSelector.getPreviousSurveyResponseByContactIds(accountId, year);
        if (npsResponses.size() < 1) {
            return new Map<String, Survey_Response__c>();
        }
        Map<String, Survey_Response__c> contactId2NearestResponse = new Map<String, Survey_Response__c>();
        // 2. get nearest data to this year
        for (Survey_Response__c src : npsResponses) {
            if (contactId2NearestResponse.containsKey(src.Contact__c) && src.Campaign__r.StartDate.year() <= contactId2NearestResponse.get(src.Contact__c).Campaign__r.StartDate.year()) {
                continue;
            }
            contactId2NearestResponse.put(src.Contact__c, src);
        }
        return contactId2NearestResponse;
    }

    public List<SurveyResponseDetail> getContactNPSSurveyResponses(String contactId) {
        // 1. Query all NPS Campaign's Campaign Member
        List<CampaignMember> campaignMembers = campaignMemberSelector.getAllNPSCampaignByContactId(contactId);
        if (campaignMembers.size() < 1) {
            return new List<SurveyResponseDetail>();
        }
        // 2. Query survey response
        List<Survey_Response__c> responses = surveyResponseSelector.getNPSSurveyResponsesByContactId(contactId);
        Map<Id, Survey_Response__c> campaignId2Resp = new Map<Id, Survey_Response__c>();
        for (Survey_Response__c resp : responses) {
            campaignId2Resp.put(resp.Campaign__r.Id, resp);
        }
        List<SurveyResponseDetail> results = new List<SurveyResponseDetail>();
        for (CampaignMember cm : campaignMembers) {
            SurveyResponseDetail srd = new SurveyResponseDetail(
                cm.Contact.Name,
                cm.Contact.Id,
                campaignId2Resp.get(cm.campaignId),
                cm.Campaign.Status,
                cm.campaignId,
                cm.Campaign.StartDate,
                cm.Contact.AccountId,
                cm.Contact.Account.Name
            );
            results.add(srd);
        }
        return results;
    }
}
