@IsTest(IsParallel=true)
private class TerritoryServiceTest {
    private static final String MOCK_USER_ID = fflib_IDGenerator.generate(User.SObjectType);
    private static TerritoryService territoryService;
    private static fflib_ApexMocks mocks;
    private static TerritorySelector mockTerritorySelector;
    private static UserTerritory2AssociationSelector mockUserTerritory2AssociationSelector;

    static {
        mocks = new fflib_ApexMocks();
        mockTerritorySelector = (TerritorySelector) mocks.mock(TerritorySelector.class);
        mockUserTerritory2AssociationSelector = (UserTerritory2AssociationSelector) mocks.mock(UserTerritory2AssociationSelector.class);
        territoryService = new TerritoryService(mockTerritorySelector, mockUserTerritory2AssociationSelector);
    }

    @IsTest
    static void givenNoMatchingTerritoriesWhenGetSubTerritoriesThenReturnEmptyList() {
        // Given
        Id mock_user_id_2 = fflib_IDGenerator.generate(User.SObjectType);
        List<Territory2> territories = new List<Territory2>{ new Territory2(Id = fflib_IDGenerator.generate(Territory2.SObjectType), Name = 'Territory 1', ForecastUserId = mock_user_id_2) };

        // When
        mocks.startStubbing();
        mocks.when(mockTerritorySelector.getTerritories()).thenReturn(territories);
        mocks.when(mockUserTerritory2AssociationSelector.getAssociationsByManageUser(MOCK_USER_ID)).thenReturn(new List<UserTerritory2Association>());
        mocks.stopStubbing();

        UserTypeTerritoryDTO result = territoryService.getTerritoriesByUserId(MOCK_USER_ID);

        // Then
        String tag = result.type;
        List<TerritoryDTO> territoryDTOS = result.allTerritories;
        Assert.areEqual(0, territoryDTOS.size(), 'Should return empty list when no matching territories');
        ((TerritorySelector) mocks.verify(mockTerritorySelector, 1)).getTerritories();
    }

    @IsTest
    static void givenMatchingTerritoriesWhenGetSubTerritoriesThenReturnHierarchy() {
        // Given
        Id parentId = fflib_IDGenerator.generate(Territory2.SObjectType);
        Id childId = fflib_IDGenerator.generate(Territory2.SObjectType);
        List<Territory2> territories = new List<Territory2>{
            new Territory2(Id = parentId, Market_Name__c = 'Parent Territory', ForecastUserId = MOCK_USER_ID),
            new Territory2(Id = childId, Market_Name__c = 'Child Territory', ParentTerritory2Id = parentId)
        };

        // When
        mocks.startStubbing();
        mocks.when(mockTerritorySelector.getTerritories()).thenReturn(territories);
        mocks.when(mockUserTerritory2AssociationSelector.getAssociationsByManageUser(MOCK_USER_ID)).thenReturn(new List<UserTerritory2Association>());
        mocks.stopStubbing();

        UserTypeTerritoryDTO result = territoryService.getTerritoriesByUserId(MOCK_USER_ID);

        // Then
        String tag = result.type;
        List<TerritoryDTO> territoryDTOS = result.allTerritories;
        Assert.areEqual(1, territoryDTOS.size(), 'Should return one root territory');
        Assert.areEqual('Parent Territory', territoryDTOS[0].name, 'Root territory name should match');
        Assert.areEqual(1, territoryDTOS[0].children.size(), 'Root should have one child');
        Assert.areEqual('Child Territory', territoryDTOS[0].children[0].name, 'Child territory name should match');
    }

    @IsTest
    static void givenCircularReferenceWhenGetSubTerritoriesThenHandleGracefully() {
        // Given
        Id territory1Id = fflib_IDGenerator.generate(Territory2.SObjectType);
        Id territory2Id = fflib_IDGenerator.generate(Territory2.SObjectType);
        List<Territory2> territories = new List<Territory2>{
            new Territory2(Id = territory1Id, Name = 'Territory 1', ForecastUserId = MOCK_USER_ID, ParentTerritory2Id = territory2Id),
            new Territory2(Id = territory2Id, Name = 'Territory 2', ParentTerritory2Id = territory1Id)
        };

        // When
        mocks.startStubbing();
        mocks.when(mockTerritorySelector.getTerritories()).thenReturn(territories);
        mocks.when(mockUserTerritory2AssociationSelector.getAssociationsByManageUser(MOCK_USER_ID)).thenReturn(new List<UserTerritory2Association>());
        mocks.stopStubbing();

        UserTypeTerritoryDTO result = territoryService.getTerritoriesByUserId(MOCK_USER_ID);

        // Then
        String tag = result.type;
        List<TerritoryDTO> territoryDTOS = result.allTerritories;
        Assert.areEqual(0, territoryDTOS.size(), 'Should not return territory despite circular reference');
    }
}
