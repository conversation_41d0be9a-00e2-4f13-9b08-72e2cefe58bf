public without sharing class CalculateHVOService {
    private IMKTContactSelector contactSelector = new MKTContactSelector();
    private LeadSelector leadSelector = new LeadSelector();
    private ICampaignMemberSelector campaignMemberSelector = new CampaignMemberSelector();
    private PersonFieldHistorySelector personFieldHistorySelector = new PersonFieldHistorySelector();
    private CampaignMemberStatusHistorySelector campaignMemberStatusHistorySelector = new CampaignMemberStatusHistorySelector();

    public CalculateHVOService() {
    }
    public CalculateHVOService(
        ICampaignMemberSelector campaignMemberSelector,
        PersonFieldHistorySelector personFieldHistorySelector,
        LeadSelector leadSelector,
        IMKTContactSelector contactSelector,
        CampaignMemberStatusHistorySelector campaignMemberStatusHistorySelector
    ) {
        this.campaignMemberSelector = campaignMemberSelector;
        this.leadSelector = leadSelector;
        this.contactSelector = contactSelector;
        this.personFieldHistorySelector = personFieldHistorySelector;
        this.campaignMemberStatusHistorySelector = campaignMemberStatusHistorySelector;
    }

    public void updateHVOForLeadContact(Set<Id> leadContactIds, Set<Id> campaignIds, List<Lead> toBeUpdateLeads, List<Contact> toBeUpdateContacts) {
        if (toBeUpdateLeads == null) {
            toBeUpdateLeads = new List<Lead>();
        }
        if (toBeUpdateContacts == null) {
            toBeUpdateContacts = new List<Contact>();
        }
        List<CampaignMember> allCampaignMembers = new List<CampaignMember>();
        if (campaignIds == null) {
            allCampaignMembers = campaignMemberSelector.getAllHVOCampaignMemberByLeadContactId(leadContactIds);
        } else {
            allCampaignMembers = campaignMemberSelector.getCampaignMemberByLeadContactIdAndNotInCampaignIds(leadContactIds, campaignIds);
        }

        Set<Id> leadOrContactIdsWithHVOMembers = new Set<Id>();
        Set<Id> memberIds = new Set<Id>();
        Map<Id, Campaign_Member_Field_History__c> leadContactIdToLatestMemberStatusHistoryMap = new Map<Id, Campaign_Member_Field_History__c>();
        Map<Id, Person_Field_History__c> leadContactIdToLatestPersonFieldHistory = new Map<Id, Person_Field_History__c>();

        if (!allCampaignMembers.isEmpty()) {

            for (CampaignMember member : allCampaignMembers) {
                Id leadOrContactId = member.ContactId != null ? member.ContactId : (member.LeadId != null ? member.LeadId : null);
                if (leadOrContactId == null) {
                    continue;
                }

                memberIds.add(member.Id);
                leadOrContactIdsWithHVOMembers.add(leadOrContactId);
            }

            //其中一些history可能是非HVO campaign下的member histories
            List<Campaign_Member_Field_History__c> allExpressIntentMemberHistories = campaignMemberStatusHistorySelector.getHVOStatusHistoriesByLeadOrContactIds(leadContactIds);

            leadContactIdToLatestMemberStatusHistoryMap = getLeadOrContactIdToLatestMemberFieldHistoryMap(allExpressIntentMemberHistories, memberIds);

            List<Person_Field_History__c> personHistories = personFieldHistorySelector.getHistoriesByLeadOrConIds(leadContactIds);
            Map<Id, List<Person_Field_History__c>> allLeadContactIdToPersonFieldHistories = CollectionUtils.groupByIdField(personHistories, 'Person_Id__c');
            leadContactIdToLatestPersonFieldHistory = getLeadContactIdToLatestPersonHistory(allLeadContactIdToPersonFieldHistories);
        }
        addToBeUpdateHVOFlag(leadContactIds, leadOrContactIdsWithHVOMembers, leadContactIdToLatestMemberStatusHistoryMap, leadContactIdToLatestPersonFieldHistory, toBeUpdateLeads, toBeUpdateContacts);
        leadSelector.updateLeads(toBeUpdateLeads);
        contactSelector.updateContacts(toBeUpdateContacts);
    }

    private Map<Id, Campaign_Member_Field_History__c> getLeadOrContactIdToLatestMemberFieldHistoryMap(List<Campaign_Member_Field_History__c> allExpressIntentMemberHistories, Set<Id> memberIds) {
        Map<Id, Campaign_Member_Field_History__c> leadContactIdToMemberFieldHistoryMap = new Map<Id, Campaign_Member_Field_History__c>();
        for (Campaign_Member_Field_History__c history : allExpressIntentMemberHistories) {
            Id leadOrContactId = history.Person__r.Contact__c != null ? history.Person__r.Contact__c : (history.Person__r.Lead__c != null ? history.Person__r.Lead__c : null);
            if (leadOrContactId == null) {
                continue;
            }

            if (history.Campaign_Member_Id__c != null && memberIds.contains(history.Campaign_Member_Id__c)) {
                if (!leadContactIdToMemberFieldHistoryMap.containsKey(leadOrContactId)) {
                    leadContactIdToMemberFieldHistoryMap.put(leadOrContactId, history);
                }
            }
        }
        return leadContactIdToMemberFieldHistoryMap;
    }

    private void addToBeUpdateHVOFlag(
        Set<Id> leadOrContactIds,
        Set<Id> leadOrContactIdsWithHVOMembers,
        Map<Id, Campaign_Member_Field_History__c> leadContactIdToLatestMemberStatusHistoryMap,
        Map<Id, Person_Field_History__c> leadContactIdToLatestPersonFieldHistoryMap,
        List<Lead> toBeUpdateLeads,
        List<Contact> toBeUpdateContacts
    ) {
        for (Id leadOrContactId : leadOrContactIds) {
            Boolean hvoRespondent = leadOrContactIdsWithHVOMembers.contains(leadOrContactId);
            Boolean activeHvoRespondent =
                leadOrContactIdsWithHVOMembers.contains(leadOrContactId) &&
                isActiveHVO(leadContactIdToLatestMemberStatusHistoryMap.get(leadOrContactId), leadContactIdToLatestPersonFieldHistoryMap.get(leadOrContactId));

            Boolean isContact = leadOrContactId.getSobjectType().getDescribe().getName() == CRMConstants.OBJECT_CONTACT;
            if (isContact) {
                toBeUpdateContacts.add(new Contact(Id = leadOrContactId, HVO_Respondent__c = hvoRespondent, Active_HVO_Respondent__c = activeHvoRespondent));
            } else {
                toBeUpdateLeads.add(new Lead(Id = leadOrContactId, HVO_Respondent__c = hvoRespondent, Active_HVO_Respondent__c = activeHvoRespondent));
            }
        }
    }

    private Boolean isActiveHVO(Campaign_Member_Field_History__c latestMemberStatusHistory, Person_Field_History__c latestPersonFieldHistory) {
        if (latestMemberStatusHistory == null || latestPersonFieldHistory == null) {
            return true;
        }

        if (latestMemberStatusHistory.Created_Date__c > latestPersonFieldHistory.Created_Date__c) {
            return true;
        }
        return false;
    }

    private Map<Id, Person_Field_History__c> getLeadContactIdToLatestPersonHistory(Map<Id, List<Person_Field_History__c>> allLeadContactIdToPersonFieldHistories) {
        Map<Id, Person_Field_History__c> filteredIdToInActivePersonFieldHistory = new Map<Id, Person_Field_History__c>();

        for (Id personId : allLeadContactIdToPersonFieldHistories.keySet()) {
            List<Person_Field_History__c> histories = allLeadContactIdToPersonFieldHistories.get(personId);
            if (histories.isEmpty()) {
                continue;
            }

            Person_Field_History__c firstInActiveCls = findFirstInActiveRecord(histories);
            if (firstInActiveCls == null) {
                continue;
            }

            Person_Field_History__c lastTimeHistory = (histories.size() > 1) ? histories[1] : null;
            if (lastTimeHistory == null || !CRMConstants.CUSTOMER_LIFECYCLE_STAGE_CONVERT.equals(lastTimeHistory.New_Value__c)) {
                filteredIdToInActivePersonFieldHistory.put(personId, firstInActiveCls);
            } else {
                Person_Field_History__c beforeConvertHistory = (histories.size() > 2) ? histories[2] : null;
                if (beforeConvertHistory == null) {
                    continue;
                }
                if (beforeConvertHistory.New_Value__c == firstInActiveCls.New_Value__c) {
                    filteredIdToInActivePersonFieldHistory.put(personId, beforeConvertHistory);
                } else {
                    filteredIdToInActivePersonFieldHistory.put(personId, firstInActiveCls);
                }
            }
        }
        return filteredIdToInActivePersonFieldHistory;
    }

    private Person_Field_History__c findFirstInActiveRecord(List<Person_Field_History__c> histories) {
        List<String> activeHvoCls = new List<String>{ CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL, CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ENGAGED };
        for (Person_Field_History__c history : histories) {
            if (!activeHvoCls.contains(history.New_Value__c)) {
                return history;
            }
        }
        return null;
    }
}
