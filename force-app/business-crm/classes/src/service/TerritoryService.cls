public without sharing class TerritoryService {
    private static final String GLOBAL_MARKETS = 'Global Markets';
    private final static Logger LOGGER = new Logger(TerritoryService.class.getName(), TeamName.Sales);

    private static final Integer MAX_DEPTH = 50;
    private static final String IS_SALES = 'isSales';
    private static final String IS_MANAGER = 'isManager';
    private static final String IS_GSTGDO = 'isGSTGDO';
    private static final String IS_MMTSST = 'isMMTSST';
    private static final String IS_MANAGER_AND_WHITE_LIST_USER = 'isManagerAndWhiteListUser';
    private TerritorySelector territorySelector = new TerritorySelector();
    private UserTerritory2AssociationSelector associationSelector = new UserTerritory2AssociationSelector();

    public TerritoryService() {
    }
    public TerritoryService(TerritorySelector MockTerritorySelector, UserTerritory2AssociationSelector mockUserTerritory2AssociationSelector) {
        territorySelector = MockTerritorySelector;
        associationSelector = mockUserTerritory2AssociationSelector;
    }

    public Territory2 getTerritoryByName(String territoryName) {
        return territorySelector.getTerritoryByName(territoryName);
    }

    public UserTypeTerritoryDTO getTerritoriesByUserId(Id userId) {
        String type = IS_SALES;

        List<Territory2> territories = territorySelector.getTerritories();
        Set<Id> manageTerritoryIds = new Set<Id>();
        for (Territory2 territory : territories) {
            if (territory.ForecastUserId == userId) {
                manageTerritoryIds.add(territory.Id);
            }
        }

        //Forecast Manager既是真正的 manager，但是也存在于白名单中，当做Forecast Manager处理，因为getAssociationsByManageUser不查既是白名单又是 manager 的人
        List<UserTerritory2Association> associations = associationSelector.getAssociationsByManageUser(userId);
        Set<Id> whiteListTerritoryIds = new Set<Id>();
        Set<String> whiteListTerritoryNames = new Set<String>();
        for (UserTerritory2Association association : associations) {
            whiteListTerritoryIds.add(association.Territory2.Id);
            whiteListTerritoryNames.add(association.Territory2.Market_Name__c);
        }

        if (manageTerritoryIds.isEmpty() && whiteListTerritoryIds.isEmpty()) {
            return new UserTypeTerritoryDTO(type, new List<TerritoryDTO>());
        }

        if (!manageTerritoryIds.isEmpty() && !whiteListTerritoryIds.isEmpty()) {
            type = IS_MANAGER_AND_WHITE_LIST_USER;
        } else if (!whiteListTerritoryIds.isEmpty()) {
            type = whiteListTerritoryNames.contains(GLOBAL_MARKETS) ? IS_GSTGDO : IS_MMTSST;
        } else if (!manageTerritoryIds.isEmpty()) {
            type = IS_MANAGER;
        }

        return buildTree(manageTerritoryIds, whiteListTerritoryIds, territories, type);
    }

    public List<String> getAllMarketDirectorByUserId() {
        List<Territory2> territory2 = territorySelector.getAllMarketDirectorByUserId();
        List<String> userIdList = new List<String>();
        for (Territory2 territory : territory2) {
            userIdList.add(String.valueOf(territory.ForecastUserId));
        }
        return userIdList;
    }

    private UserTypeTerritoryDTO buildTree(Set<Id> manageTerritoryIds, Set<Id> whiteListTerritoryIds, List<Territory2> territories, String type) {
        if (type.equals(IS_MANAGER_AND_WHITE_LIST_USER)) {
            return buildTreeForManagerAndWhiteListUser(manageTerritoryIds, whiteListTerritoryIds, territories, type);
        }
        return buildTreeForManagerOrGSTOrMMT(manageTerritoryIds, whiteListTerritoryIds, territories, type);
    }

    private UserTypeTerritoryDTO buildTreeForManagerOrGSTOrMMT(Set<Id> manageTerritoryIds, Set<Id> whiteListTerritoryIds, List<Territory2> territories, String type) {
        Set<Id> allTerritoryIds = new Set<Id>();
        allTerritoryIds.addAll(manageTerritoryIds);
        allTerritoryIds.addAll(whiteListTerritoryIds);

        List<TerritoryDTO> allMarketsTrees = getTerritoryTrees(territories, allTerritoryIds);

        return new UserTypeTerritoryDTO(type, allMarketsTrees);
    }

    private UserTypeTerritoryDTO buildTreeForManagerAndWhiteListUser(Set<Id> manageTerritoryIds, Set<Id> whiteListTerritoryIds, List<Territory2> territories, String type) {
        Set<Id> allTerritoryIds = new Set<Id>();
        allTerritoryIds.addAll(manageTerritoryIds);
        allTerritoryIds.addAll(whiteListTerritoryIds);
        List<TerritoryDTO> managerMarketsTrees = getTerritoryTrees(territories, manageTerritoryIds);
        List<TerritoryDTO> allMarketsTrees = getTerritoryTrees(territories, allTerritoryIds);
        return new UserTypeTerritoryDTO(type, allMarketsTrees, managerMarketsTrees);
    }

    private List<TerritoryDTO> getTerritoryTrees(List<Territory2> allTerritories, Set<Id> ownTerritoryIds) {
        if (allTerritories.isEmpty() || ownTerritoryIds.isEmpty()) {
            return new List<TerritoryDTO>();
        }

        Map<Id, List<TerritoryDTO>> territoryMap = new Map<Id, List<TerritoryDTO>>();
        Map<Id, TerritoryDTO> allNodes = new Map<Id, TerritoryDTO>();
        Set<Id> visitedNodes = new Set<Id>();

        // 构建 Territory2 结构，但确保 `allNodes` 只存储唯一实例
        for (Territory2 t : allTerritories) {
            if (!allNodes.containsKey(t.Id)) {
                allNodes.put(t.Id, new TerritoryDTO(t));
            }

            TerritoryDTO node = allNodes.get(t.Id);

            if (t.ParentTerritory2Id != null) {
                if (!territoryMap.containsKey(t.ParentTerritory2Id)) {
                    territoryMap.put(t.ParentTerritory2Id, new List<TerritoryDTO>());
                }

                // 只添加唯一的 Children，避免重复
                if (!territoryMap.get(t.ParentTerritory2Id).contains(node)) {
                    territoryMap.get(t.ParentTerritory2Id).add(node);
                }
            }
        }

        // 生成所有 Territory 树
        List<TerritoryDTO> resultTrees = new List<TerritoryDTO>();
        for (Id rootId : ownTerritoryIds) {
            if (!allNodes.containsKey(rootId)) {
                continue;
            }
            TerritoryDTO root = allNodes.get(rootId);
            visitedNodes.clear();
            buildHierarchy(root, territoryMap, visitedNodes, 0);
            resultTrees.add(root);
        }

        // 当一个 Territory 是另一个 Territory 的子节点时，移除此子节点构建的树
        Set<Id> allChildTerritoryIds = new Set<Id>();
        allChildTerritoryIds = getAllChildrenIds(resultTrees);

        for (Id rootId : ownTerritoryIds) {
            if (allChildTerritoryIds.contains(rootId)) {
                removeTerritoryById(resultTrees, rootId);
            }
        }
        return resultTrees;
    }

    private static Set<Id> getAllChildrenIds(List<TerritoryDTO> territories) {
        Set<Id> ids = new Set<Id>();
        for (TerritoryDTO territory : territories) {
            extractChildrenIds(territory.children, ids, 0);
        }
        return ids;
    }

    private static void extractChildrenIds(List<TerritoryDTO> children, Set<Id> ids, Integer depth) {
        if (depth > MAX_DEPTH) {
            LOGGER.error('Maximum recursion depth reached. Stopping at Territory ID: ' + children[0].id);
            return;
        }

        if (children == null || children.isEmpty()) {
            return;
        }
        for (TerritoryDTO child : children) {
            ids.add(child.id);
            extractChildrenIds(child.children, ids, depth + 1);
        }
    }

    private static List<TerritoryDTO> removeTerritoryById(List<TerritoryDTO> territories, Id targetId) {
        for (Integer i = 0; i < territories.size(); i++) {
            if (territories[i].id == targetId) {
                territories.remove(i);
                break;
            }
        }
        return territories;
    }

    private static void buildHierarchy(TerritoryDTO node, Map<Id, List<TerritoryDTO>> territoryMap, Set<Id> visitedNodes, Integer depth) {
        if (depth > MAX_DEPTH) {
            LOGGER.error('Maximum recursion depth reached. Stopping at Territory ID: ' + node.id);
            return;
        }

        // Prevent circular references
        if (visitedNodes.contains(node.id)) {
            LOGGER.error('Circular reference detected at Territory ID: ' + node.id);
            return;
        }
        visitedNodes.add(node.id);

        // Recursive search sub-Territory2
        if (territoryMap.containsKey(node.id)) {
            for (TerritoryDTO child : territoryMap.get(node.id)) {
                if (!node.children.contains(child)) {
                    // 避免重复添加
                    node.children.add(child);
                    buildHierarchy(child, territoryMap, visitedNodes, depth + 1);
                }
            }
        }
    }
}
