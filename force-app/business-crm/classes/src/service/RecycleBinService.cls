public without sharing class RecycleBinService {
    private final String CAMPAIGN_MEMBER_TYPE = Schema.CampaignMember.SObjectType.toString();
    private RecycleBinSelector recycleBinSelector = new RecycleBinSelector();
    private CampaignMemberService campaignMemberService = new CampaignMemberService();

    public RecycleBinService() {
    }

    public RecycleBinService(RecycleBinSelector recycleBinSelector, CampaignMemberService campaignMemberService) {
        this.recycleBinSelector = recycleBinSelector;
        this.campaignMemberService = campaignMemberService;
    }

    public void recycleCampaignMemberWhenMerge(Set<Id> existingCampaignMemberIdsAfterMerge, Map<Id, CampaignMember> campaignMemberMapBeforeMerge) {
        // Delete wrong records that create by delete lead/contact trigger
        List<Custom_Recycle_Bin__c> wrongRecycleRecords = recycleBinSelector.getRecycleRecordsByRecordIdsAndType(campaignMemberMapBeforeMerge.keySet(), CAMPAIGN_MEMBER_TYPE);
        recycleBinSelector.removeUndeletedRecordsInRecycleBin(wrongRecycleRecords);

        List<CampaignMember> deletedCampaignMember = new List<CampaignMember>();
        for (CampaignMember campaignMember : campaignMemberMapBeforeMerge.values()) {
            if (!existingCampaignMemberIdsAfterMerge.contains(campaignMember.Id)) {
                deletedCampaignMember.add(campaignMember);
            }
        }
        putCampaignMemberToRecycleBin(deletedCampaignMember);
    }

    public List<CampaignMember> recycleRelatedCampaignMember(Set<Id> contactOrLeadIdSet) {
        List<CampaignMember> campaignMemberList = campaignMemberService.getCampaignMemberMapByContactsOrLeads(contactOrLeadIdSet).values();
        putCampaignMemberToRecycleBin(campaignMemberList);
        return campaignMemberList;
    }

    public void putCampaignMemberToRecycleBin(List<CampaignMember> campaignMembers) {
        if (campaignMembers.isEmpty()) {
            return;
        }
        List<Custom_Recycle_Bin__c> recycleRecords = new List<Custom_Recycle_Bin__c>();
        for (CampaignMember campaignMember : campaignMembers) {
            recycleRecords.add(new Custom_Recycle_Bin__c(Type__c = CAMPAIGN_MEMBER_TYPE, Record_Id__c = campaignMember.Id, Deleted_Date__c = Datetime.now()));
        }
        recycleBinSelector.insertRecordsToRecycleBin(recycleRecords);
    }

    public void recycleCampaignMemberByDeleteAccount(List<Account> accounts) {
        List<CampaignMember> needRecycleCampaignMembers = new List<CampaignMember>();
        for (Account account : accounts) {
            if (account.MasterRecordId != null) {
                continue;
            }
            List<CampaignMember> campaignMembers = AccountDeleteContext.accountIdToCampaignMembers.get(account.Id);
            if (campaignMembers != null) {
                needRecycleCampaignMembers.addAll(campaignMembers);
            }
        }
        putCampaignMemberToRecycleBin(needRecycleCampaignMembers);
    }
}
