public with sharing class BusinessService {
    public final String MARKETING_QUALIFIED_STATUS = 'Marketing Qualified';
    public final String ALL_TW_REGION = 'ALL_TW';
    public final List<String> IN_BOUND_PROFILE_FITMENT_TRIGGERS = new List<String>{
            'Inbound - Call',
            'Inbound - Email',
            'Inbound - Web Form',
            'Referral - Alumni',
            'Referral - Apax',
            'Referral - Client or Former Client',
            'Referral - Sales',
            'Referral - TWer',
            'Other'
    };
    public final List<String> OUT_BOUND_TRIGGERS = new List<String>{
            'Outbound'
    };
    public final Id LEAD_RECORD_TYPE_ID = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Professional Services Lead').getRecordTypeId();
    public final Id CONTACT_RECORD_TYPE_ID = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId();
    public final Map<String, String> REGION_MAP = new Map<String, String>{
            'NA' => 'Americas',
            'CA' => 'Americas',
            'EU' => 'Europe',
            'LATAM' => 'Americas',
            'APAC' => 'Asia Pacific',
            'CN' => 'Asia Pacific',
            'IME' => 'India / Middle East'
    };
    public final Map<String, String> LOCALE_TO_REGION_MAP = new Map<String, String>{
            'en_AU' => 'APAC',
            'en_SG' => 'APAC',
            'en_NZ' => 'APAC',
            'zh_CN' => 'CN',
            'en_HK' => 'CN',
            'zh_HK' => 'CN',
            'en_IN' => 'IME',
            'en_US' => 'NA',
            'en_CA' => 'CA',
            'pt_BR' => 'LATAM',
            'es_CL' => 'LATAM',
            'es_EC' => 'LATAM',
            'en_GB' => 'EU',
            'de_DE' => 'EU',
            'fi_FI' => 'EU',
            'it_IT' => 'EU',
            'nl_NL' => 'EU',
            'ro_RO' => 'EU',
            'es_ES_EURO' => 'EU',
            'de_CH' => 'EU',
            'en_DE' => 'EU'
    };
    public final Map<String, String> DIVISION_MAP = new Map<String, String>{
            'CA' => 'NA - Canada', 'CN' => 'APAC - China'
    };

    private ILeadSelector leadSelector = new LeadSelector();
    private PersonSelector mktPersonSelector = new PersonSelector();
    private IMKTContactSelector contactSelector = new MKTContactSelector();
    private IMKTUserSelector userSelector = new MKTUserSelector();

    public BusinessService() {
    }

    public BusinessService(ILeadSelector leadSelector, IMKTContactSelector contactSelector, IMKTUserSelector userSelector, PersonSelector mktPersonSelector) {
        this.leadSelector = leadSelector;
        this.contactSelector = contactSelector;
        this.userSelector = userSelector;
        this.mktPersonSelector = mktPersonSelector;
    }

    public ContactLeadSummaryDTO executeSummaryQueryForAssignNewBusiness(String regions) {
        if (String.isBlank(regions)) {
            return new ContactLeadSummaryDTO();
        }
        String[] regionList = regions.split(';');
        List<Lead> leadList = new List<Lead>();
        List<Contact> contactList = new List<Contact>();
        for (String regionName : regionList) {
            String region = REGION_MAP.get(regionName);
            String division = DIVISION_MAP.get(regionName);
            List<String> triggers = new List<String>();
            triggers.addAll(OUT_BOUND_TRIGGERS);
            if (regionName != ALL_TW_REGION) {
                triggers.addAll(IN_BOUND_PROFILE_FITMENT_TRIGGERS);
            }
            leadList.addAll(leadSelector.getLeadsByListViewCriteria(MARKETING_QUALIFIED_STATUS, triggers, LEAD_RECORD_TYPE_ID, region, division, null, null, null, null));
            contactList.addAll(contactSelector.getContactsByListViewCriteria(MARKETING_QUALIFIED_STATUS, triggers, CONTACT_RECORD_TYPE_ID, region, division, null, null, null, null));
        }

        return generateSummaryInfo(leadList, contactList);
    }


    private ContactLeadSummaryDTO generateSummaryInfo(List<Lead> leadList, List<Contact> contactList) {
        Integer totalNumber = leadList.size() + contactList.size();
        if (totalNumber == 0) {
            return new ContactLeadSummaryDTO();
        }
        DateTime newestLastModifyTime = getNewestLastModifyTime(leadList, contactList);

        return new ContactLeadSummaryDTO(totalNumber, newestLastModifyTime);
    }

    private DateTime getNewestLastModifyTime(List<Lead> leadList, List<Contact> contactList) {
        List<DateTime> allDateTimeList = new List<DateTime>();
        for (Lead lead : leadList) {
            allDateTimeList.add(lead.LastModifiedDate);
        }
        for (Contact contact : contactList) {
            allDateTimeList.add(contact.LastModifiedDate);
        }
        allDateTimeList.sort();
        return allDateTimeList.get(allDateTimeList.size() - 1);
    }
}
