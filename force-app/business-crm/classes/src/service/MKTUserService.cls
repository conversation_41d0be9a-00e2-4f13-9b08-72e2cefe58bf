/**
 * Created by eric2369 on 2024/9/9.
 */

public without sharing class MKTUserService {
    private final IProfileSelector profileSelector;
    private final IPermissionSetSelector permissionSetSelector;
    private final IPermissionSetAssignmentSelector permissionSetAssignmentSelector;

    public MKTUserService() {
        profileSelector = new ProfileSelector();
        permissionSetAssignmentSelector = new PermissionSetAssignmentSelector();
        permissionSetSelector = new PermissionSetSelector();
    }

    @TestVisible
    private MKTUserService(IProfileSelector profileSelector, IPermissionSetAssignmentSelector permissionSetAssignmentSelector, IPermissionSetSelector permissionSetSelector) {
        this.profileSelector = profileSelector;
        this.permissionSetAssignmentSelector = permissionSetAssignmentSelector;
        this.permissionSetSelector = permissionSetSelector;
    }

    // key: userId value : permission set id set
    @TestVisible
    private static final Map<Id, Set<Id>> crmUserPermissionSetBackup = new Map<Id, Set<Id>>();

    public void setDefaultCurrencyIsoCodeInInsert(List<User> newUserList) {
        Map<Id, Profile> profileMap = profileSelector.getProfilesByNames(new Set<String>{ 'CRM - Sales', 'CRM - Sales Manager' });
        for (User newUser : newUserList) {
            Boolean newProfileIsSalesOrSM = profileMap.containsKey(newUser.ProfileId);
            if (newProfileIsSalesOrSM) {
                newUser.DefaultCurrencyIsoCode = 'USD';
            }
        }
    }

    public void setDefaultCurrencyIsoCodeInUpdate(Map<Id, User> oldUserMap, List<User> newUserList) {
        Map<Id, Profile> profileMap = profileSelector.getProfilesByNames(new Set<String>{ 'CRM - Sales', 'CRM - Sales Manager' });
        for (User newUser : newUserList) {
            User oldUser = oldUserMap.get(newUser.Id);
            Boolean newProfileIsSalesOrSM = profileMap.containsKey(newUser.ProfileId);
            Boolean oldProfileIsSalesOrSM = profileMap.containsKey(oldUser.ProfileId);
            if (newProfileIsSalesOrSM && !oldProfileIsSalesOrSM) {
                newUser.DefaultCurrencyIsoCode = 'USD';
            }
        }
    }

    public void updateToMarkertingUser(List<User> newUsers) {
        Map<Id, Profile> crmProfiles = getCRMProfiles();
        for (User newUser : newUsers) {
            if (crmProfiles.keySet().contains(newUser.ProfileId)) {
                newUser.UserPermissionsMarketingUser = true;
            }
        }
    }

    private Map<Id, Profile> getCRMProfiles() {
        return profileSelector.getProfilesByNames(Constants.CRM_USER_PROFILE_NAMES);
    }

    public void temporailyBackupCRMUserPermission(Map<Id, User> oldUserMap, Map<Id, User> newUserMap) {
        // get user license and profileId
        Set<Id> allProfileIds = new Set<Id>();
        for (User user : oldUserMap.values()) {
            allProfileIds.add(user.ProfileId);
        }
        for (User user : newUserMap.values()) {
            allProfileIds.add(user.ProfileId);
        }
        Map<Id, Id> profileUserLicenseMap = profileSelector.getProfileUserLicenseMap(allProfileIds);
        Set<Id> usersChangeToCRMProfle = filterCRMUserNeedReAssignPermission(oldUserMap, newUserMap, profileUserLicenseMap);
        if (usersChangeToCRMProfle.isEmpty()) {
            return;
        }

        // get all user permission set
        List<PermissionSetAssignment> allUserPermissionSet = permissionSetAssignmentSelector.getAllByUserIds(new List<Id>(usersChangeToCRMProfle));

        for (PermissionSetAssignment assignment : allUserPermissionSet) {
            Id userId = assignment.AssigneeId;
            // check if old permission set associated to the different user license
            Id newUserLicenseId = profileUserLicenseMap.get(newUserMap.get(userId).profileId);
            Id permissionSetLicenseId = assignment.PermissionSet.LicenseId;
            if (permissionSetLicenseId != null && permissionSetLicenseId != newUserLicenseId) {
                continue;
            }

            if (assignment.PermissionSet.Name == Constants.CRM_FULL_LICENSE_PERMISSION_SET_GROUP_NAME || assignment.PermissionSet.Name == Constants.TW_POWER_USER_PERMISSION_SET_GROUP_NAME) {
                continue;
            }

            Set<Id> userPermissionSets = crmUserPermissionSetBackup.get(userId);
            if (userPermissionSets == null) {
                userPermissionSets = new Set<Id>();
                crmUserPermissionSetBackup.put(userId, userPermissionSets);
            }
            userPermissionSets.add(assignment.PermissionSetId);
        }
    }

    private Set<Id> filterCRMUserNeedReAssignPermission(Map<Id, User> oldUserMap, Map<Id, User> newUserMap, Map<Id, Id> profileUserLicenseMap) {
        Set<Id> usersNeedReassignPermissnSet = new Set<Id>();
        Map<Id, Profile> crmProfiles = getCRMProfiles();
        Set<Id> crmProfileIds = crmProfiles.keySet();

        for (User newUser : newUserMap.values()) {
            User oldUser = oldUserMap.get(newUser.Id);
            Id oldUserLicense = profileUserLicenseMap.get(oldUser.ProfileId);
            Id newUserLicense = profileUserLicenseMap.get(newUser.ProfileId);
            Boolean hasCRMProfile = crmProfileIds.contains(oldUser.ProfileId) || crmProfileIds.contains(newUser.ProfileId);
            if (oldUserLicense != newUserLicense && hasCRMProfile) {
                usersNeedReassignPermissnSet.add(newUser.Id);
            }
        }
        return usersNeedReassignPermissnSet;
    }

    public void restoreCRMUserPermissionSet(Map<Id, User> newUserMap) {
        List<PermissionSetAssignment> reAssignments = new List<PermissionSetAssignment>();
        for (Id userId : crmUserPermissionSetBackup.keySet()) {
            if (newUserMap.get(userId).IsActive) {
                Set<Id> userPermissionSets = crmUserPermissionSetBackup.get(userId);

                for (Id permissionSetId : userPermissionSets) {
                    reAssignments.add(new PermissionSetAssignment(AssigneeId = userId, PermissionSetId = permissionSetId));
                }
            }
        }

        if (!reAssignments.isEmpty()) {
            insert reAssignments;
        }
    }

    public void grantCRMUserFullLicensePermissionSetGroup(Map<Id, User> oldUserMap, Map<Id, User> newUserMap) {
        Set<Id> usersNeedPermissionGroup = new Set<Id>();
        Map<Id, Profile> crmProfiles = getCRMProfiles();
        for (User newUser : newUserMap.values()) {
            User oldUser = oldUserMap.get(newUser.Id);

            if (oldUser.ProfileId != newUser.ProfileId && crmProfiles.keySet().contains(newUser.ProfileId)) {
                usersNeedPermissionGroup.add(newUser.Id);
            }
        }

        if (usersNeedPermissionGroup.isEmpty()) {
            return;
        }

        Id crmFullLicensePermissionSetId = getCRMFullLicensePermissionSetId();
        // check if this user already have CRM Full License Permmission Set Id

        Set<Id> userAlreadyHasFullLicensePermission = new Set<Id>();
        List<PermissionSetAssignment> fullLicenseAssignments = permissionSetAssignmentSelector.getByUsersIdAndPermissionSetName(
            new List<Id>(usersNeedPermissionGroup),
            Constants.CRM_FULL_LICENSE_PERMISSION_SET_GROUP_NAME
        );
        for (PermissionSetAssignment fullLicenseAssignment : fullLicenseAssignments) {
            userAlreadyHasFullLicensePermission.add(fullLicenseAssignment.AssigneeId);
        }

        usersNeedPermissionGroup.removeAll(userAlreadyHasFullLicensePermission);
        if (usersNeedPermissionGroup.isEmpty()) {
            return;
        }
        // assign permission set group
        List<PermissionSetAssignment> userWithFullCRMLicense = new List<PermissionSetAssignment>();
        for (Id userId : usersNeedPermissionGroup) {
            PermissionSetAssignment fullLicensePermissionSetgroup = new PermissionSetAssignment(AssigneeId = userId, PermissionSetId = crmFullLicensePermissionSetId);
            userWithFullCRMLicense.add(fullLicensePermissionSetgroup);
        }

        if (!userWithFullCRMLicense.isEmpty()) {
            insert userWithFullCRMLicense;
        }
    }

    private Id getCRMFullLicensePermissionSetId() {
        return permissionSetSelector.getPermissionSetByName(Constants.CRM_FULL_LICENSE_PERMISSION_SET_GROUP_NAME).Id;
    }

    public List<String> getDetUserIdList() {
        Map<String, DETTeam__c> detUsersMap = DETTeam__c.getAll();
        List<String> detUserIdList = new List<String>();

        for (String name : detUsersMap.keySet()) {
            detUserIdList.add(detUsersMap.get(name).UserId__c);
        }
        return detUserIdList;
    }
}
