public with sharing class AccountOrgChartService {
    public static IMKTContactSelector contactSelector = new MKTContactSelector();
    public static IAccountSelector accountSelector = new AccountSelector();

    public AccountOrgChartService() {
    }

    @testVisible
    private AccountOrgChartService(IMKTContactSelector mockContactSelector) {
        contactSelector = mockContactSelector;
    }

    public Map<Id, Contact> getRelationshipContactsByAccountId(String accountId) {
        // If this is a parent account，need to query child accounts
        Map<Id, Account> id2ChildAccounts = accountSelector.getChildAccountsByParentAccountIds(new Set<Id>{ accountId });

        Set<Id> childAccountIds = id2ChildAccounts.keySet();
        Set<Id> allAccountIds = new Set<Id>(childAccountIds);
        allAccountIds.add((Id) accountId);

        Map<Id, Contact> contactsInAccount = contactSelector.getContactsByAccountIds(allAccountIds);
        Set<Id> currentAccountContactsId = contactsInAccount.keySet();
        List<Id> currentExternalAccountReportsToId = new List<Id>();
        for (Contact contact : contactsInAccount.values()) {
            if (contact.ReportsToId != null && !currentAccountContactsId.contains(contact.ReportsToId)) {
                currentExternalAccountReportsToId.add(contact.ReportsToId);
            }
        }

        Map<Id, Contact> mapExternalAccountChildContacts = contactSelector.getContactsByReportsToIdAndAccount(currentAccountContactsId, allAccountIds);
        Map<Id, Contact> mapExternalAccountParentContacts = contactSelector.getContactsById(currentExternalAccountReportsToId);
        contactsInAccount.putAll(mapExternalAccountChildContacts.values());
        contactsInAccount.putAll(mapExternalAccountParentContacts.values());

        return contactsInAccount;
    }

    public Map<Id, Account> getParentAndChildAccounts(String accountId) {
        Map<Id, Account> id2ChildAccounts = accountSelector.getChildAccountsByParentAccountIds(new Set<Id>{ accountId });

        Set<Id> childAccountIds = id2ChildAccounts.keySet();
        Set<Id> allAccountIds = new Set<Id>(childAccountIds);
        allAccountIds.add((Id) accountId);

        Map<Id, Account> id2AllAccounts = accountSelector.getAllAccountsById(new List<Id>(allAccountIds));
        return id2AllAccounts;
    }
}
