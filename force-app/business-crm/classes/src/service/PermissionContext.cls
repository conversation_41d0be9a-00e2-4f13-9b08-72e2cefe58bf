public without sharing class PermissionContext {
    private static final Map<Id, Profile> profileMap = new Map<Id, Profile>();

    public static void add(Map<Id, Profile> profileIdToProfileMap) {
        if (profileIdToProfileMap == null) {
            return;
        }
        profileMap.putAll(profileIdToProfileMap);
    }

    public static Map<Id, Profile> getCRMProfiles() {
        Map<Id, Profile> crmProfiles = new Map<Id, Profile>();
        for (Profile sfProfile : profileMap.values()) {
            if (Constants.CRM_USER_PROFILE_NAMES.contains(sfProfile.Name)) {
                crmProfiles.put(sfProfile.Id, sfProfile);
            }
        }
        return crmProfiles;
    }

    public static Map<Id, Profile> getAdminAndPowerUserProfiles() {
        Map<Id, Profile> adminPowerUserProfiles = new Map<Id, Profile>();
        for (Profile sfProfile : profileMap.values()) {
            if (Constants.ADMIN_POWER_USER_PROFILE_NAMES.contains(sfProfile.Name)) {
                adminPowerUserProfiles.put(sfProfile.Id, sfProfile);
            }
        }
        return adminPowerUserProfiles;
    }
}
