public without sharing class MKTContactService {
    private final static Logger LOGGER = new Logger(MKTContactService.class.getName(), TeamName.CRM);

    private static final String PROFESSIONAL_SERVICES_LEAD = 'Professional Services Lead';
    private final static ID PS_CONTACT_ID = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Professional Services Contact').getRecordTypeId();
    private final static ID RESOURCE_CONTACT_ID = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();

    private IProfileSelector profileSelector = new ProfileSelector();
    private IMKTContactSelector contactSelector = new MKTContactSelector();
    private SurveyResponseService surveyResponseService = new SurveyResponseService();
    private ILeadSelector leadSelector = new LeadSelector();

    public MKTContactService() {
    }

    @TestVisible
    private MKTContactService(IProfileSelector profileSelector, IMKTContactSelector contactSelector, SurveyResponseService surveyResponseService) {
        this.profileSelector = profileSelector;
        this.contactSelector = contactSelector;
        this.surveyResponseService = surveyResponseService;
    }

    public void syncEngageB2B(List<Contact> contacts) {
        for (Contact contact : contacts) {
            if (contact.RecordTypeId == PS_CONTACT_ID && contact.Email != null && contact.Enable_ima_sync__c == false) {
                contact.Enable_ima_sync__c = true;
            }
        }
    }

    public void mapContactRecordTypeToLeadRecordType(List<Contact> contactList) {
        Map<String, Id> leadToContactRecordTypeIdMap = new Map<String, Id>{ PROFESSIONAL_SERVICES_LEAD => PS_CONTACT_ID };

        for (Contact con : contactList) {
            if (con.Contact_Record_Type_Conversion__c != null) {
                con.RecordTypeId = leadToContactRecordTypeIdMap.get(con.Contact_Record_Type_Conversion__c);
            }
        }
    }

    public void checkIfShouldSyncToMarketoWhenInsert(List<Contact> newContactList) {
        for (Contact contact : newContactList) {
            if (contact.ContactConverted__c) {
                continue;
            }
            Boolean anonymousOrNotHasEmail = contact.Email == null || contact.FirstName == CRMConstants.FIRST_NAME_TO_TAG_ANONYMOUS;
            if (anonymousOrNotHasEmail) {
                contact.Sync_to_Marketo__c = false;
            }
        }
    }

    public void checkIfShouldSyncToMarketoWhenUpdate(List<Contact> newContactList) {
        for (Contact contact : newContactList) {
            if (contact.Email != null && contact.FirstName != CRMConstants.FIRST_NAME_TO_TAG_ANONYMOUS) {
                contact.Sync_to_Marketo__c = true;
            }
        }
    }

    public void populateCountryFields(List<Contact> newContactList) {
        for (Contact con : newContactList) {
            if (con.Country__c != null) {
                con.MailingCountry = con.Country__c;
            }
        }
    }

    public void populateCountryFields(Map<Id, Contact> contactMapOld, List<Contact> newContactList) {
        for (Contact con : newContactList) {
            if (con.Country__c != null && con.Country__c != contactMapOld.get(con.Id).Country__c) {
                con.MailingCountry = con.Country__c;
            }
        }
    }

    public void markAsPersonalEmail(Map<Id, Contact> contactMapOld, List<Contact> contactList) {
        for (Contact con : contactList) {
            if (contactMapOld == null || contactMapOld.get(con.Id).Email != con.Email) {
                if (con.Email != null && Utility.isPersonalEmail(con.Email, con.Id)) {
                    con.Personal_Email__c = true;
                } else {
                    con.Personal_Email__c = false;
                }
            }
        }
    }

    public void setContactOriginalDate(List<Contact> newContactList) {
        for (Contact con : newContactList) {
            if (con.ContactConverted__c == false && con.RecordTypeId == PS_CONTACT_ID) {
                con.Contact_Original_Date__c = Datetime.now();
            }
        }
    }

    public void updatePartnershipInvolvedFlag(List<Contact> newContactList) {
        List<Contact> contacts = new List<Contact>();
        for (Contact contact : newContactList) {
            if (CRMConstants.partnershipLeadSource.contains(contact.LeadSource)) {
                contacts.add(new Contact(Id = contact.Id, Partnership_Involved__c = true));
            }
        }
        FieldUpdateEventService.buildAndSendFieldUpdateEvents(contacts, new Set<String>{ 'Partnership_Involved__c' });
    }

    public void updateExistedACRWhenAccountChangeBack(Map<Id, Contact> oldContactMap, List<Contact> newContactList) {
        List<Contact> contactsAccountChanged = new List<Contact>();
        for (Contact newContact : newContactList) {
            Contact oldContact = oldContactMap.get(newContact.Id);
            if (oldContact.AccountId != newContact.AccountId) {
                contactsAccountChanged.add(newContact);
            }
        }

        if (contactsAccountChanged.isEmpty()) {
            return;
        }

        List<AccountContactRelation> needUpdateACRs = new List<AccountContactRelation>();
        Map<String, AccountContactRelation> mapAccConIdAndACR = new Map<String, AccountContactRelation>();
        List<AccountContactRelation> fullACRList = [SELECT Id, ContactId, AccountId, StartDate, EndDate, Contact.Name, IsActive FROM AccountContactRelation WHERE ContactId IN :oldContactMap.keySet()];
        for (AccountContactRelation acr : fullACRList) {
            mapAccConIdAndACR.put(acr.AccountId + '_' + acr.ContactId, acr);
        }

        for (Contact con : newContactList) {
            AccountContactRelation existedACR = mapAccConIdAndACR.get(con.AccountId + '_' + con.Id);
            AccountContactRelation currentACR = mapAccConIdAndACR.get(oldContactMap.get(con.Id).AccountId + '_' + con.Id);
            if (con.AccountId != oldContactMap.get(con.Id).AccountId && existedACR != null && !existedACR.IsActive) {
                needUpdateACRs.add(updatedExistedACR(existedACR));
                needUpdateACRs.add(updatedCurrentACR(currentACR, con));
            }
        }
        if (!needUpdateACRs.isEmpty()) {
            update needUpdateACRs;
        }
    }

    private AccountContactRelation updatedExistedACR(AccountContactRelation existedACR) {
        existedACR.IsActive = true;
        existedACR.Account_History__c =
            existedACR.Contact.Name +
            ' previously worked at this account from ' +
            formateDate(existedACR.StartDate) +
            ' to ' +
            formateDate(existedACR.EndDate) +
            '. \n Please see the Activity timeline for more details.';
        existedACR.StartDate = System.Today();
        existedACR.EndDate = null;
        existedACR.Job_Title__c = null;
        existedACR.Contact_Owner__c = null;
        return existedACR;
    }

    private AccountContactRelation updatedCurrentACR(AccountContactRelation currentACR, Contact con) {
        currentACR.Job_Title__c = con.Title;
        currentACR.IsActive = false;
        currentACR.EndDate = System.Today();
        currentACR.Contact_Owner__c = con.Owner.Name;
        return currentACR;
    }

    private String formateDate(Date dt) {
        String formatedDate = dt != null ? dt.format() : null;
        return formatedDate;
    }

    public void updateNotAtAccountWhenNotAtCompanyFlagChange(Map<Id, Contact> oldContactMap, List<Contact> newContactList) {
        for (Contact con : newContactList) {
            String oldNotAtCompanyFlag = oldContactMap.get(con.Id).LID__No_longer_at_Company__c;
            String newNotAtCompanyFlag = con.LID__No_longer_at_Company__c;
            if ((oldNotAtCompanyFlag == 'Ignore' || oldNotAtCompanyFlag == null) && newNotAtCompanyFlag == 'Not at Company') {
                con.Not_at_Account__c = true;
            }
            if (oldNotAtCompanyFlag == 'Not at Company' && (newNotAtCompanyFlag == 'Ignore' || newNotAtCompanyFlag == null)) {
                con.Not_at_Account__c = false;
            }
        }
    }

    public void checkNotAtAccountFlagChange(Map<Id, Contact> oldContactMap, List<Contact> newContactList) {
        for (Contact con : newContactList) {
            Boolean oldNotAtAccountFlag = oldContactMap.get(con.Id).Not_at_Account__c;
            Boolean newNotAtAccountFlag = con.Not_at_Account__c;
            if (!oldNotAtAccountFlag && newNotAtAccountFlag) {
                con.Date_not_at_account_detected__c = Date.today();
            } else if (oldNotAtAccountFlag && !newNotAtAccountFlag) {
                con.Date_not_at_account_detected__c = null;
            }
        }
    }

    public void resetEmailInvalid(Map<Id, Contact> contactMapOld, List<Contact> contactListNew) {
        // hope to use the marketo sync value, and want to avoid this change in merge stage:
        // only when user in crm changed invalid email will set the invalid value to valid
        Id userProfileId = (Id) UserInfo.getProfileId();
        Id marketoSyncProfileId = profileSelector.getProfileByName('Marketo Sync').Id;
        if (userProfileId == marketoSyncProfileId)
            return;
        for (Contact contact : contactListNew) {
            if (contact.Is_Merge_Stage__c)
                continue;
            Contact oldContact = contactMapOld.get(contact.Id);
            if (oldContact.Email != contact.Email && oldContact.Email_Invalid__c) {
                contact.Email_Invalid__c = false;
            }
        }
    }

    public void updateStatusWhenQualifyOut(List<Contact> newContactList, Map<Id, Contact> oldContactMap) {
        for (Contact newContact : newContactList) {
            if (newContact.Is_Merge_Stage__c == true) {
                return;
            }
            if (newContact.Disqualified_Reason__c != null && oldContactMap.get(newContact.Id).Disqualified_Reason__c != newContact.Disqualified_Reason__c && newContact.RecordTypeId == PS_CONTACT_ID) {
                if (CRMConstants.WILL_NEVER_BUY_OPTIONS.contains(newContact.Disqualified_Reason__c)) {
                    newContact.Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_WILL_NEVER_BUY;
                }
                if (CRMConstants.NOT_SALES_READY_OPTIONS.contains(newContact.Disqualified_Reason__c)) {
                    newContact.Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY;
                }
                newContact.Disqualification_Date__c = Date.today();
            }
        }
    }

    public void updateAQLDurationWhenLeaveAQL(Map<Id, Contact> contactMapOld, List<Contact> contactListNew) {
        for (Contact newContact : contactListNew) {
            Contact oldContact = contactMapOld.get(newContact.Id);
            if (oldContact.Auto_Qualified_Lead__c && !newContact.Auto_Qualified_Lead__c) {
                newContact.AQL_Duration__c = oldContact.Days_as_AQL__c;
            }
        }
    }

    public void updateMQLEndDateWhenStatusChangeFromMQL(List<Contact> newContacts, Map<Id, Contact> oldContactsMap) {
        for (Contact newContact : newContacts) {
            if (oldContactsMap.get(newContact.Id).Status__c != newContact.Status__c) {
                if (oldContactsMap.get(newContact.Id).Status__c == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    newContact.MQL_End_Date__c = TimeUtils.now();
                }
                if (oldContactsMap.get(newContact.Id).Status__c != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_CONVERT && newContact.Status__c == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    newContact.MQL_End_Date__c = null;
                }
            }
        }
    }

    public void handleQualificationInfoWhenStageIsSAL(Map<Id, Contact> oldContactMap, List<Contact> newContacts) {
        Map<Id, Contact> newContactMap = new Map<Id, Contact>();
        newContactMap.putAll(newContacts);
        for (Id contactId : newContactMap.keySet()) {
            Contact newContact = newContactMap.get(contactId);
            Contact oldContact = oldContactMap.get(contactId);
            if (newContact.RecordTypeId == RESOURCE_CONTACT_ID) {
                continue;
            }
            String newStatus = newContact.Status__c;
            String oldStatus = oldContact.Status__c;
            if (newStatus == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL && !newStatus.equals(oldStatus) && oldStatus != CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                if (oldContact.leadConvertFlag__c) {
                    continue;
                }
                newContact.Recent_Qualification_Path__c = CRMConstants.QUALIFICATION_PATH_SG;
            }
        }
    }

    @TestVisible
    private Set<Id> getNeedGenerateTaskContactIds(Map<Id, Contact> oldContactMap, List<Contact> newContactList) {
        Set<Id> needGenerateTaskContactIds = new Set<Id>();
        for (Contact newContact : newContactList) {
            if (
                (newContact.RecordTypeId == PS_CONTACT_ID) &&
                (newContact.LID__No_longer_at_Company__c == 'Not at Company' &&
                oldContactMap.get(newContact.Id).LID__No_longer_at_Company__c == null) &&
                (newContact.AccountId != null)
            ) {
                needGenerateTaskContactIds.add(newContact.Id);
            }
        }
        return needGenerateTaskContactIds;
    }

    public void updateNPSInfo(List<Survey_Response__c> surveyResponseList) {
        Set<Id> contactIds = CollectionUtils.getIdSet(surveyResponseList, Survey_Response__c.Contact__c);
        Map<Id, List<Survey_Response__c>> contactToSurveyResponsesMap = surveyResponseService.getContactIdToSurveyResponsesMap(contactIds);

        List<Contact> contactList = new List<Contact>();
        for (Id contactId : contactIds) {
            List<Survey_Response__c> relatedSurveyResponses = contactToSurveyResponsesMap.get(contactId);
            Survey_Response__c lastSurveyResponse = (Survey_Response__c) CollectionUtils.getLatestRecordByDateTimeField(relatedSurveyResponses, Survey_Response__c.Response_Datetime__c);
            contactList.add(
                new Contact(
                    Id = contactId,
                    Latest_NPS_Comment__c = lastSurveyResponse == null ? '' : lastSurveyResponse.NPS_Comment__c,
                    Latest_NPS_Score__c = lastSurveyResponse == null ? '' : lastSurveyResponse.NPS_Score__c,
                    Latest_NPS_Response_Date__c = lastSurveyResponse == null ? null : lastSurveyResponse.Response_Datetime__c.date()
                )
            );
        }
        contactSelector.updateContacts(contactList);
    }

    public void removeNotAtAccountFlag(String contactId) {
        Contact contact = new Contact(Not_at_Account__c = false, Id = contactId);
        update contact;
    }

    public void clearReportsToFieldForContacts(List<Id> contactIds) {
        List<Contact> needToUpdateContacts = new List<Contact>();
        for (Id contactId : contactIds) {
            Contact contact = new Contact(Id = contactId, ReportsToId = null);
            needToUpdateContacts.add(contact);
        }

        update needToUpdateContacts;
    }

    public List<Contact> searchContactByNameAndAccountIdsWithInternalPriorityAndLimit(String name, List<Id> accountIds, Integer limitNum) {
        List<Contact> contactList = contactSelector.searchContactByNameAndAccountIdsInOrOutWithLimit(name, accountIds, true, limitNum);
        limitNum -= contactList.size();
        if (limitNum <= 0) {
            return contactList;
        } else {
            contactList.addAll(contactSelector.searchContactByNameAndAccountIdsInOrOutWithLimit(name, accountIds, false, limitNum));
            return contactList;
        }
    }

    public List<SObject> getLeadsAndContactsAssignedToMe(Id userId) {
        // for assigned new business,comment the data query not used in CTA now,not sure if it will be used in the future
        List<SObject> leadAndContacts = new List<SObject>();
        // List<Contact> marketingQualifiedOutboundContacts = contactSelector.getMarketingQualifiedOutboundContacts(userId);
        List<Contact> assignedNewBusiness = contactSelector.getAssignedNewBusiness(userId);
        // List<Contact> recentWebActivity = contactSelector.getRecentWebActivity(userId);
        // List<Contact> whoHaveMovedCompanies = contactSelector.getWhoHaveMovedCompanies(userId);

        // leadAndContacts.addAll(marketingQualifiedOutboundContacts);
        leadAndContacts.addAll(assignedNewBusiness);
        // leadAndContacts.addAll(recentWebActivity);
        // leadAndContacts.addAll(whoHaveMovedCompanies);

        List<Lead> leads = leadSelector.getLeadsAssignedToMe(userId);
        leadAndContacts.addAll(leads);

        return leadAndContacts;
    }

    public List<SObject> getAttemptedAndConversationLeadsAndContacts(Id userId) {
        List<SObject> leadAndContacts = new List<SObject>();
        List<String> subStage = new List<String>{ CRMConstants.SUBSTAGE_ATTEMPTED, CRMConstants.SUBSTAGE_CONVERSATION };
        List<Lead> leads = leadSelector.getSpecifiedSubStagePsLeadsById(userId, subStage);
        List<Contact> contacts = contactSelector.getSpecifiedSubStagePsContactsById(userId, subStage);
        leadAndContacts.addAll(leads);
        leadAndContacts.addAll(contacts);
        return leadAndContacts;
    }

    public void setContactHVOFlagWhenCLSChanged(List<Contact> newContacts, Map<Id, Contact> oldContactMap) {
        for (Contact newContact : newContacts) {
            Boolean isFromAddToAccount = LeadConvertContext.isLeadFromAddToAccount(newContact.Converted_LeadId__c);
            Boolean isContactActiveStatus = CRMConstants.ACTIVE_HVO_FLAG_CLS.contains(newContact.Status__c);

            if (isFromAddToAccount) {
                continue;
            }
            if (newContact.Status__c != oldContactMap.get(newContact.Id).Status__c && !isContactActiveStatus && newContact.Active_HVO_Respondent__c) {
                newContact.Active_HVO_Respondent__c = false;
            }
        }
    }

    public void setQuickConvertedByOnContact(List<Contact> newContactList) {
        Id currentUserId = UserInfo.getUserId();
        for (Contact psContact : newContactList) {
            if (psContact.RecordTypeId == PS_CONTACT_ID && psContact.ContactConverted__c && MKTFrontEndContext.isQuickConvertLeadToContact) {
                psContact.Quick_Converted_by__c = currentUserId;
            }
        }
    }

    public void updateContactToSalWhenContactChangedFromOtherToMQL(Map<Id, Contact> oldContactMap, List<Contact> newContactList) {
        List<Contact> contactsToUpdate = new List<Contact>();
        for (Contact contact : newContactList) {
            if (oldContactMap == null) {
                if (contact.Status__c == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    contactsToUpdate.add(new Contact(Id = contact.Id, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL));
                }
            } else {
                Contact oldContact = oldContactMap.get(contact.Id);
                if (oldContact.Status__c != contact.Status__c && contact.Status__c == CRMConstants.CUSTOMER_LIFECYCLE_STAGE_MQL) {
                    contactsToUpdate.add(new Contact(Id = contact.Id, Status__c = CRMConstants.CUSTOMER_LIFECYCLE_STAGE_ASL));
                }
            }
        }
        if(!contactsToUpdate.isEmpty()){
            FieldUpdateEventService.buildAndSendFieldUpdateEvents(contactsToUpdate, new Set<String>{ 'Status__c' });
        }
    }
}
