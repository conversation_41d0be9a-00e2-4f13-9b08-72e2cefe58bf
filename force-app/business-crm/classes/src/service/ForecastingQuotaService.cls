public with sharing class ForecastingQuotaService {
    private IForecastingQuotaSelector forecastingQuotaSelector = new ForecastingQuotaSelector();

    public ForecastingQuotaService() {
    }

    public ForecastingQuotaService(IForecastingQuotaSelector forecastingQuotaSelector) {
        this.forecastingQuotaSelector = forecastingQuotaSelector;
    }

    public List<SalesTargetDTO> getForecastingQuotaByOwnerIds(String duration, List<Id> userIds) {
        Map<String, Date> dateMap = Utility.getStartDateAndEndDate(duration, Date.today());
        Date startOfDuration = dateMap.get(Constants.START_DATE);
        Date endOfDuration = dateMap.get(Constants.END_DATE);
        List<ForecastingQuota> forecastingQuotaList = forecastingQuotaSelector.getForecastingQuotaByOwnerIds(startOfDuration, endOfDuration, userIds);

        List<SalesTargetDTO> result = new List<SalesTargetDTO>();

        for (ForecastingQuota forecastingQuota : forecastingQuotaList) {
            result.add(SalesTargetDTO.valueFromForecastingQuota(forecastingQuota));
        }

        return result;
    }

    //startDate should be like THIS_YEAR, LAST_WEEK
    public List<ForecastingQuota> getQuotasByStartDateAndUserIdsAndTerritoryIds(String startDate, List<Id> userIds, List<Id> territoryIds) {
        return forecastingQuotaSelector.getQuotasByStartDateAndUserIdsAndTerritoryIds(startDate, userIds, territoryIds);
    }

    public Map<Id, ForecastingType> getForecastingTypesByNames(List<String> names) {
        return forecastingQuotaSelector.getForecastingTypesByNames(names);
    }
}
