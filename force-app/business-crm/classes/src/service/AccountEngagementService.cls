public with sharing class AccountEngagementService {
    private static final Logger LOGGER = new Logger(AccountEngagementService.class.getName(), TeamName.CRM);

    public static final String ROLE_C_LEVEL_EXECUTIVE = 'C-Level Executive';

    public static final String FIELD_ROLE = 'Role';

    public static final String FIELD_CONTACT_ID = 'contactId';

    public static final String MAP_KEY_CONTACT = 'contact';

    public static final String MAP_KEY_LEAD = 'lead';

    public static final String CONTACT_TYPE_BUYER = 'Buyer';

    public static final String CONTACT_TYPE_EXECUTIVE_CONTACT = 'Executive Contact';
    private static final String ALL_CONTACTS = 'All Contacts';
    private static final String BUYERS = 'Buyers';
    private static final String EXECUTIVE_CONTACTS = 'Executive Contacts';

    public static IAccountSelector accountSelector = new AccountSelector();

    public static IMKTContactSelector contactSelector = new MKTContactSelector();

    public static IAccountContactRelationSelector accountContactRelationSelector = new AccountContactRelationSelector();

    public static IOpportunityContactRoleSelector opportunityContactRoleSelector = new OpportunityContactRoleSelector();

    public static HolisticEngagementSelector holisticEngagementSelector = new HolisticEngagementSelector();

    public static PersonFieldHistorySelector personFieldHistorySelector = new PersonFieldHistorySelector();

    public static ILeadSelector leadSelector = new LeadSelector();

    public AccountEngagementService() {
    }

    public AccountContactAddedDTO getContactAddedIn30Days(Set<Id> ownerIds, Set<String> marketNames, Set<String> accountSegments) {
        List<Account> accounts = accountSelector.getPartnershipAccountsByOwnerIdOrMarketName(ownerIds, marketNames, accountSegments);
        if (CollectionUtils.isEmpty(accounts)) {
            return new AccountContactAddedDTO();
        }
        Set<Id> accountIds = CollectionUtils.getIdSet(accounts);
        List<AccountContactRelation> relations = accountContactRelationSelector.getActiveAndInLast30Days(accountIds);
        Map<Id, AccountContactRelation> relationMap = getContactCountInLast30Days(relations);
        List<Lead> leadList = getLeadCountInLast30Days(accountIds);
        Map<Id, OpportunityContactRole> buyerMap = getBuyerCountInLast30Days(accountIds);
        Map<Id, Person_Field_History__c> executiveContactMap = getExecutiveContactsCountInLast30Days(accountIds);
        Map<Id, AccountContactRelation> executiveContactRelationMap = getExecutiveContactsCountInLast30Days(relationMap);
        Set<Id> ids = new Set<Id>(executiveContactMap.keySet());
        ids.addAll(executiveContactRelationMap.keySet());
        return new AccountContactAddedDTO(relationMap.keySet().size(), leadList.size(), buyerMap.keySet().size(), ids.size());
    }

    public Map<String, List<AccountContactAddedDetailDTO>> getContactAddedDetailListMap(Set<Id> ownerIds, Set<String> marketNames, Set<String> accountSegments) {
        List<Account> accounts = accountSelector.getPartnershipAccountsByOwnerIdOrMarketName(ownerIds, marketNames, accountSegments);
        Map<String, List<AccountContactAddedDetailDTO>> accountMap = new Map<String, List<AccountContactAddedDetailDTO>>();
        accountMap.put(MAP_KEY_CONTACT, new List<AccountContactAddedDetailDTO>());
        accountMap.put(MAP_KEY_LEAD, new List<AccountContactAddedDetailDTO>());
        if (CollectionUtils.isEmpty(accounts)) {
            return accountMap;
        }
        Set<Id> accountIds = CollectionUtils.getIdSet(accounts);
        List<AccountContactRelation> relations = accountContactRelationSelector.getActiveAndInLast30Days(accountIds);
        Map<Id, AccountContactRelation> relationMap = getContactCountInLast30Days(relations);
        List<Lead> leadList = getLeadCountInLast30Days(accountIds);
        Map<Id, OpportunityContactRole> buyerMap = getBuyerCountInLast30Days(accountIds);
        Map<Id, Person_Field_History__c> executiveContactMap = getExecutiveContactsCountInLast30Days(accountIds);
        Map<Id, AccountContactRelation> executiveContactRelationMap = getExecutiveContactsCountInLast30Days(relationMap);
        if (CollectionUtils.isNotEmpty(leadList)) {
            for (Lead lead : leadList) {
                accountMap.get(MAP_KEY_LEAD).add(new AccountContactAddedDetailDTO(lead));
            }
        }
        accountMap.put(MAP_KEY_CONTACT, generateContactDetailList(relationMap, buyerMap, executiveContactMap, executiveContactRelationMap));
        return accountMap;
    }

    public List<AccountContactAddedDetailDTO> generateContactDetailList(
        Map<Id, AccountContactRelation> relationMap,
        Map<Id, OpportunityContactRole> buyerMap,
        Map<Id, Person_Field_History__c> executiveContactMap,
        Map<Id, AccountContactRelation> executiveContactRelationMap
    ) {
        Set<Id> contactIds = new Set<Id>();
        contactIds.addAll(relationMap.keySet());
        contactIds.addAll(buyerMap.keySet());
        contactIds.addAll(executiveContactMap.keySet());
        contactIds.addAll(executiveContactRelationMap.keySet());
        List<Person__c> persons = contactSelector.getContactListByContactIds(contactIds);
        List<AccountContactAddedDetailDTO> detailList = new List<AccountContactAddedDetailDTO>();
        if (persons == null) {
            return new List<AccountContactAddedDetailDTO>();
        }
        for (Person__c person : persons) {
            String source = null;
            if (person.Holistic_Engagements__r != null && person.Holistic_Engagements__r.size() == 1) {
                source = person.Holistic_Engagements__r.get(0).Engagement_Source__c;
            }
            Contact contact = person.Contact__r;
            List<String> contactTypes = new List<String>();
            Date becomeBuyerTime = null;
            Date becomeExecutiveContactTime = null;
            Date becomeContactTime = null;
            if (relationMap.get(contact.Id) != null) {
                becomeContactTime = relationMap.get(contact.Id).StartDate;
            }
            if (buyerMap.get(contact.Id) != null) {
                contactTypes.add(CONTACT_TYPE_BUYER);
                becomeBuyerTime = buyerMap.get(contact.Id).CreatedDate.date();
            }
            if (executiveContactMap.get(contact.Id) != null || executiveContactRelationMap.get(contact.Id) != null) {
                contactTypes.add(CONTACT_TYPE_EXECUTIVE_CONTACT);
                if (executiveContactRelationMap.get(contact.Id) != null && executiveContactMap.get(contact.Id) == null) {
                    becomeExecutiveContactTime = executiveContactRelationMap.get(contact.Id).StartDate;
                }
                if (executiveContactMap.get(contact.Id) != null && becomeExecutiveContactTime == null) {
                    becomeExecutiveContactTime = executiveContactMap.get(contact.Id).Created_Date__c.date();
                }
            }
            detailList.add(new AccountContactAddedDetailDTO(contact, contactTypes, becomeBuyerTime, becomeExecutiveContactTime, becomeContactTime, source));
        }
        return detailList;
    }

    public static List<Lead> getLeadCountInLast30Days(Set<Id> accountIds) {
        return leadSelector.getLeadsByMatchAccountInLast30Days(accountIds);
    }

    public static Map<Id, AccountContactRelation> getContactCountInLast30Days(List<AccountContactRelation> relations) {
        Map<Id, AccountContactRelation> contactAccountMap = new Map<Id, AccountContactRelation>();
        for (AccountContactRelation relation : relations) {
            contactAccountMap.put(relation.ContactId, relation);
        }
        return last30DaysAddedContact(contactAccountMap);
    }

    public static Map<Id, AccountContactRelation> last30DaysAddedContact(Map<Id, AccountContactRelation> contactAccountIdMap) {
        Set<Id> contactIdSet = contactAccountIdMap.keySet();
        List<AccountContactRelation> beforeRelations = accountContactRelationSelector.getBefore30DaysByContactIds(contactIdSet);
        if (beforeRelations == null) {
            return contactAccountIdMap;
        }
        Set<Id> repeatContactIds = new Set<Id>();
        for (AccountContactRelation relation : beforeRelations) {
            if (!repeatContactIds.contains(relation.ContactId)) {
                if (relation.AccountId != null && relation.AccountId.equals(contactAccountIdMap.get(relation.ContactId).AccountId)) {
                    contactAccountIdMap.remove(relation.ContactId);
                }
            }
            repeatContactIds.add(relation.ContactId);
        }
        return contactAccountIdMap;
    }

    public static Map<Id, OpportunityContactRole> getBuyerCountInLast30Days(Set<Id> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return new Map<Id, OpportunityContactRole>();
        }
        Map<Id, OpportunityContactRole> contactRoleMap = new Map<Id, OpportunityContactRole>();
        List<OpportunityContactRole> in30DayBuyerResult = opportunityContactRoleSelector.getByContactsInLast30DaysByAccountId(accountIds);
        for (OpportunityContactRole contactRole : in30DayBuyerResult) {
            if (!contactRoleMap.containsKey(contactRole.ContactId)) {
                contactRoleMap.put(contactRole.ContactId, contactRole);
            }
        }
        Set<Id> in30DayBuyerIds = CollectionUtils.getIdSet(in30DayBuyerResult, FIELD_CONTACT_ID);
        List<OpportunityContactRole> before30DayBuyerResult = opportunityContactRoleSelector.getByContactsBefore30DaysByContactId(in30DayBuyerIds);
        for (OpportunityContactRole opportunityContactRole : before30DayBuyerResult) {
            contactRoleMap.remove(opportunityContactRole.ContactId);
        }
        return contactRoleMap;
    }

    public static Map<Id, Person_Field_History__c> getExecutiveContactsCountInLast30Days(Set<Id> accountIds) {
        Map<Id, Person_Field_History__c> latestRecordsMap = new Map<Id, Person_Field_History__c>();
        Set<Id> firstContactIds = new Set<Id>();
        // existing contacts that become executive contacts in the last 30 days(role updated)
        if (CollectionUtils.isNotEmpty(accountIds)) {
            List<Person_Field_History__c> fieldHistories = personFieldHistorySelector.getLast30DaysHistoryByRoleAndValueAndAccountIds(FIELD_ROLE, accountIds);
            for (Person_Field_History__c fieldHistory : fieldHistories) {
                if (!firstContactIds.contains(fieldHistory.Person_Id__c) && ROLE_C_LEVEL_EXECUTIVE.equals(fieldHistory.New_Value__c)) {
                    latestRecordsMap.put(fieldHistory.Person_Id__c, fieldHistory);
                }
                firstContactIds.add(fieldHistory.Person_Id__c);
            }
            Set<Id> last30DaysRoleChangedContactIds = latestRecordsMap.keySet();
            List<Person_Field_History__c> beforeHistories = personFieldHistorySelector.getBefore30DaysHistoryByRoleAndContactIds(FIELD_ROLE, last30DaysRoleChangedContactIds);
            Set<Id> before30DaysRoleIsNotCLevelExecutive = new Set<Id>();
            if (beforeHistories != null) {
                for (Person_Field_History__c beforeHistory : beforeHistories) {
                    if (before30DaysRoleIsNotCLevelExecutive.contains(beforeHistory.Person_Id__c)) {
                        continue;
                    }
                    before30DaysRoleIsNotCLevelExecutive.add(beforeHistory.Person_Id__c);
                    if (ROLE_C_LEVEL_EXECUTIVE.equals(beforeHistory.New_Value__c)) {
                        latestRecordsMap.remove(beforeHistory.Person_Id__c);
                    }
                }
            }
        }
        return latestRecordsMap;
    }

    public static Map<Id, AccountContactRelation> getExecutiveContactsCountInLast30Days(Map<Id, AccountContactRelation> relationMap) {
        //  Total number of new executive contacts added to my accounts in the last 30 days
        Map<Id, AccountContactRelation> accountContactRelationMap = new Map<Id, AccountContactRelation>();
        if (!relationMap.isEmpty()) {
            List<Contact> contacts = contactSelector.getContactRoleByContactIds(relationMap.keySet());
            Map<Id, AccountContactRelation> contactAccountIdMap = new Map<Id, AccountContactRelation>();
            for (Contact contact : contacts) {
                if (ROLE_C_LEVEL_EXECUTIVE.equals(contact.job_title__c)) {
                    contactAccountIdMap.put(contact.Id, relationMap.get(contact.Id));
                }
            }
            accountContactRelationMap.putAll(contactAccountIdMap);
        }
        return accountContactRelationMap;
    }

    public RecentMktgEngagementContactDto getRecentMarketingEngagementContacts(Integer lastDays, List<String> userIds, List<String> marketNames, List<String> accountSegments) {
        if ((CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(marketNames)) || CollectionUtils.isEmpty(accountSegments)) {
            return new RecentMktgEngagementContactDto(0, 0, 0, 0, 0, 0);
        }
        List<Holistic_Engagement__c> holisticEngagements = holisticEngagementSelector
            .getRecentEngagementContactsForAccounts(lastDays, new Set<String>(userIds), new Set<String>(marketNames), new Set<String>(accountSegments));
        Set<Id> contactIds = new Set<Id>();
        Set<Id> buyerContactsEngaged = new Set<Id>();
        Integer buyerContactsMarketingEngaged = 0;
        Set<Id> executiveContactsEngagedIds = new Set<Id>();
        Integer executiveContactsMarketingEngaged = 0;
        Set<Id> buyerContactIds = new Set<Id>();

        for (Holistic_Engagement__c holisticEngagement : holisticEngagements) {
            contactIds.add(holisticEngagement.Person__r.Contact__r.Id);
        }

        List<OpportunityContactRole> opportunityContactRoles = opportunityContactRoleSelector.getOrderedOppContactRoleAndOppInfoByContIds(contactIds);
        LOGGER.info('expect contactIds count: ' + contactIds.size());
        LOGGER.info('[getRecentMarketingEngagementContacts] opportunityContactRoles count: ' + opportunityContactRoles.size());
        buyerContactIds = CollectionUtils.getIdSet(opportunityContactRoles, OpportunityContactRole.ContactId);

        for (Holistic_Engagement__c holisticEngagement : holisticEngagements) {
            contactIds.add(holisticEngagement.Person__r.Contact__r.Id);
            if (ROLE_C_LEVEL_EXECUTIVE.equals(holisticEngagement.Person__r.Contact__r.job_title__c)) {
                executiveContactsMarketingEngaged++;
                executiveContactsEngagedIds.add(holisticEngagement.Person__r.Contact__r.Id);
            }
            if (buyerContactIds.contains(holisticEngagement.Person__r.Contact__r.Id)) {
                buyerContactsMarketingEngaged++;
                buyerContactsEngaged.add(holisticEngagement.Person__r.Contact__r.Id);
            }
        }
        return new RecentMktgEngagementContactDto(
            contactIds.size(),
            holisticEngagements.size(),
            buyerContactsEngaged.size(),
            buyerContactsMarketingEngaged,
            executiveContactsEngagedIds.size(),
            executiveContactsMarketingEngaged
        );
    }

    public List<MarketingEngagementDetailDTO> getMarketingEngagementDetailList(Integer lastDays, List<String> userIds, List<String> marketNames, List<String> accountSegments, String contactType) {
        if ((CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(marketNames)) || CollectionUtils.isEmpty(accountSegments)) {
            return new List<MarketingEngagementDetailDTO>();
        }
        List<Holistic_Engagement__c> holisticEngagements = holisticEngagementSelector
            .getMarketingEngagementContactsDetailForAccounts(lastDays, new Set<String>(userIds), new Set<String>(marketNames), new Set<String>(accountSegments));
        Set<Id> buyerContactIds = getBuyerContactIds(holisticEngagements);
        LOGGER.info('holisticEngagements count for ' + contactType + ': ' + holisticEngagements.size());
        if (contactType.equals(ALL_CONTACTS)) {
            return getAllContactsMarketingEngagements(buyerContactIds, holisticEngagements);
        }
        if (contactType.equals(BUYERS)) {
            return getBuyersContactsMarketingEngagements(buyerContactIds, holisticEngagements);
        }
        if (contactType.equals(EXECUTIVE_CONTACTS)) {
            return getExecutiveContactsMarketingEngagements(holisticEngagements);
        }
        return new List<MarketingEngagementDetailDTO>();
    }

    private static List<MarketingEngagementDetailDTO> getAllContactsMarketingEngagements(Set<Id> buyerContactIds, List<Holistic_Engagement__c> holisticEngagements) {
        List<MarketingEngagementDetailDTO> marketingEngagementDetailDTOS = new List<MarketingEngagementDetailDTO>();

        for (Holistic_Engagement__c holisticEngagement : holisticEngagements) {
            List<String> tags = new List<String>();
            if (buyerContactIds.contains(holisticEngagement.Person__r.Contact__r.Id)) {
                tags.add(CONTACT_TYPE_BUYER);
            }
            if (ROLE_C_LEVEL_EXECUTIVE.equals(holisticEngagement.Person__r.Contact__r.job_title__c)) {
                tags.add(CONTACT_TYPE_EXECUTIVE_CONTACT);
            }
            marketingEngagementDetailDTOS.add(new MarketingEngagementDetailDTO(holisticEngagement, tags));
        }
        return marketingEngagementDetailDTOS;
    }

    private static List<MarketingEngagementDetailDTO> getBuyersContactsMarketingEngagements(Set<Id> buyerContactIds, List<Holistic_Engagement__c> holisticEngagements) {
        List<MarketingEngagementDetailDTO> marketingEngagementDetailDTOS = new List<MarketingEngagementDetailDTO>();
        for (Holistic_Engagement__c holisticEngagement : holisticEngagements) {
            if (buyerContactIds.contains(holisticEngagement.Person__r.Contact__r.Id)) {
                marketingEngagementDetailDTOS.add(new MarketingEngagementDetailDTO(holisticEngagement, new List<String>()));
            }
        }
        return marketingEngagementDetailDTOS;
    }

    private static Set<Id> getBuyerContactIds(List<Holistic_Engagement__c> holisticEngagements) {
        Set<Id> contactIds = new Set<Id>();
        for (Holistic_Engagement__c holisticEngagement : holisticEngagements) {
            contactIds.add(holisticEngagement.Person__r.Contact__r.Id);
        }
        List<OpportunityContactRole> opportunityContactRoles = opportunityContactRoleSelector.getOrderedOppContactRoleAndOppInfoByContIds(contactIds);
        LOGGER.info('opportunityContactRoles count: ' + opportunityContactRoles.size());
        Set<Id> buyerContactIds = CollectionUtils.getIdSet(opportunityContactRoles, OpportunityContactRole.ContactId);
        return buyerContactIds;
    }

    private static List<MarketingEngagementDetailDTO> getExecutiveContactsMarketingEngagements(List<Holistic_Engagement__c> holisticEngagements) {
        List<MarketingEngagementDetailDTO> marketingEngagementDetailDTOS = new List<MarketingEngagementDetailDTO>();
        for (Holistic_Engagement__c holisticEngagement : holisticEngagements) {
            if (ROLE_C_LEVEL_EXECUTIVE.equals(holisticEngagement.Person__r.Contact__r.job_title__c)) {
                marketingEngagementDetailDTOS.add(new MarketingEngagementDetailDTO(holisticEngagement, new List<String>()));
            }
        }
        return marketingEngagementDetailDTOS;
    }
}
