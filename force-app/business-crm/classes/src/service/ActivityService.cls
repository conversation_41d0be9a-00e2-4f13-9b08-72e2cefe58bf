public without sharing class ActivityService {
    private final static Logger LOGGER = new Logger(ActivityService.class.getName(), TeamName.CRM);
    private final TaskSelector taskSelector;
    private final EventSelector eventSelector;
    private final ILeadSelector leadSelector;
    private final IMKTContactSelector contactSelector;

    public ActivityService() {
        this.contactSelector = new MKTContactSelector();
        this.leadSelector = new LeadSelector();
        this.taskSelector = new TaskSelector();
        this.eventSelector = new EventSelector();
    }

    public ActivityService(TaskSelector taskSelector, EventSelector eventSelector, ILeadSelector leadSelector, IMKTContactSelector contactSelector) {
        this.taskSelector = taskSelector;
        this.eventSelector = eventSelector;
        this.leadSelector = leadSelector;
        this.contactSelector = contactSelector;
    }

    public void setAffectedPersonsBeforeActivityOperation(List<SObject> activities) {
        Map<Id, Set<Id>> activityToPersonMap = getActivityToPersonIdMapFromActivity(activities);
        ActivityContext.add(activityToPersonMap);
    }

    public void updateLeadOrContactSubStageAfterInsert(List<SObject> newActivity) {
        Set<Id> personIds = getPersonIdsFromActivity(newActivity);
        Map<Id, List<ActivityUnifiedDTO>> personIdToActivitiesMap = getActivitiesByPersonIds(personIds);
        updateLeadOrContactSubStageAndFirstAttemptedDate(personIds, personIdToActivitiesMap, new List<String>{ CRMConstants.SUBSTAGE_NEW_ASSIGNED });
    }

    public void updateLeadOrContactSubStageAfterDelete(List<SObject> oldActivity) {
        Set<Id> personIds = ActivityContext.getPersonIds(CollectionUtils.getIdList(oldActivity));
        Map<Id, List<ActivityUnifiedDTO>> personIdToActivitiesMap = getActivitiesByPersonIds(personIds);
        updateLeadOrContactSubStageAndFirstAttemptedDate(personIds, personIdToActivitiesMap, new List<String>{ CRMConstants.SUBSTAGE_ATTEMPTED });
    }

    public void handlePersonSubstageWhenEmailActivityAfterDelete() {
        Set<Id> personIds = new Set<Id>(CRMTriggerContext.get('emailMessageRelationId'));
        Map<Id, List<ActivityUnifiedDTO>> personIdToActivitiesMap = getActivitiesByPersonIds(personIds);
        updateLeadOrContactSubStageAndFirstAttemptedDate(personIds, personIdToActivitiesMap, new List<String>{ CRMConstants.SUBSTAGE_ATTEMPTED });
    }

    public void backupAffectedPersonsWhenEmailActivityBeforeDelete(Map<Id, EmailMessage> emailMessagesMap) {
        Set<Id> emailMessageId = emailMessagesMap.keySet();
        Set<Id> personIds = new Set<Id>();
        List<EmailMessageRelation> emailMessageRelations = taskSelector.getEmailMessageRelationsByMessageId(emailMessageId);
        for (EmailMessageRelation emailMessageRelation : emailMessageRelations) {
            if (
                emailMessageRelation.RelationObjectType != null &&
                emailMessageRelation.RelationId != null &&
                (emailMessageRelation.RelationObjectType.equals('Lead') || emailMessageRelation.RelationObjectType.equals('Contact'))
            ) {
                personIds.add(emailMessageRelation.RelationId);
            }
        }
        CRMTriggerContext.set('emailMessageRelationId', new List<Id>(personIds));
    }

    public void updateLeadOrContactSubStageAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap, Map<Id, SObject> newMap) {
        Set<Id> leadAndContactIds = new Set<Id>();
        List<SObject> activityWithPersonChangedList = new List<SObject>();
        for (SObject newRecord : newList) {
            Id newRecordId = (Id) newRecord.get('Id');
            Id newWhoId = (Id) newMap.get(newRecordId).get('WhoId');
            Id oldWhoId = (Id) oldMap.get(newRecordId).get('WhoId');
            Integer oldWhoCount = (Integer) oldMap.get(newRecordId).get('WhoCount');
            Integer newWhoCount = (Integer) newMap.get(newRecordId).get('WhoCount');
            if ((newWhoId != oldWhoId || oldWhoCount != newWhoCount) && !LeadConvertContext.isContactConvertedFromLead(oldWhoId, newWhoId)) {
                activityWithPersonChangedList.add(newRecord);
            }
        }
        leadAndContactIds.addAll(ActivityContext.getPersonIds(CollectionUtils.getIdList(activityWithPersonChangedList)));
        leadAndContactIds.addAll(getPersonIdsFromActivity(activityWithPersonChangedList));

        if (leadAndContactIds.isEmpty()) {
            return;
        }
        Map<Id, List<ActivityUnifiedDTO>> whoIdToActivityMap = getActivitiesByPersonIds(leadAndContactIds);
        updateLeadOrContactSubStageAndFirstAttemptedDate(leadAndContactIds, whoIdToActivityMap, new List<String>{ CRMConstants.SUBSTAGE_NEW_ASSIGNED, CRMConstants.SUBSTAGE_ATTEMPTED });
    }

    public void updateLeadOrContactSubStageAndFirstAttemptedDate(Set<Id> leadAndContactIds, Map<Id, List<ActivityUnifiedDTO>> whoIdToActivityMap, List<String> subStage) {
        List<Lead> leads = leadSelector.getSpecifiedSubStagePsLeadsById(leadAndContactIds, subStage);
        List<Contact> contacts = contactSelector.getSpecifiedSubStagePsContactsById(leadAndContactIds, subStage);

        List<SObject> tobeUpdatedLeads = getToBeUpdateRecordAfterActivityInsertOrDeleteOrUpdate(leads, whoIdToActivityMap);
        List<SObject> tobeUpdatedContacts = getToBeUpdateRecordAfterActivityInsertOrDeleteOrUpdate(contacts, whoIdToActivityMap);

        DMLUtils.upsertAndLogErrorMsg(tobeUpdatedLeads, DMLUtils.UPDATE_DML, LOGGER);
        DMLUtils.upsertAndLogErrorMsg(tobeUpdatedContacts, DMLUtils.UPDATE_DML, LOGGER);
    }

    public Map<Id, Set<Id>> getActivityToPersonIdMapFromActivity(List<SObject> activities) {
        Map<Id, Set<Id>> activityToPersonIdsMap = new Map<Id, Set<Id>>();
        if (activities == null || activities.isEmpty()) {
            return activityToPersonIdsMap;
        }
        List<SObject> tasks = new List<SObject>();
        List<SObject> events = new List<SObject>();
        for (SObject activity : activities) {
            if (activity.getSObjectType().getDescribe().getName() == 'Task') {
                tasks.add(activity);
            } else {
                events.add(activity);
            }
        }
        List<TaskRelation> taskRelations = taskSelector.getTaskRelationsByTaskIds(CollectionUtils.getIdSet(tasks, 'Id'));
        List<EventRelation> eventRelations = eventSelector.getEventRelationByEventIds(CollectionUtils.getIdSet(events, 'Id'));

        for (TaskRelation taskRelation : taskRelations) {
            if (activityToPersonIdsMap.containsKey(taskRelation.TaskId)) {
                activityToPersonIdsMap.get(taskRelation.TaskId).addAll(new Set<Id>{ taskRelation.RelationId });
            } else {
                activityToPersonIdsMap.put(taskRelation.TaskId, new Set<Id>{ taskRelation.RelationId });
            }
        }
        for (EventRelation eventRelation : eventRelations) {
            if (activityToPersonIdsMap.containsKey(eventRelation.EventId)) {
                activityToPersonIdsMap.get(eventRelation.EventId).addAll(new Set<Id>{ eventRelation.RelationId });
            } else {
                activityToPersonIdsMap.put(eventRelation.EventId, new Set<Id>{ eventRelation.RelationId });
            }
        }
        return activityToPersonIdsMap;
    }

    public Set<Id> getPersonIdsFromActivity(List<SObject> activities) {
        List<Set<Id>> personIdList = getActivityToPersonIdMapFromActivity(activities).values();
        Set<Id> personIdSet = new Set<Id>();
        for (Set<Id> personIds : personIdList) {
            personIdSet.addAll(personIds);
        }
        return personIdSet;
    }

    private List<SObject> getToBeUpdateRecordAfterActivityInsertOrDeleteOrUpdate(List<SObject> records, Map<Id, List<ActivityUnifiedDTO>> whoIdToActivities) {
        List<SObject> tobeUpdatedRecords = new List<SObject>();
        for (SObject record : records) {
            Id recordId = (Id) record.get('Id');
            String subStage = (String) record.get('SAL_Substage__c');
            List<ActivityUnifiedDTO> activities = whoIdToActivities.get(recordId);

            if (subStage == CRMConstants.SUBSTAGE_ATTEMPTED) {
                if (activities == null) {
                    record.put('SAL_Substage__c', CRMConstants.SUBSTAGE_NEW_ASSIGNED);
                    record.put('Date_of_First_Attempt__c', null);
                    record.put('Date_of_Attempted__c', null);
                }
                if (activities != null && !activities.isEmpty()) {
                    record.put('Date_of_First_Attempt__c', getEarliestActivityDate(activities));
                }
                tobeUpdatedRecords.add(record);
            }
            if (subStage == CRMConstants.SUBSTAGE_NEW_ASSIGNED && activities != null && !activities.isEmpty()) {
                record.put('SAL_Substage__c', CRMConstants.SUBSTAGE_ATTEMPTED);
                record.put('Date_of_First_Attempt__c', getEarliestActivityDate(activities));
                tobeUpdatedRecords.add(record);
            }
        }
        return tobeUpdatedRecords;
    }

    public Map<Id, List<ActivityUnifiedDTO>> getActivitiesByPersonIds(Set<Id> personIds) {
        List<TaskRelation> taskRelations = taskSelector.getTaskRelationsByRelationId(personIds);
        List<EventRelation> eventRelations = eventSelector.getEventRelationByRelationIds(personIds);
        Map<Id, List<ActivityUnifiedDTO>> activityMap = new Map<Id, List<ActivityUnifiedDTO>>();

        for (TaskRelation taskRelation : taskRelations) {
            Task task = taskRelation.Task;
            if (task != null) {
                ActivityUnifiedDTO activityUnifiedDTO = new ActivityUnifiedDTO(
                    taskRelation.Id,
                    taskRelation.RelationId,
                    taskRelation.CreatedDate,
                    task.Subject,
                    task.TaskSubtype,
                    task.ActivityDate,
                    task.Status
                );
                if (activityMap.containsKey(taskRelation.RelationId)) {
                    activityMap.get(taskRelation.RelationId).addAll(new List<ActivityUnifiedDTO>{ activityUnifiedDTO });
                } else {
                    activityMap.put(taskRelation.RelationId, new List<ActivityUnifiedDTO>{ activityUnifiedDTO });
                }
            }
        }
        for (EventRelation eventRelation : eventRelations) {
            Event event = eventRelation.Event;
            if (event != null) {
                ActivityUnifiedDTO activityUnifiedDTO = new ActivityUnifiedDTO(
                    eventRelation.Id,
                    eventRelation.RelationId,
                    eventRelation.CreatedDate,
                    event.Subject,
                    event.EventSubtype,
                    event.ActivityDate,
                    null
                );
                if (activityMap.containsKey(eventRelation.RelationId)) {
                    activityMap.get(eventRelation.RelationId).addAll(new List<ActivityUnifiedDTO>{ activityUnifiedDTO });
                } else {
                    activityMap.put(eventRelation.RelationId, new List<ActivityUnifiedDTO>{ activityUnifiedDTO });
                }
            }
        }

        return activityMap;
    }

    public Date getEarliestActivityDate(List<ActivityUnifiedDTO> activities) {
        if (activities == null || activities.isEmpty()) {
            return null;
        }
        Date earliestActivityDate = getActivityDateFromActivity(activities[0]);
        for (ActivityUnifiedDTO activity : activities) {
            Date activityDate = getActivityDateFromActivity(activity);
            if (activityDate < earliestActivityDate) {
                earliestActivityDate = activityDate;
            }
        }
        return earliestActivityDate;
    }

    public Date getActivityDateFromActivity(ActivityUnifiedDTO activity) {
        if (activity.activityType == 'Task') {
            return activity.CreatedDate.date();
        }
        Date activityDate = activity.ActivityDate;
        return activityDate != null ? activityDate : activity.CreatedDate.date();
    }

    public List<ActivityDTO> getActivityDtosOfPerson(Id leadOrContactId) {
        List<ActivityDTO> activityDTOS = new List<ActivityDTO>();
        Map<Id, List<ActivityUnifiedDTO>> activityUnifiedDTOMap = getActivitiesByPersonIds(new Set<Id>{ leadOrContactId });
        for (List<ActivityUnifiedDTO> activities : activityUnifiedDTOMap.values()) {
            for (ActivityUnifiedDTO activityUnifiedDTO : activities) {
                activityDTOS.add(new ActivityDTO(activityUnifiedDTO.id, activityUnifiedDTO.subject, activityUnifiedDTO.activityType, activityUnifiedDTO.createdDate));
            }
        }
        return activityDTOS;
    }

    public Boolean isAllActivityWillArchivedIn7daysForPerson(Id leadOrContactId) {
        Map<Id, List<ActivityUnifiedDTO>> activityUnifiedDTOMap = getActivitiesByPersonIds(new Set<Id>{ leadOrContactId });
        List<ActivityUnifiedDTO> activities = activityUnifiedDTOMap.get(leadOrContactId);
        if (activities == null || activities.isEmpty()) {
            return false;
        }
        for (ActivityUnifiedDTO activityUnifiedDTO : activities) {
            if (existActivityArchivedOver7days(activityUnifiedDTO)) {
                return false;
            }
        }
        return true;
    }

    private Boolean existActivityArchivedOver7days(ActivityUnifiedDTO activityUnifiedDTO) {
        if (activityUnifiedDTO.activityType == 'Event') {
            return over7days(activityUnifiedDTO.activityDate);
        }

        if (activityUnifiedDTO.status != CRMConstants.ACTIVITY_COMPLETED_STATUS) {
            return true;
        }

        if (activityUnifiedDTO.activityDate != null) {
            return over7days(activityUnifiedDTO.activityDate);
        }

        return over7days(activityUnifiedDTO.createdDate.date());
    }

    private Boolean over7days(Date activityDate) {
        Date archivedDate = activityDate.addDays(365);
        return Datetime.now().date().daysBetween(archivedDate) > 7;
    }
}
