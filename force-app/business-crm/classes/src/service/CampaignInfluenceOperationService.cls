public without sharing class CampaignInfluenceOperationService {
    private CampaignInfluenceService camInfluenceService = new CampaignInfluenceService();
    private IOpportunityContactRoleSelector oppContactRoleSelector = new OpportunityContactRoleSelector();
    private static Id psOppRecordType = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Professional Services Opportunity').getRecordTypeId();
    private static Map<String, String> OPPORTUNITY_FIELDS_MAP = new Map<String, String>{
       'Is_Extension__c' => 'Is Extension',
       'Cluster__c' => 'GTM Offering',
       'CloseDate' => 'Close Date',
       'Opportunity_Contract_Value_in_USD__c' => 'Opportunity Contract in USD'
    };

    public void publishCampaignInfluenceEventsForContacts(List<OpportunityContactRole> contactRoles, String dmlType) {
        List<Campaign_Influence_Operation__e> events = new List<Campaign_Influence_Operation__e>();
        Datetime publishDatetime = Datetime.now();
        for (OpportunityContactRole contactRole : contactRoles) {
            if (contactRole.Is_Ps__c) {
                String action = null;
                String eventType = CRMConstants.INFLUENCE_EVENT_TYPE_BASED_CONTACT_FOR_PIPELINE;
                if (dmlType == DMLUtils.INSERT_DML) {
                    action = String.format('Add Contact({0}) to Opportunity({1}), Contact Role Id is {2}', new List<String>{ contactRole.ContactId,  contactRole.OpportunityId, contactRole.Id});
                }
                if (dmlType == DMLUtils.DELETE_DML) {
                    action = String.format('Remove Contact({0}) from Opportunity({1}), Contact Role Id is {2}', new List<String>{ contactRole.ContactId,  contactRole.OpportunityId, contactRole.Id});
                }
                events.add(generateInfluenceOperationEvent(contactRole.ContactId, null, eventType, action, publishDatetime));
            }
        }
        EventPublisher.publishCampaignInfluenceOperationEvent(events);
    }

    public void publishCampaignInfluenceEventsForOpportunities(List<Opportunity> newOpportunities, Map<Id, Opportunity> oldOpportunityMap) {
        List<Campaign_Influence_Operation__e> events = new List<Campaign_Influence_Operation__e>();
        Datetime publishDatetime = Datetime.now();
        for (Opportunity newOpp : newOpportunities) {
            if (newOpp.RecordTypeId != psOppRecordType) {
                continue;
            }
            Opportunity oldOpp = oldOpportunityMap.get(newOpp.Id);

            Boolean shouldRecalculatePipeline = false;
            Boolean shouldRecalculateAttribution = false;

            List<String> changes = new List<String>();

            if (oldOpp.StageName != newOpp.StageName && (oldOpp.StageName == CRMConstants.OPPORTUNITY_STAGE_CLOSED_WON || newOpp.StageName == CRMConstants.OPPORTUNITY_STAGE_CLOSED_WON)) {
                changes.add(String.format('Stage from {0} to {1}', new List<String>{ oldOpp.StageName,  newOpp.StageName}));
                shouldRecalculatePipeline = true;
            }
            for (String fieldAPIName: OPPORTUNITY_FIELDS_MAP.keySet()) {
                if (oldOpp.get(fieldAPIName) != newOpp.get(fieldAPIName)) {
                    changes.add(OPPORTUNITY_FIELDS_MAP.get(fieldAPIName) + ' from ' + oldOpp.get(fieldAPIName) + ' to ' + newOpp.get(fieldAPIName));
                    if (fieldAPIName == 'Opportunity_Contract_Value_in_USD__c') {
                        shouldRecalculateAttribution = true;
                    } else {
                        shouldRecalculatePipeline = true;
                    }
                }
            }

            String action = 'Change ' +  String.join(changes, ' && ') + ' on Opportunity(' + newOpp.Id + ')';
            if (shouldRecalculatePipeline) {
                events.add(generateInfluenceOperationEvent(null, newOpp.Id, CRMConstants.INFLUENCE_EVENT_TYPE_BASED_OPPORTUNITY_FOR_PIPELINE, action, publishDatetime));
            }
            if (shouldRecalculateAttribution) {
                events.add(generateInfluenceOperationEvent(null, newOpp.Id, CRMConstants.INFLUENCE_EVENT_TYPE_BASED_OPPORTUNITY_FOR_ATTRIBUTION, action, publishDatetime));
            }

        }
        EventPublisher.publishCampaignInfluenceOperationEvent(events);
    }

    private Campaign_Influence_Operation__e generateInfluenceOperationEvent(Id contactId, Id opportunityId, String type, String action, Datetime publishDatetime) {
        Campaign_Influence_Operation__e event = new Campaign_Influence_Operation__e();
        event.Contact_Id__c = contactId;
        event.Opportunity_Id__c = opportunityId;
        event.Type__c = type;
        event.Action__c = action;
        event.Publish_Datetime__c = publishDatetime;
        event.Publisher_Id__c = UserInfo.getUserId();
        return event;
    }

    public void recalculateCampaignInfluences(List<Campaign_Influence_Operation__e> influenceEvents) {
        Set<Id> opportunityIds = new Set<Id>();
        Set<Id> contactIds = new Set<Id>();
        for (Campaign_Influence_Operation__e event : influenceEvents) {
            if (event.Type__c == CRMConstants.INFLUENCE_EVENT_TYPE_BASED_OPPORTUNITY_FOR_PIPELINE) {
                opportunityIds.add(event.Opportunity_Id__c);
            }
            if (event.Type__c == CRMConstants.INFLUENCE_EVENT_TYPE_BASED_CONTACT_FOR_PIPELINE) {
                contactIds.add(event.Contact_Id__c);
            }
        }
        opportunityIds.remove(null);
        if (!opportunityIds.isEmpty()) {
            List<OpportunityContactRole> contactRoles = oppContactRoleSelector.getOppoContactRoleWithOppInfoByOppoIdsOrderByCreatedDateIdAsc(opportunityIds);
            for (OpportunityContactRole contactRole : contactRoles) {
                if (contactRole.Is_Ps__c) {
                    contactIds.add(contactRole.ContactId);
                }
            }
        }

        contactIds.remove(null);
        if (!contactIds.isEmpty()) {
            camInfluenceService.insertOrUpdateOrDeleteInfluencesForTheseContacts(contactIds);
        }
    }

    public void recalculateAttributionValueOfInfluences(List<Campaign_Influence_Operation__e> influenceEvents) {
        Set<Id> opportunityIds = new Set<Id>();
        for (Campaign_Influence_Operation__e event : influenceEvents) {
            if (event.Type__c == CRMConstants.INFLUENCE_EVENT_TYPE_BASED_OPPORTUNITY_FOR_ATTRIBUTION) {
                opportunityIds.add(event.Opportunity_Id__c);
            }
        }
        opportunityIds.remove(null);
        if (!opportunityIds.isEmpty()) {
            camInfluenceService.updateTouchPointAndAttributionValueOfInfluences(opportunityIds);
        }
    }
}