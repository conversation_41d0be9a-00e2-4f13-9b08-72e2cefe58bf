public without sharing class TaskService implements ITaskService {
    private static final String TASK_COMPLETED_STATUS = 'Completed';

    private static TaskSelector taskSelector = new TaskSelector();

    public void createTask(Task task) {
        insert task;
    }

    public void createReminderTask(Task reminderTask) {
        if (reminderTask != null && reminderTask.ActivityDate != null && reminderTask.OwnerId != null) {
            reminderTask.Subject = 'Follow-up reminder';
            reminderTask.Status = 'Not Started';
            insert reminderTask;
        }
    }

    public void updateOriginalSujectField(List<Task> newTasks) {
        for (Task task : newTasks) {
            if (task.Subject_Text__c != null) {
                task.Subject = task.Subject_Text__c;
            }
        }
    }

    public Integer getOpenTaskCount(Id userId) {
        return taskSelector.getOpenTaskCount(userId);
    }

    public Integer getDelegatedTaskCount(Id userId) {
        return taskSelector.getDelegatedTaskCount(userId);
    }

    public List<Task> getRecentDueOpenTaskList(Id userId, Integer openTaskAmountLimit) {
        return taskSelector.getRecentDueOpenTaskList(userId, openTaskAmountLimit);
    }

    public List<Task> getRecentDueDelegatedTaskList(Id userId, Integer delagatedTaskAmountLimit) {
        return taskSelector.getRecentDueDelegatedTaskList(userId, delagatedTaskAmountLimit);
    }

    public void changeTaskStatusToComplete(Id taskId) {
        taskSelector.changeTaskStatus(taskId, TASK_COMPLETED_STATUS);
    }

    public void changeTaskStatusToOrigin(Id taskId, String originStatus) {
        taskSelector.changeTaskStatus(taskId, originStatus);
    }
}
