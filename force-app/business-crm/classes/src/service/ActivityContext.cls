public without sharing class ActivityContext {
    private static final Map<Id, Set<Id>> activityIdToPersonIdsMap = new Map<Id, Set<Id>>();

    private ActivityContext() {
    }

    public static void add(Map<Id, Set<Id>> activityIdToPersonIds) {
        if (activityIdToPersonIds == null) {
            return;
        }
        activityIdToPersonIdsMap.putAll(activityIdToPersonIds);
    }

    public static Set<Id> getPersonIds(List<Id> activityIds) {
        Set<Id> personIds = new Set<Id>();
        for (Id activityId : activityIds) {
            if (activityIdToPersonIdsMap.containsKey(activityId)) {
                personIds.addAll(activityIdToPersonIdsMap.get(activityId));
            }
        }
        return personIds;
    }
}
