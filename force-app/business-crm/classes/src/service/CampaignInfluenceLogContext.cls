public without sharing class CampaignInfluenceLogContext {
    private static Id operatorId;
    private static List<String> operationPaths = new List<String>();
    private static Datetime operationDatetime;

    public static void setOperationInfo(Id inputOperatorId, List<String> inputOperationPaths, Datetime inputOperationDatetime) {
        operatorId = inputOperatorId;
        operationDatetime = inputOperationDatetime;
        operationPaths = inputOperationPaths;
    }

    public static Id getOperatorId() {
        return operatorId;
    }

    public static String getOperationPaths() {
        return String.join(operationPaths , '; ') ;
    }

    public static Datetime getOperationDatetime(Datetime timeNow) {
        return operationDatetime;
    }

    public static void setInfluenceLogContextByInfluenceEvent(List<Campaign_Influence_Operation__e> influenceEvents) {
        Id operatorIdFromEvent = null;
        Datetime operationDatetimeFromEvent = null;
        List<String> operationPathsFromEvent = new List<String>();
        for (Campaign_Influence_Operation__e event : influenceEvents) {
            if (operatorIdFromEvent == null) {
                operatorIdFromEvent = event.Publisher_Id__c;
            }
            if (operationDatetimeFromEvent == null) {
                operationDatetimeFromEvent = event.Publish_Datetime__c;
            }
            if (!operationPathsFromEvent.contains(event.Action__c)) {
                operationPathsFromEvent.add(event.Action__c);
            }
        }
        setOperationInfo(operatorIdFromEvent, operationPathsFromEvent, operationDatetimeFromEvent);
    }

    public static void setInfluenceLogContextByCampaignInfluence(List<Campaign_Influence__c> finalInfluenceList, String dmlType) {
        if (operatorId != null) {
            return;
        }
        Datetime timeNow = Datetime.now();
        List<String> operationPathsFromInfluence = new List<String>();
        for (Campaign_Influence__c influence : finalInfluenceList) {
            if (dmlType == DMLUtils.INSERT_DML) {
                operationPathsFromInfluence.add('Insert Campaign Influence(' + influence.Id + ')');
            } else if (dmlType == DMLUtils.UPDATE_DML) {
                operationPathsFromInfluence.add('Update Campaign Influence(' + influence.Id + ')');
            } else if (dmlType == DMLUtils.DELETE_DML) {
                operationPathsFromInfluence.add('Delete Campaign Influence(' + influence.Id + ')');
            }
        }
        setOperationInfo(UserInfo.getUserId(), operationPathsFromInfluence, timeNow);
    }

    public static void setInfluenceLogContextByCampaign(Map<Id, Campaign> oldCampaignMap) {
        List<String> operationPathsFromCampaign = new List<String>();
        for (Id campaignId : oldCampaignMap.keySet()) {
            operationPathsFromCampaign.add(String.format('Delete Campaign({0})', new List<String>{ campaignId}));
        }
        setOperationInfo(UserInfo.getUserId(), operationPathsFromCampaign, Datetime.now());
    }

    public static void setInfluenceLogContextByMember(List<CampaignMember> newMembers, Map<Id, CampaignMember> oldMemberMap, String dmlType) {
        List<String> operationPathsFromMember = new List<String>();
        if (dmlType == DMLUtils.INSERT_DML || dmlType == DMLUtils.DELETE_DML) {
            List<CampaignMember> finalCampaignMemberList = oldMemberMap == null ? newMembers : oldMemberMap.values() ;
            for (CampaignMember camMember : finalCampaignMemberList) {
                if (dmlType == DMLUtils.DELETE_DML && isCampaignMemberForDeletedCampaign(camMember)) {
                    continue;
                }
                if (camMember.HasResponded) {
                    operationPathsFromMember.add(String.format('{0} Responded Campaign({1}) Member({2})', new List<String>{ dmlType == DMLUtils.INSERT_DML ? 'Add':'Delete', camMember.CampaignId,  camMember.ContactId}));
                }
            }
        }
        if (dmlType == DMLUtils.UPDATE_DML) {
            for (CampaignMember newMember : newMembers) {
                if (newMember.HasResponded && !oldMemberMap.get(newMember.Id).HasResponded) {
                    operationPathsFromMember.add(String.format('Change Campaign({0}) Member({1}) to Responded', new List<String>{ newMember.CampaignId,  newMember.ContactId}));
                }
                if (oldMemberMap.get(newMember.Id).HasResponded && !newMember.HasResponded) {
                    operationPathsFromMember.add(String.format('Change Campaign({0}) Member({1}) to UnResponded', new List<String>{ newMember.CampaignId,  newMember.ContactId}));
                }
            }
        }
        if (!operationPathsFromMember.isEmpty()) {
            setOperationInfo(UserInfo.getUserId(), operationPathsFromMember, Datetime.now());
        }
    }

    private static Boolean isCampaignMemberForDeletedCampaign(CampaignMember camMember) {
        for (String operationPath : operationPaths) {
            if (operationPath == String.format('Delete Campaign({0})', new List<String>{ camMember.CampaignId})) {
                return true;
            }
        }
        return false;
    }
}