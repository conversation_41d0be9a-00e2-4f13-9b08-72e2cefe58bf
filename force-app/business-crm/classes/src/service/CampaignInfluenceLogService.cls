public without sharing class CampaignInfluenceLogService {
    private final Logger LOGGER = new Logger(CampaignInfluenceLogService.class.getName(), TeamName.CRM);
    private final CampaignInfluenceLogSelector influenceLogSelector = new CampaignInfluenceLogSelector();
    private static Map<String, String> KEY_INFLUENCE_FIELDS_MAP_FOR_LOG = new Map<String, String>{
        'Marketing_Pipeline__c' => 'Marketing Pipeline',
        'Touch_Point__c' => 'Touch Point',
        'Attribution_Value_In_USD__c' => 'Attribution Value In USD',
        'First_Responded_Datetime__c' => 'First Responded Datetime',
        'Frozen__c' => 'Frozen',
        'Is_Continuity_MA__c' => 'Is Continuity MA'
    };

    public void insertInfluenceLogs(List<Campaign_Influence__c> newInfluenceList, Map<Id, Campaign_Influence__c> oldInfluenceMap, String dmlType) {
        List<Campaign_Influence_Log__c> influenceLogs = new List<Campaign_Influence_Log__c>();
        Datetime timeNow = Datetime.now();

        try{
            if (dmlType == DMLUtils.INSERT_DML) {
                addInfluenceLogToListWhenCreateDeleteInfluence(influenceLogs, newInfluenceList, timeNow, 'Insert');
            }
            if (dmlType == DMLUtils.DELETE_DML) {
                addInfluenceLogToListWhenCreateDeleteInfluence(influenceLogs, oldInfluenceMap.values(), timeNow, 'Delete');
            }
            if (dmlType == DMLUtils.UPDATE_DML) {
                for (Campaign_Influence__c newInfluence: newInfluenceList) {
                    List<String> changedKeyFieldValues = new List<String>();
                    Campaign_Influence__c oldInfluence = oldInfluenceMap.get(newInfluence.Id);
                    for (String field : KEY_INFLUENCE_FIELDS_MAP_FOR_LOG.keySet()) {
                        if (newInfluence.get(field) != oldInfluence.get(field)) {
                            if (field == 'First_Responded_Datetime__c') {
                                changedKeyFieldValues.add('Change ' + KEY_INFLUENCE_FIELDS_MAP_FOR_LOG.get(field) + ' from ' + DateUtils.toGTMString(oldInfluence.First_Responded_Datetime__c) + ' to ' + DateUtils.toGTMString(newInfluence.First_Responded_Datetime__c));
                            } else {
                                changedKeyFieldValues.add('Change ' + KEY_INFLUENCE_FIELDS_MAP_FOR_LOG.get(field) + ' from ' + oldInfluence.get(field) + ' to ' + newInfluence.get(field));
                            }
                        }
                    }
                    if (!changedKeyFieldValues.isEmpty()) {
                        String operationInfluence = 'Update Campaign Influence, detail: ' + String.join(changedKeyFieldValues, ', ');
                        influenceLogs.add(generateInfluenceLog(newInfluence, timeNow, operationInfluence));
                    }
                }
            }

            if (!influenceLogs.isEmpty()) {
                setConIdOrOppoIdOnInfluenceLogWhenConIdOrOppoIdIsEmpty(influenceLogs);
                influenceLogSelector.insertInfluenceLogs(influenceLogs);
            }
        } catch (Exception ex) {
            LOGGER.error('Fail to insert campaign influence log, error message: ' + ex.getMessage() + ', stack: ' + ex.getStackTraceString() + ', context: operator: ' + CampaignInfluenceLogContext.getOperatorId() + ', operation paths: ' + CampaignInfluenceLogContext.getOperationPaths());
        }
    }

    private void addInfluenceLogToListWhenCreateDeleteInfluence(List<Campaign_Influence_Log__c> influenceLogs, List<Campaign_Influence__c> influenceList, Datetime timeNow, String dmlType) {
        for (Campaign_Influence__c influence: influenceList) {
            List<String> keyFieldValues = new List<String>();
            for (String field : KEY_INFLUENCE_FIELDS_MAP_FOR_LOG.keySet()) {
                if (field == 'First_Responded_Datetime__c') {
                    keyFieldValues.add(KEY_INFLUENCE_FIELDS_MAP_FOR_LOG.get(field) + ' is ' + DateUtils.toGTMString(influence.First_Responded_Datetime__c));
                } else {
                    keyFieldValues.add(KEY_INFLUENCE_FIELDS_MAP_FOR_LOG.get(field) + ' is ' + influence.get(field));
                }
            }
            String operationInfluence = dmlType + ' Campaign Influence, detail: ' + String.join(keyFieldValues, ', ');
            influenceLogs.add(generateInfluenceLog(influence, timeNow, operationInfluence));
        }
    }

    private Campaign_Influence_Log__c generateInfluenceLog(Campaign_Influence__c newInfluence, Datetime timeNow, String operationInfluence) {
        Campaign_Influence_Log__c influenceLog = new Campaign_Influence_Log__c();
        influenceLog.Campaign_Influence_Id__c = newInfluence.Id;
        influenceLog.Opportunity_Id__c = newInfluence.Opportunity__c;
        influenceLog.Contact_Id__c = newInfluence.Contact__c;
        influenceLog.Campaign_Id__c = newInfluence.Campaign__c;
        influenceLog.Opportunity_Contact_Role_Id__c = newInfluence.Opportunity_Contact_Role_Id__c;
        influenceLog.Operator__c = CampaignInfluenceLogContext.getOperatorId();
        influenceLog.Operation_Datetime__c = CampaignInfluenceLogContext.getOperationDatetime(timeNow);
        influenceLog.Operation_Path__c = CampaignInfluenceLogContext.getOperationPaths();
        influenceLog.Operation_influence__c = operationInfluence;
        return influenceLog;
    }

    public void updateContactIdOnInfluenceLogWhenMergeContacts(Id masterContactId, List<Contact> duplicateContacts) {
        Set<Id> duplicateContactIds = CollectionUtils.getIdSet(duplicateContacts);
        List<Campaign_Influence_Log__c> influenceLogs = influenceLogSelector.getInfluenceLogsByContactIds(duplicateContactIds);
        for (Campaign_Influence_Log__c log: influenceLogs) {
            log.Contact_Id__c = masterContactId;
        }
        influenceLogSelector.updateInfluenceLogs(influenceLogs);
    }

    private void setConIdOrOppoIdOnInfluenceLogWhenConIdOrOppoIdIsEmpty(List<Campaign_Influence_Log__c> newInfluenceLogs) {
        List<Campaign_Influence_Log__c> influenceLogsWithEmptyConIdOrOppoId = new List<Campaign_Influence_Log__c>();
        Set<Id> influenceIds = new Set<Id>();
        for (Campaign_Influence_Log__c influenceLog: newInfluenceLogs) {
            if (influenceLog.Contact_Id__c == null || influenceLog.Opportunity_Id__c == null) {
                influenceLogsWithEmptyConIdOrOppoId.add(influenceLog);
                influenceIds.add(influenceLog.Campaign_Influence_Id__c);
            }
        }
        if (influenceLogsWithEmptyConIdOrOppoId.isEmpty()) {
            return;
        }
        List<Campaign_Influence_Log__c> influenceLogs = influenceLogSelector.getInfluenceLogsByInfluenceIds(influenceIds);
        Map<Id, List<Campaign_Influence_Log__c>> influenceIdToLogsMap = CollectionUtils.groupByIdField(influenceLogs, 'Campaign_Influence_Id__c');

        for (Campaign_Influence_Log__c influenceLog: influenceLogsWithEmptyConIdOrOppoId) {
            Id campaignInfluenceId = influenceLog.Campaign_Influence_Id__c;
            if (influenceIdToLogsMap.containsKey(campaignInfluenceId) && !influenceIdToLogsMap.get(campaignInfluenceId).isEmpty()) {
                Campaign_Influence_Log__c previousLogWithConIdAndOppoId = influenceIdToLogsMap.get(campaignInfluenceId).get(0);
                influenceLog.Contact_Id__c = previousLogWithConIdAndOppoId.Contact_Id__c;
                influenceLog.Opportunity_Id__c = previousLogWithConIdAndOppoId.Opportunity_Id__c;
            }
        }
    }
}