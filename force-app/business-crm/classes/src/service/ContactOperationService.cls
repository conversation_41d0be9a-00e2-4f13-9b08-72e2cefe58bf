public without sharing class ContactOperationService {
    private AccountService accountService = new AccountService();
    private CampaignMemberService campaignMemberService = new CampaignMemberService();

    public ContactOperationService() {
    }

    public ContactOperationService(AccountService accountService, CampaignMemberService campaignMemberService) {
        this.accountService = accountService;
        this.campaignMemberService = campaignMemberService;
    }

    public void publishContactEvent(TriggerParameters tp) {
        List<Contact_Operation__e> eventsToPublish = generateContactEvent(tp);
        EventPublisher.publishContactOperationEvent(eventsToPublish);
    }

    private List<Contact_Operation__e> generateContactEvent(TriggerParameters tp) {
        List<Contact_Operation__e> eventsToGenerate = new List<Contact_Operation__e>();

        String operationType = tp.operationType;
        Map<Id, Contact> oldMap = (Map<Id, Contact>) tp.oldMap;
        Map<Id, Contact> newMap = (Map<Id, Contact>) tp.newMap;

        List<Contact> contactList = (oldMap == null) ? newMap.values() : oldMap.values();
        for (Id contactId : CollectionUtils.getIdSet(contactList)) {
            Contact oldContact = new Contact();
            if (oldMap != null) {
                oldContact = oldMap.get(contactId);
            }
            Contact newContact = new Contact();
            if (newMap != null) {
                newContact = newMap.get(contactId);
            }
            Contact_Operation__e event = new Contact_Operation__e();
            event.Record_Id__c = contactId;
            event.Operation__c = operationType;
            event.Object__c = 'Contact';
            event.Publisher__c = 'CRM';
            event.Change_Fields__c = EventUtils.getChangedFields(Contact.SObjectType, oldContact, newContact);
            event.Extra__c = EventUtils.constructChangedFieldsValueJson(new List<Schema.SObjectField>{ Contact.AccountId }, oldContact, newContact);
            eventsToGenerate.add(event);
        }
        return eventsToGenerate;
    }

    public void updateNumberOfBuyersWhenAccountIdChange(List<Contact_Operation__e> events) {
        Set<Id> accountIdSet = new Set<Id>();
        for (Contact_Operation__e event : events) {
            List<String> changeFields = Utility.getEventChangeFields(event.Change_Fields__c);
            if (event.Operation__c == 'Update' && changeFields.contains('accountid')) {
                Map<String, Object> jsonMap = (Map<String, Object>) JSON.deserializeUntyped(event.Extra__c);
                accountIdSet.add((Id) ((Map<String, Object>) jsonMap.get('oldValue')).get('AccountId'));
                accountIdSet.add((Id) ((Map<String, Object>) jsonMap.get('newValue')).get('AccountId'));
            }
        }
        accountService.syncNumberOfBuyersOfAccount(accountIdSet);
    }

    public void updateCampaignMemberWhenContactChange(List<Contact_Operation__e> events) {
        Set<Id> contactIds = new Set<Id>();
        for (Contact_Operation__e event : events) {
            List<String> changeFields = Utility.getEventChangeFields(event.Change_Fields__c);
            if (event.Operation__c == 'Update' && changeFields.contains('recent_marketing_influenced_campaign__c')) {
                contactIds.add(event.Record_Id__c);
            }
        }
        if (!contactIds.isEmpty()) {
            campaignMemberService.syncContacts(contactIds);
        }
    }
}
