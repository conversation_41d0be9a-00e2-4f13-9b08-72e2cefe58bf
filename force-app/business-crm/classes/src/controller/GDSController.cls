public with sharing class GDSController {
    @TestVisible
    private static IGDSService gdsServie = new GDSService();
    @TestVisible
    private static IOpportunitySelector opportunitySelector = new OpportunitySelector();
    @AuraEnabled
    public static void sendGDSRequest(GDS_Request__c request, Id userId) {
        try {
            gdsServie.sendGDSRequest(request, userId);
        } catch (Exception e) {
            throw new AuraHandledException('Send GDS request failed: ' + e.getMessage());
        }
    }

    @AuraEnabled(cacheable=true)
    public static List<Opportunity> searchOpportunitiesByAccountId(String name, Id accountId) {
        return opportunitySelector.searchOpportunitiesByAccountId(name, accountId);
    }
}
