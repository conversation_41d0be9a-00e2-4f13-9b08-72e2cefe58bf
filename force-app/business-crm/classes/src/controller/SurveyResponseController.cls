public with sharing class SurveyResponseController {
    @TestVisible
    private static SurveyResponseService surveyResponseService = new SurveyResponseService();

    @AuraEnabled(cacheable=true)
    public static List<SurveyResponseDetail> getAccountSurveyResponses(String accountId) {
        return surveyResponseService.getNewestNPSSurveyResponseByAccountId(accountId);
    }

    @AuraEnabled(cacheable=true)
    public static Map<String, SurveyResponseCategories> getNPSSurveyCategories(List<Id> accountIds) {
        return surveyResponseService.getNPSSurveyCategories(new Set<Id>(accountIds));
    }

    @AuraEnabled(cacheable=true)
    public static Map<String, NetPromoterScore> getNPSSurveyNetPromoterScore(List<Id> accountIds) {
        return surveyResponseService.getNPSSurveyNetPromoterScore(new Set<Id>(accountIds));
    }

    @AuraEnabled(cacheable=true)
    public static List<Survey_Response__c> getNPSAccountSurveyResponses(List<Id> accountIds) {
        return surveyResponseService.getNPSSurveyResponseByAccountIds(new Set<Id>(accountIds));
    }

    @AuraEnabled(cacheable=true)
    public static List<SurveyResponseDetail> getAccountSurveyResponsesByYear(String accountId, Integer year) {
        return surveyResponseService.getNPSSurveyResponseByAccountIdAndYear(accountId, year);
    }

    @AuraEnabled(cacheable=true)
    public static List<Integer> getSurveyResponseYears(String accountId) {
        return surveyResponseService.getNPSSurveyResponseYears(accountId);
    }

    @AuraEnabled(cacheable=true)
    public static Map<String, Survey_Response__c> getLastSurveyResponseByAccountId(String accountId, Integer year) {
        return surveyResponseService.getLastSurveyResponseByAccountId(accountId, year);
    }

    @AuraEnabled(cacheable=true)
    public static List<SurveyResponseDetail> getContactNPSSurveyResponses(String contactId) {
        return surveyResponseService.getContactNPSSurveyResponses(contactId);
    }
}
