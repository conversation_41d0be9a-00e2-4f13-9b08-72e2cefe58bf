/**
 * Created by fan<PERSON><PERSON><PERSON><PERSON> on 2025/3/26.
 */
public with sharing class RiskAlarmingController {
    @TestVisible
    private static RiskAlarmingService riskAlarmingService = new RiskAlarmingService();

    @AuraEnabled(cacheable=false)
    public static Decimal getWorkAtRiskAmount(List<Id> ownerIds, List<String> marketNames, List<String> accountSegments) {
        handleParams(ownerIds, marketNames);
        return riskAlarmingService.getWorkAtRiskAmount(ownerIds, marketNames, accountSegments);
    }

    @AuraEnabled(cacheable=false)
    public static List<WorkAsRiskDTO> getWorkAsRiskList(List<Id> ownerIds, List<String> marketNames, List<String> accountSegments) {
        handleParams(ownerIds, marketNames);
        return riskAlarmingService.getWorkAsRiskList(ownerIds, marketNames, accountSegments);
    }

    @AuraEnabled(cacheable=false)
    public static Decimal getOverdueInvoiceAmount(List<Id> ownerIds, List<String> marketNames, List<String> accountSegments) {
        handleParams(ownerIds, marketNames);
        return riskAlarmingService.getOverdueInvoiceAmount(new Set<Id>(ownerIds), new Set<String>(marketNames), accountSegments);
    }

    @AuraEnabled(cacheable=false)
    public static List<InvoiceOverDueDTO> getOverDueInvoiceList(List<Id> ownerIds, List<String> marketNames, List<String> accountSegments) {
        handleParams(ownerIds, marketNames);
        return riskAlarmingService.getOverdueInvoiceList(new Set<Id>(ownerIds), new Set<String>(marketNames), accountSegments);
    }

    private static void handleParams(List<Id> ownerIds, List<String> marketNames) {
        if (ownerIds == null) {
            ownerIds = new List<Id>();
        }
        if (marketNames == null) {
            marketNames = new List<String>();
        }
    }

    @AuraEnabled(cacheable=false)
    public static Decimal getHygieneQualityRating(List<Id> ownerIds, List<String> marketNames, List<String> forecastingStatuses, List<String> accountSegments) {
        return riskAlarmingService.getHygieneQualityRating(ownerIds, marketNames, forecastingStatuses, accountSegments);
    }

    @AuraEnabled(cacheable=false)
    public static Decimal getCriticalErrorsOpportunityNumbers(List<Id> ownerIds, List<String> marketNames, List<String> forecastingStatuses, List<String> accountSegments) {
        return riskAlarmingService.getCriticalErrorsOpportunityNumbers(ownerIds, marketNames, forecastingStatuses, accountSegments);
    }

    @AuraEnabled(cacheable=false)
    public static List<RiskAlarmingCriticalErrorsDTO> getCriticalErrorsOpportunity(List<Id> ownerIds, List<String> marketNames, List<String> forecastingStatuses, List<String> accountSegments) {
        return riskAlarmingService.getCriticalErrorsOpportunity(ownerIds, marketNames, forecastingStatuses, accountSegments);
    }

    @AuraEnabled(Cacheable=false)
    public static OpportunityWarningSignBatchCount getOpportunityWarningSignCountsBatch(
        List<Id> ownerIds,
        List<String> marketNames,
        List<String> forecastingStatuses,
        Id lastId,
        Integer batchSize,
        List<String> accountSegments
    ) {
        OpportunityWarningSignBatchResult opportunityWarningSignBatchResult = riskAlarmingService.getPSOpportunitiesWithWarningSignsBatch(
            ownerIds,
            marketNames,
            forecastingStatuses,
            lastId,
            batchSize,
            accountSegments
        );
        return new OpportunityWarningSignBatchCount(opportunityWarningSignBatchResult.records.size(), opportunityWarningSignBatchResult.hasMoreRecords, opportunityWarningSignBatchResult.lastId);
    }

    @AuraEnabled(Cacheable=false)
    public static OpportunityWarningSignBatchResult getOpportunityWarningSignsBatch(
        List<Id> ownerIds,
        List<String> marketNames,
        List<String> forecastingStatuses,
        Id lastId,
        Integer batchSize,
        List<String> accountSegments
    ) {
        return riskAlarmingService.getPSOpportunitiesWithWarningSignsBatch(ownerIds, marketNames, forecastingStatuses, lastId, batchSize, accountSegments);
    }
}
