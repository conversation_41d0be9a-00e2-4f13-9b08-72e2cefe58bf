public without sharing class ActivityController {
    @TestVisible
    private static ActivityService activityService = new ActivityService();

    @AuraEnabled(Cacheable=false)
    public static List<ActivityDTO> getActivitiesOfPerson(Id leadOrContactId) {
        return activityService.getActivityDtosOfPerson(leadOrContactId);
    }

    @AuraEnabled(Cacheable=false)
    public static Boolean isAllActivityWillArchivedIn7daysForPerson(Id leadOrContactId) {
        return activityService.isAllActivityWillArchivedIn7daysForPerson(leadOrContactId);
    }
}
