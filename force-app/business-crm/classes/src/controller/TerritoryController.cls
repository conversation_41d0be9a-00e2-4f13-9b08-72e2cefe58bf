public without sharing class TerritoryController {
    public static TerritoryService accountOrgChartService = new TerritoryService();

    @AuraEnabled(Cacheable=true)
    public static UserTypeTerritoryDTO getTerritoryByUserId(Id forecastUserId) {
        return accountOrgChartService.getTerritoriesByUserId(forecastUserId);
    }

    @AuraEnabled(Cacheable=true)
    public static List<String> getAllMarketDirectorByUserId() {
        return accountOrgChartService.getAllMarketDirectorByUserId();
    }

    @AuraEnabled(Cacheable=true)
    public static Territory2 getTerritoryByName(String territoryName) {
        return accountOrgChartService.getTerritoryByName(territoryName);
    }
}
