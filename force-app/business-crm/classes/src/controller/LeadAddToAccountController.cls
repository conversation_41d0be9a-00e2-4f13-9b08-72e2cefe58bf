public without sharing class LeadAddToAccountController {
    public static IMKTContactSelector contactSelector = new MKTContactSelector();
    private static IAccountSelector accountSelector = new AccountSelector();
    private static ILeadSelector leadSelector = new LeadSelector();
    public static ITaskService taskService = new TaskService();
    private static final MassConvertLeadService massConvertLeadService = new MassConvertLeadService();

    @TestVisible
    @AuraEnabled(Cacheable=false)
    public static Map<Id, Id> addToExistingAccount(Map<Id, String> leadIdToStatus, Id accountId, Boolean isQuickAddToAccount) {
        Map<Id, Account> accounts = accountSelector.getAllAccountsById(new List<String>{ accountId });
        Account account = accounts.get(accountId);
        if (account == null) {
            throw new AuraHandledException('Account not found.');
        }

        List<Lead> leads = new List<Lead>();
        Map<Id, Contact> leadIdToContactMap = new Map<Id, Contact>();

        for (Id leadId : leadIdToStatus.keySet()) {
            leads.add(new Lead(Id = leadId));
            leadIdToContactMap.put(leadId, new Contact(Status__c = leadIdToStatus.get(leadId)));
        }

        MKTFrontEndContext.isQuickConvertLeadToContact = isQuickAddToAccount;
        //add to account context
        LeadConvertContext.setAddedToAccountLeadIds(leadIdToStatus.keySet());
        Map<Id, Contact> leadIdToContactResultMap = massConvertLeadService.convertLeads(leads, accountId, account.OwnerId, leadIdToContactMap);
        Map<Id, Id> result = new Map<Id, Id>();
        for (Id leadId : leadIdToContactResultMap.keySet()) {
            result.put(leadId, leadIdToContactResultMap.get(leadId).Id);
        }
        return result;
    }

    @TestVisible
    @AuraEnabled(Cacheable=false)
    public static Id addToNewAccount(Id leadId, String accountName, String accountIndustry, String accountSalesMarketUnit, String status, String accountMarket) {
        Id currentUserId = UserInfo.getUserId();

        List<Lead> leads = new List<Lead>{
            new Lead(Id = leadId, Company = accountName, Industry = accountIndustry, Sales_Market_Unit_Picklist__c = accountSalesMarketUnit, Market_Picklist__c = accountMarket)
        };
        Map<Id, Contact> leadIdToContactMap = new Map<Id, Contact>{ leadId => new Contact(Status__c = status) };
        //add to account context
        LeadConvertContext.setAddedToAccountLeadIds(leadIdToContactMap.keySet());
        Map<Id, Contact> leadIdToContactResultMap = massConvertLeadService.convertLeads(leads, null, currentUserId, leadIdToContactMap);

        return leadIdToContactResultMap.get(leadId).Id;
    }

    @AuraEnabled(Cacheable=false)
    public static Id addToAccountWithFieldsUpdateAndSetReminder(Lead lead, Id accountId, String status, Task reminderTask) {
        Map<Id, Account> accounts = accountSelector.getAllAccountsById(new List<String>{ accountId });
        Account account = accounts.get(accountId);
        if (account == null) {
            throw new AuraHandledException('Account not found.');
        }

        Contact con = new Contact(Status__c = status);
        if (status == CRMConstants.MARKETING_ACTIVATED_STAGE_OPPORTUNITY) {
            con.Opportunity_Lifecycle_Date__c = Datetime.now();
        }

        Map<Id, Contact> leadIdToContactMap = new Map<Id, Contact>{ lead.Id => con };
        Map<Id, Contact> leadIdToContactResultMap = massConvertLeadService.convertLeads(new List<Lead>{ lead }, accountId, account.OwnerId, leadIdToContactMap);

        Id contactId = leadIdToContactResultMap.get(lead.Id).Id;

        if (reminderTask != null && reminderTask.ActivityDate != null && reminderTask.OwnerId != null) {
            reminderTask.Subject = 'Follow-up reminder';
            reminderTask.Status = 'Not Started';
            reminderTask.WhoId = con.Id;
            taskService.createTask(reminderTask);
        }

        return contactId;
    }

    @AuraEnabled(Cacheable=false)
    public static void mismatchAccount(List<Id> leadIds) {
        List<Lead> updatedLeads = new List<Lead>();

        for (Id leadId : leadIds) {
            updatedLeads.add(new Lead(Id = leadId, Is_Mismatched__c = true));
        }
        leadSelector.updateLeads(updatedLeads);
    }
}
