public with sharing class MKTContactController {
    private static MKTContactService marketingContactService = new MKTContactService();
    private static IMKTContactSelector contactSelector = new MKTContactSelector();
    private static ILeadSelector leadSelector = new LeadSelector();
    private static IUserSelector userSelector = new UserSelector();
    private static TaskService taskService = new TaskService();
    private static UserHierarchyService userHierarchyService = new UserHierarchyService();
    private static OpportunityContactRoleSelector opportunityContactRoleSelector = new OpportunityContactRoleSelector();

    @AuraEnabled
    public static void removeNotAtAccountFlag(String recordId) {
        marketingContactService.removeNotAtAccountFlag(recordId);
    }

    @AuraEnabled
    public static void clearReportsToFieldForContacts(Id[] contactIds) {
        marketingContactService.clearReportsToFieldForContacts(contactIds);
    }

    @AuraEnabled(Cacheable=true)
    public static List<Contact> searchContactByNameAndAccountIdsWithInternalPriority(String name, Id[] accountIds) {
        return marketingContactService.searchContactByNameAndAccountIdsWithInternalPriorityAndLimit(name, accountIds, 100);
    }

    @AuraEnabled(Cacheable=true)
    public static Map<String, String> getContactNamesByEmails(List<String> emails) {
        return contactSelector.getContactNamesByEmails(emails);
    }

    @AuraEnabled(Cacheable=true)
    public static UserHierarchyDTO getUserHierarchy(Id userId) {
        return userHierarchyService.getUserHierarchyWithWhitelist(userId);
    }

    @AuraEnabled(Cacheable=true)
    public static List<UserHierarchyWithPOHDTO> getPOHAndHierarchiesByUserId(Id userId) {
        return userHierarchyService.getAndBuildPohWithHierarchies(userId);
    }

    @AuraEnabled(Cacheable=true)
    public static List<UserHierarchyWithPOHDTO> getRootSalesPOHAndHierarchies() {
        return userHierarchyService.getAndBuildPohWithHierarchies(CRMConstants.ROOT_SALES_EMPLOYEE_ID);
    }

    @AuraEnabled(Cacheable=true)
    public static List<UserHierarchyWithPOHDTO> getAllPOHAndHierarchies() {
        return userHierarchyService.getAllPohWithHierarchies();
    }

    @AuraEnabled(Cacheable=false)
    public static List<SObject> getLeadsAndContactsAssignedToMe(Id userId) {
        return marketingContactService.getLeadsAndContactsAssignedToMe(userId);
    }

    @AuraEnabled(Cacheable=true)
    public static SObject getLeadOrContact(Id recordId, String type) {
        if (type == Constants.OBJECT_CONTACT) {
            return contactSelector.getZoomInfoRelated(new Set<Id>{ recordId }).get(0);
        }
        return leadSelector.getZoomInfoRelated(new Set<Id>{ recordId }).get(0);
    }

    @AuraEnabled(Cacheable=false)
    public static List<SObject> getAttemptedAndConversationLeadsAndContacts(Id userId) {
        return marketingContactService.getAttemptedAndConversationLeadsAndContacts(userId);
    }

    @AuraEnabled(Cacheable=true)
    public static String getSObjectTypeById(Id recordId) {
        SObjectType sObjType = recordId.getSobjectType();
        return sObjType.getDescribe().getName();
    }

    @AuraEnabled(Cacheable=false)
    public static void updateContactAndSetReminder(Contact contact, Task reminderTask) {
        contactSelector.updateContacts(new List<Contact>{ contact });
        if (reminderTask != null) {
            reminderTask.WhoId = contact.Id;
        }
        taskService.createReminderTask(reminderTask);
    }

    @AuraEnabled(Cacheable=false)
    public static void updateLeadAndSetReminder(Lead lead, Task reminderTask) {
        leadSelector.updateLeads(new List<Lead>{ lead });
        if (reminderTask != null) {
            reminderTask.WhoId = lead.Id;
        }
        taskService.createReminderTask(reminderTask);
    }

    @AuraEnabled(Cacheable=false)
    public static Id handleStageToIdentifiedOpportunity(PersonWithOpportunityCRDTO personWithOpportunityCRDTO) {
        if (personWithOpportunityCRDTO.personType == CRMConstants.OBJECT_LEAD) {
            Lead needConvertLead = new Lead(Id = personWithOpportunityCRDTO.personId);
            Id convertedContactId = LeadAddToAccountController.addToAccountWithFieldsUpdateAndSetReminder(
                needConvertLead,
                personWithOpportunityCRDTO.accountId,
                personWithOpportunityCRDTO.status,
                null
            );
            personWithOpportunityCRDTO.personId = convertedContactId;
        }

        Contact con = new Contact(
            Id = personWithOpportunityCRDTO.personId,
            Identified_Opportunity__c = personWithOpportunityCRDTO.opportunityId,
            SAL_Substage__c = personWithOpportunityCRDTO.substage,
            Identified_Opportunity_Notes__c = personWithOpportunityCRDTO.identifiedOpportunityNotes,
            Status__c = personWithOpportunityCRDTO.status,
            Opportunity_Lifecycle_Date__c = Datetime.now()
        );

        if (personWithOpportunityCRDTO.responseDate != null) {
            con.Response_Date__c = personWithOpportunityCRDTO.responseDate;
        }

        contactSelector.updateContacts(new List<Contact>{ con });
        handleContactRoleWhenIdentifiedOpportunity(con.Id, personWithOpportunityCRDTO.opportunityId, personWithOpportunityCRDTO.role, personWithOpportunityCRDTO.notes);
        return con.Id;
    }

    @Future
    private static void handleContactRoleWhenIdentifiedOpportunity(Id contactId, Id opportunityId, String role, String notes) {
        List<OpportunityContactRole> existOpportunityContactRoles = opportunityContactRoleSelector.getOppContactRoleByContIdAndOppoId(contactId, opportunityId);
        OpportunityContactRole opportunityContactRole = new OpportunityContactRole(ContactId = contactId, OpportunityId = opportunityId, Role = role, Notes__c = notes);
        if (existOpportunityContactRoles.size() > 0) {
            opportunityContactRole.Id = existOpportunityContactRoles[0].Id;
            opportunityContactRoleSelector.updateOpportunityContactRoles(new List<OpportunityContactRole>{ opportunityContactRole });
        } else {
            opportunityContactRoleSelector.insertOpportunityContactRoles(new List<OpportunityContactRole>{ opportunityContactRole });
        }
    }
}
