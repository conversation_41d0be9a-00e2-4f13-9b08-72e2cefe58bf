public with sharing class OpportunityTagController {
    @TestVisible
    private static OpportunityTagService opportunityTagService = new OpportunityTagService();

    @AuraEnabled(cacheable=false)
    public static List<Tag__c> searchDeduplicatedTagsByNameOrDescrUnderOppor(String text, List<Id> opportunityTagIds) {
        return opportunityTagService.searchDeduplicatedTagsByNameOrDescrUnderOppor(text, opportunityTagIds);
    }

    @AuraEnabled(cacheable=false)
    public static List<Opp_Tag_Relation__c> getTagsByOpportunityId(Id oppId) {
        return opportunityTagService.getTagsByOpportunityId(oppId);
    }

    @AuraEnabled(cacheable=false)
    public static List<Opp_Tag_Relation__c> getOpportunitiesByTagId(Id tagId) {
        return opportunityTagService.getOpportunitiesByTagId(tagId);
    }

    @AuraEnabled(cacheable=false)
    public static Boolean userHasAccessToAddTag() {
        return opportunityTagService.userHasAccessToAddTag();
    }
}
