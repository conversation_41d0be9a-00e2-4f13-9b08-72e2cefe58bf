public with sharing class MySalesInsigntController {
    @TestVisible
    private static GetActivityDataService getActivityDataService = new GetActivityDataService();

    @AuraEnabled(Cacheable=true)
    public static List<Object> getActivityData(String salesforceRecordId, String salesforceRecordType, String activityType) {
        return getActivityDataService.getNewestActivities(salesforceRecordId, salesforceRecordType, activityType);
    }

    @AuraEnabled(Cacheable=true)
    public static List<List<Object>> getActivityDataFromMarketo(String salesforceRecordId, String salesforceRecordType, List<String> activityTypes) {
        return getActivityDataService.getActivityDataBase(salesforceRecordId, salesforceRecordType, activityTypes);
    }

    @AuraEnabled(Cacheable=true)
    public static List<Object> getOneActivityDataForContact(String salesforceRecordId, String activityType) {
        return getActivityDataService.getOneActivityDataForContact(salesforceRecordId, activityType);
    }

    @AuraEnabled(Cacheable=true)
    public static Map<String, Integer> getTypeActivityNum(Id recordId, String objectType) {
        return getActivityDataService.getTypeActivityNum(recordId, objectType);
    }

    @AuraEnabled(Cacheable=true)
    public static Map<String, Integer> getHighlightActivityOverview(Id recordId, Integer pastDays, String objectType) {
        return getActivityDataService.getHighlightActivityOverview(recordId, pastDays, objectType);
    }

    @AuraEnabled(Cacheable=true)
    public static List<Object> getHighlightActivities(Id recordId, String objectType, Integer pastDays) {
        return getActivityDataService.getHighlightActivities(recordId, objectType, pastDays);
    }
}
