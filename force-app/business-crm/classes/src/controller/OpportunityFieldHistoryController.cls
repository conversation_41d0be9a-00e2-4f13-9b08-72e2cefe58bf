public with sharing class OpportunityFieldHistoryController {
    @TestVisible
    private static OpportunityService opportunityService = new OpportunityService();

    public OpportunityFieldHistoryController() {
    }

    @AuraEnabled(Cacheable=false)
    public static Map<String, Datetime> getLastestFieldsUpdateTime(String opportunityId, List<String> fieldNames) {
        return opportunityService.getLatestOpportunityFieldUpdatetime(opportunityId, fieldNames);
    }
}
