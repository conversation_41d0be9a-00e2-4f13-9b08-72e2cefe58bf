public with sharing class MktgEngagementController {
    @TestVisible
    private static AccountEngagementService accountEngagementService = new AccountEngagementService();

    @AuraEnabled(Cacheable=false)
    public static RecentMktgEngagementContactDto getRecentMarketingEngagementContacts(Integer lastDays, List<String> userIds, List<String> marketNames, List<String> accountSegments) {
        if (accountSegments == null || accountSegments.isEmpty()) {
            return new RecentMktgEngagementContactDto(0, 0, 0, 0, 0, 0);
        }
        try {
            return accountEngagementService.getRecentMarketingEngagementContacts(lastDays, userIds, marketNames, accountSegments);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(Cacheable=false)
    public static List<MarketingEngagementDetailDTO> getMarketingEngagementDetailList(
        Integer lastDays,
        List<String> userIds,
        List<String> marketNames,
        List<String> accountSegments,
        String contactType
    ) {
        if (accountSegments == null || accountSegments.isEmpty()) {
            return new List<MarketingEngagementDetailDTO>();
        }
        try {
            return accountEngagementService.getMarketingEngagementDetailList(lastDays, userIds, marketNames, accountSegments, contactType);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}
