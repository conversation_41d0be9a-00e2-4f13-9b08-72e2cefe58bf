public with sharing class OpportunityNextStepController {
    @TestVisible
    private static OpportunityNextStepService opportunityNextStepService = new OpportunityNextStepService();

    @AuraEnabled(cacheable=false)
    public static void syncNextStepsStatus(Id oppNextStepId, <PERSON><PERSON><PERSON> checked) {
        OpportunityNextStepService.flipOpportunityNextStepStatus(oppNextStepId, checked);
    }

    @AuraEnabled(cacheable=false)
    public static List<Opportunity_Next_Step__c> getNextStepsByOpportunityId(Id oppId) {
        return OpportunityNextStepService.getNextStepsByOpportunityId(oppId);
    }
}
