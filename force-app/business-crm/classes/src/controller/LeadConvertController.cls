public without sharing class LeadConvertController {
    public static IMKTContactSelector contactSelector = new MKTContactSelector();
    public static ILeadSelector leadSelector = new LeadSelector();
    public static IPSAEmail psaEmail = new PSAEmail();

    @AuraEnabled(Cacheable=false)
    public static void sendEmailToNewLeadContactOwner(String objectType, Id leadContactId) {
        EmailDetails emailDetails = getEmailDetails(objectType, leadContactId);
        String mailSubject = '[CRM] ' + objectType + ' assigned - ' + emailDetails.subjectDetail;
        String mailRecipient = emailDetails.recipientEmail;
        String mailBody = prepareMailBody(emailDetails);

        psaEmail.sendMailInProd(mailSubject, mailBody, mailRecipient);
    }

    @TestVisible
    private static EmailDetails getEmailDetails(String objectType, Id leadContactId) {
        EmailDetails details = new EmailDetails();

        if (objectType.equals('Lead')) {
            Lead lead = leadSelector.getLeadEmailInfoById(leadContactId)[0];
            details.objectType = 'Lead';
            details.subjectDetail = lead.Name;
            details.recipientEmail = lead.Owner.Email;
            details.contactOwnerName = lead.Owner.Name;
            details.handoverNotes = lead.Handover_Notes__c;
            details.additionalDetails = prepareMailBodyForLead(lead);
        } else {
            Contact contact = contactSelector.getContactListByContactId(new List<Id>{ leadContactId })[0];
            details.objectType = 'Contact';
            details.subjectDetail = contact.Name + ', ' + contact.Account_Name__c;
            details.recipientEmail = contact.Owner.Email;
            details.contactOwnerName = contact.Owner.Name;
            details.handoverNotes = contact.Handover_Notes__c;
            details.additionalDetails = prepareMailBodyForContact(contact);
        }

        return details;
    }

    @TestVisible
    private static String prepareMailBody(EmailDetails details) {
        String handoverNotesTitle = details.handoverNotes == '' ? '' : 'Handover notes from ' + UserInfo.getName() + ':';
        // prettier-ignore
        return '<header>Hi ' + details.contactOwnerName + ',</header>' +
            '<p>A <strong>' + details.objectType + '</strong> has been assigned to you for follow-up. Please get in touch with them as soon as possible.</p>' +
            '<section>' +
            '<h4 style="margin: 0;">' + handoverNotesTitle + '</h4>' +
            '<div>' + details.handoverNotes + '</div>' +
            '</section>' +
            details.additionalDetails +
            '<br>' +
            '<footer>' +
            '<div> --- </div>' +
            '<div>Sent by the CRM Team</div>' +
            '</footer>';
    }

    private static String prepareMailBodyForLead(Lead lead) {
        String contactLink = getLeadRecordLink(lead);
        String email = lead.Email != null ? lead.Email : '';
        String qualificationPath = lead.Recent_Qualification_Path__c != null ? lead.Recent_Qualification_Path__c : '';
        // prettier-ignore
        return '<section>' +
            '<h4 style=\'margin-bottom: 0;\'>Details:</h4>' +
            '<div>Lead Name: ' + contactLink + '</div>' +
            '<div>Lead Email: <a href=\'mailto:' + email + '\'>' + email + '</a></div>' +
            '<div>Recent Qualification Path: ' + qualificationPath + '</div>' +
            '</section>';
    }

    private static String prepareMailBodyForContact(Contact contact) {
        String contactLink = getContactRecordLink(contact);
        String accountName = contact.Account_Name__c != null ? contact.Account_Name__c : '';
        String email = contact.Email != null ? contact.Email : '';
        String qualificationPath = contact.Recent_Qualification_Path__c != null ? contact.Recent_Qualification_Path__c : '';
        // prettier-ignore
        return '<section>' +
            '<h4 style=\'margin-bottom: 0;\'>Details:</h4>' +
            '<div>Contact Name: ' + contactLink + '</div>' +
            '<div>Account Name: ' + accountName + '</div>' + // Only display this line for Contacts
            '<div>Contact Email: <a href=\'mailto:' + email + '\'>' + email + '</a></div>' +
            '<div>Recent Qualification Path: ' + qualificationPath + '</div>' +
            '</section>';
    }

    @testVisible
    private static String getContactRecordLink(Contact con) {
        if (OrganizationInfo.isSandbox()) {
            String sandboxName = OrganizationInfo.sandBoxName();
            return '<a href="https://thoughtworks--' + sandboxName + '.sandbox.lightning.force.com/lightning/r/Contact/' + con.Id + '/view">' + con.Name + '</a>';
        } else {
            return '<a href="https://thoughtworks.lightning.force.com/lightning/r/Contact/' + con.Id + '/view">' + con.Name + '</a>';
        }
    }

    private static String getLeadRecordLink(Lead lead) {
        if (OrganizationInfo.isSandbox()) {
            String sandboxName = OrganizationInfo.sandBoxName();
            return '<a href="https://thoughtworks--' + sandboxName + '.sandbox.lightning.force.com/lightning/r/Lead/' + lead.Id + '/view">' + lead.Name + '</a>';
        } else {
            return '<a href="https://thoughtworks.lightning.force.com/lightning/r/Lead/' + lead.Id + '/view">' + lead.Name + '</a>';
        }
    }

    public class EmailDetails {
        public String subjectDetail;
        public String recipientEmail;
        public String contactOwnerName;
        public String handoverNotes;
        public String objectType;
        public String additionalDetails;

        public EmailDetails() {
        }
    }
}
