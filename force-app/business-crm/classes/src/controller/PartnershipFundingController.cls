public with sharing class PartnershipFundingController {
    private static PartnershipFundingService partnershipFundingService = new PartnershipFundingService();

    @AuraEnabled(cacheable=true)
    public static Boolean getPartnershipsFundingEditAccess(Id userId) {
        return partnershipFundingService.getPartnershipsFundingEditAccess(userId);
    }

    @AuraEnabled(cacheable=false)
    public static List<Partnership_Funding__c> getPartnershipsFundings(Id partnershipId) {
        return partnershipFundingService.getPartnershipsFundings(partnershipId);
    }
}
