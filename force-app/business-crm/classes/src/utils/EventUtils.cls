public without sharing class EventUtils {
    private final static Logger LOGGER = new Logger(EventUtils.class.getName());

    public static String getChangedFields(SObjectType type, SObject oldObject, SObject newObject) {
        List<String> changedFields = new List<String>();
        Map<String, Schema.SObjectField> oppFields = type.getDescribe().fields.getMap();
        for (String fieldName : oppFields.keySet()) {
            if (oldObject.get(fieldName) != newObject.get(fieldName)) {
                changedFields.add(fieldName);
            }
        }
        return String.join(changedFields, ',');
    }

    public static String getChangedFields(SObjectType type, SObject oldObject, SObject newObject, List<String> needTrackChangeFields) {
        List<String> changedFields = new List<String>();
        Map<String, Schema.SObjectField> objectFields = type.getDescribe().fields.getMap();
        needTrackChangeFields = needTrackChangeFields == null ? new List<String>(objectFields.keySet()) : needTrackChangeFields;
        for (String fieldName : needTrackChangeFields) {
            if (oldObject.get(fieldName) != newObject.get(fieldName)) {
                changedFields.add(fieldName);
            }
        }
        return String.join(changedFields, ',');
    }

    public static String constructChangedFieldsValueJson(List<Schema.SObjectField> fieldList, SObject oldObject, SObject newObject) {
        Map<Schema.SObjectField, Object> oldValues = new Map<Schema.SObjectField, Object>();
        Map<Schema.SObjectField, Object> newValues = new Map<Schema.SObjectField, Object>();
        for (Schema.SObjectField filed : fieldList) {
            oldValues.put(filed, oldObject.get(filed));
            newValues.put(filed, newObject.get(filed));
        }
        Map<String, Map<Schema.SObjectField, Object>> jsonStructure = new Map<String, Map<Schema.SObjectField, Object>>{ 'oldValue' => oldValues, 'newValue' => newValues };
        return JSON.serialize(jsonStructure);
    }

    public static List<SObject> generateEvents(
        TriggerParameters tp,
        String publisher,
        Schema.SObjectType objType,
        Type eventObject,
        List<Schema.SObjectField> extraFieldList,
        List<String> needTrackChangeFields
    ) {
        String operationType = tp.operationType;
        List<SObject> recordList = operationType == 'Delete' ? (List<SObject>) tp.oldList : (List<SObject>) tp.newList;

        List<SObject> events = new List<SObject>();
        for (SObject record : recordList) {
            Id recordId = (Id) record.get('Id');
            SObject event = (SObject) eventObject.newInstance();
            event.put('Publisher__c', publisher);
            event.put('Record_Id__c', recordId);
            event.put('Object__c', objType.getDescribe().getName());
            event.put('Operation__c', operationType);
            if (operationType == 'Update') {
                SObject oldRecord = (SObject) tp.oldMap.get(recordId);
                String changedFields = EventUtils.getChangedFields(objType, oldRecord, record, needTrackChangeFields);
                if (String.isEmpty(changedFields)) {
                    LOGGER.info('No changed fields found for object: ' + recordId + ', user id: ' + UserInfo.getUserId());
                    continue;
                } else {
                    event.put('Change_Fields__c', changedFields);
                }
                if (CollectionUtils.isNotEmpty(extraFieldList)) {
                    event.put('Extra__c', EventUtils.constructChangedFieldsValueJson(extraFieldList, oldRecord, record));
                }
            }
            events.add(event);
        }
        return events;
    }

    public static List<SObject> generateEvents(TriggerParameters tp, String publisher, Schema.SObjectType objType, Type eventObject, List<Schema.SObjectField> extraFieldList) {
        return EventUtils.generateEvents(tp, publisher, objType, eventObject, extraFieldList, null);
    }

    public static List<SObject> generateEvents(TriggerParameters tp, String publisher, Schema.SObjectType objType, Type eventObject) {
        return EventUtils.generateEvents(tp, publisher, objType, eventObject, null, null);
    }

    public static List<SObject> generateEvents(TriggerParameters tp, String publisher, Schema.SObjectType objType, Type eventObject, List<String> needTrackChangeFields) {
        return EventUtils.generateEvents(tp, publisher, objType, eventObject, null, needTrackChangeFields);
    }
}
