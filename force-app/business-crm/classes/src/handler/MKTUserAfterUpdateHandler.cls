/**
 * Created by eric2369 on 2024/9/12.
 */

public with sharing class MKTUserAfterUpdateHandler extends TriggerHandlerBase {
    private MKTUserService mktUserService = new MKTUserService();

    public override void mainEntry(TriggerParameters tp) {
        Map<Id, User> oldUserMap = (Map<Id, User>) tp.oldMap;
        Map<Id, User> newUserMap = (Map<Id, User>) tp.newMap;
        List<User> oldUserList = tp.oldList;
        List<User> newUserList = tp.newList;
        mktUserService.grantCRMUserFullLicensePermissionSetGroup(oldUserMap, newUserMap);
        mktUserService.restoreCRMUserPermissionSet(newUserMap);
    }
}
