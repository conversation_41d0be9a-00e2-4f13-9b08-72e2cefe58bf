public without sharing class MKTContactAfterUpdateTriggerHandler extends TriggerH<PERSON>lerBase {
    private static MKTContactService marketingContactService = new MKTContactService();
    private static CampaignMemberService campaignMemberService = new CampaignMemberService();
    private static QualificationHistoryService qualificationHistoryService = new QualificationHistoryService();
    private static ILeadContactStatusService leadContactStatusService = new LeadContactStatusService();
    private static MarketingActivatedService marketingActivatedService = new MarketingActivatedService();
    private static PersonFieldHistoryService personFieldHistoryService = new PersonFieldHistoryService();
    private static PersonService personService = new PersonService();
    private static ContactOperationService contactOperationService = new ContactOperationService();
    private static EnrichmentHistoryService zoomInfoService = new EnrichmentHistoryService();
    private static LeadService leadService = new LeadService();

    public override void mainEntry(TriggerParameters tp) {
        Map<Id, Contact> oldContactMap = (Map<Id, Contact>) tp.oldMap;
        Map<Id, Contact> newContactMap = (Map<Id, Contact>) tp.newMap;
        List<Contact> newContactList = (List<Contact>) tp.newList;
        campaignMemberService.syncAccountNameOfContactToCampaignMember(oldContactMap, newContactList);
        qualificationHistoryService.insertQualificationHistory(oldContactMap, newContactList);
        leadContactStatusService.insertAndUpdateLeadContactStatusWhenLeadOrContactUpdate(oldContactMap, newContactMap, CRMConstants.OBJECT_CONTACT);
        marketingActivatedService.changeMqlOrSalMarketingActivatedByStage(newContactList, oldContactMap, CRMConstants.OBJECT_CONTACT);
        marketingActivatedService.updateMAPersonTouchPointWhenLeadOrContactToMQL(newContactList, oldContactMap, CRMConstants.OBJECT_CONTACT);
        personFieldHistoryService.updatePersonFieldHistoryWhenContactFieldsUpdate(oldContactMap, newContactList);
        personService.syncUpdatePersonFromContactOrLead(oldContactMap, newContactMap);
        contactOperationService.publishContactEvent(tp);
        zoomInfoService.storeZoomInfoUpdateHistory(oldContactMap, newContactList);
        marketingContactService.updateContactToSalWhenContactChangedFromOtherToMQL(oldContactMap, newContactList);
    }
}
