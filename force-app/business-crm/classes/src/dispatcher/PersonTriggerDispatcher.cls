public with sharing class PersonTriggerDispatcher extends TriggerDispatcherBase {
    private static Boolean isBeforeDeleteProcessing = false;
    private static Boolean isAfterUpdateProcessing = false;
    private static Boolean isAfterInsertProcessing = false;

    public virtual override void beforeDelete(TriggerParameters tp) {
        if (!isBeforeDeleteProcessing) {
            isBeforeDeleteProcessing = true;
            execute(new PersonBeforeDeleteTriggerHandler(), tp, TriggerParameters.TriggerEvent.beforeDelete);
            isBeforeDeleteProcessing = false;
        } else
            execute(null, tp, TriggerParameters.TriggerEvent.beforeDelete);
    }

    public virtual override void afterUpdate(TriggerParameters tp) {
        if (!isAfterUpdateProcessing) {
            isAfterUpdateProcessing = true;
            execute(new PersonAfterUpdateTriggerHandler(), tp, TriggerParameters.TriggerEvent.afterUpdate);
            isAfterUpdateProcessing = false;
        } else
            execute(null, tp, TriggerParameters.TriggerEvent.afterUpdate);
    }

    public virtual override void afterInsert(TriggerParameters tp) {
        if (!isAfterInsertProcessing) {
            isAfterInsertProcessing = true;
            execute(new PersonAfterInsertTriggerHandler(), tp, TriggerParameters.TriggerEvent.afterInsert);
            isAfterInsertProcessing = false;
        } else {
            execute(null, tp, TriggerParameters.TriggerEvent.afterInsert);
        }
    }
}
