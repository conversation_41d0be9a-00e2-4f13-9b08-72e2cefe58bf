public with sharing class EmailMessageTriggerDispatcher extends TriggerDispatcherBase {
    private static Boolean isBeforeDeleteProcessing = false;
    private static Boolean isAfterDeleteProcessing = false;

    public virtual override void beforeDelete(TriggerParameters tp) {
        if (!isBeforeDeleteProcessing) {
            isBeforeDeleteProcessing = true;
            execute(new EmailMessageBeforeDeleteTriggerHandler(), tp, TriggerParameters.TriggerEvent.afterDelete);
            isBeforeDeleteProcessing = false;
        } else {
            execute(null, tp, TriggerParameters.TriggerEvent.afterDelete);
        }
    }

    public virtual override void afterDelete(TriggerParameters tp) {
        if (!isAfterDeleteProcessing) {
            isAfterDeleteProcessing = true;
            execute(new EmailMessageAfterDeleteTriggerHandler(), tp, TriggerParameters.TriggerEvent.afterDelete);
            isAfterDeleteProcessing = false;
        } else {
            execute(null, tp, TriggerParameters.TriggerEvent.afterDelete);
        }
    }
}
