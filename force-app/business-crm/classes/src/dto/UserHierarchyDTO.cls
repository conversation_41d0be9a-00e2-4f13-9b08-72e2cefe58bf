public with sharing class UserHierarchyDTO {
    @AuraEnabled
    public String id { get; set; }

    @AuraEnabled
    public String name { get; set; }

    @AuraEnabled
    public String employeeId { get; set; }

    @AuraEnabled
    public String pohName { get; set; }

    @AuraEnabled
    public String type { get; set; }

    @AuraEnabled
    public Integer depth { get; set; }

    @AuraEnabled
    public Boolean hasOpportunity { get; set; }

    @AuraEnabled
    public Boolean hasTarget { get; set; }

    @AuraEnabled
    public List<UserHierarchyDTO> children { get; set; }
}
