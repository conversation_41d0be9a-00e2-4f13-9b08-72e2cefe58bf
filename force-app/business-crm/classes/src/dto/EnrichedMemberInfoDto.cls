public with sharing class EnrichedMemberInfoDto {
    public Id campaignId { get; set; }

    public String campaignName { get; set; }

    public List<Id> campaignMemberIds { get; set; }

    public Integer newlyEnrichNum { get; set; }

    public Integer reEnrichNum { get; set; }

    public Integer accountTypeMemberNum { get; set; }

    public Integer resourceLeadContactNum { get; set; }

    public Map<Id, FailedEnrichedInfoDto> failedEnrichIdToReasonMap { get; set; }

    public Boolean isAllFailed;

    public EnrichedMemberInfoDto(
            Id campaignId,
            String campaignName,
            List<Id> campaignMemberIds) {
        this.campaignId = campaignId;
        this.campaignName = campaignName;
        this.campaignMemberIds = campaignMemberIds;
        this.newlyEnrichNum = 0;
        this.reEnrichNum = 0;
        this.accountTypeMemberNum = 0;
        this.resourceLeadContactNum = 0;
        this.failedEnrichIdToReasonMap = new Map<Id, FailedEnrichedInfoDto>();
        this.isAllFailed = false;
    }
}
