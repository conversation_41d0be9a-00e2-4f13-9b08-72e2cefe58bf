/**
 * Created by fang<PERSON><PERSON><PERSON> on 2025/4/1.
 */

public with sharing class InvoiceOverDueDTO {
    @AuraEnabled
    public String id;

    @AuraEnabled
    public String name;

    @AuraEnabled
    public String accountId;

    @AuraEnabled
    public String accountName;

    @AuraEnabled
    public String accountMarketName;

    @AuraEnabled
    public String accountOwnerId;

    @AuraEnabled
    public String accountOwnerName;

    @AuraEnabled
    public Boolean invoiceAtAccountLevel;

    @AuraEnabled
    public String projectId;

    @AuraEnabled
    public String projectName;

    @AuraEnabled
    public Decimal unpaidAmountInUSD;

    @AuraEnabled
    public Decimal finalTotalInUSD;

    @AuraEnabled
    public String invoiceStatus;

    @AuraEnabled
    public Decimal overDueDays;

    @AuraEnabled
    public String ownerId;

    @AuraEnabled
    public String ownerAlias;

    public InvoiceOverDueDTO(Sales_Invoice__c salesInvoice, Decimal UnpaidAmountInUSD, Decimal FinalTotalInUSD) {
        this.id = salesInvoice.Id;
        this.name = salesInvoice.Name;
        this.accountId = salesInvoice.Account__c;
        this.accountName = salesInvoice.Account__r?.Name;
        this.accountMarketName = salesInvoice.Account__r?.Market__r?.Name__c;
        this.accountOwnerId = salesInvoice.Account__r?.OwnerId;
        this.accountOwnerName = salesInvoice?.Account__r?.Owner?.Name;
        this.invoiceAtAccountLevel = salesInvoice.Invoice_at_Account_Level__c;
        this.projectId = salesInvoice.Project__c;
        this.projectName = salesInvoice.Project__r?.Name;
        this.unpaidAmountInUSD = UnpaidAmountInUSD;
        this.finalTotalInUSD = FinalTotalInUSD;
        this.invoiceStatus = salesInvoice.Invoice_Status__c;
        this.overDueDays = salesInvoice.Days_Payment_Overdue__c;
        this.ownerId = salesInvoice.OwnerId;
        this.ownerAlias = salesInvoice.Owner?.Alias;
    }
}
