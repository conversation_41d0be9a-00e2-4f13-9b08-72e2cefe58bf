public with sharing class UserTypeTerritoryDTO {
    @AuraEnabled
    public String type;
    @AuraEnabled
    public List<TerritoryDTO> allTerritories;

    //    just for type is isManagerAndWhiteListUser
    @AuraEnabled
    public List<TerritoryDTO> managerTerritories;

    public UserTypeTerritoryDTO(String type, List<TerritoryDTO> allTerritories) {
        this.type = type;
        this.allTerritories = allTerritories;
    }

    public UserTypeTerritoryDTO(String type, List<TerritoryDTO> allTerritories, List<TerritoryDTO> managerTerritories) {
        this.type = type;
        this.allTerritories = allTerritories;
        this.managerTerritories = managerTerritories;
    }
}
