/**
 * Created by guangfeng.xu on 2025/4/14.
 */

public with sharing class MarketingEngagementDetailDTO {
    private static final String CAMPAIGN = 'Campaign';
    private static final String WEB_ACTIVITY = 'Web Activity';

    @AuraEnabled
    public String accountMarket;

    @AuraEnabled
    public Id accountId;

    @AuraEnabled
    public String accountName;

    @AuraEnabled
    public Id contactId;

    @AuraEnabled
    public String contactName;

    @AuraEnabled
    public String role;

    @AuraEnabled
    public String email;

    @AuraEnabled
    public Date engagementDate;

    @AuraEnabled
    public Decimal numberOfEngagementThisYear;

    @AuraEnabled
    public String customerLifecycleStage;

    @AuraEnabled
    public String engagementType;

    @AuraEnabled
    public String contactType;

    @AuraEnabled
    public String engagementUrl;

    @AuraEnabled
    public String engagementName;

    public MarketingEngagementDetailDTO(Holistic_Engagement__c engagement, List<String> contactTypes) {
        this.accountMarket = engagement.Person__r.Contact__r.Account.Market__r.Name;
        this.accountId = engagement.Person__r.Contact__r.Account.Id;
        this.accountName = engagement.Person__r.Contact__r.Account.Name;
        this.contactId = engagement.Person__r.Contact__r.Id;
        this.contactName = engagement.Person__r.Contact__r.Name;
        this.role = engagement.Person__r.Contact__r.job_title__c;
        this.email = engagement.Person__r.Contact__r.Email;
        this.engagementDate = engagement.Engagement_Date__c;
        this.numberOfEngagementThisYear = engagement.Person__r.Contact__r.Number_of_Engagement_This_Year__c;
        this.customerLifecycleStage = engagement.Person__r.Contact__r.Status__c;
        this.engagementType = engagement.Engagement_Type__c;
        this.contactType = String.join(contactTypes, ', ');
        this.engagementUrl = getEngagementUrl(engagement);
        this.engagementName = getEngagementName(engagement);
    }

    private static String getEngagementUrl(Holistic_Engagement__c engagement) {
        if (CAMPAIGN.equals(engagement.Engagement_Type__c)) {
            return '/' + engagement.Campaign__r.Id;
        } else if (WEB_ACTIVITY.equals(engagement.Engagement_Type__c)) {
            return engagement.Web_Activity__r.PageLink__c;
        }
        return null;
    }

    private static String getEngagementName(Holistic_Engagement__c engagement) {
        if (CAMPAIGN.equals(engagement.Engagement_Type__c)) {
            return engagement.Campaign__r.Name;
        } else if (WEB_ACTIVITY.equals(engagement.Engagement_Type__c)) {
            return engagement.Web_Activity__r.PageLink__c;
        }
        return null;
    }
}
