public with sharing class SurveyResponseCategories {
    @AuraEnabled
    public String year;
    @AuraEnabled
    public Decimal promoter;
    @AuraEnabled
    public Decimal passive;
    @AuraEnabled
    public Decimal detractor;

    public SurveyResponseCategories(String year, Decimal promoter, Decimal passive, Decimal detractor) {
        this.year = year;
        this.promoter = promoter;
        this.passive = passive;
        this.detractor = detractor;
    }
}
