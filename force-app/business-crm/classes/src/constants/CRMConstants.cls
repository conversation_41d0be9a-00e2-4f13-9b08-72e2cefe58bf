public without sharing class CRMConstants {
    public final static String RINGLEAD_INTEGRATION_USER = '<EMAIL>';
    public final static String CLIENT_ACCOUNT = 'Client Account';
    public final static String PARTNER_ACCOUNT = 'Partner Account';
    public final static String INTERNAL_ACCOUNT = 'Internal Account';
    public final static String ACCOUNT_PROSPECT_CLIENT_STATUS = 'Prospect';
    public final static String ACCOUNT_FORMER_CLIENT_STATUS = 'Former Client';
    public final static String ACCOUNT_CURRENT_CLIENT_STATUS = 'Current Client';

    public final static String CUSTOMER_LIFECYCLE_STAGE_ENGAGED = 'Engaged';
    public final static String CUSTOMER_LIFECYCLE_STAGE_MQL = 'Marketing Qualified';
    public final static String CUSTOMER_LIFECYCLE_STAGE_ASL = 'Sales Accepted';
    public final static String CUSTOMER_LIFECYCLE_STAGE_WILL_NEVER_BUY = 'Will Never Buy';
    public final static String CUSTOMER_LIFECYCLE_STAGE_NOT_SALES_READY = 'Not Sales Ready';
    public final static String CUSTOMER_LIFECYCLE_STAGE_CONVERT = 'Converting';
    public final static String CUSTOMER_LIFECYCLE_STAGE_RECYCLED = 'Recycled';

    public final static String MARKETING_ACTIVATED_STAGE_ENGAGED = 'Engaged';
    public final static String MARKETING_ACTIVATED_STAGE_MQL = 'MQL';
    public final static String MARKETING_ACTIVATED_STAGE_SAL = 'SAL';
    public final static String MARKETING_ACTIVATED_STAGE_OPPORTUNITY = 'Opportunity';
    public final static String MARKETING_ACTIVATED_STAGE_WON = 'Won';

    public final static String LAST_TOUCH = 'Last Touch';
    public final static String NON_LAST_TOUCH = 'Non-Last Touch';

    public static final String FIRST_NAME_TO_TAG_ANONYMOUS = 'Anonymised';

    public static final List<String> partnershipLeadSource = new List<String>{ 'Partnership Campaign', 'Partnership Portal', 'Partnership Sales Sourced' };

    public final static Set<String> NOT_SALES_READY_OPTIONS = new Set<String>{
        'Needs further nurture',
        'Went quiet / Lost interest',
        'No budget',
        'Not our demographic',
        'Not a decision maker / Not senior enough',
        'Project not aligned',
        'Client not aligned',
        'No current project',
        'No buy-in from stakeholders',
        'No buying intent',
        'Not enough information to assign to sales',
        'Not in active buying cycle',
        'TW Capacity',
        'Potential lead scoring issue',
        'Invalid data/Bad data',
        'Not a new business',
        'Long procurement cycle',
        'Legal/Regulatory issues',
        'Went with competitor',
        'Other-Not Sales Ready'
    };

    public final static Set<String> WILL_NEVER_BUY_OPTIONS = new Set<String>{ 'Used for testing', 'Spam', 'Competitor', 'Company size too small', 'Not a demand request', 'Other-Will Never Buy' };

    public final static String CONTRACT_RECORD_TYPE_MSA = 'Master Service Agreement';
    public final static String CONTRACT_RECORD_TYPE_SOW = 'Statement of Work';
    public final static String CONTRACT_RECORD_TYPE_PO = 'Purchase Order';
    public final static String CONTRACT_RECORD_TYPE_OTHER = 'Other';

    public final static String MARKETING_PIPELINE_ACTIVATED = 'Marketing Activated';
    public final static String MARKETING_PIPELINE_INFLUENCED = 'Marketing Influenced';
    public final static String MARKETING_PIPELINE_ACTIVATED_INFLUENCED = 'Marketing Activated & Influenced';

    public final static String CAMPAIGN_SOURCE_MARKETING = 'Marketing Influence Campaign';

    public final static String INFLUENCE_TYPE_CREATE_OPPORTUNITY = 'Influence to create opportunity';

    public final static String OPPORTUNITY_STAGE_CLOSED_WON = 'Closed Won';

    public final static String OBJECT_LEAD = 'Lead';
    public final static String OBJECT_CONTACT = 'Contact';

    public final static String LEAD_STATUS_FIELD = 'Status';
    public final static String CONTACT_STATUS_FIELD = 'Status__c';

    public final static String FAST_TRACK_REASON_PF_EID = 'Profile Fitment & Explicit Intent Detected';
    public final static String FAST_TRACK_REASON_PF_HEL = 'Profile Fitment & High Engagement Level';
    public final static String FAST_TRACK_REASON_PF_EID_HEL = 'Profile Fitment & Explicit Intent Detected & High Engagement Level';

    public final static String QUALIFICATION_DETAIL_HIGH_ENGAGEMENT_AND_EXPLICIT_INTENT = 'Person has qualified through our lead scoring model. They are a profile fit and have shown high engagement with the TW brand and exhibited high purchase intent for our services.';
    public final static String QUALIFICATION_DETAIL_HIGH_ENGAGEMENT = 'Person has qualified through our lead scoring model. They are a profile fit and have shown high engagement with the TW brand.';
    public final static String QUALIFICATION_DETAIL_EXPLICIT_INTENT = 'Person has qualified through our lead scoring model. They are a profile fit and have exhibited high purchase intent for our services.';

    public final static String QUALIFICATION_TRIGGER_OUTBOUND = 'Outbound';
    public final static String QUALIFICATION_TRIGGER_INBOUND = 'Inbound';
    public final static String QUALIFICATION_TRIGGER_REFERRAL = 'Referral';

    public final static String QUALIFICATION_PATH_SG = 'Sales Generated';
    public final static String QUALIFICATION_PATH_FAST_TRACK = 'Outbound-Fasttrack';
    public final static String QUALIFICATION_PATH_OUTBOUND = 'Outbound-Organic Scoring';
    public final static String QUALIFICATION_PATH_INBOUND = 'Inbound Inquiry';
    public final static String QUALIFICATION_PATH_OTHERS = 'Others';

    public final static String SUBSTAGE_QUALIFIED_WITHOUT_OPPORTUNITY = 'Qualified without Opportunity';
    public final static String SUBSTAGE_NEW_ASSIGNED = 'New Assigned';
    public final static String SUBSTAGE_ATTEMPTED = 'Attempted';
    public final static String SUBSTAGE_IDENTIFIED_OPPORTUNITY = 'Identified Opportunity';
    public final static String SUBSTAGE_CONVERSATION = 'In Conversation';
    public final static String SUBSTAGE_QUALIFIED_OUT = 'Qualified Out';
    public final static String SUBSTAGE_RECYCLED = 'Recycled SAL';

    public final static Set<String> COMPLETED_SUBSTAGE_SET = new Set<String>{ SUBSTAGE_QUALIFIED_OUT, SUBSTAGE_QUALIFIED_WITHOUT_OPPORTUNITY, SUBSTAGE_IDENTIFIED_OPPORTUNITY };

    public final static Integer CYCLE_OF_NEW_ASSIGNED_TO_RECYCLED = 28;

    public final static String RING_LEAD_INTEGRATION_ACCOUNT_USER_NAME = '<EMAIL>';

    public final static List<String> ENRICH_ROW_FIELD_API_NAME = new List<String>{
        'FirstName',
        'Email',
        'Phone',
        'DoNotCall',
        'Industry__c',
        'Industry',
        'City',
        'MailingCity',
        'Country',
        'Company',
        'Website',
        'Function__c',
        'LID__LinkedIn_Member_Token__c',
        'Title',
        'Not_at_Account__c',
        'Sub_Industry__c',
        'AnnualRevenue',
        'NumberOfEmployees',
        'MobilePhone'
    };

    public final static List<String> ZOOM_INFO_FIELD_API_NAME = new List<String>{
        'ZoomInfo_First_Name__c',
        'ZoomInfo_Email__c',
        'ZoomInfo_Phone__c',
        'ZoomInfo_Do_not_Call__c',
        'ZoomInfo_Mobile_Phone__c',
        'ZoomInfo_City__c',
        'ZoomInfo_Country_New__c',
        'ZoomInfo_Job_Title__c',
        'ZoomInfo_Function__c',
        'ZoomInfo_Personal_LinkedIn_URL__c',
        'ZoomInfo_Company_Name__c',
        'ZoomInfo_Company_Website__c',
        'ZoomInfo_Company_Type__c',
        'ZoomInfo_Company_Revenue__c',
        'ZoomInfo_Number_of_Company_Employees__c',
        'ZoomInfo_Company_Primary_Industry__c',
        'ZoomInfo_Company_Industries__c',
        'ZoomInfo_Person_Has_Moved__c',
        'ZoomInfo_Last_Updated_Date__c'
    };

    public final static Map<String, String> LOCALE_TO_REGION_MAP = new Map<String, String>{
        'en_AU' => 'Asia Pacific',
        'en_SG' => 'Asia Pacific',
        'en_NZ' => 'Asia Pacific',
        'zh_CN' => 'Asia Pacific',
        'en_HK' => 'Asia Pacific',
        'zh_HK' => 'Asia Pacific',
        'en_IN' => 'India / Middle East',
        'en_US' => 'Americas',
        'en_CA' => 'Americas',
        'pt_BR' => 'Americas',
        'es_CL' => 'Americas',
        'es_EC' => 'Americas',
        'en_GB' => 'Europe',
        'de_DE' => 'Europe',
        'fi_FI' => 'Europe',
        'it_IT' => 'Europe',
        'nl_NL' => 'Europe',
        'ro_RO' => 'Europe',
        'es_ES_EURO' => 'Europe',
        'de_CH' => 'Europe',
        'en_DE' => 'Europe'
    };

    public final static String ENGAGEMENT_TYPE_WEB_ACTIVITY = 'Web Activity';
    public final static String ENGAGEMENT_BEHAVIOR_VISIT_WEB_PAGE = 'Visit Web Page';

    public final static String ACTIVITY_COMPLETED_STATUS = 'Completed';
    public final static String INBOUND_CALL_OR_EMAIL = 'Inbound Call or Email';
    public final static String INBOUND_SOCIAL_MEDIA = 'Inbound Social Media';

    public final static String CAMPAIGN_HVO_TYPE = 'High Value Offer - Intent to buy';
    public final static String CAMPAIGN_MEMBER_HVO_STATUS = 'Expressed Interest';
    public final static List<String> ACTIVE_HVO_FLAG_CLS = new List<String>{
        CUSTOMER_LIFECYCLE_STAGE_CONVERT,
        CUSTOMER_LIFECYCLE_STAGE_ASL,
        CUSTOMER_LIFECYCLE_STAGE_MQL,
        CUSTOMER_LIFECYCLE_STAGE_ENGAGED
    };
    public final static String CAMPAIGN_CONTACT_US_TYPE = 'Contact Us';
    public static Set<String> ONLY_MI_CAMPAIGN_TYPES = new Set<String>{ 'Client Feedback' };

    public final static String ROOT_SALES_EMPLOYEE_ID = '10972';
    public final static String NEWLY_CREATED_OPPORTUNITY = 'Newly Created Opportunity';
    public final static String NEWLY_CLOSED_OPPORTUNITY = 'Newly Closed Opportunity';
    public final static String REMAINING_PIPELINE = 'Remaining Pipeline';

    public final static String INFLUENCE_EVENT_TYPE_BASED_OPPORTUNITY_FOR_PIPELINE = 'Based on Opportunity for Marketing Pipeline';
    public final static String INFLUENCE_EVENT_TYPE_BASED_CONTACT_FOR_PIPELINE = 'Based on Contact for Marketing Pipeline';
    public final static String INFLUENCE_EVENT_TYPE_BASED_OPPORTUNITY_FOR_ATTRIBUTION = 'Based on Opportunity for Attribution Value';

    public final static String ORIGINAL_LIST = 'old list';
    public final static String NEW_LIST = 'new list';
}
