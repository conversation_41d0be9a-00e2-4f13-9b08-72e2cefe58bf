# Salesforce Trigger Dispatcher with Dependency Injection

## Overview

The `DomainProcessBinding__mdt` system introduces dependency injection to streamline and enhance trigger management in Salesforce.
By binding domain processes to triggers via custom metadata, we decouple the trigger logic from specific handler classes,
making the system more modular, maintainable, and scalable.

### Why This Approach?

1. **Decoupling Logic**: By binding handlers via metadata instead of hardcoded logic, we achieve a higher level of abstraction, allowing for easier updates and changes without modifying core trigger code.
2. **Reusability and Flexibility**: The dependency injection approach enables different handlers to be assigned or modified dynamically based on custom metadata configurations, which can be tailored for various objects and operations.
3. **Modularization**: Each domain process is encapsulated in its own handler class, improving code organization and maintainability.
4. **Consistency**: This system ensures a consistent method of handling triggers across multiple Salesforce objects and operations by defining the behavior in metadata.
5. **Improved Governance**: The use of custom metadata ensures that configurations are easy to manage, audit, and track, making it easier to adhere to best practices.

### Key Differences from Previous Approaches

-   **Traditional Method**: In the past, trigger logic was managed through a factory method (`TriggerFactory.createTriggerDispatcher`) that instantiated specific dispatcher classes, such as `UserTriggerDispatcher`. The dispatcher class directly referenced the trigger handler and managed the execution flow for each trigger event. For example:

```apex
public class UserTriggerDispatcher extends TriggerDispatcherBase {
    private static Boolean isBeforeInsertProcessing = false;

    public virtual override void beforeInsert(TriggerParameters tp) {
        if (!isBeforeInsertProcessing) {
            isBeforeInsertProcessing = true;
            execute(new UserBeforeInsertTriggerHandler(), tp, TriggerParameters.TriggerEvent.beforeInsert);
            isBeforeInsertProcessing = false;
        } else {
            execute(null, tp, TriggerParameters.TriggerEvent.beforeInsert);
        }
    }
}
```

This required writing and maintaining separate dispatcher classes for each object and event, creating tight coupling between triggers and their handlers.

-   **New Approach**: With the introduction of custom metadata (`DomainProcessBinding__mdt`), the system dynamically injects and executes the appropriate handler class based on the metadata configuration for the trigger event. This eliminates the need to create specific dispatcher classes (like `UserTriggerDispatcher`) and removes hardcoded references to handler classes. As a result, the new approach enhances flexibility, reduces code duplication, and minimizes dependencies between trigger logic and handler classes.

## Principle of Operation

1. **Metadata-Driven Binding**: The custom metadata (`DomainProcessBinding__mdt`) defines the trigger operations and the handler classes responsible for managing those operations. The metadata includes fields for the object, operation type (e.g., beforeInsert, afterUpdate), class name, execution order, and more.
2. **Dynamic Injection**: When a trigger fires, the `DomainProcessCoordinator` dynamically injects the appropriate handler(s) based on the metadata configuration, ensuring that the correct logic is executed in the correct order.

## How to Use

### 1. Define a Domain Process

To implement this system, follow these steps:

#### 1.1. Create a Trigger Handler Class

-   Extend the `TriggerHandlerBase` interface to define the domain process handler class.
-   Implement the `mainEntry` method where the trigger logic for the domain process will reside.

```apex
public class MyUserTriggerHandler extends TriggerHandlerBase {
    public override void mainEntry(TriggerParameters tp) {
        // Define your trigger logic here
    }
}
```

#### 1.2. Create Custom Metadata for Domain Process Binding

-   Create a record in the `DomainProcessBinding__mdt` metadata type.

    -   **Label**: The name of the domain process.
    -   **ClassToInject\_\_c**: The Apex class that handles the domain process logic (e.g., `MyUserBeforeInsertTriggerHandler`).
    -   **OrderOfExecution\_\_c**: The order in which the process should be executed relative to other processes for the same trigger.
    -   **RelatedDomainBindingSObject\_\_c**: The object to which the domain process is bound (e.g., `Contact`).
    -   **RelatedDomainBindingSObjectAlternate\_\_c**: Use this field if the object cannot be bound by **RelatedDomainBindingSObject\_\_c** (e.g., `User` object bindings).
    -   **TriggerOperation\_\_c**: The type of trigger event (e.g., `beforeInsert`, `afterUpdate`).
    -   **IsActive\_\_c**: Set this to `true` to activate the domain process.
    -   **Description\_\_c**: (Optional) A brief description of the domain process.
        Here’s the corrected and optimized English version:

-   The **DomainProcessBinding** custom metadata must be managed as code and included in version control. Whether created in the environment or locally, it should be handled as code. For bulk creation, you can use the `sf cmdt generate records` command with a CSV file to generate custom metadata records.

#### 1.3. Call Domain Injection in Trigger

-   Replace the existing trigger dispatcher call with the `DomainProcessCoordinator` to leverage the dependency injection system.

```apex
trigger UserTrigger on User(before insert, after insert, before update, after update, before delete, after delete) {
    new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();
}
```

## Benefits of the New Approach

1. **Scalability**: As the application grows, more domain processes can be added by simply creating new metadata records, without altering the underlying trigger code.
2. **Maintainability**: By organizing trigger logic into individual handler classes, modifications become more isolated and easier to manage.
3. **Performance**: Custom metadata ensures that only active and necessary domain processes are executed, reducing overhead in triggers.
4. **Governance and Tracking**: All trigger logic is defined in metadata, making it easier to audit changes and track trigger behavior across objects.

This approach reduces the need for hardcoded logic in triggers, improving code flexibility and making it easier to adapt to evolving business requirements.

---

# Salesforce trigger dispatcher 与依赖注入

## 概述

`DomainProcessBinding__mdt` 系统通过依赖注入的方式简化并增强了 Salesforce 中的触发器管理。通过使用自定义元数据将领域处理逻辑与触发器绑定，我们将触发器逻辑与具体的处理类解耦，使系统更加模块化、可维护性更强并且更具扩展性。

### 为什么采用这种方法？

1. **逻辑解耦**：通过使用元数据绑定处理类而非硬编码的逻辑，我们达到了更高层次的抽象，使得更新和更改变得更加容易，无需修改核心触发器代码。
2. **可重用性和灵活性**：依赖注入允许根据自定义元数据配置动态分配或修改不同的处理类，可以针对不同的对象和操作进行调整。
3. **模块化**：每个领域处理逻辑被封装在各自的处理类中，提升了代码组织和维护性。
4. **一致性**：该系统通过在元数据中定义行为，确保了跨多个 Salesforce 对象和操作的一致触发器处理方式。
5. **改进的治理**：使用自定义元数据使得配置更易于管理、审计和跟踪，从而更容易遵循最佳实践。

### 与之前方法的主要区别

-   **传统方法**：过去，触发器逻辑是通过工厂方法 (`TriggerFactory.createTriggerDispatcher`) 来管理的，该方法实例化了特定的调度类，例如 `UserTriggerDispatcher`。调度类直接引用处理类，并管理每个触发事件的执行流程。示例代码如下：

```apex
public class UserTriggerDispatcher extends TriggerDispatcherBase {
    private static Boolean isBeforeInsertProcessing = false;

    public virtual override void beforeInsert(TriggerParameters tp) {
        if (!isBeforeInsertProcessing) {
            isBeforeInsertProcessing = true;
            execute(new UserBeforeInsertTriggerHandler(), tp, TriggerParameters.TriggerEvent.beforeInsert);
            isBeforeInsertProcessing = false;
        } else {
            execute(null, tp, TriggerParameters.TriggerEvent.beforeInsert);
        }
    }
}
```

这种方法要求为每个对象和事件编写并维护单独的调度类，导致触发器与处理类之间的紧密耦合。

-   **新方法**：通过引入自定义元数据 (`DomainProcessBinding__mdt`)，系统可以根据触发事件的元数据配置动态注入并执行相应的处理类。这消除了创建特定调度类（如 `UserTriggerDispatcher`）的需要，并移除了对处理类的硬编码引用。结果是，新方法增强了灵活性，减少了代码重复，并降低了触发器逻辑与处理类之间的依赖性。

## 工作原理

1. **基于元数据的绑定**：自定义元数据 (`DomainProcessBinding__mdt`) 定义了触发器操作及负责管理这些操作的处理类。元数据包括对象、操作类型（如 `beforeInsert`、`afterUpdate`）、类名、执行顺序等字段。
2. **动态注入**：当触发器触发时，`DomainProcessCoordinator` 会根据元数据配置动态注入相应的处理类，确保正确的逻辑按顺序执行。

## 如何使用

### 1. 定义领域处理逻辑

要实现此系统，请按以下步骤操作：

#### 1.1. 创建触发器处理类

-   扩展 `TriggerHandlerBase` 接口定义领域处理逻辑处理类。
-   实现 `mainEntry` 方法，编写领域处理的触发器逻辑。

```apex
public class MyUserTriggerHandler extends TriggerHandlerBase {
    public override void mainEntry(TriggerParameters tp) {
        // 在此定义触发器逻辑
    }
}
```

#### 1.2. 创建领域处理绑定的自定义元数据

-   在 `DomainProcessBinding__mdt` 元数据类型中创建记录。

    -   **Label**: 领域处理的名称。
    -   **ClassToInject\_\_c**: 负责处理领域逻辑的 Apex 类（例如 `MyUserBeforeInsertTriggerHandler`）。
    -   **OrderOfExecution\_\_c**: 领域处理在同一触发器中的执行顺序。
    -   **RelatedDomainBindingSObject\_\_c**: 领域处理所绑定的对象（例如 `Contact`）。
    -   **RelatedDomainBindingSObjectAlternate\_\_c**: 如果对象不能通过**RelatedDomainBindingSObject\_\_c**绑定，可使用该字段（例如 `User` 对象的绑定）。
    -   **TriggerOperation\_\_c**: 触发器事件类型（例如 `beforeInsert`、`afterUpdate`）。
    -   **IsActive\_\_c**: 设置为 `true` 以激活该领域处理。
    -   **Description\_\_c**: （可选）对领域处理的简要描述。

-   **DomainProcessBinding** 自定义元数据需要通过代码（as code）的方式进行管理，并纳入代码仓库控制。无论是从环境中创建还是在本地创建，都必须以代码形式进行管理。如果需要批量创建，可以使用 `sf cmdt generate records` 命令，通过传入 CSV 文件生成自定义元数据。

#### 1.3. 在触发器中调用领域注入

-   使用 `DomainProcessCoordinator` 替换现有的触发器调度器调用，以利用依赖注入系统。

```apex
trigger UserTrigger on User(before insert, after insert, before update, after update, before delete, after delete) {
    new DomainProcessCoordinator(User.SObjectType).processDomainLogicInjections();
}
```

## 新方法的优势

1. **可扩展性**：随着应用程序的增长，只需通过创建新的元数据记录即可添加更多的领域处理，而无需修改底层触发器代码。
2. **可维护性**：通过将触发器逻辑组织到独立的处理类中，修改变得更加独立，易于管理。
3. **性能**：自定义元数据确保只执行活动且必要的领域处理，减少触发器的开销。
4. **治理和追踪**：所有触发器逻辑都定义在元数据中，使得更容易审计更改并追踪跨对象的触发器行为。

这种方法减少了触发器中硬编码逻辑的需求，提高了代码的灵活性，并使其更容易适应不断变化的业务需求。
