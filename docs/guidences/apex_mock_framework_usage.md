# Usage Of Apex Mock Framework

Let's assume we've created our custom list interface `fflib_MyList.IList`, which we want to either verify or stub:
```Java  
public class fflib_MyList implements IList  
{  
    public interface IList    {       
	    void add(String value);       
	    String get(Integer index);       
	    void clear();       
	    Boolean isEmpty();
	    // ...    
	}
}  
```  

## Stub Object
### 1. Manually Create and Initialize Stub Objects
#### create stub object class

``` Java
/* Generated by apex-mocks-generator version 4.0.1 */  
@isTest  
public class fflib_Mocks  
{  
    public class Mockfflib_MyList implements fflib_MyList.IList  
    {  
       private fflib_ApexMocks mocks;  
  
       public Mockfflib_MyList(fflib_ApexMocks mocks)  
       {  
          this.mocks = mocks;  
       }  
  
       public void add(String value)  
       {  
          mocks.mockVoidMethod(this, 'add', new List<Type> {System.Type.forName('String')}, new List<Object> {value});  
       } 
        
	// Define methods for mocking 
	// ...
    } 
}
```
#### Initialize Stub Object for Mocking
``` Java
fflib_ApexMocks mocks = new fflib_ApexMocks();  
fflib_MyList.IList mockList = new fflib_Mocks.Mockfflib_MyList(mocks);
```
### 2. Dynamic Generation of Stub Objects Using Salesforce's [Stub API](https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_testing_stub_api.htm)
```Java  
fflib_ApexMocks mocks = new fflib_ApexMocks();  
fflib_MyList mockList = (fflib_MyList)mocks.mock(fflib_MyList.class);  
```

Notes:

1. Using the Stub API reduces effort and coupling, as you don't need to place all mock objects within the `Mocks` class.

2. The stub object class that is manually created can lead to issues if parameters are not specified correctly. For example, when setting up the `mockVoidMethod`, it's crucial to provide the required parameters to ensure the method behaves as expected.

   ```java
   // Correctly specifying parameters
	public void add(String value)  
	{  
	    mocks.mockVoidMethod(this, 'add', new List<Object> {value});  
	}
	
	// Incorrectly omitting parameters can cause unexpected behavior
	public void add(String value)  
	{  
	    // Omitting input parameter objects will cause the method to return values regardless of input
	    mocks.mockVoidMethod(this, 'add', new List<Object> {});  
	}

   ```
   It's important to provide the correct parameters to accurately simulate the desired behavior of the method being stubbed. Omitting parameters can result in unpredictable outcomes during testing, potentially making the test results unreliable.


3. Highlight the parameter changes in `mockVoidMethod` and `mockNonVoidMethod`:

    - Old:
   ```java
   public void add(String value)  
   {  
      mocks.mockVoidMethod(this, 'add', new List<Object> {value});  
   }
   ```

    - New:
   ```java
   public void add(String value)  
   {  
      mocks.mockVoidMethod(this, 'add', new List<Type> {System.Type.forName('String')}, new List<Object> {value});  
   }
   ```

4. When dealing with methods that have parameters including an Exception type, the Stub API might not work. In such cases, it's necessary to create stub objects manually or modify the method's implementation to avoid using Exception as a parameter.

   Example Error message:
   ```text
   System.TypeException: Class IPSAEmail__sfdc_ApexStub must implement the method: void IPSAEmail.sendExceptionWithExtraMsgToPsaTeam(String, Exception, String)(System Code)
   ```

5. Due to the use of dynamically generated stub objects, the old method of verifying methods will not work as before. Here's an example to show the change in the verify approach:

    - Old verify approach (with a mock):

   ```java
   public void sendEmailToPsaTeam(String subject, String body, Boolean needSandboxNameInSubject) {  
       // Use this variable to verify this method was called with the given subject
       this.subject = subject;  
       mocks.mockVoidMethod(this, 'sendEmailToPsaTeam', new List<Type>{ Type.forName('String'), Type.forName('String'), Type.forName('Boolean') }, new List<Object>{ subject, body, needSandboxNameInSubject });  
   }

   // Verify the method
   System.assertEquals('Error:Apex Job Failed', ((Mocks.PSAEmail) psaEmailService).subject);
   ```

    - New verify approach (using dynamically generated stub objects):

   ```java
   ((IPSAEmail)mocks.verify(psaEmailService, 1)).sendEmailToPsaTeam(fflib_Match.eqString('Error:Apex Job Failed'), fflib_Match.anyString(), fflib_Match.eqBoolean(true));
   ```


## Usage for Mock
### Verify Behavior Verification
#### Exact Verify
```Java  
// Given  
fflib_ApexMocks mocks = new fflib_ApexMocks();  
fflib_MyList.IList mockList = (fflib_MyList.IList)mocks.mock(fflib_MyList.class);  
  
// When  
mockList.add('bob');  
  
// Then  
((fflib_MyList.IList) mocks.verify(mockList)).add('bob');  
((fflib_MyList.IList) mocks.verify(mockList, fflib_ApexMocks.NEVER)).clear();  
```  

#### Further Assertions Using fflib_ArgumentCaptor
```Java
@isTest  
static void thatCanPerformFurtherAssertionsOnCapturedArgumentValue()  
{  
    // Given  
    fflib_ApexMocks mocks = new fflib_ApexMocks();  
    fflib_MyList mockList = (fflib_MyList)mocks.mock(fflib_MyList.class);  
  
    //When  
    TestInnerClass testValue = new TestInnerClass();  
    testValue.i = 4;  
    testValue.s = '5';  
  
    mockList.set(1, testValue);  
  
    //Then  
    fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(TestInnerClass.class);  
  
    ((fflib_MyList.IList) mocks.verify(mockList)).set(fflib_Match.anyInteger(),  argument.capture());  
  
    Object capturedArg = argument.getValue();  
    System.Assert.areNotEqual(null, capturedArg, 'CapturedArg should not be null');  
  
    System.Assert.isInstanceOfType(capturedArg, TestInnerClass.class, 'CapturedArg should be SObject, instead was ' + capturedArg);  
  
    TestInnerClass testValueCaptured = (TestInnerClass)capturedArg;  
  
    System.Assert.areEqual(4, testValueCaptured.i, 'the values inside the argument captured should be the same of the original one');  
    System.Assert.areEqual('5', testValueCaptured.s, 'the values inside the argument captured should be the same of the original one');  
}
```
### When Dependency Stubbing
#### Exact Dependency Stubbing
```Java  
fflib_ApexMocks mocks = new fflib_ApexMocks();  
fflib_MyList.IList mockList = (fflib_MyList.IList)mocks.mock(fflib_MyList.class);  
  
mocks.startStubbing();  
mocks.when(mockList.get(0)).thenReturn('bob');  
mocks.when(mockList.get(1)).thenReturn('fred');  
mocks.stopStubbing();  
```  
#### Using fflib_Match for Matchers
``` Java
@isTest  
static void whenStubSameMethodWithMatchersAndNonMatchersShouldStubInOrder()  
{  
    // Given  
    fflib_ApexMocks mocks = new fflib_ApexMocks();  
    fflib_MyList mockList = (fflib_MyList)mocks.mock(fflib_MyList.class);  
  
    mocks.startStubbing();  
  
    mocks.when(mockList.get2(1, 'Non-matcher first')).thenReturn('Bad'); //Set the return value using the non-matcher arguments  
    mocks.when(mockList.get2(fflib_Match.eqInteger(1), fflib_Match.stringContains('Non-matcher first'))).thenReturn('Good'); //Override the return value using matcher arguments  
  
    mocks.when(mockList.get2(fflib_Match.eqInteger(1), fflib_Match.stringContains('Matcher first'))).thenReturn('Bad'); //Set the return value using the matcher arguments  
    mocks.when(mockList.get2(1, 'Matcher first')).thenReturn('Good'); //Override the return value using non-matcher arguments  
  
    mocks.stopStubbing();  
  
    // When/Thens  
    System.Assert.areEqual('Good', mockList.get2(1, 'Non-matcher first'));  
    System.Assert.areEqual('Good', mockList.get2(1, 'Matcher first'));  
}
```
#### Implement fflib_IMatcher for Custom Matchers
``` Java
@isTest  
static void whenStubCustomMatchersCanBeUsed()  
{  
    // Given  
    fflib_ApexMocks mocks = new fflib_ApexMocks();  
    fflib_MyList mockList = (fflib_MyList)mocks.mock(fflib_MyList.class);  
  
    mocks.startStubbing();  
    mocks.when(mockList.get((Integer)fflib_Match.matches(new isOdd()))).thenReturn('Odd');  
    mocks.when(mockList.get((Integer)fflib_Match.matches(new isEven()))).thenReturn('Even');  
    mocks.stopStubbing();  
  
    // When  
    String s1 = mockList.get(1);  
    String s2 = mockList.get(2);  
    String s3 = mockList.get(3);  
    String s4 = mockList.get(4);  
    String s5 = mockList.get(5);  
  
    // Then  
    System.Assert.areEqual('Odd', s1);  
    System.Assert.areEqual('Even', s2);  
    System.Assert.areEqual('Odd', s3);  
    System.Assert.areEqual('Even', s4);  
    System.Assert.areEqual('Odd', s5);  
}

private class isOdd implements fflib_IMatcher  
{  
    public Boolean matches(Object arg)  
    {  
       return arg instanceof Integer ? Math.mod((Integer)arg, 2) == 1: false;  
    }  
}  
  
private class isEven implements fflib_IMatcher  
{  
    public Boolean matches(Object arg)  
    {  
       return arg instanceof Integer ? Math.mod((Integer)arg, 2) == 0: false;  
    }  
}
```

#### Implement fflib_Answer Interface for Interaction with Arguments

##### Using the Old Approach
```java
@isTest  
static void testMethod()  
{  
    // ...
    mocks.when(expReportSelector.insertExpenseReportWithResult(expenseReportToBeCreate, false)).thenReturn(createUpdateExpenseReportSuccessResultList);
    // ...
}

// Mocks
public Database.SaveResult[] insertExpenseReportWithResult(List<pse__Expense_Report__c> expenseReportList, Boolean allOrNone) {  
    insertedExpenseReports = expenseReportList;  
    expenseReportList.get(0).id = 'a000000000000X';  
    return (Database.SaveResult[]) mocks.mockNonVoidMethod(this, 'insertExpenseReportWithResult', new List<Object>{ expenseReportList, allOrNone });  
}
```

##### Implementing fflib_Answer to Enhance Interaction with Arguments
```java
@isTest  
static void testMethod()  
{  
    // ...
    mocks.when(expReportSelector.insertExpenseReportWithResult(expenseReportToBeCreate, false)).thenAnswer(  
        new ExpenseRawDataProcessQueueableTest.InsertReturnAndSetIdBackAnswer(createUpdateExpenseReportSuccessResultList)  
    );
    // ...
}

public class InsertReturnAndSetIdBackAnswer implements fflib_Answer  
{  
    private final List<Database.SaveResult> databaseSaveResult;  
    public InsertReturnAndSetIdBackAnswer(List<Database.SaveResult>  databaseSaveResult)  
    {  
        this.databaseSaveResult = databaseSaveResult;  
    }  
    public Object answer(fflib_InvocationOnMock invocation)  
    {  
        List<SObject> records = (List<SObject>) invocation.getArgument(0);  
        System.Assert.areNotEqual(null, records, 'The argument should have some value');  
        System.Assert.areNotEqual(null, databaseSaveResult, 'The argument should have some value');  
        System.assertEquals(records.size(), databaseSaveResult.size(), 'The argument should have the same size');  
        for (Integer i = 0; i < records.size(); i++)  
        {  
            if (databaseSaveResult[i].isSuccess()) {  
                records[i].put('Id', databaseSaveResult[i].getId());  
            }  
        }  
        return databaseSaveResult;  
    }  
}
```

## Utilties
### Setting a read-only field

```Java  
@isTest  
static void setReadOnlyFields_CreatedByIdSetToCurrentUserId_IdFieldSetSuccessfully() {  
  
    Account acc = new Account();  
    Id userId = fflib_IDGenerator.generate((new User()).getSObjectType());  
  
    Test.startTest();  
    acc = (Account)fflib_ApexMocksUtils.setReadOnlyFields(  
          acc,  
          Account.class,  
          new Map<SObjectField, Object>{Account.CreatedById => userId}  
    );  
    Test.stopTest();  
  
    System.Assert.areEqual(userId, acc.CreatedById);  
} 
```  
  

