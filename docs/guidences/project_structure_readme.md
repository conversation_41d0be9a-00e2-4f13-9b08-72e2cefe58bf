### Salesforce 项目目录结构与分层依赖说明文档

#### 概述

本项目采用模块化的目录结构，旨在提高代码的可维护性、扩展性和清晰度。通过分层管理和明确的依赖关系，确保各模块之间的低耦合、高内聚。此文档详细描述了项目中各个目录的功能、分层依赖关系，以及如何正确归档从环境中拉取的元数据，特别是权限集和其他可能存在依赖关系的元数据。

### 目录结构

```plaintext
force-app/
│
├── base/
│   ├── baseComponents/
│   ├── connect-api-helper/
│   ├── fflib-apex-mock/
│   ├── fflib-apex-common/
│   ├── log-service/
│   ├── trigger-framework/
│   └── utils/
│
├── core-crm/
│   ├── objects/
│   ├── objectTranslations/
│   ├── globalValueSets/
│   ├── standardValueSets/
│   ├── triggers/
│   ├── settings/
│   ├── LeadConvertSettings/
│   ├── queues/
│   ├── groups/
│   ├── PlatformEventSubscriberConfigs/
│   ├── remoteSiteSettings/
│   ├── classes/
│   └── permissionsets/
│
├── business-crm/
│   ├── aura/
│   ├── classes/
│   ├── customPermissions/
│   ├── flows/
│   ├── lwc/
│   ├── messageChannels/
│   ├── pathAssistants/
│   ├── permissionsets/
│   ├── remoteSiteSettings/
│   ├── reports/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   └── reportTypes/
│
├── business-psa/
│   ├── aura/
│   ├── classes/
│   ├── components/
│   ├── customPermissions/
│   ├── duplicateRules/
│   ├── email/
│   ├── flows/
│   ├── lwc/
│   ├── matchingRules/
│   ├── permissionsets/
│   ├── PlatformEventSubscriberConfigs/
│   ├── remoteSiteSettings/
│   ├── sharingRules/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   └── workflows/
│
├── business-sales/
│   ├── approvalProcesses/
│   ├── aura/
│   ├── classes/
│   ├── customPermissions/
│   ├── email/
│   ├── lwc/
│   ├── pathAssistants/
│   ├── permissionsets/
│   ├── PlatformEventSubscriberConfigs/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   └── workflows/
│
├── org-ui/
│   ├── applications/
│   ├── brandingSets/
│   ├── contentassets/
│   ├── documents/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   ├── lightningExperienceThemes/
│   ├── quickActions/
│   └── tabs/
│
├── org-access-mgmt/
│   ├── customPermissions/
│   ├── permissionsetgroups/
│   ├── permissionsets/
│   └── profiles/
│
├── org-unpacked/
│   └── (empty for future refactor)
│
└── sf-temp/
    └── (temporary storage for new metadata)
```

### 分层结构与依赖关系说明

1. **`sf-temp/`**：

    - **用途**：用于临时存储从环境中拉取的新元数据。此目录中的内容应在拉取后立即被分类移动到正确的模块目录。
    - **处理流程**：元数据拉取到 `sf-temp/` 后，根据其类型及归属领域决定将其移至合适的模块目录中。

2. **`base/`**：

    - **用途**：基础层，包含组织内的通用技术组件，如 API 帮助类、日志服务、触发器框架等基础功能模块。
    - **依赖关系**：其他所有模块均可以依赖`base/`，但`base/`本身不依赖任何上层模块。

3. **`core-crm/`**：

    - **用途**：核心 CRM 模型，包括组织内的通用业务能力，如 User / Resource Contact / Exchange Rate / Region / Org structure 的管理，另外，因为 Salesforce 部分元数据管理颗粒度不够细，此目录也包括对象和字段定义、全局值集、标准值集、触发器、组织级设置，作为所有业务领域模块的核心基础。
    - **依赖关系**：`core-crm/` 依赖于 `base/`，其他业务模块依赖于 `core-crm/`。

4. **业务领域模块** (`business-crm/`, `business-psa/`, `business-sales/`)：

    - **用途**：按团队或功能划分的业务领域模块，包含特定领域的类、组件、权限集、报告等。
    - **依赖关系**：业务领域模块只能依赖于`core-crm/`和`base/`，不得相互依赖，确保独立性和可维护性。

5. **`org-ui/`**：

    - **用途**：组织级别的 UI 配置，如应用程序、页面布局、快速操作、主题等。
    - **依赖关系**：`org-ui/` 可以依赖 `base/`、`core-crm/` 以及特定业务领域模块中的 UI 组件。

6. **`org-access-mgmt/`**：

    - **用途**：管理组织级别的访问控制，包括权限集、权限集组、自定义权限等。
    - **依赖关系**：`org-access-mgmt/` 依赖于 `base/` 和 `core-crm/`，并可以引用业务领域模块中的特定权限配置。此模块应在其他包之后部署。

7. **`org-unpacked/`**：
    - **用途**：临时存储尚未分类的元数据，随着项目的推进，应逐渐清理和将其内容分类到正确的模块中。
    - **依赖关系**：`org-unpacked/` 不依赖其他模块，应尽快移出其内容。

### 元数据的归档策略

为了确保元数据的合理归档，提高代码的可维护性和模块化管理，本策略提供了具体的归档指引，帮助开发人员正确地将元数据放置在适当的目录中。

#### 1. **核心元数据**

-   **无条件归档到 `core-crm/`**：
    -   **Objects**: 所有标准和自定义对象 (`objects/`)。
    -   **Object Translations**: 所有对象的翻译文件 (`objectTranslations/`)。
    -   **Global Value Sets**: 全局值集 (`globalValueSets/`)。
    -   **Standard Value Sets**: 标准值集 (`standardValueSets/`)。
    -   **Triggers**: 所有触发器 (`triggers/`)。
    -   **Settings**: 组织级的设置 (`settings/`)。
    -   **Lead Convert Settings**: 潜在客户转化设置 (`LeadConvertSettings/`)。
    -   **Queues**: 队列配置 (`queues/`)。
    -   **Groups**: 组配置 (`groups/`)。

#### 2. **条件归档元数据**

这些元数据可能会依据具体情况决定它们的归档位置：

-   **`PlatformEventSubscriberConfigs`**：

    -   **归档到 `core-crm/`**：如果用于组织范围或多个领域的核心平台事件订阅配置。
    -   **归档到业务包**：如果仅用于特定业务领域的事件订阅。

-   **`RemoteSiteSettings`**：

    -   **归档到 `core-crm/`**：如果设置是核心功能的一部分且跨域使用。
    -   **归档到业务包**：如果仅与特定业务领域相关联。

-   **`Classes`**：

    -   **归档到 `core-crm/`**：如果类是全局性的、组织级的工具或服务类。
    -   **归档到业务包**：如果类主要服务于某一业务领域模块的逻辑。

-   **`Permission Sets` 和 `Permission Set Groups`**：

    -   **归档到 `org-access-mgmt/`**：如果权限集跨多个业务领域，或者依赖于组织级别的设置和配置。
    -   **归档到业务包**：如果权限集仅适用于单一业务领域模块，不涉及跨域依赖。

-   **`Custom Permissions`**：
    -   **跟随 `Permission Sets`**：与其关联的权限集一起归档，无论是在业务包还是 `org-access-mgmt/` 中。

#### 3. **UI 相关元数据**

-   **无条件归档到 `org-ui/`**：

    -   **Applications**: 应用程序配置 (`applications/`)。
    -   **Branding Sets**: 品牌设置 (`brandingSets/`)。
    -   **Lightning Experience Themes**: Lightning 体验主题 (`lightningExperienceThemes/`)。

-   **`Documents`**：

    -   **归档到 `org-ui/`**：如果文档与 `tabs` 或其他 UI 组件有关。
    -   **归档到业务包**：如果文档仅用于特定业务领域。

-   **布局和页面配置**：
    -   **默认位置：** `Tabs`, `Quick Actions`, `Flexipages`, `Layouts`, `Pages` 应放在 `org-ui` 层。  
        **原因：** 这些配置通常与UI相关，放在 `org-ui` 层可以方便全局管理和复用。

    -   **例外情况：** 如果确定这些元数据不会引用其他业务包的内容（如LWC, Quick Actions），可以将它们直接放入对应的业务包中。  
        **注意：** 如果页面引用了如Quick Actions，确保这些Quick Actions也在同一个业务包内。这样可以避免下层依赖上层，防止循环依赖的出现。

  

### 元数据归档的操作指南

1. **拉取元数据到 `sf-temp/`**：

    - 新元数据从环境中拉取后，默认会放置在 `sf-temp/` 目录中。熟练的开发人员也可以根据元数据的最终归档位置，直接在目标目录中创建相应的 `-meta.xml` 文件，并从环境中直接拉取具体的元数据到正确的位置。

2. **确定元数据归档位置**：

    - 根据元数据的类型及其所属领域，决定将其移动到 `base/`、`core-crm/`、业务领域模块、`org-ui/` 或 `org-access-mgmt/` 中。
    - 避免将跨域依赖的元数据放置在业务领域模块中。

3. **更新与清理**：
    - 在将元数据从 `sf-temp/` 归档到适当的目录后，立即删除 `sf-temp/` 中的相关文件。
    - 定期检查 `org-unpacked/` 目录，将其内容分类并归档，最终清空该目录。

### 总结

通过清晰的分层架构和明确的依赖管理，可以确保项目的高可维护性和易扩展性。权限集及其他可能产生依赖的元数据必须根据它们的作用范围和依赖关系进行正确归档，以确保模块化设计的良好实践。这种策略有助于团队在开发、部署和维护过程中保持一致性和高效性。

---

### Salesforce Project Directory Structure and Layered Dependency Documentation

#### Overview

This project adopts a modular directory structure designed to enhance code maintainability, scalability, and clarity. By managing layers and defining explicit dependencies, we ensure low coupling and high cohesion among modules. This document provides a detailed description of the functions of each directory, the layered dependencies, and guidelines on properly archiving metadata pulled from the environment, with a particular focus on permission sets and other metadata that may have dependencies.

### Directory Structure

```plaintext
force-app/
│
├── base/
│   ├── baseComponents/
│   ├── connect-api-helper/
│   ├── fflib-apex-mock/
│   ├── fflib-apex-common/
│   ├── log-service/
│   ├── trigger-framework/
│   └── utils/
│
├── core-crm/
│   ├── objects/
│   ├── objectTranslations/
│   ├── globalValueSets/
│   ├── standardValueSets/
│   ├── triggers/
│   ├── settings/
│   ├── LeadConvertSettings/
│   ├── queues/
│   ├── groups/
│   ├── PlatformEventSubscriberConfigs/
│   ├── remoteSiteSettings/
│   ├── classes/
│   └── permissionsets/
│
├── business-crm/
│   ├── aura/
│   ├── classes/
│   ├── customPermissions/
│   ├── flows/
│   ├── lwc/
│   ├── messageChannels/
│   ├── pathAssistants/
│   ├── permissionsets/
│   ├── remoteSiteSettings/
│   ├── reports/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   └── reportTypes/
│
├── business-psa/
│   ├── aura/
│   ├── classes/
│   ├── components/
│   ├── customPermissions/
│   ├── duplicateRules/
│   ├── email/
│   ├── flows/
│   ├── lwc/
│   ├── matchingRules/
│   ├── permissionsets/
│   ├── PlatformEventSubscriberConfigs/
│   ├── remoteSiteSettings/
│   ├── sharingRules/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   └── workflows/
│
├── business-sales/
│   ├── approvalProcesses/
│   ├── aura/
│   ├── classes/
│   ├── customPermissions/
│   ├── email/
│   ├── lwc/
│   ├── pathAssistants/
│   ├── permissionsets/
│   ├── PlatformEventSubscriberConfigs/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   └── workflows/
│
├── org-ui/
│   ├── applications/
│   ├── brandingSets/
│   ├── contentassets/
│   ├── documents/
│   ├── flexipages/
│   ├── layouts/
│   ├── pages/
│   ├── lightningExperienceThemes/
│   ├── quickActions/
│   └── tabs/
│
├── org-access-mgmt/
│   ├── customPermissions/
│   ├── permissionsetgroups/
│   ├── permissionsets/
│   └── profiles/
│
├── org-unpacked/
│   └── (empty for future refactor)
│
└── sf-temp/
    └── (temporary storage for new metadata)
```

### Layered Structure and Dependency Explanation

1. **`sf-temp/`**:

    - **Purpose**: Temporary storage for newly pulled metadata from the environment. The content in this directory should be promptly classified and moved to the appropriate module directory after being pulled.
    - **Process**: After metadata is pulled into `sf-temp/`, it should be moved to the appropriate module directory based on its type and domain.

2. **`base/`**:

    - **Purpose**: The foundational layer containing general technical components within the organization, such as API helper classes, logging services, and trigger frameworks.
    - **Dependencies**: All other modules can depend on `base/`, but `base/` itself should not depend on any upper-level modules.

3. **`core-crm/`**:

    - **Purpose**: Core CRM model, including general business capabilities within the organization such as managing User/Resource Contact/Exchange Rate/Region/Org structure. Additionally, due to the granularity limitations in Salesforce metadata management, this directory also includes object and field definitions, global value sets, standard value sets, triggers, and org-level settings, serving as the core foundation for all business domain modules.
    - **Dependencies**: `core-crm/` depends on `base/`, and other business modules depend on `core-crm/`.

4. **Business Domain Modules** (`business-crm/`, `business-psa/`, `business-sales/`):

    - **Purpose**: Business domain modules categorized by team or function, containing classes, components, permission sets, reports, etc., specific to the domain.
    - **Dependencies**: Business domain modules can only depend on `core-crm/` and `base/` and should not depend on each other to ensure independence and maintainability.

5. **`org-ui/`**:

    - **Purpose**: Organization-level UI configurations such as applications, page layouts, quick actions, themes, etc.
    - **Dependencies**: `org-ui/` can depend on `base/`, `core-crm/`, and specific UI components within the business domain modules.

6. **`org-access-mgmt/`**:

    - **Purpose**: Manages organization-level access control, including permission sets, permission set groups, custom permissions, etc.
    - **Dependencies**: `org-access-mgmt/` depends on `base/` and `core-crm/`, and may reference specific permission configurations from business domain modules. This module should be deployed after other packages.

7. **`org-unpacked/`**:
    - **Purpose**: Temporary storage for uncategorized metadata, which should be gradually cleaned and its content categorized into the appropriate modules as the project progresses.
    - **Dependencies**: `org-unpacked/` does not depend on other modules and should be cleared as soon as possible.

### Metadata Archiving Strategy

To ensure proper archiving of metadata, improve code maintainability, and facilitate modular management, this strategy provides specific archiving guidelines to help developers correctly place metadata in the appropriate directories.

#### 1. **Core Metadata**

-   **Unconditional Archiving to `core-crm/`**:
    -   **Objects**: All standard and custom objects (`objects/`).
    -   **Object Translations**: Translation files for all objects (`objectTranslations/`).
    -   **Global Value Sets**: Global value sets (`globalValueSets/`).
    -   **Standard Value Sets**: Standard value sets (`standardValueSets/`).
    -   **Triggers**: All triggers (`triggers/`).
    -   **Settings**: Org-level settings (`settings/`).
    -   **Lead Convert Settings**: Lead conversion settings (`LeadConvertSettings/`).
    -   **Queues**: Queue configurations (`queues/`).
    -   **Groups**: Group configurations (`groups/`).

#### 2. **Conditional Metadata Archiving**

The archiving location of these metadata may vary depending on specific circumstances:

-   **`PlatformEventSubscriberConfigs`**:

    -   **Archive to `core-crm/`**: If used for organization-wide or multi-domain core platform event subscriptions.
    -   **Archive to Business Package**: If used solely for a specific business domain.

-   **`RemoteSiteSettings`**:

    -   **Archive to `core-crm/`**: If the setting is part of core functionality and used across domains.
    -   **Archive to Business Package**: If related to a specific business domain only.

-   **`Classes`**:

    -   **Archive to `core-crm/`**: If the class is a global or org-level utility or service class.
    -   **Archive to Business Package**: If the class primarily serves the logic of a specific business domain module.

-   **`Permission Sets` and `Permission Set Groups`**:

    -   **Archive to `org-access-mgmt/`**: If the permission set spans multiple business domains or depends on organization-level settings and configurations.
    -   **Archive to Business Package**: If the permission set is specific to a single business domain module without cross-domain dependencies.

-   **`Custom Permissions`**:
    -   **Follow `Permission Sets`**: Archive alongside associated permission sets, whether in a business package or `org-access-mgmt/`.

#### 3. **UI-Related Metadata**

-   **Unconditional Archiving to `org-ui/`**:

    -   **Applications**: Application configurations (`applications/`).
    -   **Branding Sets**: Branding settings (`brandingSets/`).
    -   **Lightning Experience Themes**: Lightning experience themes (`lightningExperienceThemes/`).

-   **`Documents`**:

    -   **Archive to `org-ui/`**: If related to `tabs` or other UI components.
-   **Archive to Business Package**: If used only within a specific business domain.

-   **Layouts and Page Configurations**:
    -   **Default Location**: `Tabs`, `Quick Actions`, `Flexipages`, `Layouts`, and `Pages` should be placed in the `org-ui` layer.  
        **Reason**: These configurations are usually UI-related, and placing them in the `org-ui` layer allows for convenient global management and reuse.

    -   **Exceptions**: If certain metadata does not reference content from other business packages (e.g., LWC, Quick Actions), they can be directly placed in the corresponding business package.  
        **Note**: If a page references Quick Actions, ensure that these Quick Actions are also within the same business package. This avoids lower-level dependencies on upper-level packages and prevents circular dependencies.

### Metadata Archiving Operational Guidelines

1. **Pull Metadata to `sf-temp/`**:

    - New metadata pulled from the environment will be placed in the `sf-temp/` directory by default. Experienced developers may directly create the corresponding `-meta.xml` file in the target directory and pull specific metadata from the environment into the correct location.

2. **Determine Metadata Archiving Location**:

    - Decide where to move the metadata based on its type and domain, choosing from `base/`, `core-crm/`, business domain modules, `org-ui/`, or `org-access-mgmt/`.
    - Avoid placing cross-domain dependent metadata in business domain modules.

3. **Update and Cleanup**:
    - After archiving metadata from `sf-temp/` to the appropriate directory, promptly delete the related files from `sf-temp/`.
    - Regularly check the `org-unpacked/` directory, categorize its content, and archive it, ultimately clearing the directory.

### Summary

By implementing a clear layered architecture and well-defined dependency management, we ensure high maintainability and scalability of the project. Permission sets and other metadata with potential dependencies must be archived correctly according to their scope and dependencies to adhere to best practices in modular design. This strategy helps the team maintain consistency and efficiency during development, deployment, and maintenance.

> Decision log for each metadata https://docs.google.com/spreadsheets/d/1AuRbpTC918DJzD8iN5d3HoCFeVAvbB6E5u4Hgm1N5Ho/edit?usp=sharing

