version: 2.1

parameters:
    SANDBOX_TO_REFRESH:
        type: string
        default: ""

    BASE_COMMIT_TO_REFRESH:
        type: string
        default: ""

    COMMIT_TO_REFRESH:
        type: string
        default: ""

    SCHEDULED_WORKFLOW_NAME:
        type: string
        default: ""

    SETUP_SANDBOX_AFTER_REFRESH:
        type: string
        default: ""

executors:
    docker-medium-debian12-nodejs-typescript-salesforcecli:
        docker:
            - image: eric2369/debian12-nodejs-typescript-salesforcecli:latest
        resource_class: medium
        environment:
            SF_CONTAINER_MODE: "true"
            SF_CRYPTO_V2: "false"
            SF_DISABLE_AUTOUPDATE: "true"
            SF_DISABLE_DNS_CHECK: "true"
            SF_DISABLE_TELEMETRY: "true"
            SF_JSON_TO_STDOUT: "true"
            SF_ORG_METADATA_REST_DEPLOY: "true"
            SF_SKIP_NEW_VERSION_CHECK: "true"
            SF_USE_PROGRESS_BAR: "false"
            SF_DISABLE_SOURCE_MEMBER_POLLING: "true"
            SF_USE_GENERIC_UNIX_KEYCHAIN: "false"

    docker-medium-debian12-nodejs-typescript-salesforcecli-chromium:
        docker:
            - image: eric2369/debian12-nodejs-typescript-salesforcecli-chromium:latest
        resource_class: medium
        environment:
            SF_CONTAINER_MODE: "true"
            SF_CRYPTO_V2: "false"
            SF_DISABLE_AUTOUPDATE: "true"
            SF_DISABLE_DNS_CHECK: "true"
            SF_DISABLE_TELEMETRY: "true"
            SF_JSON_TO_STDOUT: "true"
            SF_ORG_METADATA_REST_DEPLOY: "true"
            SF_SKIP_NEW_VERSION_CHECK: "true"
            SF_USE_PROGRESS_BAR: "false"
            SF_DISABLE_SOURCE_MEMBER_POLLING: "true"
            SF_USE_GENERIC_UNIX_KEYCHAIN: "false"

    docker-large-cimg-node:
        docker:
            - image: cimg/node:22.11.0
        resource_class: large

    docker-small-cimg-node:
        docker:
            - image: cimg/node:22.11.0
        resource_class: small

    docker-small-cimg-python:
        docker:
            - image: cimg/python:3.10
        resource_class: small

    docker-medium-cimg-openjdk:
        docker:
            - image: cimg/openjdk:20.0.1-node
        resource_class: medium

    docker-medium-docker-python-nodejs-sfdx:
        docker:
            - image: thoughtworkswuhan/docker-python-nodejs-sfdx:latest
        resource_class: medium

commands:
    install_root_dependencies_root_salesforcecli:
        description: "Install dependencies for root"
        steps:
            - restore_cache:
                  keys:
                      - v1-npm-deps-salesforcecli-{{ checksum "package-lock.json" }}
            - run:
                  name: Setup .npmrc for GitHub Packages
                  command: |
                      echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" > ~/.npmrc
                      echo "@techops-e2ecs:registry=https://npm.pkg.github.com" >> ~/.npmrc
            - run:
                  name: install dependency
                  command: npm install --no-audit
            - save_cache:
                  paths:
                      - node_modules
                  key: v1-npm-deps-salesforcecli-{{ checksum "package-lock.json" }}

    install_root_dependencies_root_node:
        description: "Install dependencies for root"
        steps:
            - restore_cache:
                  keys:
                      - v1-npm-deps-node-{{ checksum "package-lock.json" }}
            - run:
                  name: Setup .npmrc for GitHub Packages
                  command: |
                      echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" > ~/.npmrc
                      echo "@techops-e2ecs:registry=https://npm.pkg.github.com" >> ~/.npmrc
            - run:
                  name: install dependency
                  command: npm install --no-audit
            - save_cache:
                  paths:
                      - node_modules
                  key: v1-npm-deps-node-{{ checksum "package-lock.json" }}

    install_next_gen_dependencies:
        description: "Install dependencies for next-gen"
        steps:
            - restore_cache:
                  keys:
                      - v1-npm-deps-{{ checksum "deploy/next-gen/package-lock.json" }}
            - run:
                  name: Setup .npmrc for GitHub Packages
                  command: |
                      echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" > ~/.npmrc
                      echo "@techops-e2ecs:registry=https://npm.pkg.github.com" >> ~/.npmrc
            - run:
                  name: install dependency
                  command: |
                      cd deploy/next-gen && npm install --no-audit
            - save_cache:
                  paths:
                      - deploy/next-gen/node_modules
                  key: v1-npm-deps-{{ checksum "deploy/next-gen/package-lock.json" }}

    install_playwright_dependencies:
        description: "Install dependencies for playwright"
        steps:
            - restore_cache:
                  keys:
                      - v1-npm-deps-{{ checksum "playwright-tests/package-lock.json" }}
            - run:
                  name: Setup .npmrc for GitHub Packages
                  command: |
                      echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" > ~/.npmrc
                      echo "@techops-e2ecs:registry=https://npm.pkg.github.com" >> ~/.npmrc
            - run:
                  name: install dependency
                  command: |
                      cd playwright-tests && npm install --no-audit
            - save_cache:
                  paths:
                      - playwright-tests/node_modules
                  key: v1-npm-deps-{{ checksum "playwright-tests/package-lock.json" }}

    install_sfdx_browserforce_plugin:
        description: "Install sfdx-browserforce-plugin"
        steps:
            - run:
                  name: install sfdx-browserforce-plugin
                  command: |
                      if [ -n "<< pipeline.parameters.SETUP_SANDBOX_AFTER_REFRESH >>" ] && [ "<< pipeline.parameters.SETUP_SANDBOX_AFTER_REFRESH >>" = "true" ]; then
                      echo y | sf plugins:install sfdx-browserforce-plugin@4.1.2
                      fi

    compile_lwc_typescript:
        description: "Compile LWC TypeScript"
        steps:
            - run:
                  name: compile lwc typescript
                  command: npm run compile:ts

    store_next_gen_logs:
        description: "Store logs for next-gen"
        steps:
            - store_artifacts:
                  path: /root/project/deploy/next-gen/logs

    store_coverage:
        description: "Store coverage data"
        steps:
            - run:
                  name: compress test results
                  command: |
                      if [ -d /root/project/deploy/next-gen/coverage-data ]; then
                        tar -czvf /root/project/coverage-data.tar.gz /root/project/deploy/next-gen/coverage-data
                      else
                        echo "Test results directory does not exist, skipping compression."
                      fi
            - store_artifacts:
                  path: /root/project/deploy/next-gen/coverage-data
            - store_artifacts:
                  path: /root/project/coverage-data.tar.gz
            - persist_to_workspace:
                  root: /root/project/deploy/next-gen
                  paths:
                      - coverage-data

    get_coverage_from_previous_job:
        description: "Get coverage data from previous job"
        steps:
            - attach_workspace:
                  at: /tmp/workspace
            - run:
                  name: replace coverage data
                  command: |
                      if [ -f /tmp/workspace/coverage-data/coverage-data.json ]; then
                        cp -f /tmp/workspace/coverage-data/coverage-data.json /root/project/deploy/next-gen/config/coverage-data-release.json
                        echo "coverage-data.json has been replaced."
                      else
                        echo "coverage-data.json does not exist in the workspace."
                      fi
                      if [ -f /tmp/workspace/coverage-data/coverage-data-aggregate.json ]; then
                        cp -f /tmp/workspace/coverage-data/coverage-data-aggregate.json /root/project/deploy/next-gen/config/coverage-data-aggregate-release.json
                        echo "coverage-data-aggregate.json has been replaced."
                      else
                        echo "coverage-data-aggregate.json does not exist in the workspace."
                      fi

    get_deployed_job_config:
        description: "Get deployed job config"
        steps:
            - attach_workspace:
                  at: /tmp/workspace
            - run:
                  name: replace deploy-validated-job-config.json
                  command: |
                      if [ -f /tmp/workspace/deploy-validated-job-config.json ]; then
                        cp -f /tmp/workspace/deploy-validated-job-config.json /root/project/deploy/next-gen/config/deploy-validated-job-config.json
                        cat /root/project/deploy/next-gen/config/deploy-validated-job-config.json
                        echo "deploy-validated-job-config.json has been replaced."
                      else
                        echo "deploy-validated-job-config.json does not exist in the workspace."
                      fi

    store_ui_test_logs:
        description: "Store UI test details"
        steps:
            - store_artifacts:
                  path: ./ui-tests/logs
            - store_artifacts:
                  path: ./ui-tests/video-record

    store_deployed_job_config:
        description: "Store deployed job config"
        steps:
            - persist_to_workspace:
                  root: /root/project/deploy/next-gen/config
                  paths:
                      - deploy-validated-job-config.json

    deploy-sandbox:
        description: "Deploy metadata to sandbox"
        parameters:
            deployTo:
                type: string
            deployMode:
                type: string
            needSetup:
                type: string
                default: "false"
            skipConflictCheck:
                type: string
                default: "false"
        steps:
            - install_root_dependencies_root_salesforcecli
            - compile_lwc_typescript
            - install_next_gen_dependencies
            - run:
                  name: deploy sandbox
                  command: tsx ./deploy/next-gen/index.ts --deployMode << parameters.deployMode >> --deployTo << parameters.deployTo >> --needSetup << parameters.needSetup >> --skipConflictCheck << parameters.skipConflictCheck >>
                  no_output_timeout: 30m
            - store_next_gen_logs

    deploy-sandbox-and-run-test:
        description: "Deploy metadata to sandbox and run test"
        parameters:
            deployTo:
                type: string
            deployMode:
                type: string
            testMode:
                type: string
            needSetup:
                type: string
                default: "false"
            skipConflictCheck:
                type: string
                default: "false"
        steps:
            - install_root_dependencies_root_salesforcecli
            - compile_lwc_typescript
            - install_next_gen_dependencies
            - run:
                  name: deploy sandbox and run test
                  command: tsx ./deploy/next-gen/index.ts --deployMode << parameters.deployMode >> --testMode << parameters.testMode >> --deployTo << parameters.deployTo >> --needSetup << parameters.needSetup >> --skipConflictCheck << parameters.skipConflictCheck >>
                  no_output_timeout: 30m
            - store_next_gen_logs

jobs:
    secret-scan:
        executor: docker-small-cimg-python
        parallelism: 1
        steps:
            - checkout
            - run:
                  name: secret scan
                  command: bash ./deploy/script/identify_secret.sh
                  no_output_timeout: 10m

    semgrep-scan:
        executor: docker-small-cimg-python
        parallelism: 1
        steps:
            - checkout
            - run:
                  name: semgrep scan
                  command: |
                      pip3 install semgrep
                      ./deploy/script/semgrep_scan.sh
                  no_output_timeout: 10m

    structure-scan:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - install_next_gen_dependencies
            - run:
                  name: check destructiveChangesPost and destructiveChangesPre
                  command: tsx ./deploy/next-gen/checkDestructiveChanges.ts
                  no_output_timeout: 30m
            - run:
                  name: check sf-temp
                  command: ./deploy/script/check_sf_temp.sh
            - run:
                  name: check profile and permissionset format
                  command: npm install && node deploy/script/formatProfilesAndPermissionSets.js ./force-app --checkAndThrow

    lwc-unit-test:
        executor: docker-large-cimg-node
        parallelism: 1
        steps:
            - checkout
            - install_root_dependencies_root_node
            - compile_lwc_typescript
            - run:
                  name: run lwc unit test
                  command: npm run test:jest
                  no_output_timeout: 10m

    lwc-eslint-test:
        executor: docker-small-cimg-node
        parallelism: 1
        steps:
            - checkout
            - install_root_dependencies_root_node
            - compile_lwc_typescript
            - run:
                  name: run lwc eslint test
                  command: npm run test:eslint:lwc
                  no_output_timeout: 10m

    full-deploy-and-run-full-apex-test:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parameters:
            deployTo:
                type: string
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: FULL
                  testMode: FULL_APEX_TEST

    diff-deploy-and-run-related-apex-test:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        parameters:
            deployTo:
                type: string
        steps:
            - checkout
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: DIFF
                  testMode: AFFECTED_APEX_TEST

    full-validate-on-prod:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox-and-run-test:
                  deployTo: prod
                  deployMode: FULL_VALIDATE
                  testMode: FULL_APEX_TEST
            - store_deployed_job_config

    check-full-validate-on-prod-result:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - get_deployed_job_config
            - deploy-sandbox-and-run-test:
                  deployTo: prod
                  deployMode: FULL_VALIDATE
                  testMode: FULL_APEX_TEST
            - store_deployed_job_config

    deploy-prod-with-validation-id:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - get_deployed_job_config
            - deploy-sandbox:
                  deployTo: prod
                  deployMode: VALIDATED_DEPLOY

    diff-deploy-and-run-full-apex-test-with-coverage:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parameters:
            deployTo:
                type: string
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: DIFF
                  testMode: FULL_APEX_TEST_WITH_COVERAGE
            - store_coverage

    full-deploy-and-run-full-apex-test-with-coverage:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parameters:
            deployTo:
                type: string
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: FULL
                  testMode: FULL_APEX_TEST_WITH_COVERAGE
            - store_coverage

    diff-deploy-and-run-ui-test:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli-chromium
        parameters:
            deployTo:
                type: string
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: DIFF
                  testMode: UI_TEST
            - store_ui_test_logs

    full-deploy-and-run-playwright-test:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli-chromium
        parameters:
            deployTo:
                type: string
        parallelism: 1
        steps:
            - checkout
            - install_playwright_dependencies
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: FULL
                  testMode: PLAYWRIGHT_TEST
            - store_artifacts:
                  path: /root/project/playwright-tests/test-results

    diff-deploy-and-run-playwright-test:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli-chromium
        parameters:
            deployTo:
                type: string
        parallelism: 1
        steps:
            - checkout
            - install_playwright_dependencies
            - deploy-sandbox-and-run-test:
                  deployTo: << parameters.deployTo >>
                  deployMode: DIFF
                  testMode: PLAYWRIGHT_TEST
            - store_artifacts:
                  path: /root/project/playwright-tests/test-results

    diff-deploy-uat:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: sfdcuat2
                  deployMode: DIFF

    full-deploy-uat:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: sfdcuat2
                  deployMode: FULL

    diff-deploy-prod:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - get_coverage_from_previous_job
            - deploy-sandbox-and-run-test:
                  deployTo: prod
                  deployMode: DIFF
                  testMode: AFFECTED_APEX_TEST

    diff-deploy-prod-without-test:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - get_coverage_from_previous_job
            - deploy-sandbox-and-run-test:
                  deployTo: prod
                  deployMode: DIFF
                  testMode: WITHOUT_APEX_TEST

    full-deploy-playground:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: crmpg
                  deployMode: FULL

    full-deploy-sfci-sandbox:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: sfci1,sfci2,sfci3,sfci4,sfci5,sfci6,sfci7,sfci8,sfci9,sfci10
                  deployMode: FULL

    full-deploy-crm-sandbox:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: crmci01,crmci02,crmci03,crmci04,crmci05,crmci06,crmci07,crmci08
                  deployMode: FULL

    full-deploy-psa-sandbox:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: psaci1,psaci2,psaci3,psaci4,psaci5,psaci6,psaci7,psaci8
                  deployMode: FULL

    full-deploy-psafttest-sandbox:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli
        parallelism: 1
        steps:
            - checkout
            - deploy-sandbox:
                  deployTo: PSAFTtest
                  deployMode: FULL

    deploy-sandbox-metadata:
        executor: docker-medium-debian12-nodejs-typescript-salesforcecli-chromium
        parallelism: 1
        steps:
            - checkout
            - install_sfdx_browserforce_plugin
            - run:
                  name: Print Refresh Information
                  command: |
                      if [ -z "<< pipeline.parameters.COMMIT_TO_REFRESH >>" ]; then
                          COMMIT_HASH=$(git rev-parse HEAD)
                      else
                          COMMIT_HASH="<< pipeline.parameters.COMMIT_TO_REFRESH >>"
                      fi
                      echo "Refreshing sandbox [<< pipeline.parameters.SANDBOX_TO_REFRESH >>] from commit $COMMIT_HASH to << pipeline.parameters.BASE_COMMIT_TO_REFRESH >>"
            - run:
                  name: Update Base Commit (if needed)
                  command: |
                      if [ -n "<< pipeline.parameters.BASE_COMMIT_TO_REFRESH >>" ]; then
                          echo '{"revision": "<< pipeline.parameters.BASE_COMMIT_TO_REFRESH >>"}' > ./deploy/next-gen/config/base-commit.json
                      fi
            - run:
                  name: Reset Git Commit (if needed)
                  command: |
                      if [ -n "<< pipeline.parameters.COMMIT_TO_REFRESH >>" ]; then
                          git reset --hard "<< pipeline.parameters.COMMIT_TO_REFRESH >>"
                      fi
            - run:
                  name: Set deploy mode
                  command: |
                      if [ -z "<< pipeline.parameters.BASE_COMMIT_TO_REFRESH >>" ]; then
                          echo 'export DEPLOY_MODE="FULL"' >> $BASH_ENV
                      else
                          echo 'export DEPLOY_MODE="DIFF"' >> $BASH_ENV
                      fi
            - deploy-sandbox:
                  deployTo: << pipeline.parameters.SANDBOX_TO_REFRESH >>
                  deployMode: ${DEPLOY_MODE}
                  skipConflictCheck: "true"
                  needSetup: << pipeline.parameters.SETUP_SANDBOX_AFTER_REFRESH >>

    static-analysis:
        executor: docker-medium-cimg-openjdk
        parallelism: 1
        steps:
            - checkout
            - run:
                  name: install sfdx-cli sfdx-scanner
                  command: |
                      node --version
                      sudo npm install @salesforce/cli --global
                      sfdx plugins:install @salesforce/sfdx-scanner@v3.26.0
            - run:
                  name: apex static analysis
                  command: sfdx scanner:run -o sfdx-scanner.html --engine pmd --pmdconfig ./pmd-apex-rulesets/quickstart.xml -t . -s 3
                  no_output_timeout: 10m
            - store_artifacts:
                  path: ./sfdx-scanner.html

workflows:
    test_and_uat_master:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - secret-scan:
                  filters:
                      branches:
                          only:
                              - master

            - semgrep-scan:
                  filters:
                      branches:
                          only:
                              - master

            - lwc-eslint-test:
                  filters:
                      branches:
                          only:
                              - master

            - lwc-unit-test:
                  filters:
                      branches:
                          only:
                              - master

            - structure-scan:
                  filters:
                      branches:
                          only:
                              - master

            - diff-deploy-and-run-related-apex-test:
                  deployTo: pmadev,ci6,ci7,ci8,ci9,ci10,ci11,ci12,ci13,ci14,ci15
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - master

            - diff-deploy-and-run-playwright-test:
                  deployTo: ci16,ci17
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - master

            - diff-deploy-uat:
                  requires:
                      - diff-deploy-and-run-related-apex-test
                  filters:
                      branches:
                          only:
                              - master

    test_crm:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - secret-scan:
                  filters:
                      branches:
                          only:
                              - /crm-.+/

            - semgrep-scan:
                  filters:
                      branches:
                          only:
                              - /crm-.+/

            - lwc-eslint-test:
                  filters:
                      branches:
                          only:
                              - /crm-.+/

            - lwc-unit-test:
                  filters:
                      branches:
                          only:
                              - /crm-.+/

            - structure-scan:
                  filters:
                      branches:
                          only:
                              - /crm-.+/

            - full-deploy-and-run-full-apex-test:
                  deployTo: crmci01,crmci02,crmci03,crmci04,crmci05,crmci06,crmci07,crmci08
                  requires:
                      - semgrep-scan
                      - secret-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - /crm-.+/

    test_psa:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - static-analysis:
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - secret-scan:
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - semgrep-scan:
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - structure-scan:
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - lwc-eslint-test:
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - lwc-unit-test:
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - full-deploy-and-run-full-apex-test:
                  deployTo: psaci1,psaci2,psaci3,psaci4,psaci5,psaci6,psaci7,psaci8
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - /psa-.+/

            - full-deploy-psafttest-sandbox:
                  context:
                      - sfdx-master
                  requires:
                      - full-deploy-and-run-full-apex-test
                  filters:
                      branches:
                          only:
                              - /psa-.+/

    test_salesfunnel:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - secret-scan:
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

            - semgrep-scan:
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

            - lwc-eslint-test:
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

            - lwc-unit-test:
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

            - structure-scan:
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

            - full-deploy-and-run-full-apex-test:
                  deployTo: sfci1,sfci2,sfci3,sfci4,sfci5,sfci6,sfci7,sfci8
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

            - full-deploy-and-run-playwright-test:
                  deployTo: sfci9,sfci10
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - /(sf|salesfunnel)-.+/

    deploy-prod:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - secret-scan:
                  filters:
                      branches:
                          only:
                              - release

            - semgrep-scan:
                  filters:
                      branches:
                          only:
                              - release

            - lwc-eslint-test:
                  filters:
                      branches:
                          only:
                              - release

            - lwc-unit-test:
                  filters:
                      branches:
                          only:
                              - release

            - structure-scan:
                  filters:
                      branches:
                          only:
                              - release

            - diff-deploy-and-run-full-apex-test-with-coverage:
                  deployTo: rlsci1,rlsci6,rlsci7,rlsci8,rlsci9,rlsci10,rlsci11,rlsci12,rlsci13,rlsci14,rlsci15,rlsci16,rlsci17,rlsci18,rlsci19,rlsci20
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - release

            - diff-deploy-and-run-ui-test:
                  deployTo: rlsci2,rlsci3
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-release

            - diff-deploy-and-run-playwright-test:
                  deployTo: rlsci4,rlsci5
                  requires:
                      - secret-scan
                      - semgrep-scan
                      - lwc-eslint-test
                      - lwc-unit-test
                      - structure-scan
                  filters:
                      branches:
                          only:
                              - release

            - confirm-diff-deploy-prod:
                  type: approval
                  requires:
                      - diff-deploy-and-run-full-apex-test-with-coverage
                  filters:
                      branches:
                          only:
                              - release

            - diff-deploy-prod:
                  requires:
                      - confirm-diff-deploy-prod
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-deployment-permission

            - full-deploy-playground:
                  requires:
                      - confirm-diff-deploy-prod
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-deployment-permission

            - full-deploy-playground:
                  requires:
                      - confirm-deploy-prod-with-validation-id
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-deployment-permission

            - full-deploy-playground:
                  requires:
                      - confirm-diff-deploy-prod-without-test
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-deployment-permission

            - ready-full-validate-on-prod:
                  type: approval
                  requires:
                      - diff-deploy-and-run-full-apex-test-with-coverage
                  filters:
                      branches:
                          only:
                              - release

            - full-validate-on-prod:
                  requires:
                      - ready-full-validate-on-prod
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-release

            - check-full-validate-on-prod-result:
                  requires:
                      - full-validate-on-prod
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-release

            - confirm-deploy-prod-with-validation-id:
                  type: approval
                  requires:
                      - check-full-validate-on-prod-result
                  filters:
                      branches:
                          only:
                              - release

            - deploy-prod-with-validation-id:
                  requires:
                      - confirm-deploy-prod-with-validation-id
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-deployment-permission

            - approve-diff-deploy-prod-without-test:
                  type: approval
                  requires:
                      - diff-deploy-and-run-full-apex-test-with-coverage
                  filters:
                      branches:
                          only:
                              - release

            - ready-diff-deploy-prod-without-test:
                  type: approval
                  requires:
                      - approve-diff-deploy-prod-without-test
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - SFDX quick deployment approval

            - confirm-diff-deploy-prod-without-test:
                  type: approval
                  requires:
                      - ready-diff-deploy-prod-without-test
                  filters:
                      branches:
                          only:
                              - release

            - diff-deploy-prod-without-test:
                  requires:
                      - confirm-diff-deploy-prod-without-test
                  filters:
                      branches:
                          only:
                              - release
                  context:
                      - sfdx-deployment-permission

    full_deploy_all_sandbox:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, "full_deploy_all_sandbox"]

        jobs:
            - full-deploy-and-run-full-apex-test-with-coverage:
                  deployTo: pmadev,ci6,ci7,ci8,ci9,ci10,ci11,ci12,ci13,ci14,ci15,ci16,ci17

            - full-deploy-sfci-sandbox:
                  requires:
                      - full-deploy-and-run-full-apex-test-with-coverage

            - full-deploy-crm-sandbox:
                  requires:
                      - full-deploy-and-run-full-apex-test-with-coverage

            - full-deploy-psa-sandbox:
                  requires:
                      - full-deploy-and-run-full-apex-test-with-coverage

            - full-deploy-uat:
                  requires:
                      - full-deploy-and-run-full-apex-test-with-coverage

            - full-deploy-psafttest-sandbox:
                  context:
                      - sfdx-master
                  requires:
                      - full-deploy-and-run-full-apex-test-with-coverage

    full_deploy_all_sandbox_release:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, "full_deploy_all_sandbox_release"]

        jobs:
            - full-deploy-and-run-full-apex-test-with-coverage:
                  deployTo: rlsci1,rlsci2,rlsci3,rlsci4,rlsci5,rlsci6,rlsci7,rlsci8,rlsci9,rlsci10,rlsci11,rlsci12,rlsci13,rlsci14,rlsci15,rlsci16,rlsci17,rlsci18,rlsci19,rlsci20

    deploy-dev-sandbox:
        when:
            and:
                - and:
                      - not:
                            equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, ""]
                      - not:
                            equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, "PSAFTtest"]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - deploy-sandbox-metadata

    full-deploy-psafttest-sandbox:
        when:
            and:
                - equal: [<< pipeline.parameters.SANDBOX_TO_REFRESH >>, "PSAFTtest"]
                - equal: [<< pipeline.parameters.SCHEDULED_WORKFLOW_NAME >>, ""]

        jobs:
            - full-deploy-psafttest-sandbox:
                  context:
                      - sfdx-master
